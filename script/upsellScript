#start/stop script for gunicorn

start() {
  echo "Starting gunicorn..."
  HO=`hostname`
  DC=$(echo $HO| cut -d'-' -f 1)
  USER=root
  ROOT_DIR_PATH=/opt/Hotel-Personalization
  CONDA_ENV=py36
  PYTHON_VERSION=3.6

  export LOG_PATH="/opt/logs/Hotel-Personalization/upsell"
  export CONDA_BIN="/opt/anaconda2/bin"
  export PYTHONPATH=$PYTHONPATH:$ROOT_DIR_PATH/common
  #echo "python path ==> $PYTHONPATH"
  export LD_LIBRARY_PATH="/opt/anaconda2/lib/"



  export KRB5_CONFIG=/opt/Hotel-Personalization/krb5.conf

  yes | $CONDA_BIN/conda create -n ${CONDA_ENV} python=${PYTHON_VERSION}
  source $CONDA_BIN/activate ${CONDA_ENV}

  cd $ROOT_DIR_PATH

#  yes | $CONDA_BIN/pip uninstall couchbase
#  rm -rf /home/<USER>/.cache/pip/
  pip install --no-cache-dir -r ./requirements.txt
  conda install -y pandas==0.20.3

  cd ./api
   /bin/su root -c "/usr/bin/nohup gunicorn -w 17 --threads 20 -b 0.0.0.0:8080 wsgi:manager --timeout 300 --graceful-timeout 60 --pid /opt/Hotel-Personalization/flask-pid.txt --access-logfile $LOG_PATH/gunicorn-upsell-access.log --access-logformat '%(h)s %(l)s %(u)s %(t)s \"%(r)s\" %(s)s %(b)s \"%(f)s\" \"%(a)s\" \"%(D)s\"'  --log-file $LOG_PATH/upsell-gunicorn.log &"
  echo "UpSell API Started"

  /usr/bin/curl "http://$DC-zabbix.mmt.mmt/zabbix/snooze.php?action=enable&hostname=$HO"
}
stop() {

#  export CONDA_BIN="/opt/anaconda2/bin"
#  source $CONDA_BIN/deactivate
  HO=`hostname`
  DC=$(echo $HO| cut -d'-' -f 1)
  USER=mmytu
  ROOT_DIR_PATH=/opt/Hotel-Personalization
  CONDA_ENV=py36
  PYTHON_VERSION=3.6

  echo "Getting Stats first before app shutdown..."
  /usr/bin/curl "http://$DC-zabbix.mmt.mmt/zabbix/snooze.php?action=disable&hostname=$HO"

  echo "Stopping gunicorn gracefully..."
  export CONDA_BIN="/opt/anaconda2/bin"

  kill -TERM `cat /opt/Hotel-Personalization/flask-pid.txt`
  sleep 20
  > /opt/Hotel-Personalization/flask-pid.txt

  ## killing all gunicorn workers forcefully if graceful shutdown failed ...
  # echo "going to kill gunicorn forcefully using user ${USER}"
  su -l ${USER} -c "ps -ef | grep gunicorn | grep -v grep  | awk '{print \$2}' |xargs -i kill -9 {}"

  echo "Gunicorn Stopped. Please Check if there is any gunicorn process running by typing 'pgrep gunicorn'"
  #rm -f /opt/dsa/subsys/mmtwebd
  echo
}

# status(){}

case "$1" in
  start)
    start
    ;;
  stop)
    stop
    ;;
  restart)
    stop
    sleep 20
    start
    ;;
  status)
    status
    ;;
*)

echo "Usage: gunicorn-mmtwebd {start|stop|restart|status}"
exit 1
esac


