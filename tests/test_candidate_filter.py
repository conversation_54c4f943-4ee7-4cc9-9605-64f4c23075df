from flask import Flask
from common import helpers as hp

from mockito import mock
from mockito import when
from unittest import TestCase
from unittest import mock as umock
from mockito.matchers import any

from candidate_generation.candidate_filter import candidate_filter
import os

class test_candidate_filter(TestCase):
    __context = None
    __dao_mysql = mock()
    __dao_mysql_mmt_read = mock()

    def setUp(self):
        app = Flask(__name__)
        app.config['LOG'] = hp.mdc_logger("greenhorn")
        app.config['MINIMUM_RANK_PERCENTAGE'] = 0.2
        app.config['MINIMUM_HOTELS_IN_CITY'] = 10
        app.config['MINIMUM_RANK_VALUE'] = '20'
        app.config['MAXIMUM_ROOM_NIGHTS'] = 1
        app.config['S3_FILE_HOTEL_LOCUS'] = "test_hotel_location_mapping_without_poi.csv"
        app.config['DATADIR'] = os.path.dirname(os.path.abspath(__file__))
        app.config['pms_prop'] = {"nh_min_content_score": 50, "nh_min_inventory_score": 0.5, "nh_min_review_score": 0.5}
        self.__context = app.app_context()
        self.__context.push()
        self.cf = candidate_filter(self.__dao_mysql, self.__dao_mysql_mmt_read)

    def test_filter_cities(self):
        actual_result = self.cf.filter_cities([("201808011633415288", "CTDEL"), ("201808011434164683", "CTBOM")], None)
        expected_result = ( [('201808011633415288', 'CTDEL')], {'201808011434164683'}, 'HOTEL_COUNT_IN_CITY < 10', None,
                            None, {'201808011434164683': '9'})
        assert actual_result == expected_result

    def test_filter_hotels_basis_content(self):
        hotel_to_attributes = {'201808011633415288': {'cs': 60}, '201808011434164683': {'cs': 40},
                               '201808011309528826': {'cs': 90}}
        actual_result = self.cf.filter_hotels_basis_content([("201808011633415288", "HC20070108183602794"),
                                                            ("201808011434164683", "HC200701081857021907"),
                                                            ("201808011309528826", "HC200701081936471936")],
                                                           hotel_to_attributes)
        expected_result = ([('201808011633415288', 'HC20070108183602794'), ('201808011309528826', 'HC200701081936471936')],
                           {'201808011434164683'}, 'CONTENT_SCORE < 50.0', None, None, {'201808011434164683': 40})
        assert actual_result == expected_result

    def test_filter_hotels_basis_inventory(self):
        hotel_to_attributes = {'201808011633415288': {'is': 0.6}, '201808011434164683': {'is': 0.4},
                               '201808011309528826': {'is': 0.9}}
        actual_result = self.cf.filter_hotels_basis_inventory([("201808011633415288", "HC20070108183602794"),
                                                            ("201808011434164683", "HC200701081857021907"),
                                                            ("201808011309528826", "HC200701081936471936")],
                                                           hotel_to_attributes)
        expected_result = ([('201808011633415288', 'HC20070108183602794'), ('201808011309528826', 'HC200701081936471936')],
                           {'201808011434164683'}, 'INVENTORY_SCORE < 0.5', None, None, {'201808011434164683': 0.4})
        assert actual_result == expected_result

    def test_filter_hotels_basis_review(self):
        hotel_to_attributes = {'201808011633415288': {'rs': 0.6}, '201808011434164683': {'rs': 0.4},
                               '201808011309528826': {'rs': 0.9}}
        actual_result = self.cf.filter_hotels_basis_review([("201808011633415288", "HC20070108183602794"),
                                                            ("201808011434164683", "HC200701081857021907"),
                                                            ("201808011309528826", "HC200701081936471936")],
                                                           hotel_to_attributes)
        expected_result = ([('201808011633415288', 'HC20070108183602794'), ('201808011309528826', 'HC200701081936471936')],
                           {'201808011434164683'}, 'REVIEW_SCORE < 0.5', None, None, {'201808011434164683': 0.4})
        assert actual_result == expected_result

    def test_filter_fraud_hotels(self):
        mock = umock.Mock()
        cursor = mock.connection.cursor.return_value
        cursor.fetchall.return_value = [("201808011434164683", "")]
        mock.connection.cursor().execute("SELECT 1")
        when(self.__dao_mysql_mmt_read).get_fraud_hotels(any(object)).thenReturn(cursor)
        actual_result = self.cf.filter_fraud_hotels([("201808011434164683", "HC200701081857021907"),
                                                     ("201808011633415288", "HC20070108183602794")], None)
        expected_result = ([('201808011633415288', 'HC20070108183602794')], {'201808011434164683'}, 'FRAUD_HOTEL', 'NA',
                           None, None)
        assert actual_result == expected_result

    def test_filter_hotel_basis_exposure(self):
        hotel_city_list = [('201808011434164683', "HC200701081857021907"), ('201808011633415288', "HC20070108183602794")]
        mock = umock.Mock()
        cursor = mock.connection.cursor.return_value
        cursor.fetchall.return_value = [("201808011434164683", "")]
        mock.connection.cursor().execute("SELECT 1")
        when(self.__dao_mysql).get_exposed_hotels(any(object), "seq_new").thenReturn(cursor)
        actual_result = self.cf.filter_hotel_basis_exposure(hotel_city_list, None, "seq", "seq_new", "device")
        expected_result = ([('201808011633415288', 'HC20070108183602794')], {'201808011434164683'}, 'HOTEL_EXPOSED_TO_GH',
                            'NA', None, None)
        assert actual_result == expected_result


    def test_filter_hotel_basis_rank(self):
        hotel_city_list = [('201808011434164681', "HC200701081857021907"), ('201808011633415288', "HC20070108183602794"),
                           ('201808011434164682', "HC200701081857021907"), ('201808011434164683', "HC200701081857021907")]
        city_hotelcount_dict = {}
        city_hotelcount_dict.update({"HC200701081857021907": 200})
        city_hotelcount_dict.update({"HC20070108183602794": 15})
        mock = umock.Mock()
        cursor = mock.connection.cursor.return_value
        cursor.fetchall.return_value = [("201808011434164681", "HC200701081857021907", 10),
                                        ("201808011434164682", "HC200701081857021907", 30),
                                        ("201808011434164683", "HC200701081857021907", 50)]
        mock.connection.cursor().execute("SELECT 1")
        when(self.__dao_mysql).get_hotels_basis_rank(any(object), "HSEQ" + "seq").thenReturn(cursor)
        actual_result = self.cf.filter_hotel_basis_rank(hotel_city_list, city_hotelcount_dict, "seq", "seq_new", "device")
        expected_result = ([('201808011633415288', "HC20070108183602794"), ('201808011434164682', "HC200701081857021907"),
                            ('201808011434164683', "HC200701081857021907")], {'201808011434164681'}, 'RANK < 20/0.2%',
                           None, None, {'201808011434164681': 10})
        assert actual_result == expected_result

    def test_filter_hotels_basis_room_nights(self):
        hotel_city_list = [('201808011434164683', "HC200701081857021907"), ('201808011633415288', "HC20070108183602794")]
        mock = umock.Mock()
        cursor = mock.connection.cursor.return_value
        cursor.fetchall.return_value = [(201808011434164683, "HC200701081857021907", 25),
                                        (201808011633415288, "HC200701081857021907", 15)]
        mock.connection.cursor().execute("SELECT 1")
        when(self.__dao_mysql).get_hotel_location_rn(any(object), "device").thenReturn(cursor)
        actual_result = self.cf.filter_hotels_basis_room_nights(hotel_city_list, None, "seq", "seq_new", "device")
        expected_result = ([('201808011633415288', 'HC20070108183602794')], {'201808011434164683'}, 'ROOM_NIGHTS >= 1',
                           None, None, {'201808011434164683': 25})
        assert actual_result == expected_result

    def tearDown(self):
        self.__context.pop()
