# python imports
import os
import sys
import pytest

# flask imports

# ensure path setup
tests_dir = os.path.dirname(__file__)
root_dir = os.path.dirname(tests_dir)
api_dir = os.path.join(root_dir, 'api')
sys.path.append(api_dir)


# patch imports
import patches

@pytest.fixture
def basePopulation(request, monkeypatch):
    print("populating base data from conftest")
    cacheMgr = getattr(request.module, "cacheManager", patches.MockCacheManager)
    solrPreload = getattr(request.module, "solrPreload", patches.MockUpsellSolrPreload)
    daoMysql = getattr(request.module, "solrPreload", patches.MockDataAccessObject)
    monkeypatch.setattr('handlers.upsell_solr_preload.upsellSolrPreload', solrPreload)
    monkeypatch.setattr('dao.dao_mysql.DataAccessObjectMySQL', daoMysql)
    monkeypatch.setattr('common.cache_manager.CacheManager', cacheMgr)

@pytest.fixture
def app(basePopulation):

	# first patch the database and remote system access related classes

	# monkeypatch.setattr('common.cache_manager.CacheManager.get_single', lambda x: 'helllo')
	# now load the application
	# app imports
	from gh_upsell_api import create_app
	app = create_app(updates={
			'TESTING': True,
			#'DATABASE': db_path,
		})

	with app.app_context():
		pass

	yield app


@pytest.fixture
def client(app):
	return app.test_client()


@pytest.fixture
def runner(app):
	return app.test_cli_runner()
