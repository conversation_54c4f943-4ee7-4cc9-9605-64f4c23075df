from flask import Flask
from common import helpers as hp

from unittest import TestCase

from candidate_generation.review_filter import review_filter


class test_review_filter(TestCase):
    __context = None

    def setUp(self):
        app = Flask(__name__)
        app.config['LOG'] = hp.mdc_logger("greenhorn")
        app.config['MINIMUM_REVIEW_COUNT'] = '5'
        app.config['MINIMUM_REVIEW_RATING'] = '3.5'
        self.__context = app.app_context()
        self.__context.push()
        self.rp = review_filter()

    def test_filter_hotels(self):
        hotel_list = [201803081919219780, 201111211716292072, 201803080755527964, 201610061230463301,
                      201803060111136914, 201705022338158232, 201712061348366147, 201407021459574034,
                      200701251431344102]
        response = [{'ratings': [{'hotelRatings': {'201803081919219780': {'1': 1},
                                                   '201111211716292072': {'1': 2, '2': 1, '4': 1, '5': 1}},
                                  'ratingType': 'overall',
                                  'requestParam': {'hotelId': None, 'city': 'HC200701081837409134', 'country': None}}]},
                    {'ratings': [{'hotelRatings': {
                        '201803080755527964': {'5': 1},
                        '201610061230463301': {'1': 2, '4': 1},
                        '201803060111136914': {'1': 1, '5': 1}}, 'ratingType': 'overall',
                        'requestParam': {'hotelId': None, 'city': 'HC200701081837409134', 'country': None}}]}
                    ]
        actual_result = self.rp.filter_hotels(True, hotel_list, response)
        expected_result = ([201111211716292072], 'REVIEWS,RATING 5,3.5', None, None, {201111211716292072: '(5, 2.6)'})

        assert actual_result == expected_result

    def tearDown(self):
        self.__context.pop()
