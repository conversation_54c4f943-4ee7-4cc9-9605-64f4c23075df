
def test_uploader(client):
	response = client.get('/GreenHornUploader')
	assert response.status_code == 302
	assert response.content_type == 'text/html; charset=utf-8'
	assert response.content_encoding == None
	assert response.content_language.to_header() == ''
	print(response.content_encoding)
	print(response.content_language)
	print(response.content_type)
	body = response.data.decode("utf-8") 
	# assert 'Green Horn' in body
