# import unittest
import pytest
import patches
import json
from datetime import datetime, timedelta , date


@pytest.fixture
def basePopulation(basePopulation, monkeypatch, testData):
    print("populating from query based perturber")
    cacheMgr = patches.MockCacheManager
    testName = testData[0]
    # algorithmId, sequenceId, city is the key
    # startblock, blocksize, nslots, maxhotels, check_in_dow, ap_bucket, min_adults, max_adults, children, min_los, max_los
    cacheData = {'BOOSTERS_118_DEL_GH':{}, 'BOOSTERS_all_DEL_GH' : {}, 'MATCHMAKER_118_DEL_GH' : {}, 'MATCHMAKER_all_DEL_GH' : {}}
    # cacheData['BOOSTERS_118_DEL_GH']["8_5_1_2_______"] = [{"13": 0.0}, {"14": 0.0}, {"15": 0.0}, {"16": 0.0}]
    if testName == 'weekendRequest' or testName == 'weekDayRequest':
        cacheData['BOOSTERS_118_DEL_GH']["10_5_1_4_weekend_____0_6_2018-09-08_2018-09-13_1_1"] = [ {"1" : [0.0, 1] }, {"2" : [0.0, 2] }, {"3" : [0.0, 3] }, {"4" : [0.0, 4] }]
        cacheData['BOOSTERS_118_DEL_GH']["10_5_1_4_weekday_____0_6_2018-09-08_2018-09-13_1_0"] = [{"5": [0.0, 5]}, {"6": [0.0, 6]}, {"7": [0.0, 7]}, {"8": [0.0, 8]}]
    elif testName == 'childrenRequest' or testName == 'adult4Request':
        cacheData['BOOSTERS_118_DEL_GH']["10_5_1_4_weekend____1_0_6_2018-09-05_2018-09-09_1_0"] = [{"9": [0.0, 12]}, {"10": [0.0, 11]}, {"11": [0.0, 10]}, {"12": [0.0, 9]}]
        cacheData['BOOSTERS_118_DEL_GH']["10_5_1_4_weekend__4_10__0_6_2018-09-05_2018-09-09_1_0"] = [{"13": [0.0, 13]}, {"14": [0.0, 14]}, {"15": [0.0, 15]}, {"16": [0.0, 16]}]
    elif testName == 'apSpecificityRequest':
        cacheData['BOOSTERS_118_DEL_GH']["10_5_1_4__10-15_4_10__0_6___1_1"] = [{"60": [0.0, 60]}, {"61": [0.0, 61]}, {"62": [0.0, 62]}, {"63": [0.0, 63]}]
        cacheData['BOOSTERS_118_DEL_GH']["10_5_1_4__0-5_4_10__0_6___1_0"] = [{"64": [0.0, 64]}, {"65": [0.0, 65]}, {"66": [0.0, 66]}, {"67": [0.0, 67]}]
    elif testName == 'sameSpecificitySameStartBlock2Seq':
        cacheData['BOOSTERS_118_DEL_GH']["10_5_1_4_weekend____1___2018-09-05_2018-09-09_1_1"] = [{"17": [0.0, 17]}, {"18": [0.0, 18]}, {"19": [0.0, 19]}, {"20": [0.0, 20]}]
        cacheData['BOOSTERS_118_DEL_GH']["10_5_1_4___4_10__0_6_2018-09-05_2018-09-09_1_1"] = [{"21": [0.0, 21]}, {"22": [0.0, 22]}, {"23": [0.0, 23]}, {"24": [0.0, 24]}]
    elif testName == 'sameSpecificitySameStartBlock2SeqDiffLen':
        cacheData['BOOSTERS_118_DEL_GH']["10_5_1_4_weekend____1___2018-09-05_2018-09-09_1_0"] = [{"25": [0.0, 25]}, {"26": [0.0, 26]}, {"27": [0.0, 27]}, {"28": [0.0, 28]}]
        cacheData['BOOSTERS_118_DEL_GH']["10_5_1_2___4_10__0_6_2018-09-05_2018-09-09_1_0"] = [{"29": [0.0, 29]}, {"30": [0.0, 30]}]
    elif testName == 'sameSpecificitySameStartBlock3Seq':
        cacheData['BOOSTERS_118_DEL_GH']["10_5_1_4_weekend____1___2018-09-05_2018-09-09_1_0"] = [{"31": [0.0, 31]}, {"32": [0.0, 32]}, {"33": [0.0, 33]}, {"34": [0.0, 34]}]
        cacheData['BOOSTERS_118_DEL_GH']["10_5_1_2___4_10__0_6_2018-09-05_2018-09-09_1_0"] = [{"35": [0.0, 35]}, {"36": [0.0, 36]}]
        cacheData['BOOSTERS_118_DEL_GH']["10_5_1_4_weekend__4_10____2018-09-05_2018-09-09_1_1"] = [{"37": [0.0, 37]}, {"38": [0.0, 38]}, {"39": [0.0, 39]}, {"40": [0.0, 40]}]
    elif testName == 'sameSpecificityDiffStartBlock':
        cacheData['BOOSTERS_118_DEL_GH']["10_5_1_4_weekend____1___2018-09-05_2018-09-09_1_1"] = [{"41": [0.0, 41]}, {"42": [0.0, 42]}, {"43": [0.0, 43]}, {"44": [0.0, 44]}]
        cacheData['BOOSTERS_118_DEL_GH']["9_5_1_4___4_10__0_6_2018-09-05_2018-09-09_1_0"] = [{"45": [0.0, 45]}, {"46": [0.0, 46]}, {"47": [0.0, 47]}, {"48": [0.0, 48]}]
    elif testName == 'diffSpecificitySameStartBlock':
        cacheData['BOOSTERS_118_DEL_GH']["10_5_1_4_weekend____1_0_6_2018-09-05_2018-09-09_1_1"] = [{"49": [0.0, 49]}, {"50": [0.0, 50]}, {"51": [0.0, 51]}, {"52": [0.0, 52]}]
        cacheData['BOOSTERS_118_DEL_GH']["9_5_1_4___4_10__0_6_2018-09-05_2018-09-09_1_1"] = [{"53": [0.0, 53]}, {"54": [0.0, 54]}, {"55": [0.0, 55]}, {"56": [0.0, 56]}]
        cacheData['BOOSTERS_118_DEL_GH']["9_5_1_3________2018-09-05_2018-09-09_1_1"] = [{"57": [0.0, 57]}, {"58": [0.0, 58]}, {"59": [0.0, 59]}]
    elif testName == 'appFestRequest' :
        cacheData['BOOSTERS_118_DEL_GH']["10_5_1_4_weekend____1_0_6_2018-09-05_2018-09-09_23_0"] = [{"68": [0.0, 60]}, {"69": [0.0, 61]},{"70": [0.0, 62]}, {"71": [0.0, 63]}]
        cacheData['BOOSTERS_118_DEL_GH']["9_5_1_4___4_10__0_6_2018-09-05_2018-09-09_24_0"] = [{"72": [0.0, 64]}, {"73": [0.0, 65]},{"74": [0.0, 66]}, {"75": [0.0, 67]}]
    elif testName == 'matchmakerWeekendRequest' :
        cacheData['MATCHMAKER_118_DEL_GH']["8_5_1_4_weekend_____0_6_2018-09-08_2018-09-13_1_0"] = [{"1": [0.0, 1]}, {"2": [0.0, 2]}, {"3": [0.0, 3]}, {"4": [0.0, 4]}]
    cacheMgr.get_single = lambda x,y : cacheData[y]
    monkeypatch.setattr('common.cache_manager.CacheManager', cacheMgr)


@pytest.fixture(params = ['weekendRequest', 'weekDayRequest', 'childrenRequest', 'apSpecificityRequest', 'adult4Request', 'sameSpecificitySameStartBlock2Seq',
            'sameSpecificitySameStartBlock2SeqDiffLen', 'sameSpecificitySameStartBlock3Seq', 'sameSpecificityDiffStartBlock', 'diffSpecificitySameStartBlock',
            'appFestRequest', 'matchmakerWeekendRequest'])
def testData(request):
    crtd_date = date(2018,8,29)
    weekDay = crtd_date.weekday()
    weekendCheckInDate = crtd_date + timedelta(days= (-weekDay + 7 + 5))
    weekendRequest = ({"mode":"BOOSTERS","city":"DEL","checkin":str(weekendCheckInDate),"checkout":str(weekendCheckInDate + timedelta(days=3)),"source_seq_id":"hsq118",
     "roomStayParams":[{"guestCounts":[{"count":"2","ageQualifyingCode":"10","ages":None}]}]},
            {"success": "true", "responses": [{"start_block": 10, "block_size": 5, "no_of_slots": 1, "max_hotel": 4, "hotels": [{"hotelId":"1","slotBucket":"B1PR"}, {"hotelId":"2","slotBucket":"B1PR"}, {"hotelId":"3","slotBucket":"B1PR"}, {"hotelId":"4","slotBucket":"B1PR"}]}], "error": []})
    weekDayRequest = (
    {"mode": "BOOSTERS", "city": "DEL", "checkin": str(weekendCheckInDate + timedelta(days=3)), "checkout": str(weekendCheckInDate + timedelta(days=4)), "source_seq_id": "hsq118",
     "roomStayParams": [{"guestCounts": [{"count": "2", "ageQualifyingCode": "10", "ages": None}]}]},
    {"success": "true", "responses": [ {"start_block": 10, "block_size": 5, "no_of_slots": 1, "max_hotel": 4, "hotels": [{"hotelId":"5","slotBucket":"B1NP"}, {"hotelId":"6","slotBucket":"B1NP"}, {"hotelId":"7","slotBucket":"B1NP"}, {"hotelId":"8","slotBucket":"B1NP"}]}], "error": []})
    childrenRequest = (
    {"mode": "BOOSTERS", "city": "DEL", "checkin": str(weekendCheckInDate),"checkout":str(weekendCheckInDate + timedelta(days=3)), "source_seq_id": "hsq118",
     "roomStayParams": [{"guestCounts": [{"count": "2", "ageQualifyingCode": "8", "ages": None}]}]},
    {"success": "true", "responses": [ {"start_block": 10, "block_size": 5, "no_of_slots": 1, "max_hotel": 4, "hotels": [{"hotelId":'12',"slotBucket":"B1NP"}, {"hotelId":'11',"slotBucket":'B1NP'}, {"hotelId":'10',"slotBucket":'B1NP'}, {"hotelId":'9',"slotBucket":'B1NP'}]}], "error": []})
    apSpecificityRequest = (
        {"mode": "BOOSTERS", "city": "DEL","checkin": str(datetime.now().date() + timedelta(days=10)),"checkout": str(datetime.now().date() + timedelta(days=12)), "source_seq_id": "hsq118",
         "roomStayParams": [{"guestCounts": [{"count": "4", "ageQualifyingCode": "10", "ages": None}]}]},
        {"success": "true", "responses": [{"start_block": 10, "block_size": 5, "no_of_slots": 1, "max_hotel": 4, "hotels": [{"hotelId":"60","slotBucket":"B1PR"}, {"hotelId":"61","slotBucket":"B1PR"}, {"hotelId":"62","slotBucket":"B1PR"}, {"hotelId":"63","slotBucket":"B1PR"}]}], "error": []})
    adult4Request = (
    {"mode": "BOOSTERS", "city": "DEL", "checkin": str(weekendCheckInDate),"checkout":str(weekendCheckInDate + timedelta(days=3)), "source_seq_id": "hsq118",
     "roomStayParams": [{"guestCounts": [{"count": "4", "ageQualifyingCode": "10", "ages": None}]}]},
    {"success": "true", "responses": [ {"start_block": 10, "block_size": 5, "no_of_slots": 1, "max_hotel": 4, "hotels": [{"hotelId":"13","slotBucket":'B1NP'}, {"hotelId":"14","slotBucket":'B1NP'}, {"hotelId":"15","slotBucket":'B1NP'}, {"hotelId":"16","slotBucket":'B1NP'}]}], "error": []})
    sameSpecificitySameStartBlock2Seq = (
    {"mode": "BOOSTERS", "city": "DEL", "checkin": str(weekendCheckInDate), "checkout": str(weekendCheckInDate + timedelta(days=3)), "source_seq_id": "hsq118",
      "roomStayParams": [ {"guestCounts": [{"count": "4", "ageQualifyingCode": "10", "ages": None}]}, {"guestCounts": [{"count": "4", "ageQualifyingCode": "8", "ages": None}]}]},
    {"success": "true", "responses": [{"start_block": 10, "block_size": 5, "no_of_slots": 1, "max_hotel": 8, "hotels": [{"hotelId":"17","slotBucket":"B1PR"}, {"hotelId":"21","slotBucket":"B2PR"}, {"hotelId":"18","slotBucket":"B1PR"}, {"hotelId":"22","slotBucket":"B2PR"}, {"hotelId":"19","slotBucket":"B1PR"}, {"hotelId":"23","slotBucket":"B2PR"}, {"hotelId":"20","slotBucket":"B1PR"}, {"hotelId":"24","slotBucket":"B2PR"}]}], "error": []})
    sameSpecificitySameStartBlock2SeqDiffLen = (
        {"mode": "BOOSTERS", "city": "DEL", "checkin": str(weekendCheckInDate), "checkout": str(weekendCheckInDate + timedelta(days=3)), "source_seq_id": "hsq118",
         "roomStayParams": [{"guestCounts": [{"count": "4", "ageQualifyingCode": "10", "ages": None}]}, {"guestCounts": [{"count": "4", "ageQualifyingCode": "8", "ages": None}]}]},
        {"success": "true", "responses": [{"start_block": 10, "block_size": 5, "no_of_slots": 1, "max_hotel": 6, "hotels": [{"hotelId":"25","slotBucket":"B1NP"}, {"hotelId":"29","slotBucket":"B2NP"}, {"hotelId":"26","slotBucket":"B1NP"}, {"hotelId":"30","slotBucket":"B2NP"}, {"hotelId":"27","slotBucket":"B1NP"}, {"hotelId":"28","slotBucket":"B1NP"}]}], "error": []})
    sameSpecificitySameStartBlock3Seq = (
        {"mode": "BOOSTERS", "city": "DEL", "checkin": str(weekendCheckInDate), "checkout": str(weekendCheckInDate + timedelta(days=3)), "source_seq_id": "hsq118",
         "roomStayParams": [{"guestCounts": [{"count": "4", "ageQualifyingCode": "10", "ages": None}]}, {"guestCounts": [{"count": "4", "ageQualifyingCode": "8", "ages": None}]}]},
        {"success": "true", "responses": [{"start_block": 10, "block_size": 5, "no_of_slots": 1, "max_hotel": 10, "hotels": [{"hotelId":"31","slotBucket":"B1NP"}, {"hotelId":"35","slotBucket":"B2NP"}, {"hotelId":"37","slotBucket":"B3PR"}, {"hotelId":"32","slotBucket":"B1NP"}, {"hotelId":"36","slotBucket":"B2NP"}, {"hotelId":"38","slotBucket":"B3PR"}, {"hotelId":"33","slotBucket":"B1NP"}, {"hotelId":"39","slotBucket":"B3PR"}, {"hotelId":"34","slotBucket":"B1NP"}, {"hotelId":"40","slotBucket":"B3PR"}]}], "error": []})
    sameSpecificityDiffStartBlock = (
        {"mode": "BOOSTERS", "city": "DEL", "checkin": str(weekendCheckInDate), "checkout": str(weekendCheckInDate + timedelta(days=3)), "source_seq_id": "hsq118",
         "roomStayParams": [{"guestCounts": [{"count": "4", "ageQualifyingCode": "10", "ages": None}]}, {"guestCounts": [{"count": "4", "ageQualifyingCode": "8", "ages": None}]}]},
        {"success": "true", "responses": [
            {"start_block": 10, "block_size": 5, "no_of_slots": 1, "max_hotel": 4, "hotels": [{"hotelId":"41","slotBucket":"B1PR"}, {"hotelId":"42","slotBucket":"B1PR"}, {"hotelId":"43","slotBucket":"B1PR"}, {"hotelId":"44","slotBucket":"B1PR"}]},
            {"start_block": 9, "block_size": 5, "no_of_slots": 1, "max_hotel": 4, "hotels": [{"hotelId":"45","slotBucket":"B2NP"}, {"hotelId":"46","slotBucket":"B2NP"}, {"hotelId":"47","slotBucket":"B2NP"}, {"hotelId":"48","slotBucket":"B2NP"}]}],
         "error": []})
    diffSpecificitySameStartBlock = (
        {"mode": "BOOSTERS", "city": "DEL", "checkin": str(weekendCheckInDate), "checkout": str(weekendCheckInDate + timedelta(days=3)), "source_seq_id": "hsq118",
         "roomStayParams": [{"guestCounts": [{"count": "4", "ageQualifyingCode": "10", "ages": None}]}, {"guestCounts": [{"count": "4", "ageQualifyingCode": "8", "ages": None}]}]},
        {"success": "true", "responses": [ {"start_block": 10, "block_size": 5, "no_of_slots": 1, "max_hotel": 4, "hotels": [{"hotelId":"49","slotBucket":"B1PR"}, {"hotelId":"50","slotBucket":"B1PR"}, {"hotelId":"51","slotBucket":"B1PR"}, {"hotelId":"52","slotBucket":"B1PR"}]},
            {"start_block": 9, "block_size": 5, "no_of_slots": 1, "max_hotel": 7, "hotels": [{"hotelId":"53","slotBucket":"B2PR"}, {"hotelId":"54","slotBucket":"B2PR"}, {"hotelId":"55","slotBucket":"B2PR"}, {"hotelId":"56","slotBucket":"B2PR"}, {"hotelId":"57","slotBucket":"B3PR"}, {"hotelId":"58","slotBucket":"B3PR"}, {"hotelId":"59","slotBucket":"B3PR"}]}], "error": []})
    appFestRequest = (
        {"mode": "BOOSTERS", "city": "DEL", "checkin": str(weekendCheckInDate),
         "checkout": str(weekendCheckInDate + timedelta(days=3)), "source_seq_id": "hsq118",
         "roomStayParams": [{"guestCounts": [{"count": "4", "ageQualifyingCode": "10", "ages": None}]},
                            {"guestCounts": [{"count": "4", "ageQualifyingCode": "8", "ages": None}]}]},
        {"success": "true", "responses": [
            {"start_block": 10, "block_size": 5, "no_of_slots": 1, "max_hotel": 4, "hotels": [{"hotelId":"68","slotBucket":"B1NP"}, {"hotelId":"69","slotBucket":"B1NP"}, {"hotelId":"70","slotBucket":"B1NP"}, {"hotelId":"71","slotBucket":"B1NP"}]},
            {"start_block": 9, "block_size": 5, "no_of_slots": 1, "max_hotel": 4,"hotels": [{"hotelId":"72","slotBucket":"B2NP"}, {"hotelId":"73","slotBucket":"B2NP"}, {"hotelId":"74","slotBucket":"B2NP"}, {"hotelId":"75","slotBucket":"B2NP"}]}], "error": []}
    )
    matchmakerWeekendRequest = ({"mode": "MATCHMAKER", "city": "DEL", "checkin": str(weekendCheckInDate),
                                 "checkout": str(weekendCheckInDate + timedelta(days=3)), "source_seq_id": "hsq118",
                                 "roomStayParams": [
                                     {"guestCounts": [{"count": "2", "ageQualifyingCode": "10", "ages": None}]}]},
                                {"success": "true", "responses": [
                                    {"start_block": 8, "block_size": 5, "no_of_slots": 1, "max_hotel": 4,
                                     "hotels": [{"hotelId":"1","slotBucket":"B1NP"}, {"hotelId":"2","slotBucket":"B1NP"}, {"hotelId":"3","slotBucket":"B1NP"}, {"hotelId":"4","slotBucket":"B1NP"}]}], "error": []})
    reqTupleList = {'weekendRequest' : weekendRequest, 'weekDayRequest' : weekDayRequest, 'childrenRequest' : childrenRequest, "apSpecificityRequest" : apSpecificityRequest,
                   'adult4Request' : adult4Request , 'sameSpecificitySameStartBlock2Seq' : sameSpecificitySameStartBlock2Seq,
                    'sameSpecificitySameStartBlock2SeqDiffLen':sameSpecificitySameStartBlock2SeqDiffLen, 'sameSpecificitySameStartBlock3Seq' : sameSpecificitySameStartBlock3Seq,
                    'sameSpecificityDiffStartBlock':sameSpecificityDiffStartBlock, 'diffSpecificitySameStartBlock': diffSpecificitySameStartBlock,
                    'appFestRequest' : appFestRequest, 'matchmakerWeekendRequest' : matchmakerWeekendRequest}

    identifier = request.param
    def getRequestResponse():
        return reqTupleList[identifier]
    return (identifier, getRequestResponse)

def testGreenHornBoosterResponse(testData, client):
    req, resp = testData[1]()
    response = client.post('/GreenHorn', data =json.dumps(req), content_type='application/json')
    print(response.data)
    assert response.status_code == 200
    assert json.loads(response.data) == resp
