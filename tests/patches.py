# python imports
import os
import sys
from math import *


class MockCursor:

	def __init__(self, rows=[]):
		self.rows = rows

	def fetchall(self):
		return self.rows

class MockUpsellSolrPreload(object):
    city_radious_dictionary = {}


    def getDistance(self, dictionary):
        lat1 = radians(float(dictionary["lat1"]))
        lon1 = radians(float(dictionary["lon1"]))
        lat2 = radians(float(dictionary["lat2"]))
        lon2 = radians(float(dictionary["lon2"]))
        dlon = lon2 - lon1
        dlat = lat2 - lat1
        a = sin(dlat / 2) ** 2 + cos(lat1) * cos(lat2) * sin(dlon / 2) ** 2
        c = 2 * atan2(sqrt(a), sqrt(1 - a))
        distance = self.earth_radious * c
        return distance

    def getcityDetailFromSolr(self):
    	return self.city_radious_dictionary

# TODO fill static data in these APIS
# Alternatively, serve these queries from a local SQLite DB in test directory
# This would require pre-filling the SQLite DB with appropriate tables and data
class MockDataAccessObject(object):

    def __init__(self):
    	pass

    def close(self):
    	pass

    def get_active_boosters(self):
    	return MockCursor()

    def get_inactive_cities(self):
        return MockCursor()

    def get_inactive_boosters(self):
    	return MockCursor()

    def make_sequence_inactive(self,uid):
    	pass

    def make_hotels_inactive(self,uid):
    	pass

    def find_input_newhotel_sequence(self,source_seq_id,algorithm):
    	return MockCursor()

    def find_input_booster_sequence(self,source_seq_id,start_block,algorithm):
    	return MockCursor()
  
    def add_hotels(self,rows):
    	pass
 
    def add_sequence(self,uid,description,source_seq_id,start_block,algorithm,block_size,nslots,max_hotels,start_date,end_date,
                     check_in_dow, ap_bucket, min_adults, max_adults, children, min_los, max_los, modified_by):
    	pass
 
    def find_input_active_uid(self,source_seq_id,start_block,algorithm):
    	return MockCursor()

    def find_other_active_uids(self,source_seq_id,start_block,algorithm):
    	return MockCursor()

    def find_input_uid_active_hotels(self,uids):
    	return MockCursor()

    def update_hotels(self,hotels,uid):
    	pass

    def remove_hotels(self,hotels,uid):
    	pass

    def remove_cities(self,cities,uid):
    	pass

    def fetch_active_sequences(self,mode):
    	return MockCursor()

class MockExploreSetUpdater(object):

    def __init__(self):
        self.__dao_mysql = MockDataAccessObject()

class MockCacheManager(object):

    def __init__(self, cfg):
        pass

    def put(self, key, value, ttl=0):
        pass

    def remove(self, key):
        pass