from flask import Flask
from common import helpers as hp

from mockito import mock
from mockito import when
from unittest import TestCase
from unittest import mock as umock

from candidate_generation.candidate_scorer import candidate_scorer


class test_candidate_scorer(TestCase):
    __context = None
    __dao_mysql = mock()
    __dao_mysql_mmt_read = mock()

    def setUp(self):
        app = Flask(__name__)
        app.config['LOG'] = hp.mdc_logger("greenhorn")
        self.__context = app.app_context()
        self.__context.push()
        self.cs = candidate_scorer(self.__dao_mysql, self.__dao_mysql_mmt_read)

    def test_score_basis_area(self):
        hotel_location_list = [("201803291422113988", "HC200701081837409134"), ("201804241237567325", "HC200701081837409134"),
                               ("201805071634477445", "HC201412061617199547")]

        hotel_to_area = {}
        hotel_to_area.update({"201803291422113988": "8827"})
        hotel_to_area.update({"201804241237567325": "46130"})
        hotel_to_area.update({"201805071634477445": "74838"})

        hotel_to_city = {}
        hotel_to_city.update({"201803291422113988": ["HC200701081837409134"]})
        hotel_to_city.update({"201804241237567325": ["HC200701081837409134"]})
        hotel_to_city.update({"201805071634477445": ["HC201412061617199547"]})

        area_to_hotels = {}
        area_to_hotels.update({"8827": ["6674531553139752", "201803291422113988"]})
        area_to_hotels.update({"46130": ["20070111172337592", "201104121801563220", "201804241237567325"]})
        area_to_hotels.update({"74838": ["201512021610347455", "201805071634477445"]})

        city_to_hotels = {}
        city_to_hotels.update(
            {"HC200701081837409134": ["201803291422113988", "201804241237567325", "2014102210553390"]})
        city_to_hotels.update({"HC201412061617199547": ["201805071634477445", "20151202162505958"]})

        hotel_to_booking = {"201803291422113988": 10, "201804241237567325": 23, "201805071634477445": 11,
                            "6674531553139752": 43,
                            "20070111172337592": 16, "201104121801563220": 10, "201512021610347455": 9,
                            "2014102210553390": 5,
                            "20151202162505958": 22}

        # For dom
        actual_result = self.cs.score_basis_area(True, "dummy_device", hotel_location_list, hotel_to_area, hotel_to_city,
                                                 area_to_hotels, city_to_hotels, hotel_to_booking, None, None)
        expected_result = {'201803291422113988': 1, '201804241237567325': 1, '201805071634477445': 0.394}
        assert actual_result == expected_result

        # For intl
        actual_result = self.cs.score_basis_area(False, "dummy_device", hotel_location_list, hotel_to_area, hotel_to_city,
                                                 area_to_hotels, city_to_hotels, hotel_to_booking, None, None)
        expected_result = {'201803291422113988': 0, '201804241237567325': 0, '201805071634477445': 0.606}
        assert actual_result == expected_result

    def test_score_basis_quality(self):
        hotel_to_quality_score = {"201802061516295109": {"cs": 100.0, "is": 1.0, "rs": 1.0, "qs": 1.0},
                                  "201803092222129883": {"cs": 60.0, "is": 0.5, "rs": 0.6, "qs": 0.6}}
        actual_result = self.cs.score_basis_quality(None, "device", ["201802061516295109", "201803092222129883"], None, None, None,
                                                    None, None, "seq", hotel_to_quality_score)
        expected_result = {'201802061516295109': 1.0, '201803092222129883': 0.0}
        assert actual_result == expected_result

    def test_score_basis_rank(self):
        hotel_to_location_list = [("201802061516295109", "CTDEL"),("201803061132488922", "CTDEL"),
                                  ("201803092222129883", "CTDEL"),("201803131516019245", "CTDEL"),
                                  ("201803231127268639", "CTDEL"), ("201803261534518287", "CTDEL"),
                                  ("201803271729543792", "CTDEL"), ("201803291730361796", "CTDEL"),
                                  ("201804171003141898", "CTDEL"), ("201804171549242853", "CTDEL")]
        hotels = set([hotel for hotel, location in hotel_to_location_list])
        mock = umock.Mock()
        cursor = mock.connection.cursor.return_value
        cursor.fetchall.return_value = [("201802061516295109", "CTDEL", 2), ("201803092222129883", "CTDEL", 12),
                                        ("201804171549242853", "CTDEL", 3), ("201803261534518287", "CTDEL", 7)]
        mock.connection.cursor().execute("SELECT 1")
        when(self.__dao_mysql).get_hotel_location_rank(hotels, "HSEQ" + "seq").thenReturn(cursor)
        actual_result = self.cs.score_basis_rank(None, "device", hotel_to_location_list, None, None, None,
                                                 None, None, "seq", None)
        expected_result = {'201802061516295109': 1.0, '201803092222129883': 0.0, '201804171549242853': 0.9,
                           '201803261534518287': 0.5}
        assert actual_result == expected_result

    def tearDown(self):
        self.__context.pop()
