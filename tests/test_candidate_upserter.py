from flask import Flask
from common import helpers as hp

from mockito import mock
from mockito import when
from unittest import TestCase
from unittest import mock as umock

from candidate_generation.candidate_upserter import candidate_upserter


class test_candidate_upserter(TestCase):
    __context = None
    __dao_mysql = mock()

    def setUp(self):
        app = Flask(__name__)
        app.config['LOG'] = hp.mdc_logger("greenhorn")
        app.config['PUSH_GH_CANDIDATES_TO_DB_DOM'] = True
        app.config['PUSH_GH_CANDIDATES_TO_DB_INTL'] = True
        app.config['PUSH_FILTERED_HOTELS_TO_DB_DOM'] = True
        app.config['PUSH_FILTERED_HOTELS_TO_DB_INTL'] = True
        app.config['DEFAULT_USER'] = 'greenhorn'
        app.config['DEFAULT_START_BLOCK'] = 5
        app.config['DEFAULT_BLOCKSIZE'] = 5
        app.config['DEFAULT_N_SLOTS'] = 1
        app.config['DEFAULT_MAX_HOTELS'] = 100
        app.config['DEFAULT_START_RANK'] = -1
        app.config['DEFAULT_END_RANK'] = -1
        app.config['BKG_ABS_CUTOFF_VAL'] = 0
        app.config['BKG_ABS_CUTOFF_PERC'] = 0
        app.config['SOURCE_CANDIDATE_GENERATION']= 'CandidateGeneration'
        self.__context = app.app_context()
        self.__context.push()
        self.cu = candidate_upserter(self.__dao_mysql)

    def test_get_boosted_sequence_uids(self):
        uid = "702a1924-6a42-11e8-a6f1-2eebe4673372"
        mock = umock.Mock()
        cursor = mock.connection.cursor.return_value
        cursor.fetchall.return_value = [(uid, "")]
        mock.connection.cursor().execute("SELECT 1")
        when(self.__dao_mysql).get_boosted_sequence_uids("seq_new").thenReturn(cursor)
        actual_result = self.cu.get_boosted_sequence_uids("android", "seq_new")
        expected_result = [uid]
        assert actual_result == expected_result

    def test_add_or_update_boosted_sequence(self):
        uids = ["702a1924-6a42-11e8-a6f1-2eebe4673372"]
        mock = umock.Mock()
        cursor = mock.connection.cursor.return_value
        cursor.fetchall.return_value = None
        mock.connection.cursor().execute("SELECT 1")
        when(self.__dao_mysql).update_sequence(uids[0], 'greenhorn').thenReturn(cursor)
        actual_result = self.cu.add_or_update_boosted_sequence(True, "android", uids, "seq_new")
        expected_result = uids[0]
        assert actual_result == expected_result

    def test_get_boosted_hotels_for_uid(self):
        uid = ["702a1924-6a42-11e8-a6f1-2eebe4673372"]
        mock = umock.Mock()
        cursor = mock.connection.cursor.return_value
        cursor.fetchall.return_value = [("201803201342388355", ""), ("201803201355204596", ""),
                                        ("201803201402228657", "")]
        mock.connection.cursor().execute("SELECT 1")
        when(self.__dao_mysql).get_boosted_hotels_for_uid(uid).thenReturn(cursor)
        actual_result = self.cu.get_boosted_hotels_for_uid(uid)
        expected_result = ['201803201342388355', '201803201355204596', '201803201402228657']
        assert actual_result == expected_result

    @umock.patch("candidate_generation.candidate_upserter.Uploader")
    def test_push_boosted_hotels_to_db_basis_device(self, mocked_class):
        uid = ["702a1924-6a42-11e8-a6f1-2eebe4673372"]
        filtered_hotel_to_city_list = [("201808011633415288", "HC20070108183602794", "New Delhi And NCR"),
                                       ("201808011434164683", "HC200701081857021907", "Bangalore")]

        hotel_scores = {"201808011633415288": 0.6, "201808011434164683": 0.1}
        self.cu.push_boosted_hotels_to_db_basis_device(True, "android", None, filtered_hotel_to_city_list, hotel_scores)
        self.cu.push_boosted_hotels_to_db_basis_device(True, "android", uid, filtered_hotel_to_city_list, hotel_scores)

    def test_get_filtered_hotels_for_uid(self):
        uid = ["702a1924-6a42-11e8-a6f1-2eebe4673372"]
        mock = umock.Mock()
        cursor = mock.connection.cursor.return_value
        cursor.fetchall.return_value = [("201803201342388355", ""), ("201803201355204596", ""),
                                        ("201803201402228657", "")]
        mock.connection.cursor().execute("SELECT 1")
        when(self.__dao_mysql).get_filtered_hotels_for_uid(uid).thenReturn(cursor)
        actual_result = self.cu.get_filtered_hotels_for_uid(uid)
        expected_result = ['201803201342388355', '201803201355204596', '201803201402228657']
        assert actual_result == expected_result

    def test_push_filtered_hotels_to_db(self):
        device_to_uid = {"android": "702a1924-6a42-11e8-a6f1-2eebe4673372",
                         "iphone": "817eeaec-6a42-11e8-acb8-2eebe4673372",
                         "desktop": "d578e94c-a9b9-11e8-acd5-d85de2103030"}

        filtered_hotel_city_list = [("201808011633415288", "HC20070108183602794", "New Delhi And NCR")]
        filtered_out_hotel_to_city = {"201808011434164683": "HC200701081857021907"}
        filtered_out_hotel_to_reason = {"201808011434164683": "CONTENT ADDRESS"}
        filtered_out_hotel_to_score = {"201808011434164683": 0.2}

        filtered_data = (
            filtered_hotel_city_list, filtered_out_hotel_to_city, "NA", None, None,
            filtered_out_hotel_to_score)

        mock = umock.Mock()
        cursor = mock.connection.cursor.return_value
        cursor.fetchall.return_value = [("201803201342388355", ""), ("201803201355204596", ""),
                                        ("201803201402228657", "")]
        mock.connection.cursor().execute("SELECT 1")
        when(self.__dao_mysql).get_filtered_hotels_for_uid("702a1924-6a42-11e8-a6f1-2eebe4673372").thenReturn(cursor)
        when(self.__dao_mysql).get_filtered_hotels_for_uid("817eeaec-6a42-11e8-acb8-2eebe4673372").thenReturn(cursor)
        when(self.__dao_mysql).get_filtered_hotels_for_uid("d578e94c-a9b9-11e8-acd5-d85de2103030").thenReturn(cursor)

        self.cu.push_filtered_hotels_to_db(True, "filter_cities", "android,iphone,desktop", device_to_uid, filtered_data)

        # get_filtered_hotels_for_uid

        filtered_data = (
            filtered_hotel_city_list, filtered_out_hotel_to_city, None, "NA", filtered_out_hotel_to_reason,
            None)
        self.cu.push_filtered_hotels_to_db(True, "filter_cities", "android,iphone,desktop", device_to_uid, filtered_data)

    def tearDown(self):
        self.__context.pop()
