from flask import Flask
from common import helpers as hp

from mockito import mock
from mockito import when
from unittest import TestCase
from unittest import mock as umock
from mockito.matchers import any

from candidate_generation.candidate_generator import candidate_generator

import os

class test_candidate_generator(TestCase):
    __context = None
    __dao_mysql = mock()
    __cu = mock()

    def setUp(self):
        app = Flask(__name__)
        app.config['LOG'] = hp.mdc_logger("greenhorn")
        app.config['S3_FILE_HOTEL_LOCUS'] = "test_hotel_location_mapping.csv"
        app.config['DATADIR'] = os.path.dirname(os.path.abspath(__file__))
        app.config['NEW_HOTELS_FILTERS'] = ["filter_cities", "filter_hotels_basis_content",
                                            "filter_hotels_basis_inventory", "filter_hotels_basis_review",
                                            "filter_fraud_hotels"]
        app.config['NEW_HOTELS_FILTERS_DEVICE'] = ["filter_hotel_basis_exposure", "filter_hotel_basis_rank",
                                                   "filter_hotels_basis_room_nights"]
        app.config['NEW_HOTELS_SCORERS'] = ["score_basis_area", "score_basis_quality", "score_basis_rank"]
        app.config['MAIL_SUBJECT_GH_CANDIDATE_GENERATION'] = 'New Hotels Generation Notification'
        app.config['MAIL_TO'] = None
        app.config['MAIL_TO_GH_CANDIDATE_GENERATION'] = None
        self.__context = app.app_context()
        self.__context.push()

    @umock.patch("candidate_generation.candidate_generator.candidate_upserter")
    @umock.patch("candidate_generation.candidate_generator.dao_mysql_hotel_booker")
    @umock.patch("candidate_generation.candidate_generator.dao_mysql_mmt_read")
    @umock.patch("candidate_generation.candidate_generator.dao_mysql")
    def test_generate_candidates(self, mocked_dao_mysql, mocked_dao_mysql_mmt_read, mocked_dao_mysql_hotel_booker,
                                 mocked_candidate_upserter):
        mocked_dao_mysql.return_value = self.__dao_mysql
        mocked_dao_mysql_mmt_read.return_value = self.__dao_mysql
        mocked_dao_mysql_hotel_booker.return_value = self.__dao_mysql
        mocked_candidate_upserter.return_value.get_boosted_sequence_uids.return_value = [
            "702a1924-6a42-11e8-a6f1-2eebe4673372"]

        cg = candidate_generator()
        mock = umock.Mock()
        cursor = mock.connection.cursor.return_value
        cursor.fetchall.return_value = [("201808011434164683", "HC200701081857021907"),
                                        ("201808011441096193", "HC200701081857021907"),
                                        ("201808011633415288", "HC20070108183602794"),
                                        ("201808011635096637", "HC20070108183602794"),
                                        ("201808011635414423", "HC20070108183602794"),
                                        ("201808011309528826", "HC200701081936471936")]
        mock.connection.cursor().execute("SELECT 1")
        when(self.__dao_mysql).get_new_hotel_city_list(True).thenReturn(cursor)

        cursor.fetchall.return_value = [("HC200701081857021907", 200), ("HC20070108183602794", 15),
                                        ("HC200701081936471936", 60)]
        mock.connection.cursor().execute("SELECT 1")
        when(self.__dao_mysql).get_hotel_count_for_cities(any(object)).thenReturn(cursor)
        cg.generate_candidates(True, "android")

        mocked_candidate_upserter.return_value.get_boosted_sequence_uids.return_value = [
            "702a1924-6a42-11e8-a6f1-2eebe4673372", "817eeaec-6a42-11e8-acb8-2eebe4673372"]
        cg.generate_candidates(True, "android")

    @umock.patch("candidate_generation.candidate_generator.dao_mysql_hotel_booker")
    @umock.patch("candidate_generation.candidate_generator.dao_mysql")
    @umock.patch("candidate_generation.candidate_generator.dao_mysql_mmt_read")
    def test_get_hotel_to_area_list(self, mocked_dao_mysql, mocked_dao_mysql_mmt_read, mocked_dao_mysql_hotel_booker):
        mocked_dao_mysql.return_value = self.__dao_mysql
        mocked_dao_mysql_mmt_read.return_value = self.__dao_mysql
        mocked_dao_mysql_hotel_booker.return_value = self.__dao_mysql
        cg = candidate_generator()
        hotels = {"202101080917098042", "202101080902056153", "202101080901589370", "202101071731102826",
                  "202101071655392131"}
        actual_result = cg.get_hotel_to_area_list(hotels)
        expected_result = [("202101071655392131", "ARBANDRAW"), ("202101071731102826", "ARBANDRAW"),
                           ("202101080901589370", "ARANDH"), ("202101080902056153", "ARBORIV"),
                           ("202101080917098042", "ARANDH")]
        assert expected_result == actual_result

    @umock.patch("candidate_generation.candidate_generator.dao_mysql_hotel_booker")
    @umock.patch("candidate_generation.candidate_generator.dao_mysql")
    @umock.patch("candidate_generation.candidate_generator.dao_mysql_mmt_read")
    def test_get_area_to_hotels(self, mocked_dao_mysql, mocked_dao_mysql_mmt_read, mocked_dao_mysql_hotel_booker):
        mocked_dao_mysql.return_value = self.__dao_mysql
        mocked_dao_mysql_mmt_read.return_value = self.__dao_mysql
        mocked_dao_mysql_hotel_booker.return_value = self.__dao_mysql
        cg = candidate_generator()
        areas = {"ARBANDRAW", "ARANDH", "ARBORIV"}
        actual_result = cg.get_area_to_hotels(areas)
        expected_result = {'ARBANDRAW': ['202101071655392131', '202101071731102826'], 'ARANDH': ['202101080901589370',
                                                                                                 '202101080917098042'],
                           'ARBORIV': ['202101080902056153']}
        assert expected_result == actual_result

    @umock.patch("candidate_generation.candidate_generator.dao_mysql_hotel_booker")
    @umock.patch("candidate_generation.candidate_generator.dao_mysql")
    @umock.patch("candidate_generation.candidate_generator.dao_mysql_mmt_read")
    def test_get_location_to_hotels(self, mocked_dao_mysql, mocked_dao_mysql_mmt_read, mocked_dao_mysql_hotel_booker):
        mocked_dao_mysql.return_value = self.__dao_mysql
        mocked_dao_mysql_mmt_read.return_value = self.__dao_mysql
        mocked_dao_mysql_hotel_booker.return_value = self.__dao_mysql
        cg = candidate_generator()
        locations = ['CTDEL', 'CTBOM']
        actual_result = cg.get_location_to_hotels(locations)
        expected_result = {'CTDEL': ['202101031631379699', '202101031631381544', '202101031702043858',
                                     '202101041313214651', '202101061132084780', '202101061132155822',
                                     '202101061132217566', '202101061146145005', '202101061147093754',
                                     '202101061147393660'],
                           'CTBOM': ['202101051448017530', '202101051448058033', '202101051612075225',
                                     '202101051734478265', '202101071655392131', '202101071731102826',
                                     '202101080901589370', '202101080902056153', '202101080917098042']}
        assert expected_result == actual_result

    def tearDown(self):
        self.__context.pop()
