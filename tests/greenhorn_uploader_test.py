import os
import sys

# flask imports

# ensure path setup
tests_dir = os.path.dirname(__file__)
root_dir = os.path.dirname(tests_dir)
api_dir = os.path.join(root_dir, 'api')
sys.path.append(api_dir)

from datetime import datetime


from gh_upsell_api  import create_app

from unittest import TestCase
from unittest import mock as umock


from mockito import mock
class greenhorn_uploader_test(TestCase):
    __context = None
    __dao_mysql = mock()
    client = None

    def setUp(self):
        app = create_app(updates={
            'TESTING': True,
            # 'DATABASE': db_path,
        })
        self.__context = app.app_context()
        self.__context.push()
        self.client = app.test_client()


    @umock.patch("validator.authenticate.Authentication.user_autheticate")
    def login_test(self,auth):
            auth.return_value = {"success": True}
            response = self.client.post("/authenticate",
                             data={"username": "abc",
                                   "password": "xyz"})
            assert response.status_code == 302
            assert response.content_type == 'text/html; charset=utf-8'
            assert response.content_encoding == None
            assert response.content_language.to_header() == ''
            return response

    def test_uploader(self):
            self.login_test()
            response = self.client.get('/GreenHornUploader')
            assert response.status_code == 200
            assert response.content_type == 'text/html; charset=utf-8'
            assert response.content_encoding == None
            assert response.content_language.to_header() == ''



    @umock.patch("dao.dao_mysql.DataAccessObjectMySQL.fetch_active_sequences")
    @umock.patch("common.cache_manager.CacheManager.get_single")
    @umock.patch("common.cache_manager.CacheManager.put")
    def test_fetchsequences(self,cache_put,cache_get,fs):
            moc = umock.Mock()
            res = (('1233773e-a5ed-11e8-942d-b05adad8387d', None, 'test50', 'BOOSTERS', '119', 12, 1, 3, 2, 2, '22 Aug 2018', '31 Aug 2018', 'weekend', '0-0', 0, 100, 1, 0, 10, None, None, None,None, datetime(2018, 8, 22, 14, 53, 47)),
                   ('23369e9e-aa84-11e8-b118-48e24411b68b', '1233773f-a5ed-11e8-942d-b05adad8387d', 'test101', 'BOOSTERS', 'all', 22, 1, 3, 4, 7, '28 Aug 2018', '31 Aug 2018', None, None, None, None, None, None, None, 'INTL', 1, datetime(2018, 8, 28, 0, 0),datetime(2018, 8, 28, 0, 0), datetime(2018, 8, 28, 11, 5, 14)),
                   ('23369e9e-aa84-11e8-b118-48e24411b68b', '123377af-a5ed-11e8-942d-b05adad8387d,1233773a-a5ed-11e8-942d-b05adad8387d','test101', 'BOOSTERS', 'all', 22, 1, 3, 4, 7, '28 Aug 2018',
                    '31 Aug 2018', None, None, None, None, None, None, None, 'INTL', 1, datetime(2018, 8, 28, 0, 0),
                    datetime(2018, 8, 28, 0, 0), datetime(2018, 8, 28, 11, 5, 14)))
            cursor = moc.connection.cursor.return_value
            cursor.fetchall.return_value = res
            cursor.description = [['uid'], ['mm_uid'],['description'], ['algorithm'], ['seqid'], ['startblock'], ['isactive'], ['blocksize'], ['nslots'], ['maxhotels'], ['startdate'], ['enddate'], ['check_in_dow'], ['ap_bucket'], ['min_adults'], ['max_adults'],['children'], ['min_los'], ['max_los'], ['domain'], ['priority'], ['min_check_in_date'], ['max_check_in_date'] ,['lastupdated']]
            moc.connection.cursor().execute("SELECT 1")
            cache_get.return_value = True
            cache_put.return_value = ''
            fs.return_value = cursor
            response=self.login_test()

            response = self.client.get("/FetchSequence",query_string={'mode': 'BOOSTERS-DOM', 'callback': ' '})
            actual_result  = ' ([{"uid": ["1233773e-a5ed-11e8-942d-b05adad8387d"], "mm_uid": null, "description": "test50", "algorithm": "BOOSTERS", ' \
                             '"seqid": "119", "startblock": 12, "isactive": 1, "blocksize": 3, "nslots": 2, "maxhotels": 2, ' \
                             '"startdate": "22 Aug 2018", "enddate": "31 Aug 2018", "check_in_dow": "weekend", "ap_bucket": "0-0", ' \
                             '"min_adults": 0, "max_adults": 100, "children": 1, "min_los": 0, "max_los": 10, "domain": null, "priority": null,' \
                             ' "min_check_in_date": null, "max_check_in_date": null, "lastupdated": "2018-08-22 14:53:47"}, {"uid": ["23369e9e-aa84-11e8-b118-48e24411b68b", "1233773f-a5ed-11e8-942d-b05adad8387d"], "mm_uid": "1233773f-a5ed-11e8-942d-b05adad8387d",' \
                             ' "description": "test101", "algorithm": "BOOSTERS", "seqid": "all", "startblock": 22, "isactive": 1, "blocksize": 3,' \
                             ' "nslots": 4, "maxhotels": 7, "startdate": "28 Aug 2018", "enddate": "31 Aug 2018", "check_in_dow": null, "ap_bucket": null,' \
                             ' "min_adults": null, "max_adults": null, "children": null, "min_los": null, "max_los": null, "domain": "INTL", "priority": 1,' \
                             ' "min_check_in_date": "2018-08-28 00:00:00", "max_check_in_date": "2018-08-28 00:00:00", "lastupdated": "2018-08-28 11:05:14"},' \
                             ' {"uid": ["23369e9e-aa84-11e8-b118-48e24411b68b", "123377af-a5ed-11e8-942d-b05adad8387d", "1233773a-a5ed-11e8-942d-b05adad8387d"], "mm_uid": "123377af-a5ed-11e8-942d-b05adad8387d,1233773a-a5ed-11e8-942d-b05adad8387d",' \
                             ' "description": "test101", "algorithm": "BOOSTERS", "seqid": "all", "startblock": 22, "isactive": 1, "blocksize": 3,' \
                             ' "nslots": 4, "maxhotels": 7, "startdate": "28 Aug 2018", "enddate": "31 Aug 2018", "check_in_dow": null, "ap_bucket": null,' \
                             ' "min_adults": null, "max_adults": null, "children": null, "min_los": null, "max_los": null, "domain": "INTL", "priority": 1,' \
                             ' "min_check_in_date": "2018-08-28 00:00:00", "max_check_in_date": "2018-08-28 00:00:00", "lastupdated": "2018-08-28 11:05:14"}])'

            actual_result = actual_result.encode('ASCII')

            assert response.data == actual_result
            assert response.status_code == 200
            assert response.content_type == 'text/html; charset=utf-8'
            assert response.content_encoding == None
            assert response.content_language.to_header() == ''


    @umock.patch("resources.ExploreSetUpdater.dao_mysql")
    @umock.patch("resources.Uploader.dao_mysql")
    @umock.patch("common.cache_manager.CacheManager.get_single")
    @umock.patch("common.cache_manager.CacheManager.put")
    @umock.patch("common.cache_manager.CacheManager.remove")
    def test_append_sequence_single_uid(self, cache_remove, cache_put, cache_get, dao_mysql, cache_refresh_dao_mysql):
        singleUidData = ({"uid": "1233773e-a5ed-11e8-942d-b05adad8387d", "end_date": "2018-08-31 00:00:00",
                          "algorithm": "NEWHOTELS", "seqid" : "00", "retire_rotate_signals" : "{}", 'city_hotels': open('test_greenhorn', 'rb')},
                         [
                             [
                                 [datetime(2019, 10, 5, 18, 00)], ['234'], ['115'], ['103'], ['911'], ['2222220309483221'],
                                 ['1232334343619'],
                                 ['************'], ['5678394548798'], ['7453733472352346'], ['2323210304839'],
                                 ['220033591']
                             ],
                             [
                                 [
                                     datetime.now(), 10
                                 ]
                             ],
                             [
                                 ['BOOSTERS_119_BLR_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___', '201209101102301', 0.0, 20597],
                                 ['BOOSTERS_119_BLR_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___', '201508061302301', 0.0, 20599],
                                 ['BOOSTERS_119_DEL_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___', '200910012133082', 0.0, 20601],
                                 ['BOOSTERS_119_DEL_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___', '312254274337451', 0.0, 20603],
                                 ['BOOSTERS_120_BLR_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___', '20120910110230', 0.0, 20605],
                                 ['BOOSTERS_120_BLR_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___', '20150806130230', 0.0, 20607],
                                 ['BOOSTERS_120_DEL_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___', '20091001213308', 0.0, 20609],
                             ],
                             [
                                 ['NEWHOTELS_118_DEL_GH'], ['NEWHOTELS_118_BLR_GH'], ['NEWHOTELS_118_JAI_GH'],
                                 ['BOOSTERS_118_DEL_GH'],
                                 ['BOOSTERS_118_BLR_GH'], ['BOOSTERS_118_JAI_GH'], ['BOOSTERS_117_DEL_GH'],
                                 ['BOOSTERS_119_DEL_GH']
                             ]
                         ]
        )
        self.append_execute_test(cache_remove, cache_put, cache_get, dao_mysql, cache_refresh_dao_mysql, singleUidData)


    @umock.patch("resources.ExploreSetUpdater.dao_mysql")
    @umock.patch("resources.Uploader.dao_mysql")
    @umock.patch("common.cache_manager.CacheManager.get_single")
    @umock.patch("common.cache_manager.CacheManager.put")
    @umock.patch("common.cache_manager.CacheManager.remove")
    def test_append_sequence_two_uid(self, cache_remove, cache_put, cache_get, dao_mysql, cache_refresh_dao_mysql):
        twoUidData = (
            {"uid": "1233773e-a5ed-11e8-942d-b05adad8387d,1233773f-a5ed-11e8-942d-b05adad8387d", "end_date": "31 Aug 2018",
             "algorithm": "NEWHOTELS", "seqid" : "00", "retire_rotate_signals" : "{}", 'city_hotels': open('test_greenhorn', 'rb')},
                [
                    [
                        [datetime(2019, 11, 5, 18, 00)], ['234'], ['115'], ['103'], ['911'], ['2222220309483221'], [
                        '1232334343619'],
                        ['************'], ['5678394548798'], ['7453733472352346'], ['2323210304839'], [
                        '220033591']
                    ],
                    [
                        [
                            datetime.now(), 10
                        ]
                    ],
                    [
                        [datetime(2019, 12, 5, 18, 00)], ['234'], ['115'], ['103'], ['911'], ['2222220309483221'],
                        ['1232334343619'],
                        ['************'], ['5678394548798'], ['7453733472352346'], ['2323210304839'],
                        ['220033591']
                    ],
                    [
                        [
                            datetime.now(), 10
                        ]
                    ],
                    [
                        ['BOOSTERS_119_BLR_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___', '201209101102301', 0.0, 20597],
                        ['BOOSTERS_119_BLR_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___', '201508061302301', 0.0, 20599],
                        ['BOOSTERS_119_DEL_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___', '200910012133082', 0.0, 20601],
                        ['BOOSTERS_119_DEL_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___', '312254274337451', 0.0, 20603],
                        ['BOOSTERS_120_BLR_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___', '20120910110230', 0.0, 20605],
                        ['BOOSTERS_120_BLR_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___', '20150806130230', 0.0, 20607],
                        ['BOOSTERS_120_DEL_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___', '20091001213308', 0.0, 20609],
                    ],
                    [
                        ['NEWHOTELS_118_DEL_GH'], ['NEWHOTELS_118_BLR_GH'], ['NEWHOTELS_118_JAI_GH'],
                        ['BOOSTERS_118_DEL_GH'],
                        ['BOOSTERS_118_BLR_GH'], ['BOOSTERS_118_JAI_GH'], ['BOOSTERS_117_DEL_GH'],
                        ['BOOSTERS_119_DEL_GH']

                    ]
                ]
            )
        self.append_execute_test(cache_remove, cache_put, cache_get, dao_mysql, cache_refresh_dao_mysql, twoUidData)


    def append_execute_test(self,cache_remove, cache_put,cache_get, dao_mysql,cache_refresh_dao_mysql, testData):
            req, dao = testData

            #, find_input_uid_all_hotels): cache_refresh_dao_mysql cache_get
            moc = umock.Mock()
            cursor = moc.connection.cursor.return_value
            cursor.fetchall.side_effect = dao
            cache_put.return_value = ''
            cache_get.return_value = True
            cache_remove.return_value = ''
            cache_refresh_dao_mysql.return_value.get_active_boosters.return_value = cursor
            cache_refresh_dao_mysql.return_value.get_inactive_boosters.return_value = cursor
            dao_mysql.return_value.get_hotel_sequence_for_uid.return_value = cursor
            dao_mysql.return_value.get_startdate.return_value = cursor

            self.login_test()
            response = self.client.post("/appendSequence",data = req)

            actual_result = '{\n  "message": "Success", \n  "success": true\n}\n'
            actual_result = actual_result.encode('ASCII')

            assert response.data == actual_result
            assert response.status_code == 200
            assert response.content_type == 'application/json'
            assert response.content_encoding == None
            assert response.content_language.to_header() == ''

    @umock.patch("resources.ExploreSetUpdater.dao_mysql")
    @umock.patch("resources.Uploader.dao_mysql")
    @umock.patch("common.cache_manager.CacheManager.get_single")
    @umock.patch("common.cache_manager.CacheManager.put")
    @umock.patch("common.cache_manager.CacheManager.remove")
    def test_delete_sequence_single_uid(self, cache_remove, cache_put, cache_get, dao_mysql, cache_refresh_dao_mysql):
        payload = {"uid": "1233773e-a5ed-11e8-942d-b05adad8387d", "algorithm": "NEWHOTELS", "seqid": "00"}
        self.delete_execute_test(cache_remove, cache_put, cache_get, dao_mysql, cache_refresh_dao_mysql, payload)

    @umock.patch("resources.ExploreSetUpdater.dao_mysql")
    @umock.patch("resources.Uploader.dao_mysql")
    @umock.patch("common.cache_manager.CacheManager.get_single")
    @umock.patch("common.cache_manager.CacheManager.put")
    @umock.patch("common.cache_manager.CacheManager.remove")
    def test_delete_sequence_two_uid(self, cache_remove, cache_put, cache_get, dao_mysql, cache_refresh_dao_mysql):
        payload = {"uid": "1233773e-a5ed-11e8-942d-b05adad8387d,23369e9e-aa84-11e8-b118-48e24411b68b", "algorithm": "NEWHOTELS", "seqid": "00"}
        self.delete_execute_test(cache_remove, cache_put, cache_get, dao_mysql, cache_refresh_dao_mysql, payload)

    def delete_execute_test(self,cache_remove, cache_put,cache_get, dao_mysql,cache_refresh_dao_mysql, payload):
            moc = umock.Mock()
            cursor = moc.connection.cursor.return_value
            cursor.fetchall.side_effect = [
                                        [
                                            ['BOOSTERS_119_BLR_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___1', '201209101102301', 0.0, 20597],
                                            ['BOOSTERS_119_BLR_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___1', '201508061302301', 0.0, 20599],
                                            ['BOOSTERS_119_DEL_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___0', '200910012133082', 0.0, 20601],
                                            ['BOOSTERS_119_DEL_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___0', '312254274337451', 0.0, 20603],
                                            ['BOOSTERS_120_BLR_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___0', '20120910110230', 0.0, 20605],
                                            ['BOOSTERS_120_BLR_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___0', '20150806130230', 0.0, 20607],
                                            ['BOOSTERS_120_DEL_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___0', '20091001213308', 0.0, 20609],
                                        ],
                                        [
                                            ['NEWHOTELS_118_DEL_GH'], ['NEWHOTELS_118_BLR_GH'], ['NEWHOTELS_118_JAI_GH'], ['BOOSTERS_118_DEL_GH'],
                                            ['BOOSTERS_118_BLR_GH'], ['BOOSTERS_118_JAI_GH'], ['BOOSTERS_117_DEL_GH'], ['BOOSTERS_119_DEL_GH']

                                        ]
            ]
            cache_put.return_value = ''
            cache_get.return_value = True
            cache_remove.return_value = ''

            cache_refresh_dao_mysql.return_value.get_active_boosters.return_value = cursor
            cache_refresh_dao_mysql.return_value.get_inactive_boosters.return_value = cursor

            dao_mysql.return_value.make_sequence_inactive.return_value = ''
            dao_mysql.return_value.make_hotels_inactive.return_value = ''

            self.login_test()
            response = self.client.post("/deleteSequence",data = payload)

            actual_result = '{\n  "message": "Success", \n  "success": true\n}\n'
            actual_result = actual_result.encode('ASCII')

            assert response.data == actual_result
            assert response.status_code == 200
            assert response.content_type == 'application/json'
            assert response.content_encoding == None
            assert response.content_language.to_header() == ''

    @umock.patch("resources.ExploreSetUpdater.dao_mysql")
    @umock.patch("resources.Uploader.dao_mysql")
    @umock.patch("common.cache_manager.CacheManager.get_single")
    @umock.patch("common.cache_manager.CacheManager.put")
    @umock.patch("common.cache_manager.CacheManager.remove")
    def test_remove_sequence_single_uid(self, cache_remove, cache_put, cache_get, dao_mysql, cache_refresh_dao_mysql):
        payload = {"uid": "1233773e-a5ed-11e8-942d-b05adad8387d", "algorithm": "NEWHOTELS", "end_date" : "31 Aug 2018",'city_hotels': open('test_greenhorn', 'rb')
            , "seqid": "00", "retire_rotate_signals": "{}"}
        self.remove_execute_test(cache_remove, cache_put, cache_get, dao_mysql, cache_refresh_dao_mysql, payload)

    @umock.patch("resources.ExploreSetUpdater.dao_mysql")
    @umock.patch("resources.Uploader.dao_mysql")
    @umock.patch("common.cache_manager.CacheManager.get_single")
    @umock.patch("common.cache_manager.CacheManager.put")
    @umock.patch("common.cache_manager.CacheManager.remove")
    def test_remove_sequence_two_uid(self, cache_remove, cache_put, cache_get, dao_mysql, cache_refresh_dao_mysql):
        payload = {"uid": "1233773e-a5ed-11e8-942d-b05adad8387d,23369e9e-aa84-11e8-b118-48e24411b68b", "algorithm" : "NEWHOTELS",
                   "end_date": "31 Aug 2018",'city_hotels': open('test_greenhorn', 'rb'), "seqid" : "00", "retire_rotate_signals" : "{}"}
        self.remove_execute_test(cache_remove, cache_put, cache_get, dao_mysql, cache_refresh_dao_mysql, payload)

    def remove_execute_test(self, cache_remove, cache_put, cache_get, dao_mysql, cache_refresh_dao_mysql, payload):
            moc = umock.Mock()
            cursor = moc.connection.cursor.return_value
            cursor.fetchall.side_effect = [
                                            [
                                                ['BOOSTERS_119_BLR_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___0', '201209101102301', 0.0, 20597],
                                                ['BOOSTERS_119_BLR_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___1', '201508061302301', 0.0, 20599],
                                                ['BOOSTERS_119_DEL_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___1', '200910012133082', 0.0, 20601],
                                                ['BOOSTERS_119_DEL_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___1', '312254274337451', 0.0, 20603],
                                                ['BOOSTERS_120_BLR_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___0', '20120910110230', 0.0, 20605],
                                                ['BOOSTERS_120_BLR_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___0', '20150806130230', 0.0, 20607],
                                                ['BOOSTERS_120_DEL_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___0', '20091001213308', 0.0, 20609],
                                            ],
                                            [
                                                ['NEWHOTELS_118_DEL_GH'], ['NEWHOTELS_118_BLR_GH'], ['NEWHOTELS_118_JAI_GH'], ['BOOSTERS_118_DEL_GH'],
                                                ['BOOSTERS_118_BLR_GH'], ['BOOSTERS_118_JAI_GH'], ['BOOSTERS_117_DEL_GH'], ['BOOSTERS_119_DEL_GH']

                                            ]
            ]
            cache_put.return_value = ''
            cache_get.return_value = True
            cache_remove.return_value = ''

            cache_refresh_dao_mysql.return_value.get_active_boosters.return_value = cursor
            cache_refresh_dao_mysql.return_value.get_inactive_boosters.return_value = cursor

            dao_mysql.return_value.remove_cities.return_value = ''
            dao_mysql.return_value.remove_hotels.return_value = ''

            self.login_test()
            response = self.client.post("/removeSequence", data=payload)

            actual_result = '{\n  "message": "Success", \n  "success": true\n}\n'
            actual_result = actual_result.encode('ASCII')

            assert response.data == actual_result
            assert response.status_code == 200
            assert response.content_type == 'application/json'
            assert response.content_encoding == None
            assert response.content_language.to_header() == ''


    @umock.patch("resources.ExploreSetUpdater.dao_mysql")
    @umock.patch("resources.Uploader.dao_mysql")
    @umock.patch("common.cache_manager.CacheManager.get_single")
    @umock.patch("common.cache_manager.CacheManager.put")
    @umock.patch("common.cache_manager.CacheManager.remove")
    def test_add_sequence_without_matchmaker(self, cache_remove, cache_put, cache_get, dao_mysql, cache_refresh_dao_mysql):
        payload = {
                        'mode': 'BOOSTERS-DOM' ,'description' : 'newTest', 'source_seq_id_txt': '1212' ,'appfest_booster': 'on',
                        'source_seq_id_rdbtn': '00', 'start_block': '3', 'block_size': '55', 'nslots': '6',
                        'ap_bucket': '1-1', 'max_hotels': '', 'check_in_dow': 'weekday', 'los_min': '0', 'los_max': '1',
                        'min_adults': '0', 'max_adults': '1', 'children': 'yes_child', 'min_check_in_date': '2018-09-01','max_check_in_date': '2018-09-06',
                        'start_date': '2018-09-01', 'end_date': '2018-09-04','is_personalized':'1', 'action': 'ADD' , 'city_hotels': open('test_greenhorn', 'rb')
                  }
        result = '{\n  "message": "Success", \n  "success": true\n}\n'
        self.execute_add_test(cache_remove, cache_put, cache_get, dao_mysql, cache_refresh_dao_mysql, payload, result)

    @umock.patch("resources.ExploreSetUpdater.dao_mysql")
    @umock.patch("resources.Uploader.dao_mysql")
    @umock.patch("common.cache_manager.CacheManager.get_single")
    @umock.patch("common.cache_manager.CacheManager.put")
    @umock.patch("common.cache_manager.CacheManager.remove")
    def test_add_sequence_invalid_slots(self, cache_remove, cache_put, cache_get, dao_mysql, cache_refresh_dao_mysql):
        payload = {
                        'mode': 'BOOSTERS-DOM', 'description': 'newTest', 'source_seq_id_txt': '1212', 'appfest_booster': 'on',
                        'source_seq_id_rdbtn': '00', 'start_block': '3', 'block_size': '55', 'nslots': '66',
                        'ap_bucket': '1-1', 'max_hotels': '', 'check_in_dow': 'weekday', 'los_min': '0', 'los_max': '1',
                        'min_adults': '0', 'max_adults': '1', 'children': 'yes_child', 'min_check_in_date': '2018-09-01',
                        'max_check_in_date': '2018-09-06',
                        'start_date': '2018-09-01', 'end_date': '2018-09-04','is_personalized':'1' , 'action': 'ADD',
                        'city_hotels': open('test_greenhorn', 'rb')
                  }
        result = '{\n  "message": "Block Size Should Be Greater Than Perturbed Slots Per Block", \n  "success": false\n}\n'
        self.execute_add_test(cache_remove, cache_put, cache_get, dao_mysql, cache_refresh_dao_mysql, payload, result)

    @umock.patch("resources.ExploreSetUpdater.dao_mysql")
    @umock.patch("resources.Uploader.dao_mysql")
    @umock.patch("common.cache_manager.CacheManager.get_single")
    @umock.patch("common.cache_manager.CacheManager.put")
    @umock.patch("common.cache_manager.CacheManager.remove")
    def test_add_sequence_with_matchmaker(self, cache_remove, cache_put, cache_get, dao_mysql, cache_refresh_dao_mysql):
        payload = {
                        'mode': 'BOOSTERS-DOM', 'description': 'helloTest', 'source_seq_id_txt': '4654', 'appfest_booster': 'on',
                        'source_seq_id_rdbtn': '00', 'start_block': '3', 'block_size': '55', 'nslots': '6',
                        'ap_bucket': '1-1', 'max_hotels': '', 'check_in_dow': 'weekday', 'los_min': '0', 'los_max': '1',
                        'min_adults': '0', 'max_adults': '1', 'children': 'yes_child', 'min_check_in_date': '2018-09-01',
                        'max_check_in_date': '2018-09-06', 'matchmaker_booster': '1', 'mm_start_block_value': '2',
                        'start_date': '2018-09-01', 'end_date': '2018-09-04','is_personalized':'1', 'action': 'ADD',
                        'city_hotels': open('test_greenhorn', 'rb')
                    }
        result = '{\n  "message": "Success", \n  "success": true\n}\n'
        self.execute_add_test(cache_remove, cache_put, cache_get, dao_mysql, cache_refresh_dao_mysql, payload, result)

    def execute_add_test(self, cache_remove, cache_put, cache_get, dao_mysql, cache_refresh_dao_mysql, payload, actual_result):
            moc = umock.Mock()
            cursor = moc.connection.cursor.return_value
            cursor.fetchall.side_effect = [ [],
                                            [],
                                            [
                                                ['BOOSTERS_119_BLR_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___0', '201209101102301', 0.0, 20597],
                                                ['BOOSTERS_119_BLR_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___1', '201508061302301', 0.0, 20599],
                                                ['BOOSTERS_119_DEL_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___1', '200910012133082', 0.0, 20601],
                                                ['BOOSTERS_119_DEL_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___1', '312254274337451', 0.0, 20603],
                                                ['BOOSTERS_120_BLR_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___0', '20120910110230', 0.0, 20605],
                                                ['BOOSTERS_120_BLR_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___1', '20150806130230', 0.0, 20607],
                                                ['BOOSTERS_120_DEL_GH', '12_3_2_2_weekend_0-0_0_100_1_0_10___1', '20091001213308', 0.0, 20609],
                                            ],
                                            [
                                                ['NEWHOTELS_118_DEL_GH'], ['NEWHOTELS_118_BLR_GH'], ['NEWHOTELS_118_JAI_GH'], ['BOOSTERS_118_DEL_GH'],
                                                ['BOOSTERS_118_BLR_GH'], ['BOOSTERS_118_JAI_GH'], ['BOOSTERS_117_DEL_GH'], ['BOOSTERS_119_DEL_GH']

                                            ],
                                            [],
                                            []
            ]
            cache_put.return_value = ''
            cache_get.return_value = True
            cache_remove.return_value = ''

            cache_refresh_dao_mysql.return_value.get_active_boosters.return_value = cursor
            cache_refresh_dao_mysql.return_value.get_inactive_boosters.return_value = cursor

            dao_mysql.return_value.find_input_booster_sequence.return_value = cursor
            dao_mysql.return_value.find_input_active_description.return_value = cursor
            dao_mysql.return_value.add_sequence.return_value = ''
            dao_mysql.return_value.add_hotels.return_value = ''

            self.login_test()
            response = self.client.post("/addSequence", data=payload)

            actual_result = actual_result.encode('ASCII')

            assert response.data == actual_result
            assert response.status_code == 200
            assert response.content_type == 'application/json'
            assert response.content_encoding == None
            assert response.content_language.to_header() == ''
