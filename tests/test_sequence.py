import pytest

def test_sequence(client):
	response = client.get('/FetchSequence', query_string={'mode': ' ', 'callback': ' '})
	assert response.status_code == 302
	body = response.get_data(as_text=True)
#
def test_explore_set_updater(client):

    response = client.get('/CacheRefresh')
    assert response.status_code == 200
    # assert str(type(response)) == '<class \'couchbase.exceptions._UnknownHostError_0x15 (generated, catch UnknownHostError)\'>'

def test_green_horn(client):
	response = client.get('/GreenHorn', query_string={'mode': ' ', 'callback': ' '})
	assert response.status_code == 405
	body = response.get_data(as_text=True)
