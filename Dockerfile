# BASE IMAGE
FROM 524881529748.dkr.ecr.ap-south-1.amazonaws.com/mmt-ecs-base:python3.6-15-ubuntu22

# INSTALL ANACONDA
RUN wget https://repo.continuum.io/archive/Anaconda2-2019.07-Linux-x86_64.sh -O ~/anaconda.sh
RUN /bin/bash ~/anaconda.sh -b -u -p /opt/conda
RUN yes | rm ~/anaconda.sh

RUN apt-get update && apt-get install -y  gcc g++ libssl-dev libbz2-dev libldap2-dev python3-dev libevent-dev alien  libsasl2-dev


COPY docker-requirements.txt /tmp/

# ACTIVATE CONDA & INSTALL REQUIREMENTS
RUN yes | /opt/conda/bin/conda create --name py36 python=3.6 && \
    /bin/bash -c "source /opt/conda/bin/activate py36 && pip install --no-cache-dir -r /tmp/docker-requirements.txt"
# WORKING DIRECTORY
RUN mkdir -p /opt/Hotel-Personalization
COPY . /opt/Hotel-Personalization
WORKDIR /opt/Hotel-Personalization/api

# LOG DIRECTORY
RUN mkdir -p /opt/logs/Hotel-Personalization/upsell

# RUN
EXPOSE 8080
CMD ["/opt/conda/envs/py36/bin/gunicorn", "-c", "gunicorn_config.py", "wsgi:manager"]