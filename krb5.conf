[logging]
 default = FILE:/var/log/krb5libs.log
 kdc = FILE:/var/log/krb5kdc.log
 admin_server = FILE:/var/log/kadmind.log

[libdefaults]
default_realm = MMT.MMT
        clockskew       =       300
        ticket_lifetime =       1d
        renew_lifetime = 365d
        forwardable = true
        default_tgs_enctypes = rc4-hmac
        default_tkt_enctypes = rc4-hmac
        permitted_enctypes = rc4-hmac
        udp_preference_limit = 1
        kdc_timeout = 3000
        proxiable       =       true
        dns_lookup_realm = true
        dns_lookup_kdc = true
        allow_weak_crypto = true

[domain_realm]
        .kerberos.server = MMT.MMT
        .mmt.mmt = MMT.MMT
        mmt.mmt = MMT.MMT
        mmt     = MMT.MMT
 
[appdefaults]
        pam = {
        ticket_lifetime         = 1d
        renew_lifetime          = 365d
        forwardable             = true
        proxiable               = false
        retain_after_close      = false
        minimum_uid             = 0
        debug                   = false
        }

