##!/usr/bin/env bash
#
## ACTIVATE CONDA
#yes | /opt/conda/bin/conda create --name py36 python=3.6
#source /opt/conda/bin/activate py36
#
## INSTALL REQUIREMENTS
#wget http://packages.couchbase.com/releases/couchbase-release/couchbase-release-1.0-6-x86_64.rpm
#rpm -iv couchbase-release-1.0-6-x86_64.rpm
#yum install -y libcouchbase-devel libcouchbase2-bin gcc gcc-c++ python-devel libevent-devel openldap-devel
#pip install --no-cache-dir -r /tmp/docker-requirements.txt
