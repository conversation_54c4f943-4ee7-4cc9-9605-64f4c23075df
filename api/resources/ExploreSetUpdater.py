# Author: <PERSON><PERSON><PERSON>
# Date: 14 Dec 2017

# coding: utf-8

import pandas as pd
import os


from common.cache_manager import CacheManager
from dao.dao_mysql import DataAccessObjectMySQL as dao_mysql
from flask import current_app as app
import common.helpers as hp
from common.aerospike_wrapper import AerospikeWrapper
import copy

class ExploreSetUpdater(object):

    __dao_mysql = None

    def __init__(self):
        self.log = hp.mdc_logger("ExploreSetUpdater")
        self.log.info("Initializing Explore Set Updater")
        self.__dao_mysql = dao_mysql()
        fname = app.config['S3_FILE_HOTEL_LOCUS']
        data_dir = app.config['DATADIR']
        self.hotel_location_df = pd.read_csv(os.path.join(data_dir, fname),dtype={'hotel_id':str,'location_id':str})

    def get_active_boosters(self, algorithm, seqid, brand='MMT'):
        cur = self.__dao_mysql.get_active_boosters(algorithm, seqid,brand)
        boosted_hotels_df = pd.DataFrame(x for x in cur.fetchall())
        if boosted_hotels_df.empty:
            return dict()

        boosted_hotels_df.columns = ['algorithm_seqid_city', 'blockConfig', 'hotel_id', 'score', 'display_sequence',
                                     'booster_type', 'boost_percentage', 'automated_booster_type', 'city']
        try:
            boosted_hotels_df = boosted_hotels_df.astype({'hotel_id': str})
            boosted_hotels_df = self.__update_location(boosted_hotels_df)
            slot_cache_data = self.__get_slot_cache_data(boosted_hotels_df)
            percent_cache_data = self.__get_percent_cache_data(boosted_hotels_df)
            cache_data = copy.deepcopy(slot_cache_data) if slot_cache_data else dict()
        except Exception as e:
            self.log.error("Error in get_active_boosters" + str(e))

        if percent_cache_data:
            for key, value in percent_cache_data.items():
                if key not in cache_data:
                    cache_data[key] = dict()
                cache_data[key].update(value)
        return cache_data

    def get_inactive_boosters(self, seqid_city_map, algorithm, seqid, brand='MMT'):
        cur = self.__dao_mysql.get_inactive_boosters(algorithm, seqid, brand)
        inactive_hotels = pd.DataFrame(x for x in cur.fetchall())
        if inactive_hotels.empty:
            return []
        inactive_hotels.columns = ['algorithm_seqid_city', 'hotel_id', 'automated_booster_type', 'city']

        inactive_hotels = inactive_hotels.astype({'hotel_id': str})
        inactive_hotels = self.__update_location(inactive_hotels)

        inactive_booster_keys = set()
        for ix, rec in inactive_hotels.iterrows():
            inactive_booster_key = rec['algorithm_seqid_city'][:-3] + "_" + str(rec['location_id']) + rec['algorithm_seqid_city'][-3:]
            inactive_booster_keys.add(inactive_booster_key)

        self.log.info("Done with Generating keys for Inactive Boosters")

        inactive_booster_keys = list(inactive_booster_keys - set(seqid_city_map.keys()))
        return inactive_booster_keys

    def refresh_cache(self, algorithm, seqid, brand='MMT'):
        try:
            seqid_city_map = self.get_active_boosters(algorithm, seqid, brand)
        except Exception as e:
            seqid_city_map = dict()
            self.log.error("Error in fetching active boosters " + str(e))

        try:
            inactive_booster_keys = self.get_inactive_boosters(seqid_city_map, algorithm, seqid, brand)
        except Exception as e:
            inactive_booster_keys = []
            self.log.error("Error in fetching inactive boosters " + e)

        try:
            if brand:
                namespace = app.config['AEROSPIKE_NAMESPACE']
                ttl = app.config['AEROSPIKE_TTL']
                asp_set = 'gh_{0}'.format(brand.lower())
                for key, value in list(seqid_city_map.items()):
                    AerospikeWrapper(app.config).set_data(key, value, asp_set, namespace, ttl)
                self.log.info("Boosters updated in Aerospike cache")

                for key in inactive_booster_keys:
                    AerospikeWrapper(app.config).remove_data(key,asp_set,namespace)

        except Exception as e:
            self.log.error("Error in updating aerospike " + e)

        self.__dao_mysql.close()
        return "SUCCESS"

    def __get_slot_cache_data(self, boosted_hotels_df):
        cache_data = dict()
        if not boosted_hotels_df.empty:
            slot_boosted_hotels_df = boosted_hotels_df[boosted_hotels_df['booster_type'] == 'S']
            slot_boosted_hotels_df = slot_boosted_hotels_df[['algorithm_seqid_city', 'blockConfig', 'hotel_id', 'score',
                                                             'display_sequence', 'location_id']]
            for index, boosted_hotel in slot_boosted_hotels_df.iterrows():
                # Slot Cache Key e.g. BOOSTERS_633_CTGOI_GH
                cache_key = self.__get_cache_key(boosted_hotel)
                # Slot Sub Key e.g. 4_20_6_1835___4_50_0_____20_0
                # Automated Slot Sub Key e.g. AS_4_20_6_1835___4_50_0_____20_0
                sub_key = boosted_hotel[1]
                # Slot Sub Value e.g. {201507261817591247 : [0.0, 34545]}
                sub_value = {boosted_hotel[2].replace("\r", "").replace("\n", ""): [boosted_hotel[3], boosted_hotel[4]]}
                cache_data.setdefault(cache_key, {}).setdefault(sub_key, []).append(sub_value)
        return cache_data

    def __get_percent_cache_data(self, boosted_hotels_df):
        cache_data = dict()
        if not boosted_hotels_df.empty:
            percent_boosted_hotels_df = boosted_hotels_df[boosted_hotels_df['booster_type'] == 'P']
            percent_boosted_hotels_df = percent_boosted_hotels_df[
                ['algorithm_seqid_city', 'blockConfig', 'hotel_id', 'score',
                 'display_sequence', 'location_id', 'boost_percentage']]
            for index, boosted_hotel in percent_boosted_hotels_df.iterrows():
                # Percent Cache Key e.g. BOOSTERS_636_CTDEL_GH
                cache_key = self.__get_cache_key(boosted_hotel)
                # Percent Sub Key e.g. P_30________2022-05-11_2022-12-31_20_0
                # Automated Percent Sub Key - Not Supported
                sub_key = boosted_hotel[1]
                # Percent Sub Value: {201204261605196731 : [0.0, 167, 35.0]}
                sub_value = {boosted_hotel[2].replace("\r", "").replace("\n", ""): [boosted_hotel[3], boosted_hotel[4],
                                                                                    boosted_hotel[6]]}
                cache_data.setdefault(cache_key, {}).setdefault(sub_key, []).append(sub_value)
        return cache_data

    def __get_cache_key(self, boosted_hotel):
        return boosted_hotel['algorithm_seqid_city'][:-3] + "_" + str(boosted_hotel['location_id']) \
               + boosted_hotel['algorithm_seqid_city'][-3:]

    def __update_location(self, booster_df):
        # The city is not available in boosted_hotels table for non-automated booster.
        # Therefore, getting city of hotel from hotel_location_mapping table.
        non_automated_booster_df = booster_df[booster_df['automated_booster_type'].isnull()]

        if not non_automated_booster_df.empty:
            non_automated_booster_df = pd.merge(non_automated_booster_df,
                                                self.hotel_location_df[['hotel_id', 'location_id']], on=['hotel_id'])

        # The city is available in boosted_hotels table for automated booster. Therefore, using it.
        automated_booster_df = booster_df[booster_df['automated_booster_type'].notnull()]

        if not automated_booster_df.empty:
            automated_booster_df['location_id'] = automated_booster_df.apply(lambda row: row.city, axis=1)

        if not non_automated_booster_df.empty and not automated_booster_df.empty:
            booster_df = pd.concat([non_automated_booster_df, automated_booster_df])
        elif not non_automated_booster_df.empty:
            booster_df = non_automated_booster_df
        else:
            booster_df = automated_booster_df

        booster_df.drop(['automated_booster_type', 'city'], axis=1, inplace=True)
        return booster_df

    def add_booster_delete_id(self, booster_delete_id):
        namespace = app.config['AEROSPIKE_NAMESPACE']
        ttl = app.config['AEROSPIKE_TTL']
        asp_set = 'gh_delete'
        AerospikeWrapper(app.config).set_data(booster_delete_id, "SUCCESS", asp_set, namespace, ttl)
        self.log.info("Boosters updated in Aerospike cache")
        return "SUCCESS"

    def get_delete_status(self, deleteId):
        namespace = app.config['AEROSPIKE_NAMESPACE']
        asp_set = 'gh_delete'
        return AerospikeWrapper(app.config).get_data(deleteId, asp_set, namespace)