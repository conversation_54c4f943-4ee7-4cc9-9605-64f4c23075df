import common.helpers as hp
from flask import current_app as app
import pandas as pd
import json
import requests

class ManualRankManager(object):
    __log = 'Manual Rank Manager :: '

    def __init__(self):
        self.log = hp.mdc_logger("ManualRankManager")
        self.log.info(self.__log + "initializing")

    def push_rank(self,htl_loc_csv,rank_csv,city_list, brand):
        self.log.info("Manual Rank Push for:"+str(len(city_list)))
        loc_df = pd.read_csv(htl_loc_csv,dtype={'hotel_id':str})
        self.log.info("Hotel_location data size:"+str(loc_df.shape))
        rank_df = pd.read_csv(rank_csv,dtype={'hotel_id':str,'seq_id':int})
        self.log.info("Rank data size:" + str(rank_df.shape))
        rank_df = rank_df.merge(loc_df, on=['hotel_id'], how='inner')
        self.log.info("After Merging Hotel location:"+ str(rank_df.shape))
        rank_df = rank_df.groupby(['location_id', 'seq_id'])['hotel_id'].apply(list)
        rank_df = rank_df.reset_index()
        rank_df = rank_df[(rank_df['seq_id'] > 0) & (rank_df['seq_id'] < 151)]
        for city in city_list:
            self.log.info("Manual rank For city:"+ city)
            city_df = rank_df[rank_df['location_id'] == city]
            self.log.info("Data For City:" + str(city_df.shape))
            if city_df.empty == False:
                for index,row in city_df.iterrows():
                    # city_df.set_index(['location_id','seq_id'],inplace = True)
                    req = {}
                    req['hotelIds'] = row['hotel_id']
                    req['cityCode'] = row['location_id']
                    req['seqId'] = row['seq_id']
                    req['brand'] = brand
                    req = json.dumps(req)
                    r = requests.post(url=app.config['MANUAL_RANK_API_URL'], data=req, headers=app.config['MANUAL_RANK_API_HEADERS'])
                    self.log.info("Data For City:" + r.text)
                    if 'Success' != r.text:
                        return False
        return True
