# Author: <PERSON><PERSON><PERSON>
# Date: 05 Feb 2018

# coding: utf-8
from automated_booster.automated_booster_generator import AutomatedBoosterGenerator
from common.cache_manager import CacheManager
from common.mail_util import mail_util
from dao.dao_mysql import DataAccessObjectMySQL as dao_mysql
from dao.dao_mysql_mmt3_read import dao_mysql_mmt3_read as dao_mysql_mmt3_read
from datetime import datetime
from dateutil.parser import parse
from flask import request, current_app as app
from flask_login import current_user
from resources.ExploreSetUpdater import ExploreSetUpdater
import csv

import common.helpers as hp
import json
import os
import traceback
import uuid
from werkzeug.utils import secure_filename


class Uploader(object):

    __dao_mysql = None

    def __init__(self):
        self.log = hp.mdc_logger("Uploader")
        self.__dao_mysql = dao_mysql()
        self.__dao_mysql_mmt3_read = dao_mysql_mmt3_read()
        if request.data.decode() != "":
            self.params = json.loads(request.data.decode())
        else:
            self.params = request.form
        self.__automated_booster_generator = AutomatedBoosterGenerator(self.__dao_mysql, self.__dao_mysql_mmt3_read)

    def getListOfSeqId(self,seqIdForBoosters):
        return [str(seqId).strip() for seqId in seqIdForBoosters.split(',')]

    def initializeDataForAdd(self):
        source = self.params["source"] if self.params.get('source') is not  None else None
        mode = (self.params["mode"]).split("-")
        self.algorithm = mode[0]
        self.source_seq_id = set(self.getListOfSeqId(self.params["source_seq_id_txt"]) if self.params["source_seq_id_txt"] is not '' else [self.params["source_seq_id_rdbtn"]])
        self.description = self.params["description"]
        self.brand = self.params["brand"]
        self.min_check_in_date = self.params['min_check_in_date'] or None
        self.max_check_in_date = self.params['max_check_in_date'] or None
        self.start_date = self.params['start_date']
        self.end_date = self.params['end_date']
        self.check_in_dow = None if self.params.get('check_in_dow') == 'ignore_dow' else self.params['check_in_dow']
        self.is_personalized = '1' if self.params.get('personalization') is not None else '0'
        self.domain = mode[1]
        self.priority ='20' if self.params.get('appfest_booster') is not None else '1'
        self.add_Matchmaker_Booster = '1' if self.params.get('matchmaker_booster') is not None else '0'
        self.matchmaker_start_block = self.params.get('mm_start_block_value')
        self.user_segments = self.params.get('user_segments')
        self.lob = self.params.get('lobs')

        if source == "AUTOBOOSTERS":
            self.auto_booster_id = self.params.get('auto_booster_id')
            self.shuffling_enable = self.params.get('shuffling_enable')
            self.retire_enable = self.params.get('retire_enable')
        else:
            self.auto_booster_id = None
            self.shuffling_enable = None
            self.retire_enable = None
        # None is added as default since in cases where empty string is returned it will be converted to None
        if source == "AUTOBOOSTERS":
            app.config['LOG'].info('apBucket: {}, BoosterId: {}'.format(self.params.get('ap_bucket'), self.auto_booster_id))
            ap_bucket_value = self.params.get('ap_bucket')
            ap_bucket_list = [ap_bucket_value] if ap_bucket_value else []
        else:
            ap_bucket_list = self.params.getlist('ap_bucket')
        if ap_bucket_list is not None and len(ap_bucket_list) == 1:
            self.ap_bucket = ap_bucket_list[0]
        elif ap_bucket_list is not None and len(ap_bucket_list) > 1:
            min_val = min([(int)(bkt.split('-')[0]) for bkt in ap_bucket_list])
            max_val = max([(int)(bkt.split('-')[1]) for bkt in ap_bucket_list])
            self.ap_bucket = (str)(min_val) + '-' + (str)(max_val)
        else:
            self.ap_bucket = None
        self.los_max = self.params.get('los_max') or None
        self.los_min = self.params.get('los_min') or 0 if self.los_max is not None else None
        self.max_adults = self.params.get('max_adults') or None
        self.min_adults = self.params.get('min_adults') or 0 if self.max_adults is not None else None
        self.children = None if self.params.get('children') == 'ignore_child' else self.params['children'] == 'yes_child'
        if(self.params.get('automated_hotel_list') is not None):
            success, message, csv_data_list = self.getDataFromHotelList()
        else:
            success, message, csv_data_list = self.getDataFromCSVFile()

        self.retire_hotels = self.params.get('retire_hotels')
        if self.retire_hotels:
            self.retire_enable = 'Y'
            self.retirement_strategy = self.params.get('retirement_strategy_value')
        else:
            self.retirement_strategy = None

        self.automated_booster = self.params.get('automated_booster')
        # self.booster_list contains hotel_id, boost_percentage (optional/empty), location_id (optional/empty)
        if self.automated_booster:
            self.automated_booster_type = self.params.get('automated_booster_type_value')
            self.automated_booster_experiment = int(self.params.get('automated_booster_experiment_value'))
            self.booster_list = self.__automated_booster_generator.generate_booster_list(self.automated_booster_type, self.automated_booster_experiment, csv_data_list)
        else:
            self.automated_booster_type = None
            self.automated_booster_experiment = None
            self.booster_list = csv_data_list

        self.booster_type = self.params.get('booster_type')
        if self.booster_type == "percentage_based":
            self.start_block = 0
            self.nslots = 0
            self.block_size = 0
            self.default_percentage = self.params.get('def_percentage')
            self.max_hotels = 0
        else:
            self.default_percentage = 0
            self.start_block = self.params['start_block']
            self.block_size = self.params['block_size']
            self.nslots = self.params['nslots']
            self.max_hotels = self.params.get('max_hotels') or len(self.booster_list)
            if not self.automated_booster and self.max_hotels == 0:
                return (False, "CSV should not be empty | Max hotels should not be 0")

        if self.algorithm == 'BOOSTERS' or self.algorithm == 'AUTO':
            corporate_only = "false"
            if self.params.get('rotate_hotels_corporate_only'):
                corporate_only = "true"

            self.retire_rotate_signals = "{\"booking_relative\":" + self.params.get('bkg_rel_cutoff_val') + \
                                         ", \"corporate\":" + corporate_only \
                                         + "}" \
                if self.params.get('rotate_hotels') else "{}"
        elif self.algorithm == 'NEWHOTELS':
            self.retire_rotate_signals = "{\"booking_absolute\":[" + str(app.config['BKG_ABS_CUTOFF_VAL']) + ","\
                                         + str(app.config['BKG_ABS_CUTOFF_PERC']) + "]}"

        self.rotation_time_minutes = self.params.get('rotation_time_minutes') or None
        self.pokus_exp = self.params.get('pokus_exp')
        return (success, message)

    def initializeDataForDownload(self):
        self.uid = self.params['uid']

    def initializeDataForDelete(self):
        self.uid = self.params['uid'].split(",")
        self.algorithm = self.params['algorithm']
        self.source_seq_id = self.params['seqid']
        self.brand = self.params['brand']
        self.deleteId = self.params['deleteId']

    def initializeDataForDeleteStatus(self):
        self.deleteId = self.params['deleteId']

    def readDescriptionsFromInputFile(self):
        f = request.files['bulk_delete_descriptions']
        filename = secure_filename(f.filename)
        f.save(os.path.join(app.config['UPLOAD_FOLDER'], filename))
        descriptions = []
        with open(app.config['UPLOAD_FOLDER'] + "/" + filename) as data:
            for line in csv.reader(data):
                if len(line) > 0:
                    descriptions.append(line[0])
        return descriptions

    def initializeAndDeactivateBoostersForBulkDelete(self):
        mu = mail_util()
        mail_subject = app.config['MAIL_SUBJECT_BULK_DELETION']
        mail_content = f"\nBulk Deletion Requested by User - {current_user.username}\n\n"

        try:
            descriptions = self.readDescriptionsFromInputFile()
            if len(descriptions) == 0:
                mail_content += 'Uploaded file is Empty.'
            else:
                descriptions_str = '","'.join(descriptions)
                cur = self.__dao_mysql.find_input_descriptions(descriptions_str)
                sequences = cur.fetchall()
                active_descriptions = []
                inactive_descriptions = []
                active_descriptions_details = []
                if len(sequences) > 0:
                    for sequence in sequences:
                        if sequence[2]:
                            active_descriptions.append(sequence[0])
                            active_descriptions_details.append({
                                'uid': sequence[1],
                                'algorithm': sequence[3],
                                'seqid': sequence[4],
                                'description': sequence[0]
                            })
                        else:
                            inactive_descriptions.append(sequence[0])
                not_present_descriptions = list(set(descriptions) - set(list(active_descriptions + inactive_descriptions)))
            

                for active_descriptions_detail in active_descriptions_details:
                    self.uid = [active_descriptions_detail['uid']]
                    self.algorithm = active_descriptions_detail['algorithm']
                    self.source_seq_id = active_descriptions_detail['seqid']
                    self.deleteSequence()

                mail_content += "Descriptions which got deactivated ::\n\n" + str(active_descriptions) if active_descriptions else ''
                mail_content += "\n\nDescriptions which were already deactivated ::\n\n" + str(inactive_descriptions) if inactive_descriptions else ''
                mail_content += "\n\nDescriptions which are not present ::\n\n" + str(not_present_descriptions) if not_present_descriptions else ''
        except Exception as exc:
            mail_content += f"Something went wrong :\n\n{str(exc)}"
        finally:
            mu.send_mail(mail_subject, mail_content, app.config['MAIL_TO_BULK_DELETION'])

        return True, "Success"

    def initializeDataForCache(self):
        self.algorithm = self.params['algorithm']
        self.seqId = self.params['seqId']

    def initializeDataForAppendRemove(self):
        self.uid = self.params['uid'].split(",")
        self.algorithm = self.params['algorithm']
        success, message, csv_data_list = self.getDataFromCSVFile()
        self.booster_list = csv_data_list
        self.end_date = self.params['end_date']
        self.max_hotels = self.params.get('max_hotels') or len(self.booster_list)
        self.source_seq_id = self.params['seqid']
        self.retire_rotate_signals = self.params['retire_rotate_signals']
        self.brand = self.params['brand']
        return (success, message)

    # Case 1 - Non-Automated Booster: CSV contains hotel id and hotel boost percentage
    # Case 2 - Automated Booster - CSV contains location id
    def getDataFromCSVFile(self):
        success = True
        message = None
        f = request.files['city_hotels']
        filename = secure_filename(f.filename)
        f.save(os.path.join(app.config['UPLOAD_FOLDER'], filename))
        data = []
        with open(app.config['UPLOAD_FOLDER'] + "/" + filename) as fl:
            for line in fl:
                val = line.strip() if line else None
                if val != "" and val not in data:
                    data.append(val.split(","))
        return (success, message, data)
    def getDataFromHotelList(self):
        success = True
        message = None
        data = []
        hotels = self.params.get('automated_hotel_list')
        for hotel in hotels:
            data.append(hotel.split(","))
        return (success, message, data)

    def delete_greenhorn_sequence(self):
        '''
        Unique identifier of the sequence to be deleted <algorithm,seqid,startblock>
        '''
        for uid in self.uid:
            cur = self.__dao_mysql.make_sequence_inactive(uid)
            cur = self.__dao_mysql.make_hotels_inactive(uid)

    # APPEND: Not supported for Automated Booster
    def append_greenhorn_hotels(self, username, htl_score, source):

        for uid in self.uid:
            input_hotels = self.booster_list
            if len(input_hotels) > 0:
                lastupdated = datetime.now()
                cur = self.__dao_mysql.get_boosted_sequence_info(uid)
                data_tuple = cur.fetchall()
                table_data = data_tuple[0]
                startdate = table_data[0]
                booster_type = table_data[1]
                def_boost_percentage = table_data[2]
                startdate = max(lastupdated, startdate)
                existing_hotels = []
                max_display_sequence = 0
                # Get existing hotels in db for uid
                cur = self.__dao_mysql.get_hotel_sequence_for_uid(uid)
                rows = cur.fetchall()
                if rows:
                    existing_hotels = [row[0] for row in rows]
                    # Sort by display sequence and get max display sequence (always >= max original sequence)
                    rows = sorted(rows, key=lambda row: row[1])
                    max_display_sequence = rows[-1][1]

                insert_rows = ""
                update_rows_with_score = []
                update_rows = []
                update_rows_display = []

                sequence = max_display_sequence + 1
                for h in input_hotels:
                    hotel_id = h[0]

                    if booster_type == 'P':
                        boost_percentage = def_boost_percentage if len(h) <= 1 or h[1] is None or h[1] is '' or h[1].isspace() else h[1]
                    else:
                        boost_percentage = 0

                    # Update if in existing hotels
                    if hotel_id in existing_hotels:

                        # Do not update signals in case of candidate generation
                        # as updating signals would override signals provided by user through UI
                        if source == app.config['SOURCE_CANDIDATE_GENERATION']:
                            update_rows_with_score.append((htl_score[hotel_id], 'active', startdate,
                                                               lastupdated, username, sequence, sequence, boost_percentage, uid, hotel_id))
                        # Do not update score as updating score
                        # would override score generated by candidate generation
                        elif source == app.config['SOURCE_UPLOADER']:
                            update_rows.append(('active', startdate,
                                                             lastupdated, username, sequence, sequence, boost_percentage, uid, hotel_id))
                        # Only update display sequence
                        elif source == app.config['SOURCE_RETIRE_ROTATE']:
                            update_rows_display.append((lastupdated, username, sequence, boost_percentage, uid, hotel_id))

                    # Insert if not in existing hotels
                    else:
                        insert_rows = insert_rows + "('" + str(uid) + "','" + \
                        hotel_id + "','" + \
                        str(htl_score[hotel_id]) + "','active',-1,-1,'" + \
                        str(startdate) + "','" + \
                        str(parse(self.end_date).strftime("%Y-%m-%d %H:%M:%S")) + "','" + \
                        str(lastupdated) + "','" + username + "','" + \
                        self.retire_rotate_signals + "'," + str(sequence) + "," + str(sequence) + ","  +\
                        str(boost_percentage) +  ",''),"

                    sequence = sequence + 1

                insert_rows = insert_rows[:-1]
                if insert_rows:
                    self.__dao_mysql.add_hotels(insert_rows)
                if update_rows_with_score:
                    self.__dao_mysql.update_hotels_with_score(update_rows_with_score)
                if update_rows:
                    self.__dao_mysql.update_hotels(update_rows)
                if update_rows_display:
                    self.__dao_mysql.update_hotels_display(update_rows_display)

    # REMOVE: Not supported for Automated Booster
    def remove_greenhorn_hotels(self):
        hotels = "(" + ",".join(h[0].rstrip("\r\n") for h in self.booster_list) + ")"

        for uid in self.uid:
            self.__dao_mysql.remove_hotels(hotels, uid)

    '''
    Adds entry for boosted hotels and boosted sequence
    '''
    def add_greenhorn_sequence(self,seq_id,description, brand, master_uid=None):
        uid = self.__add_greenhorn_sequence(seq_id, description, self.algorithm, self.start_block, master_uid)
        self.triggerCacheRefresh(self.algorithm, seq_id, brand)
        if self.add_Matchmaker_Booster == '1':
            mmDesc = "MM_" + description
            mmAlgorithm = "MATCHMAKER"
            mmStartBlock = self.matchmaker_start_block

            mmUid = self.__add_greenhorn_sequence(seq_id, mmDesc, mmAlgorithm, mmStartBlock, master_uid)
            cur = self.__dao_mysql.add_uid_map(uid, mmUid)
            self.triggerCacheRefresh(mmAlgorithm, seq_id, brand)

    def __add_greenhorn_sequence(self,seq_id,description, mmAlgorithm, mmStartBlock, master_uid=None):
        # generate uid
        uid = uuid.uuid1()
        # prepare to add records
        rows = ""
        lastupdated = datetime.now()
        display_order = 1
        if self.booster_type == "percentage_based":
            booster_type_col = 'P'
        else:
            booster_type_col = 'S'

        for h in self.booster_list:
            hotel_id = h[0]
            hotel_percentage = 0
            retire_absolute_threshold = 0
            retire_conversion_threshold = 0
            location_id = ''
            if booster_type_col == 'P':
                hotel_percentage = self.default_percentage if self.isValueAbsent(h, 1) else h[1]
            if self.automated_booster and not self.isValueAbsent(h, 2):
                location_id = h[2]
            if self.retire_hotels and self.retire_enable == 'Y':
                if not self.isValueAbsent(h, 2):
                    retire_absolute_threshold = h[2]
                    if not self.isValueAbsent(h, 3):
                        retire_conversion_threshold = h[3]
                else:
                    self.log.error('Retire Absolute Threshold is mandatory for hotel id: {} as retire hotel is enabled'.format(hotel_id))
                    break

            rows = rows + "('" + str(uid) + "','" + \
                       hotel_id + "'," +\
                       "0.0,'active',-1,-1,'" + \
                       str(self.start_date) + "','" + \
                       str(parse(self.end_date).strftime("%Y-%m-%d %H:%M:%S")) + "','" + \
                       str(lastupdated) + "','" + current_user.username + "','" + \
                       self.retire_rotate_signals + "'," + str(display_order) + "," + str(display_order) + \
                       ","+ str(hotel_percentage) + ",'" + location_id + "'," + str(retire_absolute_threshold) + "," + str(retire_conversion_threshold) + "),"
            display_order = display_order + 1

        cur = self.__dao_mysql.add_sequence(uid, description, self.brand, seq_id, mmStartBlock, mmAlgorithm, self.block_size,
                                            self.nslots, self.max_hotels, self.start_date, self.end_date,
                                            self.check_in_dow, self.ap_bucket, self.min_adults, self.max_adults,
                                            self.children, self.los_min, self.los_max, self.priority, self.domain,
                                            self.is_personalized, self.min_check_in_date, self.max_check_in_date, current_user.username,
                                            self.rotation_time_minutes, self.retire_rotate_signals, booster_type_col, self.default_percentage, self.retirement_strategy,
                                            self.automated_booster_type, self.automated_booster_experiment, self.auto_booster_id, self.shuffling_enable, self.retire_enable, self.user_segments, self.pokus_exp, master_uid)
        if rows:
            rows = rows[:-1]
            cur = self.__dao_mysql.add_hotels(rows)
        return uid

    def if_sequence_exists(self,seq_id):
        if self.algorithm == 'NEWHOTELS':
            return self.__dao_mysql.find_input_newhotel_sequence(seq_id,self.algorithm,self.domain)

        if self.algorithm == 'BOOSTERS' or self.algorithm == 'AUTO':
            return self.__dao_mysql.find_input_booster_sequence(seq_id,self.start_block,self.algorithm,self.check_in_dow, self.ap_bucket,
                                self.min_adults, self.max_adults, self.children, self.los_min, self.los_max,self.domain,self.min_check_in_date,self.max_check_in_date,self.is_personalized, self.booster_type, self.default_percentage)

    def addNewSequence(self):

        '''
        Validations:
        Different process for new hotels & booster modes
        '''

        self.log.info('Add Seq for Algo,Desc,User: {},{},{}'.format(self.algorithm, self.description, current_user.username))
        master_uid = uuid.uuid1()

        try:
            for seqId in self.source_seq_id :
                cur = self.if_sequence_exists(seqId)
                if len(cur.fetchall()) > 0:
                    self.log.error("Error: Input hotel sequence already exists")
                    return (False,"Error: Input hotel sequence already exists for SeqId" + seqId)

            cur = self.__dao_mysql.find_input_active_description(self.description)
            if len(cur.fetchall()) > 0 :
                self.log.error("Error: description already exists, choose diff. description")
                return (False, "Error: Input description already exists")

            brand = self.brand
            for seqId in self.source_seq_id:
                if len(self.source_seq_id) > 1 :
                    desc = self.description + "_" + str(seqId)
                else :
                    desc = self.description
                self.add_greenhorn_sequence(seqId,desc,brand,master_uid)

        except Exception:
            self.log.error("Error in hotel uploader: " + traceback.format_exc())
            return (False, "Error in hotel uploader")

        self.__dao_mysql.close()
        return (True, "Success")

    def deleteSequence(self):
        self.log.info('Delete Seq for Uid,User,Brand: {},{},{}'.format(self.uid, current_user.username, self.brand))
        self.delete_greenhorn_sequence()
        seqIds = self.source_seq_id.split(",")
        for seqId in seqIds:
            self.log.info('Triggering cache refresh for SeqId,Algo,Brand: {},{},{}'.format(seqId, self.algorithm, self.brand))
            self.triggerCacheRefresh(self.algorithm, seqId, self.brand)

        if self.deleteId:
            self.putDeleteIdInCache(self.deleteId)
        return (True, "Success")

    def putDeleteIdInCache(self, deleteId):
        esu = ExploreSetUpdater()
        esu.add_booster_delete_id(deleteId)

    def deleteSequenceStatus(self):
        self.log.info('Delete Seq Status for DeleteId: {}'.format(self.deleteId))
        esu = ExploreSetUpdater()
        status = esu.get_delete_status(self.deleteId)
        if status:
            return (True, status)
        else:
            return (False, "Status not found")

    # APPEND: Not supported for Automated Booster
    def appendSequence(self):
        self.log.info('Append Seq for Uid,User,Brand: {},{},{}'.format(self.uid, current_user.username, self.brand))
        htl_score = {h[0] : 0.0 for h in self.booster_list}
        self.append_greenhorn_hotels(current_user.username,htl_score, app.config['SOURCE_UPLOADER'])
        self.triggerCacheRefresh(self.algorithm, self.source_seq_id, self.brand)
        return (True, "Success")

    # REMOVE: Not supported for Automated Booster
    def removeSequence(self):
        self.log.info('Remove Seq for Uid,User,Brand: {},{},{}'.format(self.uid, current_user.username,self.brand))
        self.remove_greenhorn_hotels()
        self.triggerCacheRefresh(self.algorithm, self.source_seq_id, self.brand)
        return (True, "Success")

    def getData(self):
        cur = self.__dao_mysql.fetch_hotel_from_uid(self.uid)
        rows = cur.fetchall()
        boosted_hotels = [row for row in rows]
        boosted_hotels_header = ','.join(desc[0] for desc in cur.description)
        cur = self.__dao_mysql.fetch_sequence_from_uid(self.uid)
        rows = cur.fetchall()
        boosted_sequence = [row for row in rows]
        boosted_sequence_header = ','.join(desc[0] for desc in cur.description)
        return (True,boosted_hotels,boosted_sequence,boosted_hotels_header,boosted_sequence_header)

    def triggerCacheRefresh(self, algorithm, seqid, brand='MMT'):
        esu = ExploreSetUpdater()
        return esu.refresh_cache(algorithm, seqid, brand)

    def triggerCacheRefreshApi(self):
        self.triggerCacheRefresh(self.algorithm, self.seqId)

    def triggerCacheRefreshAerospikeApi(self):
        brands = ['MMT', 'GI']
        for brand in brands:
            self.log.info("Starting to refresh aerospike cache for brand {0}".format(brand))
            cur = self.__dao_mysql.fetch_sequence_from_seqid_algorithm(brand)
            sequences = cur.fetchall()
            for sequence in sequences:
                self.log.info("Aerospike cache refresh for seqid {0}, algorithm {1} and brand {2}".format(sequence[0],sequence[1],brand))
                self.triggerCacheRefresh(sequence[1], sequence[0], brand)
            self.log.info("Finished refreshing aerospike cache for brand {0}".format(brand))


    def isValueAbsent(self, booster, index):
        return len(booster) <= index or booster[index] is None or booster[index] is '' or booster[index].isspace()
