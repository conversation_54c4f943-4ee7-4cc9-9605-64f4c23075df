# Author: <PERSON><PERSON><PERSON>
# Date: 12 Mar 2018

# coding: utf-8

import json

import datetime
from dao.dao_mysql import DataAccessObjectMySQL as dao_mysql
from flask import request, current_app as app
from flask_login import current_user

class FetchSequence(object):

    __dao_mysql = None

    def __init__(self):
        app.config['LOG'].info("Initializing Fetch Sequence")
        self.__dao_mysql = dao_mysql()       

    def myconverter(self, o):
        if isinstance(o, datetime.datetime):
            return o.__str__()

    def fetch_sequence(self,mode):
        app.config['LOG'].info('fetch sequence for user : {}'.format(current_user.username))
        if mode == 'AUTO':
            cur = self.__dao_mysql.fetch_active_auto_sequences()
        else :
            temp = mode.split("-")
            algorithm = temp[0]
            domain = temp[1]
            cur = self.__dao_mysql.fetch_active_sequences(algorithm,domain)
        rows = cur.fetchall()
        boosters = [dict((cur.description[i][0], value) for i, value in enumerate(row)) for row in rows]

        # Combine child boosters into their respective master booster
        master_boosters = {}
        for booster in boosters:
            master_uid = booster.get('master_uid')
            if master_uid:
                if master_uid not in master_boosters:
                    master_boosters[master_uid] = booster
                    master_boosters[master_uid]['combined_uids'] = [booster['uid']]
                    master_boosters[master_uid]['combined_mm_uids'] = [booster['mm_uid']] if booster.get('mm_uid') else []
                    master_boosters[master_uid]['combined_seqids'] = [booster['seqid']]
                else:
                    master_boosters[master_uid]['combined_uids'].append(booster['uid'])
                    if booster.get('mm_uid'):
                        master_boosters[master_uid]['combined_mm_uids'].append(booster['mm_uid'])
                    master_boosters[master_uid]['combined_seqids'].append(booster['seqid'])
            else:
                master_boosters[booster['uid']] = booster

        # Update master boosters with combined fields
        for uid, booster in master_boosters.items():
            booster['uid'] = ','.join(booster.get('combined_uids', [booster['uid']]))
            booster['mm_uid'] = ','.join(booster.get('combined_mm_uids', []))
            booster['seqid'] = ','.join(booster.get('combined_seqids', [booster['seqid']]))
            # Remove temporary fields
            booster.pop('combined_uids', None)
            booster.pop('combined_mm_uids', None)
            booster.pop('combined_seqids', None)

        final_boosters = []
        for master_uid, booster in master_boosters.items():
            uid = []
            uid= uid + booster.get('uid').split(",")
            if booster.get('mm_uid') is not None:
                uid = uid + booster.get('mm_uid').split(",")
            booster['uid'] = uid
            if booster.get("booster_type") == 'P':
                booster["booster_type"] = "Percentage Based"
            elif booster.get("booster_type") == 'S':
                booster["booster_type"] = "Slot Based"
            final_boosters.append(booster)

        self.__dao_mysql.close()
        return json.dumps((final_boosters[0] if final_boosters else None) if False else final_boosters, default = self.myconverter)

