# dictionary of device index to device data i.e. device name, device table name
device_data = {
    0: {
        "name": "DESKTOP",
        "table_name": "hotel_sequence_b2c"
    },
    1: {
        "name": "IPHONE",
        "table_name": "hotel_sequence_iphone"
    },
    2: {
        "name": "ANDRO<PERSON>",
        "table_name": "hotel_sequence_android"
    }
}


def __get_device_index(experiment):
    index = int((experiment - 1) / 50)
    return index


def get_rank_table_and_column(experiment):
    rank_table = device_data[__get_device_index(experiment)]["table_name"]
    rank_column = "HSEQ{:03d}".format(50 if experiment % 50 == 0 else experiment % 50)
    return rank_table, rank_column


def get_device_name(experiment):
    device_name = device_data[__get_device_index(experiment)]["name"]
    return device_name
