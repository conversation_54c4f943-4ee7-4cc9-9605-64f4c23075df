# Author: <PERSON><PERSON><PERSON>
# Date: 14 Dec 2017

# coding: utf-8


from common.cache_manager import CacheManager

import json
from flask import request, current_app as app
import common.helpers as hp


class ExploreSetValidator(object):

    def __init__(self):
        self.log = hp.mdc_logger("ExploreSetValidator")
        self.log.info("Initializing Explore Set Validator")

        params = json.loads(request.data)

        # request nodes
        self.keys = list(params['keys'].split(','))


