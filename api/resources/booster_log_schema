{"fields": [{"name": "booster_id", "type": "string", "default": "null", "doc": "Booster id"}, {"name": "hsq_id", "type": ["null", "string"], "default": null, "doc": "HSQ id"}, {"name": "status", "type": "string", "default": "null", "doc": "status of booster creation"}, {"name": "error_code", "type": ["null", "string"], "default": null, "doc": "error caused in creation"}, {"name": "reason", "type": ["null", "string"], "default": null, "doc": "Underlying reason, like exception"}, {"name": "server_timestamp", "type": "long", "default": 0, "doc": "Record push datetime"}, {"name": "context", "type": {"name": "context", "doc": "Context of this record", "fields": [{"name": "template_id", "type": "string", "default": "null", "doc": "template"}, {"name": "server_timestamp", "type": "long", "default": 0, "doc": "timestamp"}, {"name": "topic_name", "type": "string", "default": "null", "doc": "kafka topic"}], "type": "record", "namespace": "com.mmt.logging.context"}, "default": {}}], "type": "record", "name": "hotels_ranking_booster_status"}