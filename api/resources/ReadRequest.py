# Author: <PERSON><PERSON><PERSON>
# Date: 24 Jan 2017

# coding: utf-8

from validator.CheckSettings import Settings

import json, requests
from flask import Flask, request, current_app as app
import common.helpers as hp

class ReadRequest(object):
    def __init__(self):
        self.log = hp.mdc_logger("ReadRequest")
        if request.data.decode() != "":
            self.params = json.loads(request.data.decode())
        else:
            self.params = request.form
            self.lob = request.form.get('lobs')
            self.appfest_booster = request.form.get('appfest_booster')

    def is_appfest_booster(self):
        return self.appfest_booster == 'on'

    def get_lob(self):
        return self.lob

    def read_request_add_bu(self):
        # request nodes
        self.algorithm = self.params["mode"]
        self.source_seq_id = self.params["source_seq_id_rdbtn"] or self.params["source_seq_id_txt"]
        self.action = self.params['action']

        #self.city_hotels = self.params['city_hotels']
        self.start_date = self.params['start_date']
        self.end_date = self.params['end_date']
        self.booster_type = self.params.get('booster_type')
        self.start_block = 0 if not self.params['start_block'] else int(self.params['start_block'])
        self.block_size = 0 if not self.params['block_size'] else int(self.params['block_size'])
        self.nslots =  0 if not self.params['nslots'] else int(self.params['nslots'])
        self.lob = self.params.get('lobs')
        self.appfest_booster = None if not hasattr(self, 'appfest_booster') else self.appfest_booster


        # validate if all request nodes are present
        cs = Settings(app.config['LOG'])
        return cs.check_settings_add_bu(self.algorithm, self.source_seq_id, self.start_date, self.end_date,
                                        self.block_size, self.nslots, app.config['LOG'], self.booster_type,
                                        self.appfest_booster, self.lob, self.start_block)

    def read_request_modify_bu(self):
        self.uid = self.params['uid']
        if not self.uid:
            self.log.error('uid can not be null !')
            return 1
        return 0

    def read_request_delete_bu(self):
        self.uid = self.params['uid']
        self.deleteId = self.params["deleteId"]
        if not self.uid:
            self.log.error('uid can not be null !')
            return 1
        return 0

    def read_request_cache_refresh(self):
        self.algorithm = self.params['algorithm']
        self.seqId = self.params['seqId']
        if not self.algorithm:
            self.log.error('algorithm is missing')
            return 1
        if not self.seqId:
            self.log.error('seqid is missing')
            return 1
        return 0

    def read_request_delete_status(self):
        self.deleteId = self.params['deleteId']
        if not self.deleteId:
            self.log.error('deleteId can not be null !')
            return 1
        return 0
