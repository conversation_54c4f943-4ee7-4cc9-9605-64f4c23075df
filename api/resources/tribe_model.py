from flask import current_app as app
import os
import pandas as pd
from math import radians, cos, sin, asin, sqrt,isnan
import re
import common.helpers as hp

class TribeModel(object):
    #hotel_data_col is the list of columns which will be used for calculating the similar hotels basis price
    def __init__(self, dataroot, hotel_data_csv, hotel_location_csv, number_of_hotels_limit):
        # init logger

        self.log = hp.mdc_logger("TribeModel")
        self.log.info("Server starting..")
        df_hotel_data = pd.read_csv(os.path.join(dataroot, hotel_data_csv))
        df_hotel_loc = pd.read_csv(os.path.join(dataroot, hotel_location_csv))
        df_hotel_data = df_hotel_data.merge(df_hotel_loc, on='hotel_id', how='inner')
        df_hotel_data['hotel_id'] = df_hotel_data['hotel_id'].astype(str)
        hotel_data_col = ['hotel_id', 'location_id', 'htl_lat', 'htl_lng', 'price']
        for column in df_hotel_data :
            if re.search("AP_\d+$",column) :
                hotel_data_col.append(column)
        #using only subset of columns which will be required for the calculation of similar hotels
        df_hotel_data = df_hotel_data[hotel_data_col]
        #hotel_data_index_dict is a dictionary where hotel_data_col is used as keys and the index of hotel_data_col is used as values
        #hotel_data_col : ['AP_1','AP_2' ,....]
        #hotel_data_index_dict : {'AP_1' : 0, 'AP_2' : 1 , ....}
        self.hotel_data_index_dict = {}
        for i in range(len(hotel_data_col)) :
            self.hotel_data_index_dict[hotel_data_col[i]] = i

        self.ct2h = {}
        for index, row in df_hotel_data.iterrows():
            if row['location_id'] not in self.ct2h:
                self.ct2h[row['location_id']] = {}
            self.ct2h[row['location_id']][row['hotel_id']] = list(row)

        self.number_of_hotels_limit = int(number_of_hotels_limit)
        self.log.info("Data loaded successfully. Initialization done.")

    def healthy(self):
        if len(self.hotel_data_index_dict) > 0 and len(self.ct2h) > 0 :
            return True
        return False

    def identify_tribe_hotels(self, hotel_id, city,ap):

        city_hotel_dict = self.ct2h.get(city, {})
        cur_hotel_attr = city_hotel_dict.get(str(hotel_id), None)
        resp = {'servedSeq': 'TribeNotFound', 'hotelList': []}
        price_index = -1
        if cur_hotel_attr is None:
            return {'servedSeq': 'Tribe', 'hotelList': []}
        #finding the price_index
        # either the AP price or the mean price for pivot hotel would be used for finding the similar hotels
        if "AP_" + str(ap) in self.hotel_data_index_dict and not isnan(cur_hotel_attr[self.get_attr_index("AP_" + str(ap))]):
            price_index = self.get_attr_index("AP_" + str(ap))
        elif not isnan(cur_hotel_attr[self.get_attr_index("price")]):
            price_index = self.get_attr_index("price")
        hotelList = []
        if cur_hotel_attr is not None and price_index > 0:
            lat_index = self.get_attr_index('htl_lat')
            lng_index = self.get_attr_index('htl_lng')
            lat1 = cur_hotel_attr[lat_index]
            lon1 = cur_hotel_attr[lng_index]
            price = cur_hotel_attr[price_index]
            min_price, max_price = (price * 0.75, price * 1.25)
            # df_filtered = city_hotel_df[(city_hotel_df['price'] > min_price) & (city_hotel_df['price'] < max_price)]
            finalList = []
            for hotelId in city_hotel_dict:
                htl_attr = city_hotel_dict[hotelId]
                lat2 = htl_attr[lat_index]
                lng2 = htl_attr[lng_index]
                if lat2 and lng2 and htl_attr[price_index] > min_price and htl_attr[price_index] < max_price :
                    finalList.append((hotelId, self.haversine(lon1, lat1, lng2, lat2)))
            finalList.sort(key=lambda x: x[1])
            hotelList = [{'hotelId': str(x[0]), 'score': 100} for x in finalList[:self.number_of_hotels_limit]]
            resp = {'servedSeq': 'Tribe', 'hotelList': hotelList}

        resp['hotelList'] = hotelList

        return resp


    def get_attr_index(self,htl_attr):
        return self.hotel_data_index_dict.get(htl_attr)

    def haversine(self, lon1, lat1, lon2, lat2):
        """
        Calculate the great circle distance between two points
        on the earth (specified in decimal degrees)
        """
        # convert decimal degrees to radians
        lon1, lat1, lon2, lat2 = map(radians, [lon1, lat1, lon2, lat2])

        # haversine formula
        dlon = lon2 - lon1
        dlat = lat2 - lat1
        a = sin(dlat / 2) ** 2 + cos(lat1) * cos(lat2) * sin(dlon / 2) ** 2
        c = 2 * asin(sqrt(a))
        r = 6371  # Radius of earth in kilometers. Use 3956 for miles
        return c * r