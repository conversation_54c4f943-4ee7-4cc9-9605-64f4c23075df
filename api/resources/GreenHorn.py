# Author: <PERSON><PERSON><PERSON>
# Date: 19 Dec 2017

# coding: utf-8


from datetime import timedelta, datetime

import json
from flask import request, current_app as app
import itertools
from common.Utils import DateUtils
from common.Utils import QueryFeaturizer
from common.upsell_constants import UpsellConstants
import common.helpers as hp
import time

import copy
#US stands for User Segment
PERCENT_USER_SEGMENT_BOOSTER = 'P_US__________1_0'


class GreenHorn(object):
    log = hp.mdc_logger("GreenHorn")

    def __init__(self):

        self.request = json.loads(request.data)
        # request nodes
        self.algorithm = self.request["mode"]
        self.source_seq_id = self.request["source_seq_id"]
        self.city = self.request['city']
        self.exp_data = self.request.get('exp_data',{})
        self.user_segments = self.request.get('user_segments', [])


    def __update_percent_booster_data(self, percent_booster_data, current_specificity, current_hotel_list):
        for current_hotel in current_hotel_list:
            # current_hotel contains only one item of form {hotel_id: [score, display_sequence, boost_percentage]}
            for current_id, current_data in current_hotel.items():
                current_boost_percentage = current_data[2]
                data = percent_booster_data[current_id] if current_id in percent_booster_data else None
                specificity = data["specificity"] if data else 0
                boost_percentage = data["boostPercentage"] if data else 0
                # If H1 is present in (UID1, BOOSTERS, HSQXXX) as well as in (UID2, BOOSTERS, HSQXXX), then
                # H1's boost percentage will be picked from UID having more specificity. If specificity is same, then
                # H1's boost percentage will be picked from H1 having more boost percentage (can be +ve as well as -ve).
                if current_id not in percent_booster_data or current_specificity > specificity or \
                        (current_specificity == specificity and current_boost_percentage > boost_percentage):
                    percent_booster_data[current_id] = {
                        "specificity": current_specificity,
                        "boostPercentage": current_boost_percentage
                    }

    def __get_percent_response_list(self, percent_booster_data, slot_booster_hotels, uid,auto_booster_id):
        percent_response_list = []
        if not percent_booster_data:
            return percent_response_list

        [percent_booster_data.pop(hotel) for hotel in slot_booster_hotels if hotel in percent_booster_data]
        if not percent_booster_data:
            return percent_response_list

        hotels = []
        for hotel_id, hotel_data in percent_booster_data.items():
            hotel = {
                "hotelId": hotel_id,
                "boostPercentage": hotel_data["boostPercentage"]
            }
            hotels.append(hotel)

        percent_response = {
            "booster_type": "PERCENT",
            "uid": uid,
            "auto_booster_id": auto_booster_id,
            "hotels": hotels
        }
        percent_response_list.append(percent_response)
        return percent_response_list

    '''
    Rearranges the starting block of non appfest booster relative to the appfest booster Only if the staring block of non appfest booster
    is lower than the appfest booster. If there is no  appfest booster the starting block of non appfest booster will remain same.
    Example : non appfest booster starting block : 7
              appfest booster starting block : 8
              non appfest booster starting block will shift to 8 + 7 = 15
    '''

    def rearrange_block(self, slot_booster_data, start_block, appfest_start_block, specificty, booster):
        if start_block <= appfest_start_block and specificty < 20:
            booster['start_block'] = start_block + appfest_start_block
            slot_booster_data.setdefault(start_block + appfest_start_block, {}).setdefault(specificty, []).append(booster)
        else:
            slot_booster_data.setdefault(start_block, {}).setdefault(specificty, []).append(booster)

    def __rearrange_slot_booster(self, slot_booster_data, max_starting_block):
        if max_starting_block != -1:
            slot_booster_data_copy = copy.deepcopy(slot_booster_data)
            slot_booster_data = dict()
            [self.rearrange_block(slot_booster_data, start_block, max_starting_block, specificity, booster.copy())
             for (start_block, specificityColl) in slot_booster_data_copy.items()
             for (specificity, booster_list) in specificityColl.items() for booster in booster_list]
        return slot_booster_data

    def __update_slot_booster_data(self, hotel_data_list, slot_booster_data, max_starting_block, bucket_id, slot_booster,
                                   specificity, is_personalized):
        bucket_id += 1
        personalization_bckt = "B" + str(bucket_id) + ("PR" if is_personalized else "NP")
        htl_to_score = dict([(key, d[key]) for d in hotel_data_list for key in d])
        hotelids = list(htl_to_score.keys())
        hotel_scores = [(htl, htl_to_score[htl]) for htl in hotelids]
        slot_booster["hotels"] = [{"hotelId": htl, "slotBucket": personalization_bckt} for htl, score in
                                  sorted(hotel_scores, key=lambda x: x[1][1])]
        if specificity >= 20:
            max_starting_block = max(slot_booster["start_block"], max_starting_block)
        if specificity > 0:
            slot_booster_data.setdefault(slot_booster["start_block"], {}).setdefault(specificity, []).append(slot_booster)
        else:
            # decrementing bucket_id by one since the current block will not be sued in result
            bucket_id -= 1
        return max_starting_block, bucket_id

    def __initialize_slot_booster(self, k_list, index, is_automated, uid, auto_booster_id):
        return {
            "booster_type": "SLOT",
            "is_automated": is_automated,
            "start_block": int(k_list[0 + index]),
            "block_size": int(k_list[1 + index]),
            "no_of_slots": int(k_list[2 + index]),
            "max_hotel": int(k_list[3 + index]),
            "uid": uid,
            "auto_booster_id": auto_booster_id
        }

    '''
    Returns how specific is the boosted sequence with respect to the incoming query. None or empty values in the boosted sequence 
    are considered as ignorable. If any of the conditions fail with incoming request
    specificity is returned as 0. Any other condition satisfied by the request adds to the specificity
    '''

    def get_specificity_for_booster(self, check_in_dow, ap_bucket, min_adults, max_adults, children, min_los, max_los,
                                    priority, min_check_in_date, max_check_in_date):
        specificity = 1
        if check_in_dow is not None:
            isWeekendVisit = QueryFeaturizer.get_is_weekend_visit(DateUtils.get_dow(self.request['checkin']),
                                                                  DateUtils.get_dow(self.request['checkout']))
            if isWeekendVisit ^ (check_in_dow == 'weekend'):
                return 0
            specificity += 1
        if ap_bucket is not None:
            ap = DateUtils.Date2MinusDate1Days(None, self.request['checkin'])
            min_ap, max_ap = map(int, ap_bucket.split('-'))
            if min_ap > ap or max_ap < ap:
                return 0
            specificity += 1
        if min_adults is not None:
            adults = 0
            for rsp in self.request['roomStayParams']:
                adults = adults + next(
                    ((int)(gc['count']) for gc in rsp['guestCounts'] if (int)(gc['ageQualifyingCode']) == 10), 0)
            if adults < int(min_adults) or adults > int(max_adults or '3000'):
                return 0
            specificity += 1
        if children is not None:
            childCount = 0
            for rsp in self.request['roomStayParams']:
                childCount = childCount + next(
                    ((int)(gc['count']) for gc in rsp['guestCounts'] if (int)(gc['ageQualifyingCode']) == 8), 0)
            if (children == '1') ^ (childCount > 0):
                return 0
            specificity += 1
        if min_los is not None:
            los = DateUtils.Date2MinusDate1Days(self.request['checkin'], self.request['checkout'])
            if los < int(min_los) or los > int(max_los or '3000'):
                return 0
            specificity += 1
        if min_check_in_date is not None:
            min_date = max(datetime.strptime(min_check_in_date, "%Y-%m-%d").date(),
                           datetime.strptime(self.request['checkin'], "%Y-%m-%d").date())
            max_date = min(datetime.strptime(max_check_in_date, "%Y-%m-%d").date(),
                           datetime.strptime(self.request['checkout'], "%Y-%m-%d").date() - timedelta(days=1))
            if DateUtils.Date2MinusDate1Days(min_date, max_date) >= 0:
                specificity += 1
            else:
                return 0

        return specificity * priority

    '''
    This function takes in a dictionary in the following format
                {
                    startBlock1 : {
                        specificity1 : [sequenceArr],
                        specificity2 : [sequenceArr]
                    },
                    startBlock2 : {
                        specificity3 : [sequenceArr],
                        specificity4 : [sequenceArr]
                    }
                }
    First it interleaves all the sequences having the same starting block and same specificity.
    After that it starts appending sequence of low specificity into sequence of high specificity
    The final result is a set of sequences with diff. starting blocks
    '''

    def get_list_post_conflict_resolution(self, specificityDict):
        # resolving conflicts with same starting block and same specificity
        firstResolution = [(sp, self.interleaver(bl)) for (sb, specificityColl) in specificityDict.items() for (sp, bl)
                           in specificityColl.items()]
        # sorting the dictionary with starting block and specificity in reverse order
        firstResolution.sort(key=lambda x: (x[1]['start_block'], x[0]), reverse=True)
        startBlock = -1
        resolvedBoosters = []
        for sp, booster in firstResolution:
            if startBlock != booster['start_block']:
                booster['hotels'] = booster['hotels'][0:booster['max_hotel']]
                resolvedBoosters.append(booster)
                startBlock = booster['start_block']
            else:
                lastBooster = resolvedBoosters[-1]
                # getting all the lastBooster Hotel as a set in last_booster_hotels
                last_booster_hotels = {hotel['hotelId'] for hotel in lastBooster['hotels'] if 'hotelId' in hotel}
                [lastBooster['hotels'].append(htl) for htl in booster['hotels'][0:booster['max_hotel']] if
                 htl["hotelId"] not in last_booster_hotels]
                lastBooster['max_hotel'] = len(lastBooster['hotels'])
        return resolvedBoosters

    def interleaver(self, boosterList):
        if len(boosterList) == 1:
            return boosterList[0]
        hotels = []
        prev_booster_hotels = set()
        [(hotels.append(val), prev_booster_hotels.add(val['hotelId'])) for tup in
         itertools.zip_longest(*[b['hotels'][0:b['max_hotel']] for b in boosterList]) for
         val in tup if val is not None and val['hotelId'] not in prev_booster_hotels]
        blockSize = max([b['block_size'] for b in boosterList])
        numOfSlots = max([b['no_of_slots'] for b in boosterList])
        return {'block_size': blockSize, 'no_of_slots': numOfSlots, 'start_block': boosterList[0]['start_block'],
                'hotels': hotels, 'max_hotel': len(hotels)}

