from flask import current_app as app
import requests
import json
from handlers.hotel_attributes_handler import HotelAttributesHandler


class CommonHandler(object):

    def __init__(self, handler_name):
        app.config['LOG'].info(handler_name + " :: Initializing handler")
        api = handler_name + '_API_'
        self.status_code = [200]
        self.url = app.config[api + 'URL']
        self.headers = app.config[api + 'HEADERS']
        self.timeout = app.config[api + 'TIMEOUT']
        if handler_name == 'HOTEL_ATTRIBUTES':
            self.handler = HotelAttributesHandler(handler_name)
        app.config['LOG'].info(handler_name + " :: Initialized handler")

    def get_response(self, parameters):
        try:
            request_list = self.handler.create_request_list(parameters)
            api_response_list = [self.get_api_response(request) for request in request_list]
            response = self.handler.populate_response(api_response_list)
        except Exception as e:
            raise Exception(e, "Exception while getting response")
        return response

    # Currently supports POST request.
    # If required, update as per requirements.
    def get_api_response(self, request):
        try:
            response_json = requests.post(url=self.url, data=json.dumps(request), headers=self.headers, timeout=self.timeout)
            if response_json.status_code in self.status_code:
                response = json.loads(response_json.text)
            else:
                raise Exception("Status code = " + str(response_json.status_code) + " while getting api response")
        except Exception as e:
            raise Exception(e, "Exception while getting api response")
        return response
