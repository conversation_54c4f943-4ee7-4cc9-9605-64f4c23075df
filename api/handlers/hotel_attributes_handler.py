from flask import current_app as app

import statistics


class HotelAttributes<PERSON><PERSON><PERSON>(object):

    def __init__(self, handler_name):
        self.log_prefix = handler_name + ' :: '
        self.api_limit = app.config['HOTEL_ATTRIBUTES_API_LIMIT']

    def create_request_list(self, location_list):
        app.config['LOG'].info(self.log_prefix + "Creating request")
        request_list = [location_list[i:i + self.api_limit] for i in range(0, len(location_list), self.api_limit)]
        app.config['LOG'].info(self.log_prefix + "Created request")
        return request_list

    def populate_response(self, api_response_list):
        app.config['LOG'].info(self.log_prefix + "Populating response")
        final_hotel_to_attributes = dict()
        location_to_median_qs = dict()
        try:
            for api_response in api_response_list:
                for location, hotel_attributes in api_response.items():
                    qs_list = list()
                    for hotel, attributes in hotel_attributes.items():
                        if attributes and attributes['qs'] != 0.0:
                            qs_list.append(attributes['qs'])
                    if qs_list:
                        location_to_median_qs[location] = statistics.median(qs_list)
            for api_response in api_response_list:
                for location, hotel_attributes in api_response.items():
                    for hotel, attributes in hotel_attributes.items():
                        final_hotel_to_attributes[hotel] = attributes
        except Exception as e:
            raise Exception(e, "Exception in populating response " + self.log_prefix)
        app.config['LOG'].info(self.log_prefix + "Populated response")
        return final_hotel_to_attributes, location_to_median_qs
