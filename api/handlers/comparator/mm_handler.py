import json, requests
import time
import os
import common.helpers as hp


class ComparatorMMHandler(object):
    mm_json_path = "/../../config/mm_distance.json"
    log = hp.mdc_logger("ComparatorMMHandler")

    def __init__(self, param, hotels, distanceInMeters):
        self.param = param
        self.hotels = hotels
        self.distanceInMeters = distanceInMeters

    def get_result_from_mm(self):
        try:
            result = {}
            if self.hotels:
                config_file = os.path.dirname(os.path.realpath(__file__)) + ComparatorMMHandler.mm_json_path
                with open(config_file, 'r') as f:
                    request = json.load(f)

                request['correlationKey'] = self.param['correlationKey']
                request['cityCode'] = self.param['cityCode']
                request['countryCode'] = self.param['countryCode']
                request['pivotHotelId'] = self.param['hotel']['hotelId']
                request['selectedTags'] = self.param.get('selectedTags')
                request['latLng'] = self.param.get('latLng')
                request['newApp'] = self.param.get('newApp',False)
                request['hotelIds'] = [hotel['hotelId'] for hotel in self.hotels]
                request['cutOffDistanceInMeters'] = self.distanceInMeters
                headers = {
                    'Content-type': 'application/json',
                    'region': self.param.get('region', 'in'),
                    'language': self.param.get('language', 'eng')
                }
                result = hp.get_json_data_from_post_call(ComparatorMMHandler.log, ComparatorMMHandler.cfg,
                                                         'mmComparatorApiUrl', headers, request, "MM Comparator")
        except Exception as e:
            ComparatorMMHandler.log.error('Exception occurred while getting response from matchmaker for city {}: {}'
                                          .format(self.param['cityCode'], e))
        return result
