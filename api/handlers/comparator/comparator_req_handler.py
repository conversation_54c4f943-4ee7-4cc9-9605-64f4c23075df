import json

from flask import request, current_app as app
import os
import yaml
import time
import common.helpers as hp

from .dr_handler import Comparator<PERSON><PERSON>andler
from .hotstore_handler import ComparatorHotStoreHandler
from .tribe_handler import ComparatorTribeHandler
from .mm_handler import ComparatorMMHandler
import asyncio
import sys


class ComparatorHandler(object):
    config_file = os.path.dirname(os.path.realpath(__file__)) + "/../../config/comparator_config.yaml"
    with open(config_file, 'r') as cf:
        cfg = yaml.safe_load(cf)
    log = hp.mdc_logger("ComparatorHandler")
    ComparatorDRHandler.cfg = ComparatorHotStoreHandler.cfg = ComparatorTribeHandler.cfg = ComparatorMMHandler.cfg = cfg
    header_region = 'region'
    header_language = 'language'
    default_region = 'in'
    default_language = 'eng'
    star_rating_key = 'sr'
    review_rating_key = 'rr'

    def update_params(self, params, headers, header_name, default_header_value):
        if not headers or header_name not in headers:
            self.log.error("Missing request header: {}".format(header_name))
            params[header_name] = default_header_value
        else:
            params[header_name] = headers[header_name] \
                if headers[header_name] \
                else default_header_value

    @hp.mdc_uuid
    def post(self):
        params = json.loads(request.data)
        headers = request.headers
        self.log.info("Request: {}".format(params))
        self.update_params(params, headers, self.header_region, self.default_region)
        self.update_params(params, headers, self.header_language, self.default_language)
        self.log.info("Region: {}, Language: {}"
                      .format(params.get(self.header_region), params.get(self.header_language)))
        t = time.time()
        try:
            resp = self.get_comparator_hotel(params)
            # self.log.info("total time taken for comaparator api" + str(time.time() - t))
            if not resp:
                return self.get_error_response({"code": "2000", "message": "No hotel found"})
            return json.dumps(resp)

        except Exception as e:
            self.log.error("Exception occurred for city {}: {}".format(params['cityCode'], e))
            return self.get_error_response({"code": "5000", "message": "something went wrong"})

    @hp.mdc_uuid
    def postV2(self):
        params = json.loads(request.data)
        self.log.info("request is {}".format(params))
        t = time.time()
        try:
            resp = self.get_comparator_hotels_without_ordering(params)
            # self.log.info("total time taken for comaparator v2 api" + str(time.time() - t))
            if not resp:
                return self.get_error_response({"code": "2000", "message": "No hotel found"})
            return json.dumps(resp)

        except Exception as e:
            self.log.error("Exception occurred for city {}: {}".format(params['cityCode'], e))
            return self.get_error_response({"code": "5000", "message": "something went wrong"})

    @hp.mdc_uuid
    def postV3(self):
        params = json.loads(request.data)
        self.log.info("request is {}".format(params))
        t = time.time()
        try:
            resp = self.get_comparator_hotels_without_ordering(params,False)
            json_response = self.get_error_response({"code": "2000", "message": "No hotel found"})
            if resp and resp.get('hotel') and len(resp['hotel']) > 0:
                mm_result = ComparatorMMHandler(params, resp['hotel'], ComparatorMMHandler.pms_props[
                    'cut_off_distance_in_meters_for_go_stays']).get_result_from_mm()
                recomm_hotel = self.filter_hotels_by_attributes(mm_result, params)
                if len(recomm_hotel) > 0:
                    resp['hotel'] = recomm_hotel
                    json_response = json.dumps(resp)
            return json_response

        except Exception as e:
            self.log.exception("Exception occurred for city {}".format(params['cityCode']))
            return self.get_error_response({"code": "5000", "message": "something went wrong"})

    def get_htl_attributes(self,city_code):
        url = self.cfg[self.cfg['Env']]['htlAtributeUrl']+city_code
        return hp.get_data_for_url(url,self.log)

    '''Filter similar hotels for GoStays basis following criteria:
    1. Filter hotels with non-zero star and review rating 
    2. Filter hotels with star and review rating less than equal to star and review rating for GoStay (i.e. pivot) hotel
    '''
    def filter_hotels_by_attributes(self, mm_result, params):
        recomm_hotel = []
        if mm_result and mm_result.get('hotel') and len(mm_result['hotel']) > 0:
            pivot_hotel_id = params['hotel']['hotelId']
            hotel_attributes_dict = self.get_htl_attributes(params['cityCode'])
            if not hotel_attributes_dict:
                self.log.info("No hotel attributes")
                return recomm_hotel

            pivot_star_rating = self.get_rating(hotel_attributes_dict, pivot_hotel_id, self.star_rating_key)
            pivot_review_rating = self.get_rating(hotel_attributes_dict, pivot_hotel_id, self.review_rating_key)
            self.log.info('Star, review rating for pivot hotel: {}, {}'.format(pivot_star_rating, pivot_review_rating))
            if pivot_star_rating == 0.0 or pivot_review_rating == 0.0:
                return recomm_hotel

            for hotel in mm_result['hotel']:
                hotel_id = hotel['hotelId']
                if pivot_hotel_id == hotel_id:
                    continue
                star_rating = self.get_rating(hotel_attributes_dict, hotel_id, self.star_rating_key)
                review_rating = self.get_rating(hotel_attributes_dict, hotel_id, self.review_rating_key)
                self.log.info('Star, review rating for {}: {}, {}'.format(hotel_id, star_rating, review_rating))
                if star_rating != 0.0 and star_rating <= pivot_star_rating \
                        and review_rating != 0.0 and review_rating <= pivot_review_rating:
                    recomm_hotel.append({"hotelId": hotel["hotelId"]})
        return recomm_hotel

    def get_comparator_hotels_without_ordering(self, param,call_recency=True):

        hot_store = ComparatorHotStoreHandler(param)
        recency = ComparatorDRHandler(param)if call_recency else None

        t = time.time()
        tribe = ComparatorTribeHandler(param, ComparatorHandler.tribe_model)
        tribe.get_tribe_response()
        asyncio.set_event_loop(asyncio.new_event_loop())
        start_time = asyncio.get_event_loop().time()
        loop = asyncio.get_event_loop()
        future_tribe = asyncio.Future()

        loop.run_until_complete(self.get_all_data( hot_store, recency, param))
        self.log.info("Total time {}".format(asyncio.get_event_loop().time() - start_time))
        loop.close()

        # self.log.info("time taken for get all data{}".format(time.time() - t))
        result = None
        comparator_response = {}

        result = hot_store.get_filtered_result_from_hot_store(tribe.tribe_resp)

        if not result and recency:
            result = hot_store.get_filtered_result_from_hot_store(recency.recency_resp)
            recencyCutOff = ComparatorHandler.cfg[ComparatorHandler.cfg['Env']]['v2RecencyCutOff']
            self.log.info("No hotels from tribe or hot store. Returning recency result")
            if len(result) > recencyCutOff:
                result = result[:recencyCutOff]
            comparator_response = {'hotel': result}
        else:
            comparator_response = {'hotel': result}
        return comparator_response

    def get_similar_hotels_manual(self, param):
        # self.log.info(
        #     "Comparing Manual Hotels {} Added by user with pivot Hotel {} ".format(param['comparatorHotelList'],
        #                                                                            param['hotel']['hotelId']))
        compare_hotels = [{"hotelId": x} for x in param.get('comparatorHotelList')]
        mm_result = ComparatorMMHandler(param, compare_hotels, 60000000).get_result_from_mm()
        recomm_hotel = []
        for hotel in mm_result['hotel']:
            if param['hotel']['hotelId'] == hotel['hotelId']:
                continue
            rtb = hotel["reasonToBelieve"]
            if len(rtb) > 0:
                rtb[0]['type'] = "Distance"
            recomm_hotel.append(
                {"hotelId": hotel["hotelId"], "reasonToBelieve": rtb,
                 "crossSellTag": hotel.get("crossSellTag", None)})
        self.log.info("Returned hotels {}".format(recomm_hotel))
        return {'hotel': recomm_hotel}

    def get_similar_hotels_dom(self, param):
        is_corporate_request = param.get('profileType') and param['profileType'].upper() == 'BUSINESS'
        tribe = ComparatorTribeHandler(param, ComparatorHandler.tribe_model)
        param['is_filter_detail_required'] = True if not is_corporate_request else False
        hot_store = ComparatorHotStoreHandler(param)
        recency = ComparatorDRHandler(param)
        tribe.get_tribe_response()
        # self.log.info(
        #     "Premier ComparatorDRHandler pmsProps: {}, ComparatorHotStoreHandler pmsProps {}, ComparatorMMHandler pmsProps {}".format(
        #         ComparatorDRHandler.pms_props, ComparatorHotStoreHandler.pms_props, ComparatorMMHandler.pms_props))
        t = time.time()

        asyncio.set_event_loop(asyncio.new_event_loop())
        start_time = asyncio.get_event_loop().time()
        loop = asyncio.get_event_loop()
        future_tribe = asyncio.Future()

        loop.run_until_complete(self.get_all_data(hot_store, recency, param))
        self.log.info("Total time {}".format(asyncio.get_event_loop().time() - start_time))
        loop.close()

        # self.log.info("time taken for get all data{}".format(time.time() - t))
        result = None
        is_sorting_required = True
        # self.log.info(
        #     "ComparatorDRHandler pmsProps: {}, ComparatorHotStoreHandler pmsProps {}, ComparatorMMHandler pmsProps {}".format(
        #         ComparatorDRHandler.pms_props, ComparatorHotStoreHandler.pms_props, ComparatorMMHandler.pms_props))
        result = hot_store.get_filtered_result_from_hot_store(tribe.tribe_resp)
        if not result:
            self.log.info("No hotels from tribe or hot store. Returning recency result")
            result = hot_store.get_filtered_result_from_hot_store(recency.recency_resp)
            is_sorting_required = False
        comparator_htls = recency.sort_based_on_RI(result, is_sorting_required, is_corporate_request)
        if comparator_htls and len(comparator_htls) > 0:
            comparator_htls = self.prioritize_mmt_assured(comparator_htls, hot_store, param) \
                if not is_corporate_request else self.sort_for_corporate(hot_store, comparator_htls)
        return comparator_htls

    def get_comparator_hotel(self, param):
        is_manual_hotel_request = param.get('comparatorHotelList')
        if is_manual_hotel_request:
            return self.get_similar_hotels_manual(param)

        is_intl_request = param.get('countryCode') and param['countryCode'].upper() != 'IN'
        comparator_htls = self.get_similar_hotels_intl(param) if is_intl_request else self.get_similar_hotels_dom(param)
        mm_result = ComparatorMMHandler(param, comparator_htls, ComparatorMMHandler.pms_props['cut_off_distance_in_meters']).get_result_from_mm()
        recomm_hotel = []
        comparator_response = {}
        if len(mm_result.get('hotel', [])) > 0:
            mm_hotels_dict = dict()
            for hotel in mm_result['hotel']:
                mm_hotels_dict[hotel['hotelId']] = hotel
            for res in comparator_htls:
                if len(recomm_hotel) < int(ComparatorHandler.pms_props['comparator_hotel_threshold']):
                    if mm_hotels_dict.get(res["hotelId"], None):
                        rtb = mm_hotels_dict[res["hotelId"]]["reasonToBelieve"]
                        if len(rtb) > 0:
                            rtb[0]['type'] = "Distance"
                        recomm_hotel.append(
                            {"hotelId": res["hotelId"], "reasonToBelieve": rtb,
                             "crossSellTag": res.get("crossSellTag", None)})
                else:
                    break
            comparator_response = {'hotel': recomm_hotel}
        self.log.info("Returned hotels {}".format(len(recomm_hotel)))
        return comparator_response

    @staticmethod
    def get_error_response(error):
        params = {"hotel": [], "error": [error]}
        return json.dumps(params)

    async def get_all_data(self, hot_store, recency, param):
        # A list to hold our things to do via async
        pass

    def get_similar_hotels_intl(self, param):
        log_identifier = 'Intl_Comparator'
        intl_handler = ComparatorDRHandler(param)
        comparator_cfg = ComparatorHandler.cfg[ComparatorHandler.cfg['Env']]
        request = intl_handler.get_request(comparator_cfg['intlComparatorAlgoId'])
        try:
            response = hp.get_json_data_from_post_call(ComparatorDRHandler.log, ComparatorDRHandler.cfg,
                                                 'intlComparatorApiUrl', {'Content-type': 'application/json'},
                                                 request, log_identifier,comparator_cfg['intlComparatorApiTimeout'])
            result = []
            if response.get('responses'):
                result = response.get('responses')[0].get('output_hotels',[])
                result = [{'hotelId':x} for x in result]
            self.log.info("Hotels returned from {} is {}".format(log_identifier,len(result)))
            result = result[:int(ComparatorDRHandler.pms_props.get('filtered_hotel_threshold', '20'))]
        except Exception as e:
            result = []
            self.log.error("Exception occured while fetching data from:{} Error:{}".format(log_identifier, e))
        return result

    # In case of corporate, bring corporate hotels to the top after pivot hotel
    def sort_for_corporate(self, hot_store, hotels):
        try:
            sorted_hotels = list()
            non_mybiz_assured_hotels = list()
            mybiz_assured_hotel_ids = hot_store.get_mybiz_assured_hotel_ids(hotels)

            # pivot hotel - hotels[0]
            sorted_hotels.append(hotels[0])
            for hotel in hotels[1:]:
                if hotel['hotelId'] in mybiz_assured_hotel_ids:
                    sorted_hotels.append(hotel)
                else:
                    non_mybiz_assured_hotels.append(hotel)
            sorted_hotels.extend(non_mybiz_assured_hotels)

        except Exception as e:
            sorted_hotels = hotels
            self.log.error("Exception while sorting for corporate {}".format(e))

        sorted_hotels = sorted_hotels[:int(ComparatorDRHandler.pms_props.get('filtered_hotel_threshold', '20'))]
        return sorted_hotels


    # determines that we shouldn't call DR(recency) or HotStore (GoStays) in case newApp is true and locType is null or empty , else we should
    def call_downstream(self, params):
        if params.get('newApp') and not params.get('locType'):
            return False
        return True

    def prioritize_mmt_assured(self, response, hot_store, param):
        enable_mmt_assured_flow = (ComparatorMMHandler.pms_props['enable_mmt_assured_flow'].lower() == 'true')
        if not enable_mmt_assured_flow:
            return response
        try:
            hotel_rank_dict = dict()
            rank = 0
            for hotel in response:
                rank = rank + 1
                hotel_rank_dict[hotel['hotelId']] = rank
            pivot_hotel = next(iter(hotel_rank_dict))
            mmta_hotel_set = hot_store.get_mmta_hotel_rank_dict(hotel_rank_dict.keys())
            # If number of MMT Assured hotels in response is already greater than equal to threshold,
            # no Non MMT Assured hotel is to be replaced with MMT Assured hotel
            threshold_property = 'mmt_assured_threshold' if pivot_hotel in mmta_hotel_set else \
                'non_mmt_assured_threshold'
            mmta_threshold = int(ComparatorMMHandler.pms_props[threshold_property])
            if len(mmta_hotel_set) >= mmta_threshold:
                self.log.info("No replacement of non-mmta > mmta")
                prioritized_response = response
            else:
                nonmmta_mmta_hotels_dict = self.get_nonmmta_mmta_hotels_dict(param.get('cityCode'))
                prioritized_response = self.get_prioritized_response(hotel_rank_dict, mmta_hotel_set,
                                                                     nonmmta_mmta_hotels_dict, param) \
                    if nonmmta_mmta_hotels_dict else response
        except Exception as e:
            prioritized_response = response
            self.log.error("Exception while prioritizing for personal {}".format(e))
        return prioritized_response

    def get_prioritized_response(self, hotel_rank_dict, mmta_hotel_set, nonmmta_mmta_hotels_dict, param):
        prioritized_response = list()
        final_mmta_hotel_set = set()
        pivot_hotel = next(iter(hotel_rank_dict))
        # replace_nonmmta_count represents the number of Non MMT Assured hotels to be replaced with MMT Assured hotels
        count_property = 'mmt_assured_count' if pivot_hotel in mmta_hotel_set else 'non_mmt_assured_count'
        replace_nonmmta_count = int(ComparatorMMHandler.pms_props[count_property])
        sold_out_hotels = param.get('soldHotelList')
        sold_out_hotel_set = set(sold_out_hotels) if sold_out_hotels else set()
        prioritized_response.append({'hotelId': pivot_hotel})
        for hotel in hotel_rank_dict.keys():
            if hotel == pivot_hotel or hotel in final_mmta_hotel_set:
                continue
            prioritized_hotel = hotel
            # MMT Assured Hotel
            if hotel in mmta_hotel_set:
                final_mmta_hotel_set.add(prioritized_hotel)
            # Non MMT Assured Hotel
            elif replace_nonmmta_count > 0:
                replaced_mmta_hotel = self.get_replaced_mmta_hotel(hotel, hotel_rank_dict, nonmmta_mmta_hotels_dict,
                                                                   sold_out_hotel_set)
                if replaced_mmta_hotel:
                    self.log.info(hotel + ' nmmta replaced with mmta '+ replaced_mmta_hotel)
                    prioritized_hotel = replaced_mmta_hotel
                    final_mmta_hotel_set.add(prioritized_hotel)
                    replace_nonmmta_count -= replace_nonmmta_count
                else:
                    self.log.info('no replacement for '+hotel)
            prioritized_response.append({'hotelId': prioritized_hotel})
        return prioritized_response

    @staticmethod
    def get_replaced_mmta_hotel(non_mmta_hotel, hotel_rank_dict, nonmmta_mmta_hotels_dict, sold_out_hotel_set):
        replaced_mmta_hotel = None
        mmta_hotels = nonmmta_mmta_hotels_dict.get(non_mmta_hotel)
        if mmta_hotels:
            # horizontally_visible_count represents the number of similar hotels shown horizontally on UI
            horizontally_visible_count = int(ComparatorMMHandler.pms_props['horizontally_visible_count'])
            for mmta_hotel in mmta_hotels:
                mmta_hotel_rank = hotel_rank_dict.get(mmta_hotel)
                mmta_hotel_rank = mmta_hotel_rank if mmta_hotel_rank else sys.maxsize
                if mmta_hotel not in sold_out_hotel_set and (mmta_hotel_rank - 1) > horizontally_visible_count:
                    replaced_mmta_hotel = mmta_hotel
                    break
        return replaced_mmta_hotel

    # Non MMT to MMT Assured hotels dictionary e.g. { "NH1": ["MH1", "MH2"], "NH2": ["MH1", "MH3", "MH4"]}
    def get_nonmmta_mmta_hotels_dict(self, location_id):
        self.log.info("In get_nonmmta_mmta_hotels_dict")
        cm = app.config['cb_obj']
        # GS represent Go Stays
        nonmmta_mmta_hotels_dict = cm.get_single("GS_" + location_id) if location_id else None
        if not nonmmta_mmta_hotels_dict:
            self.log.info("No MMT Assured hotels corresponding to Non MMT Assured hotels in cache for location {}"
                          .format(location_id))
        self.log.info("Out get_nonmmta_mmta_hotels_dict")
        return nonmmta_mmta_hotels_dict

    def get_rating(self, hotel_attributes_dict, hotel_key, hotel_attribute_key):
        rating = 0.0
        if hotel_key in hotel_attributes_dict:
            rating = hotel_attributes_dict.get(hotel_key).get(hotel_attribute_key, 0.0)
        return rating
