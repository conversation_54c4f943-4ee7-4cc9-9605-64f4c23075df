import json, requests
import time
import asyncio
import common.helpers as hp

from datetime import datetime



class ComparatorTribeHandler(object):
    log = hp.mdc_logger("ComparatorTribeHandler")

    def __init__(self, param , hotel_to_tribe):
        self.param = param
        self.tribe_resp = {}
        self.hotel_to_tribe = hotel_to_tribe

    def get_tribe_response(self):

        hotel_id = self.param['hotel']['hotelId']
        city = self.param['cityCode']
        checkin = self.param['checkin']
        ap = self.Date2MinusDate1Days(date2=checkin)
        resp = self.hotel_to_tribe.identify_tribe_hotels(hotel_id, city, ap)
        respLength = len(resp['hotelList'])
        self.log.info("Tribe hotels : {} ".format(respLength))

        self.tribe_resp = resp

    def Date2MinusDate1Days(self, date1=None,formatString='%Y-%m-%d', date2=None):
        '''
        Compute date2-date1 in days
        :return: number of days
        '''
        if not date1: date1 = datetime.now().date()
        if not date2: date2 = datetime.now().date()
        if type(date1) == str:
            date1 = datetime.strptime(date1, formatString).date()
        if type(date2) == str:
            date2 = datetime.strptime(date2, formatString).date()
        # print (date1, date2)
        return (date2 - date1).days

    '''
    @asyncio.coroutine
    def get_result_from_tribe(self, future):
        tribe_result = {}
        try:
            request = {'correlationKey': self.param['correlationKey'],
                       'hotelId': self.param['hotel']['hotelId'],
                       'city': self.param['cityCode'],
                       "algorithmId": "tribe_v1",
                       "requestType": "DYNAMIC_RANKING"
                       }

            tribe_result = hp.get_json_data_from_post_call(ComparatorTribeHandler.log, ComparatorTribeHandler.cfg,
                                                           'dsTribeApiUrl', {'Content-type': 'application/json'},
                                                           request, "Tribe")
        except:
            ComparatorTribeHandler.log.error(
                'error occured while getting comparator tribe data ' + self.param['cityCode'], exc_info=True)
        future.set_result(tribe_result)

    '''