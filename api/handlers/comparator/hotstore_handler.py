import common.helpers as hp


class ComparatorHotStoreHandler(object):
    log = hp.mdc_logger("ComparatorHotStoreHandler")

    def __init__(self, param):
        self.pivot_hotel = {'hotelId': param['hotel']['hotelId']}
        self.city = param['cityCode']
        self.request_param = param
        self.hot_store_response = {}
        self.filter_detail_response = {}
        self.is_filter_detail_required = param.get('is_filter_detail_required')

    def get_filtered_result_from_hot_store(self, tribe_hotels):
        hotel_details_for_filtering = []
        pivot_found = False
        if self.hot_store_response.get('response', None) and tribe_hotels.get('hotelList', None):
            hot_store_hotels = self.hot_store_response['response']['hotel_details']['hotels']
            for hotel in tribe_hotels['hotelList']:
                hotel_details = {}
                hotel_details['hotelId'] = str(hotel['hotelId'])
                try:
                    i = hot_store_hotels.index(hotel_details['hotelId'])
                except ValueError as e:
                    self.log.info("hotelId {} not present in hot store".format(hotel['hotelId']))
                    continue
                hotel_details['index'] = i
                hotel_details['STAR_RATING'] = next((k for k, v in
                                                     self.hot_store_response['response']['filter_categories'][
                                                         'STAR_RATING'].items()
                                                 if len(v) > i and v[i] == 1), None)
                hotel_details['STAR_RATING'] = int(hotel_details['STAR_RATING']) if hotel_details[
                    'STAR_RATING'] and "UNRATED" != hotel_details['STAR_RATING'].upper() else 0

                hotel_details['PROPERTY_TYPE'] = next((k for k, v in
                                                       self.hot_store_response['response']['filter_categories'][
                                                           'PROPERTY_TYPE'].items()
                                                       if len(v) > i and v[i] == 1), None)

                hotel_details['USER_RATING'] = float(
                    self.hot_store_response['response']['filter_values']['USER_RATING'][i]) if float(
                    self.hot_store_response['response']['filter_values']['USER_RATING'][i]) != -1.0 else 3.5
                if hotel_details['hotelId'] == self.pivot_hotel['hotelId']:
                    pivot_found = True
                    self.log.info("pivot hotel condition met {} ".format(hotel_details))
                    self.pivot_hotel = hotel_details
                hotel_details_for_filtering.append(hotel_details)
        else:
            self.log.info("No response from hot store or tribe")

        if not pivot_found:
            self.log.info("pivot hotel condition not met")
            return []

        ur_filter_list = pt_filter_list = sr_filter_list = None
        if self.request_param.get("appliedFilterMap", None):
            filter_list = None
            if self.request_param["appliedFilterMap"].get("STAR_RATING", None):
                sr_filter_list = [int(sr_filter["filterValue"])
                                  if "UNRATED" != sr_filter["filterValue"].upper() else 0
                                  for sr_filter in
                                  self.request_param["appliedFilterMap"]["STAR_RATING"]]
            if self.request_param["appliedFilterMap"].get("USER_RATING", None):
                ur_filter_list = [float(ur_filter["filterValue"]) for ur_filter in
                                  self.request_param["appliedFilterMap"]["USER_RATING"]]
            if self.request_param["appliedFilterMap"].get("PROPERTY_TYPE", None):
                pt_filter_list = [pt_filter["filterValue"] for pt_filter in
                                  self.request_param["appliedFilterMap"]["PROPERTY_TYPE"]]

        star_rating_filters = self.get_star_rating_filter(sr_filter_list)
        user_rating_filters = self.get_min_user_rating(ur_filter_list)
        property_type_filters = pt_filter_list if pt_filter_list else [self.pivot_hotel["PROPERTY_TYPE"]]

        post_filter_res = self.apply_filters(star_rating_filters, user_rating_filters, property_type_filters, hotel_details_for_filtering, self.pivot_hotel['hotelId'])

        #self.log.info("Hot store response {}".format(self.hot_store_response))

        filtered_result = [{'hotelId': x['hotelId']} for x in post_filter_res]

        if {'hotelId' : self.pivot_hotel['hotelId']} not in filtered_result:
            self.log.info("pivot hotel filtered out")
            return []
        # self.log.debug("pmsProps: {}".format(ComparatorHotStoreHandler.pms_props))
        if len(filtered_result) > int(ComparatorHotStoreHandler.pms_props.get('filtered_hotel_threshold','20')):
            # self.log.info("inserting pivot hotel to the first position so that it won't get removed by threshold")
            filtered_result.remove({'hotelId' : self.pivot_hotel['hotelId']})
            filtered_result.insert(0, {'hotelId' : self.pivot_hotel['hotelId']})

        return filtered_result

    def apply_filters(self, star_rating_filters, user_rating_filters, property_type_filters, hotel_details_for_filtering, pivot_hotel_id):
        # self.log.info(
        #     "star : {}, user : {}, property:{}".format(star_rating_filters, user_rating_filters, property_type_filters))
        return [comparator_hotel for comparator_hotel in hotel_details_for_filtering if
                comparator_hotel['hotelId'] == pivot_hotel_id
                or comparator_hotel['STAR_RATING'] in star_rating_filters
                and comparator_hotel['USER_RATING'] >= user_rating_filters
                and comparator_hotel['PROPERTY_TYPE'] in property_type_filters]

    '''
    Return user filter list if there otherwise use below matrix
    All the hotels which have rating greater than the Details hotel can be taken, for hotels which have lesser rating we can use the below matrix
    For hotel equal to 3 star, they can only have hotels which have rating 3, 4 and 5
    For hotel equal to 4 star, they can have 3,4 and 5
    For hotel equal to 5 star they can have 4 and 5
    For hotel less than 3 star they should have either same or higher rating
    '''

    def get_star_rating_filter(self, filter_list):
        # self.log.info("star filter list {}. ".format(filter_list))
        if not filter_list:
            if self.pivot_hotel['STAR_RATING'] > 3:
                filter_list = list(range(self.pivot_hotel['STAR_RATING'] - 1, 6))
            elif self.pivot_hotel['STAR_RATING'] <= 3:
                filter_list = list(range(self.pivot_hotel['STAR_RATING'], 6))
        return filter_list

    '''
    Return minimum of user filter list if there otherwise use below matrix
    All the hotels which have rating greater than the Details hotel can be taken, for hotels which have lesser rating we can use the below matrix
    For hotels above 3 user rating, we can consider the recommended hotel which are 0.2 less from Pivot Hotel
    For hotels above 3.5 to 4 user rating, we can consider the recommended hotel which are in range of 0.1 less than pivot hotel
    For any pivot hotels, all the similar hotels that have 4to5 rating can be taken
    '''

    def get_min_user_rating(self, filter_list):
        # self.log.info("user filter list {} ".format(filter_list))
        min_rat = self.pivot_hotel['USER_RATING']
        if filter_list:
            min_rat = min(filter_list)
        else:
            if self.pivot_hotel['USER_RATING'] > 4:
                min_rat = 3.6
            elif self.pivot_hotel['USER_RATING'] >= 3.5:
                min_rat = self.pivot_hotel['USER_RATING'] - 0.4
            elif self.pivot_hotel['USER_RATING'] >= 3:
                min_rat = self.pivot_hotel['USER_RATING'] - 0.6
        return min_rat

    def get_mybiz_assured_hotel_ids(self, hotels):
        mybiz_assured_hotel_ids = set()

        if not self.hot_store_response or not self.hot_store_response.get('response'):
            return mybiz_assured_hotel_ids

        response = self.hot_store_response.get('response')
        try:
            hotstore_hotels = response['hotel_details']['hotels']
            mybiz_assured_signals = response['filter_categories']['HOTEL_CATEGORY']['MyBiz Assured']

            if hotstore_hotels and mybiz_assured_signals:
                mybiz_assured_signals_size = len(mybiz_assured_signals)
                for hotel in hotels:
                    hotel_id = hotel['hotelId']
                    index = hotstore_hotels.index(hotel_id) if hotel_id in hotstore_hotels else -1
                    if index !=-1 and index < mybiz_assured_signals_size and mybiz_assured_signals[index]:
                        mybiz_assured_hotel_ids.add(hotel_id)

        except Exception as e:
            raise Exception(e, "Exception while getting mybiz assured hotels")

        return mybiz_assured_hotel_ids

    @staticmethod
    def get_filter_detail_headers():
        api_key = ComparatorHotStoreHandler.cfg[ComparatorHotStoreHandler.cfg['Env']]['filterDetailApiKey']
        headers = {
            'api-key': api_key,
            'Content-type': 'application/json'
        }
        return headers

    def get_filter_detail_request(self):
        location_id = self.request_param.get('cityCode')
        if self.request_param.get('newApp'):
            location_type = self.request_param.get('locType').lower()
            location_id_key = "hotel_" + location_type + ".id"
        else:
            location_id_key = "city_code"

        request = {
            "filter": {
                "$and": [
                    {
                        location_id_key: location_id
                    },
                    {
                        "is_active": True
                    }
                ]
            },
            "queryList": [
                "htl_attributes.Gostays"
            ],
            "supplierCode": "MMT"
        }
        return request

    def get_mmta_hotel_rank_dict(self, hotel_set):
        mmta_hotel_set = set()
        if not self.filter_detail_response or not self.filter_detail_response.get('resultList'):
            return mmta_hotel_set
        result_list = self.filter_detail_response.get('resultList')
        for result in result_list:
            hotel_id = result.get('_id')
            hotel_attributes = result.get('htl_attributes')
            if hotel_id and hotel_id in hotel_set and hotel_attributes:
                attribute_type = 'Gostays'
                attribute_value = hotel_attributes.get(attribute_type)
                if attribute_value and attribute_value.lower() == attribute_type.lower():
                    mmta_hotel_set.add(hotel_id)
        return mmta_hotel_set
