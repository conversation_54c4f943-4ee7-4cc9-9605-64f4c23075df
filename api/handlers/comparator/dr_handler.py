import json, requests
import os
import time
import asyncio
import common.helpers as hp
import math


class ComparatorDRHandler(object):
    hotel_recommender_json_path = "/../../config/hotel_recommender_recency.json"
    log = hp.mdc_logger("ComparatorDRHandler")

    def __init__(self, param):
        self.param = param
        self.recency_resp = {}
        self.last_booked = None
        self.last_viewed = None

    def get_request(self,algorithm_id='v.1_rec'):
        hotel = []
        config_file = os.path.dirname(os.path.realpath(__file__)) + ComparatorDRHandler.hotel_recommender_json_path
        with open(config_file, 'r') as f:
            data = json.load(f)
        hotel.append(self.param['hotel'])
        data['actionContext']['hotels'] = hotel
        data['queryContext']['roomStayParams'] = self.param['roomStayCandidates']
        data['queryContext']['checkIn'] = self.param['checkin']
        data['queryContext']['checkOut'] = self.param['checkout']
        data['queryContext']['destination'] = self.param['cityCode']
        data['queryContext']['destinationType'] = self.param.get('locType')
        if 'deviceId' in self.param:
            data['userContext']['deviceId'] = self.param['deviceId']
        if 'deviceType' in self.param:
            data['userContext']['deviceType'] = self.param['deviceType']
        if 'pEmailId' in self.param:
            data['userContext']['bookingEmail'] = self.param['pEmailId']
        if 'uuid' in self.param:
            data['userContext']['uuid'] = self.param['uuid']
        if 'profileType' in self.param:
            data['userContext']['profile_type'] = self.param['profileType']
        data['newApp'] = self.param.get('newApp',False)
        data['algorithmId'] = algorithm_id
        return data

    '''
    @asyncio.coroutine
    def get_data_from_recency(self):
        hotel = []
        try:
            config_file = os.path.dirname(os.path.realpath(__file__)) + ComparatorDRHandler.hotel_recommender_json_path
            with open(config_file, 'r') as f:
                data = json.load(f)
            hotel.append(self.param['hotel'])
            data['actionContext']['hotels'] = hotel
            data['queryContext']['roomStayParams'] = self.param['roomStayCandidates']
            data['queryContext']['checkIn'] = self.param['checkin']
            data['queryContext']['checkOut'] = self.param['checkout']
            data['queryContext']['destination'] = self.param['cityCode']
            if 'deviceId' in self.param:
                data['userContext']['deviceId'] = self.param['deviceId']
            if 'deviceType' in self.param:
                data['userContext']['deviceType'] = self.param['deviceType']
            if 'pEmailId' in self.param:
                data['userContext']['bookingEmail'] = self.param['pEmailId']
            data['uuid'] = self.param['uuid']
            data['profile_type'] = self.param['profileType']
            resp = hp.get_json_data_from_post_call(ComparatorDRHandler.log, ComparatorDRHandler.cfg, 'dsApiUrl',
                                            {'Content-type': 'application/json'}, data, "DR Recency")
            # ComparatorDRHandler.log.info("DR response is {}".format(resp))
            if resp and resp.get('responses', None):
                self.recency_resp = {'hotelList' : [{'hotelId': str(x[0])} for x in resp['responses'][0]['output_hotels']]}
                self.last_booked = resp['responses'][0].get('last_booked_hotels', [])
                self.last_viewed = resp['responses'][0].get('last_viewed_hotels', [])
        except:
            ComparatorDRHandler.log.error(
                'error occurred while getting recency data ' + self.param['cityCode'], exc_info=True)
    '''

    def set_response(self, response):
        if response and response.get('responses', None) and len(response['responses']) > 0:
            first_respone = response['responses'][0]
            self.recency_resp = {'hotelList': [{'hotelId': str(x[0])} for x in first_respone.get('output_hotels',[])]}
            self.last_booked = first_respone.get('last_booked_hotels', [])
            self.last_viewed = first_respone.get('last_viewed_hotels', [])

    def findIndex(self, hotel):
        index = len(self.recency_resp['hotelList'])
        try:
            index = self.recency_resp['hotelList'].index(hotel)
        except ValueError as ve:
            self.log.info("hotel not present in recency {}".format(hotel))
        return index

    def sort_based_on_RI(self, filtered_hotels, isSortingRequired, is_corporate_request):
        comparator_response = {}
        try:
            if self.recency_resp:
                    dr_hotels = self.recency_resp['hotelList']
                    filtered_hotels = filtered_hotels if is_corporate_request \
                        else filtered_hotels[:int(ComparatorDRHandler.pms_props.get('filtered_hotel_threshold', '20'))]
                    #Sorting will required for tribe filtered_hotels and for recency result no need of sorting
                    comparator_response = sorted(filtered_hotels,
                                                 key=lambda x: self.findIndex(x)) if isSortingRequired else filtered_hotels
                    hotels_to_add = list(
                        [[str(hotel), "Last Booked"] for hotel in self.last_booked] + [[str(hotel), "Last Viewed"] for hotel
                                                                                  in
                                                                                  self.last_viewed])
                    ComparatorDRHandler.log.info("Hotels to add {}".format(hotels_to_add))

                    for hotel in hotels_to_add:
                        index = next(
                            (index for (index, d) in enumerate(comparator_response) if d['hotelId'] == hotel[0]),
                            None)
                        if index is not None:
                            comparator_response[index]["crossSellTag"] = hotel[1]
                        else:
                            comparator_response.insert(math.ceil(len(comparator_response) / 2),
                                                       {'hotelId': str(hotel[0]), "crossSellTag": hotel[1]})
            else:
                    # self.log.info("No output hotels from from recency. Recency Response: {}".format(self.recency_resp))
                    self.log.info("No output hotels from from recency")
        except Exception as e:
            ComparatorDRHandler.log.error('Exception occurred while getting recency data for city {}: {}'
                                          .format(self.param['cityCode'], e))
        return comparator_response
