class DefaultConfig(object):
    # rof_params
    TIME_PERIOD = 7
    BIAS = 0
    WEIGHT_LIST = '0.3,0.5,0.2'

    LOGDIR = '/opt/logs/Hotel-Personalization/upsell/'
    DATADIR = '/opt/Hotel-Personalization/api/data/'

    # sequence updater api
    UPDATE_URL = 'http://ds-hotel-sequpd.mmt.mmt:5023/HotelRanking/v2'

    TIMEOUT = 0.200

    # redis details
    REDIS_HOST = 'redis-htl-singluarity.mmt.mmt'
    REDIS_PORT = 7000

    MYSQL_PORT = 3306
    MYSQL_READ_TIMEOUT = 720
    MYSQL_RETRY_QUERY_COUNT = 3

    # htl content db
    MYSQL_HOST_HTL_CONTENT = 'mysql-htl-2-master.mmt.mmt'
    MYSQL_USER_HTL_CONTENT = 'htlcontent'
    MYSQL_PASSWORD_HTL_CONTENT = 'htLc@nt3nt'
    MYSQL_DB_HTL_CONTENT = 'HTL_CONTENT'

    # mmt read db
    MYSQL_HOST_MMT_READ = 'mysql-htl-mmt-slave.mmt.mmt'
    MYSQL_USER_MMT_READ = 'htlcontent'
    MYSQL_PASSWORD_MMT_READ = 'htLc@nt3nt'
    MYSQL_DB_MMT_READ = 'mmt'

    # mmt booker db
    MYSQL_HOST_HOTEL_BOOKER = 'mysql-htl-1-slave.mmt.mmt'
    MYSQL_USER_HOTEL_BOOKER = 'htlcontent'
    MYSQL_PASSWORD_HOTEL_BOOKER = 'htLc@nt3nt'
    MYSQL_DB_HOTEL_BOOKER = 'hotel_booker'

    # mmt write db
    MYSQL_HOST_MMT_WRITE = 'mysql-htl-mmt-master.mmt.mmt'
    MYSQL_USER_MMT_WRITE = 'htlcontent'
    MYSQL_PASSWORD_MMT_WRITE = 'htLc@nt3nt'
    MYSQL_DB_MMT_WRITE = 'mmt'
    HOTEL_LISTABLE_LOCATION_MAPPING = 'htl_loc_mapping_without_poi.csv'

    # mmt3 read db
    MYSQL_HOST_MMT3_READ = 'mysql-htl-3-slave.mmt.mmt'
    MYSQL_USER_MMT3_READ = 'htlcontent'
    MYSQL_PASSWORD_MMT3_READ = 'htLc@nt3nt'
    MYSQL_DB_MMT3_READ = 'mmt'

    # hive
    HIVE_HOST = 'cmmt-53-27.mmt.mmt'
    HIVE_PORT = 10000
    HIVE_KEYTAB = '/etc/dshtlnewhotels.keytab ds_htl_new_hotels'

    AWS_REGION = 'ap-south-1'
    ALLOWED_EXTENSIONS = set(['csv'])
    RS_HOST = 'redshift-hotel.c2nkbt0yawao.ap-south-1.redshift.amazonaws.com'
    RS_PORT = 5439
    RS_USER = 'devuser'
    RS_CONFIG_P = 'Welcome123'
    RS_MMT_DB = 'data_science_fstore'

    ## candidate generation configuration ##
    TOP_CITIES_INTL = ['-1022136', '-1322723', '-1353149', '-1771148', '-2403412', '-2437894', '-2683839', '-2701757',
                       '-3096949', '-3242432', '-3249904', 'MEL1', 'OOL', 'SYD', 'BAK', 'VIE', 'BRU', 'PNH', 'SIEM',
                       'YTO', 'YVR', 'CAN', 'PEK', 'SHA', 'SHEN', 'PRG', 'CAI', 'NCE', 'PAR1', 'BER', 'MUC', 'ATH1',
                       'BUD', 'ABAL', 'LOBK', 'JKT', 'DUB1', 'FLR', 'MIL', 'ROM', 'VCE', 'TYO', 'NBO', 'MFM', 'JHB',
                       'PEN1', 'LANK', 'GENT', 'ZKL', 'RGN', 'AMS', 'POKH', 'AKL1', 'MCT', 'CEB', 'WAW', 'DOH', 'MOW',
                       'SIN1', 'JNB', 'CPT', 'SEL1', 'BCN', 'MAD1', 'CMB', 'BNTO', 'KNDY', 'STO', 'LUCE', 'CNX', 'KOSA',
                       'HUAH', 'HKT', 'BKK', 'PEEP', 'IST1', 'IEV', 'AUH', 'SHAR', 'AJMA', 'DUBW', 'LHR', 'LON1', 'LAS',
                       'LAX', 'NYC', 'ORL', 'SFO', 'HAN', 'SGN1', 'MANF', 'TBS', 'DMAL', 'PRA1', 'THP', 'PTG1', 'KOW1',
                       'DHK']

    PUSH_FILTERED_HOTELS_TO_DB_DOM = True
    PUSH_FILTERED_HOTELS_TO_DB_INTL = True

    PUSH_GH_CANDIDATES_TO_DB_DOM = True
    PUSH_GH_CANDIDATES_TO_DB_INTL = True

    # filters
    NEW_HOTELS_FILTERS = ["filter_cities", "filter_hotels_basis_content", "filter_hotels_basis_inventory",
                          "filter_hotels_basis_review", "filter_fraud_hotels"]
    NEW_HOTELS_FILTERS_DEVICE = ["filter_hotel_basis_exposure", "filter_hotel_basis_rank",
                                 "filter_hotels_basis_room_nights"]
    NEW_HOTELS_SCORERS = ["score_basis_area", "score_basis_quality", "score_basis_rank"]

    # new hotels
    NEW_HOTELS_FROM = '-6 month'
    NEW_HOTELS_TO = '0 day'
    MINIMUM_HOTELS_IN_CITY = 20

    # rank
    MINIMUM_RANK_VALUE = '30'
    MINIMUM_RANK_PERCENTAGE = 0.2

    # room nights
    MAXIMUM_ROOM_NIGHTS = 20

    #Manual Rank
    MANUAL_RANK_API_URL = 'http://*************:8080/hotel-ranking/pushManualRank'
    MANUAL_RANK_API_HEADERS = {'Content-Type': 'application/json'}

    # bookings
    BOOKINGS_FROM = '-30 day'
    BOOKINGS_TO = '0 day'

    # hotel attributes
    HOTEL_ATTRIBUTES_API_LIMIT = 5000
    HOTEL_ATTRIBUTES_API_URL = "http://*************:8080/hotel-ranking/v1/hotelAttributes"
    HOTEL_ATTRIBUTES_API_HEADERS = {'Content-Type': 'application/json'}
    HOTEL_ATTRIBUTES_API_TIMEOUT = 5

    # scorers
    SCORE_MULTIPLIER_AREA = 0.3
    SCORE_MULTIPLIER_QUALITY = 0.2
    SCORE_MULTIPLIER_RANK = 0.1

    DELTA_DAYS = -14

    # Booking Absolute
    BOOKING_ABSOLUTE = 'booking_absolute'
    BKG_ABS_CUTOFF_VAL = 5
    BKG_ABS_CUTOFF_PERC = 1

    # Booking Relative
    BOOKING_RELATIVE = 'booking_relative'
    BKG_REL_CUTOFF = 0.3

    # source of appending boosted hotels
    SOURCE_UPLOADER = 'Uploader'
    SOURCE_CANDIDATE_GENERATION = 'CandidateGeneration'
    SOURCE_RETIRE_ROTATE = 'RetireRotate'

    SESSION_EXPIRY_TIME = 60 * 1000 * 30
    COOKIES_EXPIRY_TIME = 60 * 1000 * 31

    # LDAP CONFIGURATION MMT
    LDAP_IP_MMT = 'ldap://************:389/'
    USERNAME_PREFIX_MMT = 'mmt\\'
    BASE_DN_MMT = 'DC=mmt,DC=com'
    GROUP_NAME_MMT = 'cn=greenhornbooster,ou=mmt groups,dc=mmt,dc=com'

    # LDAP CONFIGURATION GI
    LDAP_IP_GI = 'ldap://*************:389/'
    USERNAME_PREFIX_GI = 'mihindia\\'
    BASE_DN_GI = 'DC=idc,DC=mihindia,DC=com'
    GROUP_NAME_GI = 'cn=greenhornbooster,ou=dls,dc=idc,dc=mihindia,dc=com'

    # boosted sequence
    DEFAULT_START_BLOCK = 5
    DEFAULT_BLOCKSIZE = 5
    DEFAULT_N_SLOTS = 1
    DEFAULT_MAX_HOTELS = 100
    DEFAULT_PRIORITY = 1

    # boosted hotels
    DEFAULT_START_RANK = -1
    DEFAULT_END_RANK = -1
    DEFAULT_USER = 'GreenHorn'

    # mail
    MAIL_FROM = '<EMAIL>'


    MAIL_TO = '<EMAIL>'
    MAIL_TO_GH_CANDIDATE_GENERATION = ['<EMAIL>', '<EMAIL>']
    MAIL_TO_AUTOMATED_BOOSTER_REFRESH = ['<EMAIL>']

    HOTEL_DATA_CSV = "domesticcities_meanprice_new.csv"
    NUMBER_OF_HOTELS_LIMIT = 50

    MAIL_SUBJECT_GH_CANDIDATE_GENERATION = 'Greenhorn Candidate Generation Notification'
    MAIL_SUBJECT_HOTEL_LOCATION_MAPPING = 'Hotel Location Mapping Table Update'
    MAIL_SUBJECT_AEROSPIKE_CACHE_REFRESH = 'GreenHorn Aerospike Cache Refresh'
    MAIL_SUBJECT_AUTOMATED_BOOSTER_REFRESH = 'Automated Booster Refresh Notification'
    S3_BKT = "model-ds"
    S3_BKT_INVENTORY = "go-mmt-hotels-bucket"
    S3_BKT_INVENTORY_KEY = "inventory-etl/hourly/raw_json/"
    S3_BKT_KEY_BASE = "personalization/retire-rotate/"
    S3_BKT_KEY = "personalization/latest/"
    S3_FILE_BOOKING = "booking.csv"
    S3_FILE_HOTEL_LOCUS = "hotel_location_mapping.csv"
    S3_FILE_HOTEL_LOCUS_WITHOUT_POI = "htl_loc_mapping_without_poi.csv"
    S3_DOWNLOAD = 1

    MAIL_SUBJECT_BULK_DELETION = 'Greenhorn Bulk Deletion Notification'
    MAIL_TO_BULK_DELETION = ['<EMAIL>', '<EMAIL>', '<EMAIL>']

    # Aerospike config
    AEROSPIKE_HOST = [('*************', 3000)]
    AEROSPIKE_NAMESPACE = 'ns_256'
    AEROSPIKE_NAMESPACE_COMMON = 'ns_256'
    AEROSPIKE_TTL = 7 * 24 * 60 * 60
    AEROSPIKE_COMMON_DATA_SET = 'common_data'

class TestingConfig(DefaultConfig):
    TIMEOUT = 1.010

    MYSQL_PORT = 3306

    # htl content db
    MYSQL_HOST_HTL_CONTENT = '*************'
    MYSQL_USER_HTL_CONTENT = 'mmtweb1'
    MYSQL_PASSWORD_HTL_CONTENT = 'p@55word'
    MYSQL_DB_HTL_CONTENT = 'mmt'

    # mmt read db
    MYSQL_HOST_MMT_READ = '*************'
    MYSQL_USER_MMT_READ = 'mmtweb1'
    MYSQL_PASSWORD_MMT_READ = 'p@55word'
    MYSQL_DB_MMT_READ = 'mmt'

    # hive
    HIVE_HOST = 'cmmt-53-27.mmt.mmt'
    HIVE_PORT = 10000
    HIVE_KEYTAB = '/etc/dshtlnewhotels.keytab ds_htl_new_hotels'

    LOGDIR = '/opt/logs/Hotel-Personalization/upsell/'
    DATADIR = '/opt/Hotel-Personalization/api/data/'
    # Aerospike config
    AEROSPIKE_HOST = [('*************', 3000)]
    AEROSPIKE_NAMESPACE = 'ns_256'
    AEROSPIKE_NAMESPACE_COMMON = 'ns_256'
    AEROSPIKE_TTL = 7 * 24 * 60 * 60
    AEROSPIKE_COMMON_DATA_SET = 'common_data'


import os

config_dir = os.path.dirname(__file__)
api_dir = os.path.dirname(config_dir)
root_dir = os.path.dirname(api_dir)
