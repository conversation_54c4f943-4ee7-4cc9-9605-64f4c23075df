<!DOCTYPE html>
<html lang="en">
<head>
    {{ JSGlue.include() }}
    <script src="/static/styles/js/jquery-1.12.3.min.js" type="text/javascript"></script>
    <script src="/static/styles/js/bootstrap.js" type="text/javascript"></script>
    <script src="/static/styles/js/jquery.multi-select.min.js"></script>
    <meta charset="UTF-8">
    <title>Premium Hotels Upload</title>
    <script>
        $(function (){
        $('#add_hotels').on('submit', function(e){
                e.preventDefault();
                var formData = new FormData(this);
                $.ajax({
                    type: 'POST',
                    url:  Flask.url_for('triggerUploaderForPremiumHotels'),
                    data: formData,
                    success: function (response){
                        if (response.success)
                            alert("Successfully completed.")
                        else
                            alert("Failed.")
                    },
                    cache: false,
                    contentType: false,
                    processData:false
                });
            })
        })
    </script>
</head>
<body>
    <div>
        <form id='add_hotels' action={{url_for("triggerUploaderForPremiumHotels")}} method="post" enctype = "multipart/form-data" name="form_add_hotels" class="form_data">
            <p class="clearfix append_bottom25">
                <label for="upload_file" accesskey="U">Upload CSV files</label>
                <!-- upload button -->
                <span class="upload_file_wrap make-relative">
                    <input type="file" id="upload_file" required name="city_hotels">
                    <span id="upload_file_id"></span>
                </span> <!-- /upload button -->
                <div class="clearfix">
                    <label for="upload_files"></label>
                    <input type="submit" class="primary_btn" value="Submit"/>
                </div>
            </p>
        </form>
    </div>
</body>
</html>
