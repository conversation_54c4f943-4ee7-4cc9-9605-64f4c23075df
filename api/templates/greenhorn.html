<!doctype html>
<html>
<head>
    {{ JSGlue.include() }}
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title><PERSON> Horn</title>
    <link href="/static/styles/css/font-awesome.css" rel="stylesheet">
    <link href="/static/styles/css/reboot.css" rel="stylesheet">
    <link href="/static/styles/css/fonts.css" rel="stylesheet">
    <link href="/static/styles/css/style.css?v=2" rel="stylesheet">
	<link href="/static/styles/css/progressbar.css" rel="stylesheet">
    <link href="/static/styles/css/cohort-selection.css" rel="stylesheet">
    <script src="/static/styles/js/jquery-1.12.3.min.js" type="text/javascript"></script>
    <script src="/static/styles/js/bootstrap.js" type="text/javascript"></script>
    <script src="/static/styles/js/jquery.multi-select.min.js"></script>
    <script>
        $(function () {
            // Get the select element
            var select = document.getElementById("lobs");

            fetch("/booster-restriction-buckets")
                .then(response => response.json())
                .then(restrictions => {
                    restrictions.forEach(function (lob_wise_restrictions) {
                        // Create a new option element
                        var el = document.createElement("option");
                        // Set the value and text of the option
                        el.value = lob_wise_restrictions['lob'];
                        el.text = lob_wise_restrictions['lob'];
                        // Set "DH" as the default selected option
                        if (lob_wise_restrictions['lob'] === 'DH') {
                            el.selected = true;
                        }
                        // Append the option to the select element
                        select.appendChild(el);
                    });
                })
                .catch(error => {
                    // Handle any errors that occurred during the fetch
                    console.error(error);
                });
        });
    </script>

    <script>
        function toggleFields() {
            var selection = document.getElementById("selection").value;
            if (selection === "cohort") {
                document.getElementById("source_seq_id_txt").value = '';
                document.getElementById("p_cohort").style.display = "block";
                document.getElementById("available_lobs").style.display = "block"; // Show available lobs
                document.getElementById("selected_lobs").style.display = "block"; // Show drop zone
                document.getElementById("p_source_seq_id_txt").style.display = "none";
            } else {
                document.getElementById("p_cohort").style.display = "none";
                document.getElementById("available_lobs").style.display = "none"; // Hide available lobs
                document.getElementById("selected_lobs").style.display = "none"; // Hide drop zone
                document.getElementById("p_source_seq_id_txt").style.display = "block";
            }
        }

        document.addEventListener('DOMContentLoaded', function () {
            var draggables = document.querySelectorAll('.draggable');
            var dropzone = document.getElementById('selected_lobs');
            var availableLobs = document.getElementById('available_lobs');

            draggables.forEach(draggable => {
                draggable.addEventListener('dragstart', dragStart);
            });

            dropzone.addEventListener('dragover', dragOver);
            dropzone.addEventListener('drop', dropItem);

            function dragStart(event) {
                event.dataTransfer.setData('text', event.target.id);
            }

            function dragOver(event) {
                event.preventDefault();
            }

            function dropItem(event) {
                event.preventDefault();
                const itemId = event.dataTransfer.getData('text');
                const draggedItem = document.getElementById(itemId);
                draggedItem.remove();
                const clonedItem = draggedItem.cloneNode(true);
                clonedItem.classList.add('selected-lob');
                clonedItem.innerHTML = draggedItem.innerHTML + ' <span class="remove" onclick="removeItem(event)">×</span>';
                dropzone.appendChild(clonedItem);
            }

            window.removeItem = function (event) {
                const itemToRemove = event.target.parentElement;
                const lobId = itemToRemove.id;
                const newItem = document.createElement('div');
                newItem.classList.add('draggable');
                newItem.id = lobId;
                newItem.setAttribute('draggable', 'true');
                newItem.textContent = itemToRemove.textContent.replace(' ×', '');
                availableLobs.appendChild(newItem);
                newItem.addEventListener('dragstart', dragStart);
                itemToRemove.remove();
            }
        });
    </script>

    <script>
        var seqid_to_dvc  = {
                0: "WEB",
                1: "IOS",
                2: "ANDROID",
                3: "PWA"
            };

        $(function () {
            var _redirectTimeout = {{config['SESSION_EXPIRY_TIME']}}; // 30 minutes timeout - test with 10 * 1000 for ten seconds :)
            var _redirectUrl = Flask.url_for('logout'); // logout URL
            var _redirectHandle = null;

            function resetRedirect() {
                if (_redirectHandle) clearTimeout(_redirectHandle);
                _redirectHandle = setTimeout(function () {
                    window.location.href = _redirectUrl;
                }, _redirectTimeout);
            }

            $.ajaxSetup({ complete: function () { resetRedirect(); } }); // reset idle redirect when an AJAX request completes

            resetRedirect(); // start idle redirect timer initially.
        });



        $(function (){

            $('#append_form').on('submit', function(e){
                e.preventDefault();
                $('.modal-content.gh-modalcontents .close').click()
				$('#message-heading').html($('#append-booster-heading').html())
                $('#message-text').html('Please wait ... Append in Progress')
                $('.modal.fade.js-message-modal').modal('toggle');
                var formData = new FormData(this);
                $.ajax({
                    type: 'POST',
                    url:  Flask.url_for('triggerAppend'),
                    data: formData,
                    success: function (response){
                        if (response.success)
                            $('#message-text').html('Successfully Appended booster file')
                        else
                            $('#message-text').html(response.message)
                    },
                    cache: false,
                    contentType: false,
                    processData:false
                });
            })

            $('#remove_form').on('submit', function(e1){
                e1.preventDefault();
                $('.modal-content.gh-modalcontents .close').click()
                $('#message-heading').html($('#remove-booster-heading').html())
                $('#message-text').html('Please wait ... Removal in Progress')
                $('.modal.fade.js-message-modal').modal('toggle');
                var formData = new FormData(this);
                $.ajax({
                    type: 'POST',
                    url: Flask.url_for('triggerRemove'),
                    data: formData,
                    success: function (response){
                        if (response.success)
                            $('#message-text').html('Successfully Removed booster file')
                        else
                            $('#message-text').html(response.message)
                    },
                    cache: false,
                    contentType: false,
                    processData:false
                });
            })
            $('#add_form').on('submit', function(e){
                var isRestrictionSatisfied = false ;
                if(document.getElementById("percentage_based").checked){
                var percent = document.getElementById("default_percentage").value;
                    if(percent > 100){
                        alert('Default percentage value should not be greater than  100');
                        document.getElementById("default_percentage").focus();
                        return false;
                    }
                } else {
                    var lob = document.getElementById("lobs").value;
                    if (lob === 'default') {
                        isRestrictionSatisfied = checkCriteria();
                        if (isRestrictionSatisfied == false)
                            return false;
                    }
                }
                if (document.getElementById("selection").value === "cohort") {
                    var selectedLobs = document.getElementById("selected_lobs");
                    var selectedValues = Array.from(selectedLobs.getElementsByClassName("selected-lob")).map(item => item.id);
                    document.getElementById("source_seq_id_txt").value = selectedValues.join(",");
                }
                e.preventDefault();
                $('#message-heading').html('Booster Upload')
                $('#message-text').html('Please wait ... Upload in Progress')
                $('.modal.fade.js-message-modal').modal('toggle');
				var formData = new FormData(this);
                $.ajax({
                    type: 'POST',
                    url: Flask.url_for('triggerUploader'),
                    data: formData,
                    success: function (response){
                        $('#message-heading').html('Booster Upload Status')
                        if (response.success)
                            $('#message-text').html('Successfully Uploaded booster file')
                        else
                            $('#message-text').html(response.message)
                        assign()
                    },
                    cache: false,
                    contentType: false,
                    processData:false
                });
            })
            $('#delete_bulk_form').on('submit', function(e){
                e.preventDefault();
                $('#message-heading').html('Bulk Deleting Boosters')
                $('#message-text').html('Please wait ... Deletion in Progress')
                $('.modal.fade.js-message-modal').modal('toggle');
				var formData = new FormData(this);
                $.ajax({
                    type: 'POST',
                    url: Flask.url_for('triggerBulkDelete'),
                    data: formData,
                    success: function (response){
                        $('#message-heading').html('Boosters Bulk Deletion Status')
                        if (response.success)
                            $('#message-text').html('Successfully Deleted Active boosters from file')
                        else
                            $('#message-text').html(response.message)
                        assign()
                    },
                    cache: false,
                    contentType: false,
                    processData:false
                });
            })
        });

        function createElementFromHTML(htmlString) {
          var div = document.createElement('div');
          div.innerHTML = htmlString.trim();

          // Change this to div.childNodes to support multiple top-level nodes
          return div.firstChild;
        }

        function deleteApi(uid, description,algorithm,seqid,booster_brand,  deleteId = null){
            var check = confirm("Are you sure?");
            $('#message-heading').html('Delete : ' + description)
            $('#message-text').html('Please wait ... Deletion in Progress')
            $('.modal.fade.js-message-modal').modal('toggle');
			if(check == true){
			    var data = {
                    "uid":uid, "algorithm":algorithm, "seqid":seqid, "brand":booster_brand , "deleteId":deleteId
                };


                $.ajax({
                     url: Flask.url_for('triggerDelete'),
                     data: data,
                     type: "POST",
                     success: function(response){
                            if (response.success)
                                $('#message-text').html('Successfully Deleted Booster Sequence')
                            else
                                $('#message-text').html(response.message)
                            assign()
                     },
                     error: function(e) {
                            console.log(e);
                     }
                });
            }
        }

        function appendApi(uid, description,enddate,algorithm,seqid,retire_rotate_signals,booster_brand) {
            document.getElementById("uid_append").value = uid
            document.getElementById("enddate_append").value = enddate
            document.getElementById("algorithm_append").value = algorithm
            document.getElementById("seqid_append").value = seqid
            document.getElementById("retire_rotate_signals_append").value = retire_rotate_signals
            document.getElementById("booster_brand_append").value = booster_brand
            $('#append-booster-heading').html('Append : ' + description)
            return true;
        }

        function removeApi(uid, description,enddate,algorithm,seqid,retire_rotate_signals, booster_brand) {
            document.getElementById("uid_remove").value = uid
            document.getElementById("enddate_remove").value = enddate
            document.getElementById("algorithm_remove").value = algorithm
            document.getElementById("seqid_remove").value = seqid
            document.getElementById("retire_rotate_signals_remove").value = retire_rotate_signals
            document.getElementById("booster_brand_remove").value = booster_brand
            $('#remove-booster-heading').html('Remove : ' + description)
            return true;
        }
        // Function to generate a UUID
        function generateUUID() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
            });
        }
        async function deleteAsyncApi(uid, description,algorithm,seqid,booster_brand,  deleteId = nul) {

             var data = {
                    "uid":uid, "algorithm":algorithm, "seqid":seqid, "brand":booster_brand
                };

                if(deleteId){
                    data.deleteId = deleteId;
                }
                $.ajax({
                     url: Flask.url_for('triggerDelete'),
                     data: data,
                     type: "POST",
                     success: function(response){
                            assign()
                     },
                     error: function(e) {
                            console.log(e);
                     }
                });
        }


        async function deleteAndPopulateApi(boosterData) {
            //first call delete api and then check delete booster status in every 1 minute for 10 times and then call populate api

            var uid = boosterData.uid.join(',');
            var description = boosterData.description;
            var algorithm = boosterData.algorithm;
            var seqid = boosterData.seqid;
            var booster_brand = boosterData.brand;
            var deleteId = generateUUID();
            var check = confirm("Are you sure?");
            $('#message-heading').html('Delete and Populate : ' + description)
            $('#message-text').html('Please wait ... Deletion in Progress')
            $('.modal.fade.js-message-modal').modal('toggle');

            if (check == true) {
                try {
                    // Call deleteAsyncApi and wait for it to complete
                    await deleteAsyncApi(uid, description, algorithm, seqid, booster_brand, deleteId);

                    // Check delete status every 1 minute for 10 times
                    for (let count = 0; count < 10; count++) {
                        try {
                            console.log("calling delete status for count: " + count + " deleteId: " + deleteId);

                            let response = await $.ajax({
                                url: Flask.url_for('triggerDeleteStatus'),
                                data: {
                                    "deleteId": deleteId
                                },
                                type: "POST"
                            });

                            console.log(" status response:"+JSON.stringify(response));


                            if (response.success) {

                                $('#message-text').html('Successfully Deleted Booster Sequence...you can proceed to creation');

                                assign();


                                // Collapse 'Active Sequences' section
                                $('#active_sequences_block h2 a').addClass('collapsed');
                                $('.booster_table.section_data').css('display', 'none');



                                // Call populateApi after successful deletion
                                populateApi(boosterData);
                                // Countdown timer for auto-closing the modal
                                let countdown = 5;
                                const countdownInterval = setInterval(() => {
                                    $('#message-text').html(`Successfully Deleted Booster Sequence...you can proceed to creation. Closing in ${countdown} seconds.`);
                                    countdown--;
                                    if (countdown < 0) {
                                      clearInterval(countdownInterval);
                                      $('.modal.fade.js-message-modal').modal('toggle');
                                    }
                                }, 1000);
                                return;
                            } else {
                                await new Promise(resolve => setTimeout(resolve, 60000)); // Wait for 1 minute
                            }
                        } catch (e) {
                            console.log(e);
                        }
                    }

                    // If the status check fails after 10 retries, call populateApi
                    $('#message-text').html('Got Timeout...you still proceed to creation');
                    // Collapse 'Active Sequences' section

                    $('#active_sequences_block h2 a').addClass('collapsed');
                    $('.booster_table.section_data').css('display', 'none');
                    populateApi(boosterData);
                    // Countdown timer for auto-closing the modal
                    let countdown = 5;
                    const countdownInterval = setInterval(() => {
                        $('#message-text').html(`Successfully Deleted Booster Sequence...you can proceed to creation. Closing in ${countdown} seconds.`);
                        countdown--;
                        if (countdown < 0) {
                            clearInterval(countdownInterval);
                            $('.modal.fade.js-message-modal').modal('toggle');
                        }
                    }, 1000);


                } catch (e) {
                    console.log(e);
                }
            }

        }
        function populateApi(boosterDataString) {

            // Parse the JSON string into an object
            console.log(boosterDataString);

            var boosterData = boosterDataString;

            // Now you can access individual fields from boosterData object

            document.getElementById("Brand").value = boosterData.brand;
            document.getElementById("description").value = boosterData.description;
            document.getElementById("source_seq_id_txt").value = boosterData.seqid;
            document.getElementById("user_segment_id").value = boosterData.user_segments;
            if(boosterData.priority === 20){
                document.getElementById("appfest_booster").checked = true;
            }
            if(boosterData.mm_uid){
                document.getElementById("matchmaker_booster").checked = true;
                toggleMMBooster();
            }
            //document.querySelector(`input[name="boosterType"][value="${boosterData.boosterType}"]`).checked = true;

            if(boosterData.booster_type === 'Slot Based'){
                var slotBasedRadio = document.getElementById("slot_based");
                slotBasedRadio.checked = true;

                document.getElementById("start_block").value = boosterData.startblock;
                document.getElementById("block_size").value = boosterData.blocksize;
                document.getElementById("nslots").value = boosterData.nslots;

                boosterToggle(slotBasedRadio);

            }else{
                var percentageBasedRadio = document.getElementById("percentage_based");
                percentageBasedRadio.checked = true;

                document.getElementById("default_percentage").value = boosterData.boost_percentage;
                boosterToggle(percentageBasedRadio);
            }

            if(boosterData.min_check_in_date !== null){
                document.getElementById("min_check_in_date").value = boosterData.min_check_in_date.split(' ')[0];
            }
            if(boosterData.max_check_in_date !== null){
                document.getElementById("max_check_in_date").value = boosterData.max_check_in_date.split(' ')[0];
            }
            document.getElementById("min_los").value = boosterData.min_los;
            document.getElementById("max_los").value = boosterData.max_los;
            document.getElementById("min_adults").value = boosterData.min_adults;
            document.getElementById("max_adults").value = boosterData.max_adults;
            document.getElementById("ignore_dow").checked = boosterData.ignoreDow;
            if(boosterData.children === null){
                document.getElementById("ignore_child").checked = true;
            }else if(boosterData.children === '1'){
                document.getElementById("yes_child").checked = true;
            }else{
                document.getElementById("no_child").checked = true;
            }

            if(boosterData.check_in_dow === 'weekend'){
                document.getElementById("weekend").checked = true;
            }else if(boosterData.check_in_dow === 'weekday'){
                document.getElementById("weekday").checked = true;
            }else{
                document.getElementById("ignore_dow").checked = true;
            }

            // Split the ap_bucket value by the delimiter (assuming it's a comma)
            if(boosterData.ap_bucket !== null){
                var apBucketValues = boosterData.ap_bucket.split(',');

                // Iterate over the values and set the corresponding checkboxes to checked
                apBucketValues.forEach(function(value) {
                    var checkbox = document.querySelector(`input[name="ap_bucket"][value="${value}"]`);
                    if (checkbox) {
                        checkbox.checked = true;
                    }
                });
            }

            document.getElementById("pokus_exp").value = boosterData.pokus_exp;


          }

        function setDateBoundaries() {
            var today = new Date();
            var dd = today.getDate();
            var mm = today.getMonth()+1; //January is 0!
            var yyyy = today.getFullYear();
            if(dd<10)
                dd='0'+dd
            if(mm<10)
                mm='0'+mm
            today = yyyy+'-'+mm+'-'+dd;
            maxDate = $('#end_date').val()
            minDate = $('#start_date').val() || today
            checkMinDate =  $('#min_check_in_date').val() || today
            if (maxDate)
                $('#start_date').attr('max', maxDate)
            $('#start_date').attr('min', today)
            $('#end_date').attr('min', minDate)
            $('#min_check_in_date').attr('min', minDate)
            $('#max_check_in_date').attr('min', checkMinDate)

        }

        function checkCriteria()  {
            var start_hotel_index = parseInt(document.getElementById("start_block").value);
            var block_size = parseInt(document.getElementById("block_size").value);
            var nslots = parseInt(document.getElementById("nslots").value);
            var is_appfest_booster = document.getElementById('appfest_booster').checked
            if (nslots == 0) {
                alert('no of slots per block should be greater than zero');
                document.getElementById("nslots").focus();
                return false;
            } else if (!is_appfest_booster) {
                if ( block_size > 0 && nslots > 0 ) {
                    /* For 1st 20 slots  */
                    if( start_hotel_index <= 20) {
                       if(block_size / nslots < 5 ) {
                           alert('For 1st 20 slots, only 1 in 5 hotels can be boosted');
                           document.getElementById("nslots").focus();
                           return false;
                       }
                     }
                    else if( start_hotel_index <= 50)  {                        /* For 21st to 50 slots  */
                        if( block_size / nslots < 2.5 ) {
                           alert('For 21st to 50 slots, only 2 in 5 hotels can be boosted');
                           document.getElementById("nslots").focus();
                           return false;
                        }
                    }
                    else {                                                      /* For slots  > 50  */
                        if( block_size / nslots < 2 ) {
                           alert('For greater than 50 slots, a max of 1 in 2 hotels can be boosted');
                           document.getElementById("nslots").focus();
                           return false;
                         }
                    }
                }
            }
            return true;
        }

        function setMinMaxBoundary(eleId, modifiedAttrb) {
            if (modifiedAttrb != undefined && $('#'+modifiedAttrb).val() == '') {
                $('#min_'+eleId).val('')
                $('#max_'+eleId).val('')
                return
            }
            minValue = $('#min_'+eleId).val() || 0
            maxValue = $('#max_'+eleId).val()
            if (maxValue)
                $('#min_'+eleId).attr('max', maxValue)
            $('#min_'+eleId).attr('min', 0)
            $('#max_'+eleId).attr('min', minValue)
            if ($('#min_'+eleId).val() == '' && $('#max_'+eleId).val() != '')
                $('#min_'+eleId).val(0)
            if ($('#min_'+eleId).val() != '' && $('#max_'+eleId).val() == '')
                $('#max_'+eleId).val($('#max_'+eleId).attr('max'))
        }

        function JSON2CSV(boosted_hotels,boosted_sequence,sequence_title,hotel_title) {

            var csvData = '';
            sequence_title  =  sequence_title + '\r\n' ;
            hotel_title = hotel_title + '\r\n' ;
            csvData += sequence_title;

            for(var i = 0;i < boosted_sequence.length;i++){
                   var row = '';
                   for (var index in boosted_sequence[i]) {
                        if(index == 2 && boosted_sequence[i][index - 1] == "NEWHOTELS"){
                            row += seqid_to_dvc[boosted_sequence[i][index] ] + ',';
                        }else{
                            row += boosted_sequence[i][index] + ',';
                        }
                    }
                    row = row.slice(0, -1);
                    csvData += row + '\r\n\n\n';
            }
            csvData += hotel_title;
            for (var i = 0; i < boosted_hotels.length; i++) {
                    var row = '';
                    for (var index in boosted_hotels[i]) {

                          row += boosted_hotels[i][index]  + ',';

                    }

                row = row.slice(0, -1);
                csvData += row + '\r\n';
            }
            return csvData;
        }


        function downloadApi(description, uid ) {

                $.ajax({
                     url: Flask.url_for('triggerDownLoad'),
                     data: {
                        "uid":uid
                     },
                     type: "POST",
                     success: function(response){
                            var csv = JSON2CSV(response.boosted_hotels,response.boosted_sequence,response.boosted_sequence_header, response.boosted_hotels_header);
                            var downloadLink = document.createElement("a");
                            var blob = new Blob([csv]);
                            var url = URL.createObjectURL(blob);
                            downloadLink.href = url;
                            var date = new Date();
                            downloadLink.download = description +"_" + date.getDate() + "-" + String(date.getMonth()+1) + "-" + date.getFullYear()+ ".csv"  ;

                            document.body.appendChild(downloadLink);
                            downloadLink.click();
                            document.body.removeChild(downloadLink);
                     },
                     error: function(e) {
                            console.log(e);
                     }
                });

        }


        function compareAsc(x,y,index){
            if(index == 6 || index == 7){
                return (new Date(x)).getTime() > (new Date(y)).getTime();
            }else if(index == 0){
                return x.toLowerCase() > y.toLowerCase();
            }else {
                 return Number(x) > Number(y);
            }

        }

        function compareDesc(x,y,index){

            if(index == 6 || index == 7){
                return (new Date(x)).getTime() < (new Date(y)).getTime();
            }else if(index == 0){
                return x.toLowerCase() < y.toLowerCase();
            }else {
                 return Number(x) < Number(y);
            }

        }

        function sortTable(column_index) {
              var table, rows, switching, i, x, y, shouldSwitch, dir, switchcount = 0;
              table = document.querySelector("div.booster_table table");
              switching = true;
              rows = table.rows;
              dir = 1 // dir =1 for ascending order sorting dir = 0 for descending order sorting
              while (switching) {
                    switching = false;
                    for (i = 1; i < (rows.length - 1); i++) {
                          shouldSwitch = false;
                          x = rows[i].getElementsByTagName("TD")[column_index];
                          y = rows[i + 1].getElementsByTagName("TD")[column_index];
                          if (column_index == 1 && x.innerHTML == 'all' && y.innerHTML != 'all'){
                                shouldSwitch = true;
                                break;
                          }

                          if(column_index != 1 || (column_index==1 && y.innerHTML != 'all')){
                              if (dir) {
                                  if(compareAsc(x.innerHTML ,y.innerHTML,column_index)){
                                      shouldSwitch= true;
                                      break;
                                  }
                              }else{
                                   if(compareDesc(x.innerHTML ,y.innerHTML,column_index)){
                                      shouldSwitch= true;
                                      break;
                                  }
                              }
                          }
                    }

                    if (shouldSwitch) {
                          rows[i].parentNode.insertBefore(rows[i + 1], rows[i]);
                          switching = true;
                          switchcount ++;
                    }else if(!switchcount){
                          if(!dir)
                            break;
                          switching = true;
                          dir = 0;
                    }
              }
              for(i=0;i <= 7;i++){
                    rows[0].getElementsByTagName("th")[i].childNodes[1].className = 'fa fa-fw fa-sort'
              }
              if(dir == 0){
                    rows[0].getElementsByTagName("th")[column_index].childNodes[1].className = 'fa fa-fw fa-sort-up'
              }else{
                    rows[0].getElementsByTagName("th")[column_index].childNodes[1].className = 'fa fa-fw fa-sort-down'
              }
        }

        function toggleMMBooster() {
            if(document.getElementById("matchmaker_booster").checked == true){
                $('#mm_start_block_text').show()
                $('#mm_start_block_value').show()
                document.getElementById("mm_start_block_value").required = true;
            } else {
                $('#mm_start_block_text').hide()
                $('#mm_start_block_value').hide()
                document.getElementById("mm_start_block_value").required = false;
            }
        }

        function toggleRetireHotels() {
            if(document.getElementById("retire_hotels").checked == true){
                $('#retirement_strategy').show()
                $('#retirement_strategy_value').show()
                document.getElementById("retirement_strategy_value").required = true;
            } else {
                $('#retirement_strategy').hide()
                $('#retirement_strategy_value').hide()
                document.getElementById("retirement_strategy_value").required = false;
            }
        }

        function toggleRotateHotels() {
            if(document.getElementById("rotate_hotels").checked == true){
                $('#bkg_rel_cutoff_txt').show()
                $('#bkg_rel_cutoff_val').show()
                $('#rotate_hotels_corporate_txt').show()
                $('#rotate_hotels_corporate_only').show()
                document.getElementById("bkg_rel_cutoff_val").required = true;
                <!-- Hide automated booster as rotate hotels not supported for automated booster -->
                $('#p_automated_booster').hide()
                document.getElementById("automated_booster").checked = false
            } else {
                $('#bkg_rel_cutoff_txt').hide()
                $('#bkg_rel_cutoff_val').hide()
                $('#rotate_hotels_corporate_txt').hide()
                $('#rotate_hotels_corporate_only').hide()
                document.getElementById("bkg_rel_cutoff_val").required = false;
                <!-- Show automated booster -->
                $('#p_automated_booster').show()
            }
        }

         function toggleAutomatedBooster() {
             if(document.getElementById("automated_booster").checked == true){
                 $('#automated_booster_experiment').show()
                 $('#automated_booster_experiment_value').show()
                 $('#automated_booster_type').show()
                 $('#automated_booster_type_value').show()
                 $('#automated_booster_file_description').show()
                 document.getElementById("automated_booster_experiment").required = true;
                <!-- Hide rotate hotels as rotate hotels not supported for automated booster -->
                 $('#p_rotate_hotels').hide()
                 document.getElementById("rotate_hotels").checked = false
             } else {
                 $('#automated_booster_experiment').hide()
                 $('#automated_booster_experiment_value').hide()
                 $('#automated_booster_type').hide()
                 $('#automated_booster_type_value').hide()
                 $('#automated_booster_file_description').hide()
                 document.getElementById("automated_booster_experiment").required = false;
                <!-- Show rotate hotels -->
                 $('#p_rotate_hotels').show()
             }
         }

        function getBoosterElement(str,boosterTitle){

            var oImg = document.createElement("img");
            oImg.src = "/static/styles/images/" + str
            oImg.style.width = "30px"
            oImg.style.height = "30px"
            oImg.setAttribute('title', boosterTitle)
            oImg.className = "booster"
            return oImg
        }

        function assign(){
            var ul = createElementFromHTML('<ul class="action_list"><li><a href="#" class="lato-bold action_link delete_class">Delete</a></li><li><a href="javascript:void(0);" data-toggle="modal" data-target=".js-append-modal" class="lato-bold action_link append_class">Append</a></li><li><a href="javascript:void(0);" data-toggle="modal" data-target=".js-remove-modal" class="lato-bold action_link remove_class">Remove</a></li><li><a href="javascript:void(0);" data-toggle="modal" data-target=".js-download-modal" class="lato-bold action_link download_class">Download</a></li></ul>')
            var ul_automated_booster = createElementFromHTML('<ul class="action_list"><li><a href="#" class="lato-bold action_link delete_class">Delete</a></li><li><a href="javascript:void(0);" data-toggle="modal" data-target=".js-download-modal" class="lato-bold action_link download_class">Download</a></>li></ul>')
            var ul_master_booster = createElementFromHTML('<ul class="action_list"><li><a href="#" class="lato-bold action_link delete_class">Delete</a></li><li><a href="javascript:void(0);" data-toggle="modal" data-target=".js-download-modal" class="lato-bold action_link download_class">Download</a></li><li><a href="javascript:void(0);" data-toggle="modal" data-target=".js-populate-modal" class="lato-bold action_link populate_class">Delete & Populate</a></li></ul>')

            //empty your table here
            $("div.booster_table").find("tr:gt(0)").remove()
            $('#ap_bucket').multiSelect()
            $('.domain').attr('disabled',true)
            $(document).ready(function () {
                toggleFields();
            });
            if(document.getElementById("switch__input_1").checked == true || document.getElementById("switch__input_2").checked == true) {
                $('#delete_bulk_sequences').hide()
                $('#delete_bulk_sequences_template').hide()
                $('#p_source_seq_id_rdtn').hide()
                $('#p_user_segment_id').show()
                $('#p_check_in_dow').show()
                $('#p_ap_bucket').show()
                $('#p_brand').show()
                $('#p_los').show()
                $('#p_adults').show()
                $('#p_children').show()
                $('#p_max_hotels').hide()
                $('#p_appfest_booster').show()
                $('#p_matchmaker_booster').show()
                $('#p_check_in_date').show()
                $('#p_rotation_time_minutes').hide()
                $('#p_rotate_hotels').show()
                $('#p_retire_hotels').show()
                $('#p_automated_booster').show()
                $('#setup_new_sequence').show()
                $('#sequence_template').show()
                $('#active_sequences_block').show()
                $('#myTable').show()
                toggleMMBooster()
                toggleRotateHotels()
                toggleRetireHotels()
                toggleAutomatedBooster()
                //need to be change in the next release
                $('#p_personalization').hide()
                if(document.getElementById("switch__input_1").checked == true){
                    document.getElementById("mode").value = document.querySelector("label[for='switch__input_1']").innerHTML;
                    $('#dom').prop('checked',true)
                }else{
                    document.getElementById("mode").value = document.querySelector("label[for='switch__input_2']").innerHTML
                    $('#intl').prop('checked',true)

                }
                document.getElementById('max_hotels').value = null
                var percentageBoostType = document.getElementById("percentage_based");
                if( percentageBoostType != null && percentageBoostType.checked){
                    initializePercentageBoosterType();
                }else{
                    initializeSlotBoosterType();
                }
            }
            if(document.getElementById("switch__input_3").checked == true || document.getElementById("switch__input_4").checked == true || document.getElementById("switch__input_6").checked == true) {
                $('#p_source_seq_id_rdtn').show()
                $('#p_source_seq_id_txt').hide()
                $('#p_user_segment_id').hide()
                $('#p_check_in_dow').hide()
                $('#p_ap_bucket').hide()
                $('#p_brand').show()
                $('#p_los').hide()
                $('#p_adults').hide()
                $('#p_children').hide()
                $('#p_max_hotels').hide()
                $('#p_appfest_booster').hide()
                $('#p_matchmaker_booster').hide()
                $('#p_check_in_date').hide()
                $('#delete_bulk_sequences').hide()
                $('#delete_bulk_sequences_template').hide()

                $('#p_rotation_time_minutes').hide()
                $('#p_rotate_hotels').hide()
                $('#p_retire_hotels').hide()
                $('#p_automated_booster').hide()

                $('#p_personalization').hide()
                $('#setup_new_sequence').hide()
                $('#sequence_template').hide()
                $('#active_sequences_block').show()
                $('#myTable').show()

                if (document.getElementById("switch__input_3").checked == true){
                    document.getElementById("mode").value = document.querySelector("label[for='switch__input_3']").innerHTML
                    $('#dom').prop('checked',true)
                }else if(document.getElementById("switch__input_6").checked == true){
                    document.getElementById("mode").value = document.querySelector("label[for='switch__input_6']").innerHTML
                    $('#dom').prop('checked',true)
                }else{
                    document.getElementById("mode").value = document.querySelector("label[for='switch__input_4']").innerHTML
                    $('#intl').prop('checked',true)
                }
                //All the below fields need to be unset as they are not being used in NEWHOTELS

                document.getElementById("mm_start_block_value").required = false;
                document.getElementById('source_seq_id_txt').value = '';
                document.getElementById('user_segment_id').value = '';
                document.getElementById('ignore_child').checked = true;
                document.getElementById('appfest_booster').checked  =false;

                document.getElementById('matchmaker_booster').checked  =false;
                document.getElementById('personalization').checked = false;
                document.getElementById('ignore_dow').checked = true;
                document.getElementById('rotate_hotels').checked  =false;
                var elements = document.getElementById("ap_bucket").options;
                for(var i = 0; i < elements.length; i++){
                    elements[i].selected = false;
                }

                document.getElementById('min_los').value = null;
                document.getElementById('max_los').value = null;
                document.getElementById('min_adults').value = null;
                document.getElementById('max_adults').value = null;
                document.getElementById('ignore_dow').checled = true;
                document.getElementById("min_check_in_date").valueAsDate = null;
                document.getElementById("max_check_in_date").valueAsDate = null;
                document.getElementById('automated_booster').checked = false;

            }if(document.getElementById("switch__input_5").checked == true){
                $('#delete_bulk_sequences').show()
                $('#delete_bulk_sequences_template').show()
                $('#p_source_seq_id_rdtn').hide()
                $('#p_source_seq_id_txt').hide()
                $('#p_user_segment_id').hide()
                $('#p_check_in_dow').hide()
                $('#p_ap_bucket').hide()
                $('#p_brand').hide()
                $('#p_los').hide()
                $('#p_adults').hide()
                $('#p_children').hide()
                $('#p_max_hotels').hide()
                $('#p_appfest_booster').hide()
                $('#p_matchmaker_booster').hide()
                $('#p_check_in_date').hide()
                $('#p_rotation_time_minutes').hide()
                $('#p_rotate_hotels').hide()
                $('#p_retire_hotels').hide()
                $('#p_automated_booster').hide()
                $('#p_personalization').hide()
                $('#setup_new_sequence').hide()
                $('#sequence_template').hide()
                $('#myTable').hide()
                $('#active_sequences_block').hide()
                $('#setup_new_sequence').hide()
            }
            setDateBoundaries()
            setMinMaxBoundary('adults')
            setMinMaxBoundary('los')

            $.ajax({
                 url: Flask.url_for('triggerFetchSequence') + "?mode=" + document.getElementById("mode").value,
                 data: '',
                 success: function(response){
                    document.querySelector("span.badge").innerHTML = response.length
                    var tableBody = document.querySelector("div.booster_table tbody")

                    for(i=0;i<response.length;i++){
                        json = response[i]
                        var tr = document.createElement('TR')
                        tableBody.appendChild(tr);
                        var td = document.createElement('TD')
                        td.appendChild(document.createTextNode(json["brand"]));
                        tr.appendChild(td)
                        var td = document.createElement('TD')
                        let desc =   json["description"];
                        let booster_brand = json["brand"];
                        td.appendChild(document.createTextNode(desc));
                        var boosterDiv = document.createElement('div')
                        const automated_booster_type_list = ["AA"];
                        var automated_booster = automated_booster_type_list.includes(json['automated_booster_type'])

                        if(json['mm_uid']){
                            boosterDiv.appendChild(getBoosterElement("matchmaker.png","MatchMaker Booster"))
                        }
                        if(json['is_personalized']){
                            boosterDiv.appendChild(getBoosterElement("personalization.jpeg","Personalization"))
                        }
                        if(json['priority'] >= 20){
                            boosterDiv.appendChild(getBoosterElement("appFest.png","AppFest Booster"))
                        }
                        if(automated_booster){
                            boosterDiv.appendChild(getBoosterElement("automatedBooster.png","Automated Booster"))
                        }

                        td.appendChild(boosterDiv)
                        tr.appendChild(td)
                        var td = document.createElement('TD')

                        if(mode.value == "NEWHOTELS-DOM" || mode.value == "NEWHOTELS-INTL"){

                            td.appendChild(document.createTextNode(seqid_to_dvc[Number(json["seqid"])]));
                        }else{
                            td.appendChild(document.createTextNode(json["seqid"]));
                        }
                        tr.appendChild(td)
                        var td = document.createElement('TD')
                        td.appendChild(document.createTextNode(json["startblock"]));
                        tr.appendChild(td)
                        var td = document.createElement('TD')
                        td.appendChild(document.createTextNode(json["blocksize"]));
                        tr.appendChild(td)
                        var td = document.createElement('TD')
                        td.appendChild(document.createTextNode(json["nslots"]));
                        tr.appendChild(td)
                        var td = document.createElement('TD')
                        td.appendChild(document.createTextNode(json["maxhotels"]));
                        tr.appendChild(td)
                        var td = document.createElement('TD')
                        td.appendChild(document.createTextNode(json['startdate']));
                        tr.appendChild(td)
                        var td = document.createElement('TD')
                        td.appendChild(document.createTextNode(json['enddate']));
                        tr.appendChild(td)
                        var td = document.createElement('TD')
                        td.appendChild(document.createTextNode(json['booster_type']))
                        tr.appendChild(td)
                        var td = document.createElement('TD')
                        var queryFilter = []
                        if (json['check_in_dow'])
                            queryFilter.push("Check In Day : " + json['check_in_dow'])
                        if (json['ap_bucket'])
                            queryFilter.push("AP Bucket : " + json['ap_bucket'])
                        if (json['min_adults'] || json['max_adults'])
                            queryFilter.push("Adults : " + (json['max_adults'] ? (json['min_adults'] + ' to ' + json['max_adults']) : 'More than ' + json['min_adults']))
                        if (json['children'] != null)
                            queryFilter.push("Children : " + (json['children'] ? 'Yes' : 'No'))
                        if (json['min_los'] || json['max_los'])
                            queryFilter.push("Stay Length : " + (json['max_los'] ? (json['min_los'] + ' to ' + json['max_los']) : 'More than ' + json['min_los']))
                        td.innerHTML = "<p>" + queryFilter.join('<br>') + "</p>"
                        tr.appendChild(td)
                        var cln = ul.cloneNode(true);
                        if (automated_booster) {
                            cln = ul_automated_booster.cloneNode(true);
                        }
                        if(json['master_uid']!== null){
                            cln = ul_master_booster.cloneNode(true);
                        }
                        delete_button = cln.querySelector('a.delete_class')
                        functionString = "deleteApi('" + json['uid'] + "', '" + desc + "','" + json['algorithm'] + "','" + json['seqid'] + "','" + booster_brand + "')"
                        delete_button.setAttribute("onclick", functionString)
                        <!-- Append and Remove are not supported for Automated Booster -->
                        if (!automated_booster && json['master_uid'] === null) {
                           append_button = cln.querySelector('a.append_class')
                           functionString = "appendApi('" + json['uid'] + "', '" + desc + "','" +json['enddate'] + "','" + json['algorithm'] + "','" + json['seqid'] + "','" + json['retire_rotate_signals'] + "','" + booster_brand + "')"
                           append_button.setAttribute("onclick", functionString)
                           remove_button = cln.querySelector('a.remove_class')
                           functionString = "removeApi('" + json['uid'] + "', '" + desc + "','"+json['enddate'] + "','" + json['algorithm'] + "','" + json['seqid'] + "','" + json['retire_rotate_signals'] + "','" + booster_brand + "')"
                           remove_button.setAttribute("onclick", functionString)
                        }
                        download_button = cln.querySelector('a.download_class')
                        functionString = "downloadApi('" + desc + "','" + json['uid'][0] + "')"
                        download_button.setAttribute("onclick",functionString)
                        if(json['master_uid']!== null){
                             populate_button = cln.querySelector('a.populate_class')
                             jsonString = JSON.stringify(json)
                             functionString = "deleteAndPopulateApi("+jsonString+")"
                             populate_button.setAttribute("onclick",functionString)
                        }

                        var td = document.createElement('TD')
                        td.appendChild(cln);

                        tr.appendChild(td)
                    }
                    //your logic for table populated

                  },
                 crossDomain: true,
                 dataType: 'jsonp',
                 jsonpCallback: "jsoncallback"

            });
        }

        function boosterToggle(boosterType) {
            if(boosterType.value == "slot_based") {
                initializeSlotBoosterType();
            }else {
                initializePercentageBoosterType();
            }
        }
         function initializePercentageBoosterType() {
            $('#tag_hotel_index').hide()
            $('#tag_hotel_index').attr('required', false)
            $('#tag_block_size').hide()
            $('#tag_block_size').attr('required', false)
            $('#tag_perturbed_slots').hide()
            $('#tag_perturbed_slots').attr('required', false)
            $('#p_rotate_hotels').hide()
            document.getElementById('rotate_hotels').checked = false;
            $('#tag_default_percentage').show();
            $('#default_percentage').attr('required', true)
            $('#p_automated_booster').hide()
            document.getElementById("automated_booster").checked = false;
         }
         function initializeSlotBoosterType() {
            $('#tag_hotel_index').show()
            $('#tag_hotel_index').attr('required', true)
            $('#tag_block_size').show()
            $('#tag_block_size').attr('required', true)
            $('#tag_perturbed_slots').show()
            $('#tag_perturbed_slots').attr('required', true)
            $('#p_rotate_hotels').show()
            $('#tag_default_percentage').hide()
            $('#default_percentage').attr('required', false);
            $('#p_automated_booster').show()
         }
    </script>
</head>
<body onload="assign()">
<div id="progress" class="waiting">
    <dt></dt>
    <dd></dd>
</div>
<!-- header -->
<div class="ch__innerHeader append_bottom20">
        <div class="ch__mainNavSearchContainer clearfix">
                <img src="/static/styles/images/logo.jpg" alt="logo" class="pull-left">
                <a href="#" class="pull-left">Greenhorn Management Console</a>
                <form action={{url_for("logout")}} method = "GET" style="background-color:none;position:absolute;top:7%;left:97%;"  >
                    <button type="submit" style="background-color:orange;color:white;border: 2px solid green;cursor:pointer;border-radius:15px;width:80px;height:40px">Logout</button>
                 </form>
        </div>

        <div id='alert_header' style='height:30px;background:red;color:white;text-align:center;padding:3px;display:none'>

        </div>


</div>


<!-- /header -->

<!-- Content data -->
<div class="content">
    <div class="container">
        <div class="append_bottom20">
        <!-- toggle buttons -->
        <div class="clearfix append_bottom15">
            <div class="pull-left"><h2>Mode</h2></div>
            <div class="switchBTN"><input id="switch__input_1" class="switchBTN__toogle" name="widgetOption" value="true" type="radio" checked="checked" onchange="assign()"><label for="switch__input_1" class="label_text selected">BOOSTERS-DOM</label></div>
            <div class="switchBTN"><input id="switch__input_2" class="switchBTN__toogle" name="widgetOption" value="false" type="radio" onchange="assign()"><label for="switch__input_2" class="label_text">BOOSTERS-INTL</label></div>
            <div class="switchBTN"><input id="switch__input_3" class="switchBTN__toogle" name="widgetOption" value="false" type="radio" onchange="assign()"><label for="switch__input_3" class="label_text">NEWHOTELS-DOM</label></div>
            <div class="switchBTN"><input id="switch__input_4" class="switchBTN__toogle" name="widgetOption" value="false" type="radio" onchange="assign()"><label for="switch__input_4" class="label_text">NEWHOTELS-INTL</label></div>
            <div class="switchBTN"><input id="switch__input_5" class="switchBTN__toogle" name="widgetOption" value="false" type="radio" onchange="assign()"><label for="switch__input_5" class="label_text">Bulk Ops</label></div>
            <div class="switchBTN"><input id="switch__input_6" class="switchBTN__toogle" name="widgetOption" value="false" type="radio" onchange="assign()"><label for="switch__input_6" class="label_text">AUTO</label></div>

        </div>
        <!-- /toggle buttons -->
        <!-- section heading -->
        <div class="section_heading" id="active_sequences_block">
            <h2><a href="javascript:void(0);" class="collapsed"><span class="arrow"></span> Active Sequences <span class="badge hide"></span></a></h2>
        </div>
        <!-- /section heading -->

        <!-- AC booster content -->
        <div class="booster_table section_data hide">
            <table width="100%" cellspacing="0" cellpadding="0" id="myTable" >
                <thead>
                <tr class="table_heading lato-bold">
                    <th width="6%">Brand <i class="fa fa-fw"></i></th>
                    <th width="10%" onclick="sortTable(0)" id = "desc">Description <i class="fa fa-fw fa-sort"></i></th>
                    <th width="6%" onclick="sortTable(1)">Rank Order <i class="fa fa-fw fa-sort"></i></th>
                    <th width="6%" onclick="sortTable(2)">Starting Block <i class="fa fa-fw fa-sort"></i></th>
                    <th width="6%" onclick="sortTable(3)">Block Size <i class="fa fa-fw fa-sort"></i></th>
                    <th width="6%" onclick="sortTable(4)">Slots per block <i class="fa fa-fw fa-sort"></i></th>
                    <th width="8%" onclick="sortTable(5)">Max hotels perturbed <i class="fa fa-fw fa-sort"></i></th>
                    <th width="10%" onclick="sortTable(6)">Start Date <i class="fa fa-fw fa-sort"></i></th>
                    <th width="8%" onclick="sortTable(7)">End Date <i class="fa fa-fw fa-sort"></i></th>
                    <th width="8%" >Booster Type <i class="fa fa-fw fa-sort"></i></th>
                    <th width="20%">Query Filters</th>
                    <th width="16%">Action</th>
                  </tr>
                </thead>
                <tbody>

                </tbody>
            </table>
        </div>
        </div>
        <!-- /AC booster content -->


        <!-- section heading -->
        <div class="section_heading clearfix" id = "setup_new_sequence">
            <h2><a href="javascript:void(0);"><span class="arrow"></span> Setup New Sequence </a></h2>
        </div>
        <!-- /section heading -->

        <!-- setup new booster -->
        <div class="section_data append_bottom20" id = "sequence_template">
            <div class="card">
                <form id='add_form' action={{url_for("triggerUploader")}} method="post" enctype = "multipart/form-data" name="form_addBooster" class="form_data">
                    <fieldset>
                        <input type="hidden" id="mode" value="" name="mode">
                        <p class="clearfix append_bottom10" id="p_brand">
                            <label for="brand">Brand</label>
                            <select id="Brand" name="brand">
                              <option>MMT</option>
                              <option>GI</option>
                            </select>
                        </p>
                        <p class="clearfix append_bottom10">
                            <label for="description" accesskey="B">Description</label>
                            <input type="text" required id="description" name="description"/>
                        </p>

                        <p class="clearfix append_bottom10" id="p_selection">
                            <label for="selection">Select Type</label>
                            <select id="selection" name="selection" onchange="toggleFields()">
                                <option value="rank_order">Rank Order</option>
                                <option value="cohort">Cohort</option>
                            </select>
                        </p>

                        <p class="clearfix append_bottom10" id="p_cohort" style="display:none;">
                            <label for="cohort">Cohort</label>
                        <div id="selected_lobs" class="dropzone" style="display: none;">
                            <p>Drop selected items here</p>
                        </div>
                        <div id="available_lobs" style="display: none;">
                            <div class="draggable" draggable="true" id="CORP">CORP</div>
                            <div class="draggable" draggable="true" id="HOMESTAY">HOMESTAY</div>
                            <div class="draggable" draggable="true" id="HOMESTAY_PREMIUM">HOMESTAY_PREMIUM</div>
                            <div class="draggable" draggable="true" id="DAYUSE">DAYUSE</div>
                            <div class="draggable" draggable="true" id="AFFILIATES">AFFILIATES</div>
                            <div class="draggable" draggable="true" id="SEM_GCC">SEM_GCC</div>
                            <div class="draggable" draggable="true" id="SEM_NON_GCC">SEM_NON_GCC</div>
                            <div class="draggable" draggable="true" id="GCC">GCC</div>
                            <div class="draggable" draggable="true" id="SEO_GCC">SEO_GCC</div>
                            <div class="draggable" draggable="true" id="SEO_NON_GCC">SEO_NON_GCC</div>
                            <div class="draggable" draggable="true" id="MYPARTNER">MYPARTNER</div>
                            <div class="draggable" draggable="true" id="B2C_Premium">B2C_Premium</div>
                            <div class="draggable" draggable="true" id="INBOUND_B2C">INBOUND_B2C</div>
                            <div class="draggable" draggable="true" id="B2C">B2C</div>
                        </div>
                        </p>

                        <p class="clearfix append_bottom10" id="p_source_seq_id_txt">
                            <label for="source_seq_id_txt" accesskey="D">Rank Order</label>
                            <input type="text"  pattern = "([aA][lL][lL]|[0-9,]*)" id="source_seq_id_txt" name="source_seq_id_txt"  />
                        </p>

                        <p class="clearfix append_bottom10" id="p_user_segment_id">
                            <label for="user_segment_id" accesskey="D">User Segments</label>
                            <input type="text" id="user_segment_id" name="user_segments"  />
                        </p>

                        <p class="clearfix append_bottom10" id = "p_appfest_booster">
                            <label for="appfest_booster">APPFest Booster</label>
                            <input type="checkbox" id="appfest_booster" name="appfest_booster"/>
                        </p>


                        <p class="clearfix append_bottom10" id = "p_matchmaker_booster">
                            <label for="matchmaker_booster">Matchmaker Booster</label>
                            <input type="checkbox" id="matchmaker_booster" onchange="toggleMMBooster()" name="matchmaker_booster"/>
                            <label style="margin-left:70px;" id="mm_start_block_text">Start MM Index</label>
                            <input style="width: 150px;margin-left:-80px;" type="text" id="mm_start_block_value" name="mm_start_block_value" class="number-only" >
                        </p>


                        <p class="clearfix append_bottom10" id = "p_personalization">
                            <label for="appfest_booster">Personalization</label>
                            <input type="checkbox" id="personalization" name="personalization"/>
                        </p>


                        <p class="clearfix append_bottom10" id="p_source_seq_id_rdtn">
                            <label for="source_seq_id_rdbtn">Rank Order</label>
                            <input type="radio" id="IOS" name="source_seq_id_rdbtn" value="01"> <label for="IOS">IOS</label>
                            <input type="radio" id="ANDROID" name="source_seq_id_rdbtn" value="02"> <label for="ANDROID">ANDROID</label>
                            <input type="radio" id="WEB" name="source_seq_id_rdbtn" value="00" checked="true"> <label for="WEB">WEB</label>
                            <input type="radio" id="PWA" name="source_seq_id_rdbtn" value="03"> <label for="PWA">PWA</label>
                        </p>

                        <p class="clearfix append_bottom10" id = "booster_type">
                            <label for="booster_type">Booster Type</label>
                            <input type="radio" id="slot_based" name="booster_type" value="slot_based" checked="true" onclick="boosterToggle(this);" > <label for="slot_based">Slot</label>
                            <input type="radio" id="percentage_based" name="booster_type" value="percentage_based" onclick="boosterToggle(this);"> <label for="percentage_based">Percentage</label>
                        </p>

                        <p class="clearfix append_bottom10" id="p_dom_id">
                            <label for="dom_id">Market</label>
                            <input type="radio" id="dom" name="domain" value="DOM" class = "domain" checked="true"> <label for="DOMESTIC">DOM</label>
                            <input type="radio" id="intl" name="domain" class = "domain" value="INTL"> <label for="INTERNATIONAL">INTL</label>
                        </p>

                        <p class="clearfix append_bottom10" id="p_lobs">
                            <label for="lobs">Lobs</label>
                            <select id="lobs" name="lobs">
                            </select>
                        </p>

                        <p class="clearfix append_bottom10" id="tag_hotel_index">
                            <label for="start_block" accesskey="R">Starting Hotel Index</label>
                            <input type="text"  class="number-only" id="start_block" name ="start_block"/>
                        </p>
                        <p class="clearfix append_bottom10" id="tag_block_size">
                            <label for="block_size" accesskey="S">Block Size</label>
                            <input type="text"  class="number-only" id="block_size" name="block_size"/>
                        </p>
                        <p class="clearfix append_bottom10" id="tag_perturbed_slots">
                            <label for="nslots" accesskey="P">Perturbed Slots per block</label>
                            <input type="text"  class="number-only" id="nslots" name="nslots" />
                        </p>

                        <p class="clearfix append_bottom10" id="tag_default_percentage">
                            <label for="default_percentage">Default Percentage</label>
                            <input type="number"  id="default_percentage" name="def_percentage" />
                        </p>

                        <p class="clearfix append_bottom10" id="p_ap_bucket">
                            <label for="ap_bucket">AP Bucket</label>
                            <select multiple="true" id="ap_bucket" name="ap_bucket">
                              <option value="0-0">0</option>
                              <option value="1-1">1</option>
                              <option value="2-2">2</option>
                              <option value="3-7">3-7</option>
                              <option value="8-15">8-15</option>
                              <option value="16-30">16-30</option>
                              <option value="30-3000">30+</option>
                            </select>
                        </p>


                        <p class="clearfix append_bottom10" id="p_max_hotels">
                            <label for="max_hotels" accesskey="M">Max hotels perturbed</label>
                            <input type="text" class="number-only" id="max_hotels" name="max_hotels"/>
                        </p>
                        <p class="clearfix append_bottom10" id="p_check_in_dow">
                            <label for="check_in_dow">Check In Day of Week</label>
                            <input type="radio" id="weekend" name="check_in_dow" value="weekend"> <label for="weekend">Week End</label>
                            <input type="radio" id="weekday" name="check_in_dow" value="weekday"> <label for="weekday">Week Day</label>
                            <input type="radio" id="ignore_dow" name="check_in_dow" value="ignore_dow" checked="true"> <label for="ignore_dow">Ignore</label>
                        </p>

                        <p class="clearfix append_bottom10" id = "p_check_in_date">
                            <label for="check_in_date" >Check-In Date</label>
                            <input type="date" id="min_check_in_date" name="min_check_in_date" onchange="setDateBoundaries()" /> to
                            <input type="date" id="max_check_in_date" name="max_check_in_date" onchange="setDateBoundaries()" />
                        </p>

                        <p class="clearfix append_bottom10" id = "p_los">
                            <label for="los">Length of Stay</label>
                            <input type="number" id="min_los" name="los_min" max = 10 min = 1 onchange="setMinMaxBoundary('los', 'min_los')"> to
                            <input type="number" id="max_los" name="los_max" max = 10 min = 1 onchange="setMinMaxBoundary('los', 'max_los')">
                        </p>
                        <p class="clearfix append_bottom10" id = "p_adults">
                            <label for="adults">No. of adults</label>
                            <input type="number" id="min_adults" name="min_adults" max = 100 min = 1 onchange="setMinMaxBoundary('adults', 'min_adults')"> to
                            <input type="number" id="max_adults" name="max_adults" max = 100 min = 1 onchange="setMinMaxBoundary('adults', 'max_adults')">
                        </p>
                        <p class="clearfix append_bottom10" id = "p_children">
                            <label for="children">Children</label>
                            <input type="radio" id="no_child" name="children" value="no_child"> <label for="no_child">No</label>
                            <input type="radio" id="yes_child" name="children" value="yes_child"> <label for="yes_child">Yes</label>
                            <input type="radio" id="ignore_child" name="children" value="ignore_child" checked="true"> <label for="ignore_child">Ignore</label>
                        </p>



                        <p class="clearfix append_bottom10">
                            <label for="start_date" accesskey="M">Starting Date</label>
                            <input type="date" required id="start_date" name="start_date" onchange="setDateBoundaries()"/>
                        </p>
                        <p class="clearfix append_bottom10">
                            <label for="end_date" accesskey="M">Ending Date</label>
                            <input type="date" required id="end_date" name="end_date" onchange="setDateBoundaries()" />
                        </p>

                        <p class="clearfix append_bottom10" id = "p_rotate_hotels">
                            <label for="rotate_hotels">Rotate Hotels</label>
                            <input type="checkbox" id="rotate_hotels" onchange="toggleRotateHotels()" name="rotate_hotels"/>
                            <label style="margin-left:70px;" id="bkg_rel_cutoff_txt">Relative Booking Cutoff</label>
                            <input style="width: 150px;margin-left:-20px;" type="number" id="bkg_rel_cutoff_val" name="bkg_rel_cutoff_val" value="0.3" step="0.01" min="0.01" max="1">
		            <label style="margin-left: 70px; display: inline-block;" id="rotate_hotels_corporate_txt">Corporate only</label>
		            <input type="checkbox" id="rotate_hotels_corporate_only" name="rotate_hotels_corporate_only">
                        </p>

                        <p class="clearfix append_bottom10" id="p_rotation_time_minutes">
                            <label for="rotation_time_minutes" accesskey="M">Rotation Time (In Minutes)</label>
                            <input type="number" id="rotation_time_minutes" name="rotation_time_minutes" min="1"/>
                        </p>

                        <p class="clearfix append_bottom10" id="p_retire_hotels">
                           <label for="retire_hotels">Retire Hotels</label>
                           <input type="checkbox" id="retire_hotels" onchange="toggleRetireHotels()"
                              name="retire_hotels"/>
                           <label style="margin-left:70px;" id="retirement_strategy">Retirement Strategy</label>
                           <select style="width: 150px;margin-left:-20px;" id="retirement_strategy_value"
                              name="retirement_strategy_value">
                              <option value="Impression And Conversion">Impression And Conversion</option>
                           </select>
                        </p>

                        <p class="clearfix append_bottom10" id="p_automated_booster">
                           <label for="automated_booster">Automate Booster</label>
                           <input type="checkbox" id="automated_booster" onchange="toggleAutomatedBooster()"
                              name="automated_booster"/>
                           <label style="margin-left:40px;" id="automated_booster_experiment">Experiment</label>
                           <input style="width: 150px;margin-left:-100px;" type="number" id="automated_booster_experiment_value"
                              name="automated_booster_experiment_value" value=119 step=0 min=1 max=150>
                           <label style="margin-left:40px;" id="automated_booster_type">Type</label>
                           <select style="width: 150px;margin-left:-150px;" id="automated_booster_type_value"
                              name="automated_booster_type_value">
                              <option value="AA">AA</option>
                           </select>
                           <label style="margin-left:40px;" id="automated_booster_file_description">* File expects Location Ids</label>
                        </p>
                        <p class="clearfix append_bottom10">
                            <label for="pokus_exp" >Pokus Experiment Driven</label>
                            <input type="text" id="pokus_exp" name="pokus_exp"/>
                        </p>

                        <input type="hidden" id="action" value="ADD" name="action">
                        <p class="clearfix append_bottom25">
                            <label for="upload_file" accesskey="U">Upload files</label>

                            <!-- upload button -->
                            <span class="upload_file_wrap make-relative">
                                <a href="#" class="font12 flL light-black secondry_btn">Select File</a>
                                <input type="file" id="upload_file" required name="city_hotels" onChange="getFileName(this,'upload_file_id');">
                                <span id="upload_file_id"></span>
                            </span>
                            <!-- /upload button -->

                            <!-- Download button-->
                            <a href="/downloadSampleFile" download="sampleFile.csv" class="font12 flL light-black secondry_btn">Download Sample file</a>
                            <!-- Download button-->
                        </p>

                        <div class="clearfix">
                            <label for="upload_files"></label>
                            <input type="submit" class="primary_btn" value="Submit" />
                        </div>

                    </fieldset>
                </form>
            </div>
        </div>
        <!-- /setup new booster -->

        <!-- section heading -->
        <div class="section_heading clearfix" id = "delete_bulk_sequences" >
            <h2><a href="javascript:void(0);"><span class="arrow"></span> Delete Sequences </a></h2>
        </div>
        <!-- /section heading -->

         <!-- delete boosters in bulk -->
        <div class="section_data append_bottom20" id = "delete_bulk_sequences_template" style="display: none;" >
            <div class="card">
                <form id='delete_bulk_form' action={{url_for("triggerBulkDelete")}} method="post" enctype = "multipart/form-data" name="form_bulkDeleteBoosters" class="form_data">
                    <fieldset>
                        <p class="clearfix append_bottom25">
                            <label for="bulk_delete_file_id" accesskey="U">Upload File</label>

                            <!-- upload button -->
                            <span class="bulk_delete_file_wrap make-relative">
                                <a href="#" class="font12 flL light-black secondry_btn">Select File</a>
                                <input type="file" id="bulk_delete_file_id" required name="bulk_delete_descriptions" onChange="getFileName(this,'bulk_delete_file');">
                                <span id="bulk_delete_file"></span>
                            </span>
                            <!-- /upload button -->
                        </p>

                        <div class="clearfix">
                            <label for="bulk_delete_descriptions"></label>
                            <input type="submit" class="primary_btn" value="Submit" />
                        </div>

                    </fieldset>
                </form>
            </div>
        </div>
        <!-- /setup new booster -->

    </div>

    <!-- modal append -->
    <div class="modal fade js-append-modal" tabindex="-1" role="dialog" aria-hidden="true">
      <div class="modal-dialog modal-lg">
        <div class="modal-content gh-modalcontents">
            <a class="close" data-dismiss="modal" aria-label="Close">
              <span class="o-cross-symbol text-center append_cross" aria-hidden="true"> &#10005;</span>
            </a>
            <div class="modal__head lato-black">
                <h3 id='append-booster-heading'>Append</h3>
            </div>

            <!-- form -->
            <form id='append_form' class="form_data" action={{url_for("triggerAppend")}} method="post" enctype = "multipart/form-data" name="append_form">
                <fieldset>

                        <input type="hidden" id="uid_append" value="" name="uid">
                        <input type="hidden" id="enddate_append" value="" name="end_date">
                        <input type="hidden" id="algorithm_append" value="" name="algorithm">
                        <input type="hidden" id="seqid_append" value="" name="seqid">
                        <input type="hidden" id="retire_rotate_signals_append" value="" name="retire_rotate_signals">
                        <input type="hidden" id="booster_brand_append" value="" name="brand">


                        <p class="clearfix append_bottom25">
                            <label for="upload_file_2" accesskey="U">Upload files</label>

                            <!-- upload button -->
                            <span class="upload_file_wrap make-relative">
                                <a href="#" class="font12 flL light-black secondry_btn">Upload File</a>
                                <input type="file" id="upload_file_2" name="city_hotels" onChange="getFileName(this,'upload_file_id_2');">
                                <span id="upload_file_id_2"></span>
                            </span>
                            <!-- /upload button -->
                        </p>

                        <div class="clearfix">
                            <label for="upload_files"></label>
                            <input type="submit" class="primary_btn" value="Submit"/>
                        </div>

                    </fieldset>
                </form>
            <!-- /form -->
        </div>
      </div>
    </div>
    <!-- /modal append-->


    <!-- modal remove -->
    <div class="modal fade js-remove-modal" tabindex="-1" role="dialog" aria-hidden="true">
      <div class="modal-dialog modal-lg">
        <div class="modal-content gh-modalcontents">
            <a class="close" data-dismiss="modal" aria-label="Close">
              <span class="o-cross-symbol text-center remove_cross" aria-hidden="true"> &#10005;</span>
            </a>
            <div class="modal__head lato-black">
                <h3 id = 'remove-booster-heading'>Remove</h3>
            </div>

            <!-- form -->
            <form id="remove_form" class="form_data" action={{url_for("triggerRemove")}} method="post" enctype = "multipart/form-data" name="remove_form">
                <fieldset>
                        <input type="hidden" id="uid_remove" value="" name="uid">


                        <input type="hidden" required id="enddate_remove" name="end_date"  />
                        <input type="hidden" id="algorithm_remove" value="" name="algorithm">
                        <input type="hidden" id="seqid_remove" value="" name="seqid">
                        <input type="hidden" id="retire_rotate_signals_remove" value="" name="retire_rotate_signals">
                        <input type="hidden" id="booster_brand_remove" value="" name="brand">


                        <p class="clearfix append_bottom25">
                            <label for="upload_file_1" accesskey="U">Upload files</label>

                            <!-- upload button -->
                            <span class="upload_file_wrap make-relative">
                                <a href="#" class="font12 flL light-black secondry_btn">Upload File</a>
                                <input type="file" id="upload_file_1" name="city_hotels" onChange="getFileName(this,'upload_file_id_1');">
                                <span id="upload_file_id_1"></span>
                            </span>
                            <!-- /upload button -->
                        </p>

                        <div class="clearfix">
                            <label for="upload_files"></label>
                            <input type="submit" class="primary_btn" value="Submit" />
                        </div>

                    </fieldset>
                </form>
            <!-- /form -->
        </div>
      </div>
    </div>
    <!-- /modal remove-->


    <!-- csv upload success failure message -->
    <div class="modal fade js-message-modal" tabindex="-1" role="dialog" aria-hidden="true">
      <div class="modal-dialog modal-lg">
        <div class="modal-content gh-modalcontents">
            <a class="close" data-dismiss="modal" aria-label="Close">
              <span class="o-cross-symbol text-center remove_cross" aria-hidden="true"> &#10005;</span>
            </a>
            <div class="modal__head lato-black">
                <h3 id="message-heading">Remove</h3>
            </div>

            <p id = "message-text">

            </p>
        </div>
      </div>
    </div>
    <!-- /modal remove-->


</div>
<!-- /Content data -->

<script type="text/javascript" src="/static/styles/js/custom.js"></script>

</body>
</html>

