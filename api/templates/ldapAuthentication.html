<!DOCTYPE html>
<html>
<head>
<meta name="viewport" content="width=device-width, initial-scale=1">
<style>
body {font-family: Arial, Helvetica, sans-serif;}
form {border: 3px solid #f1f1f1;}

input[type=text], input[type=password] {
    width: 100%;
    padding: 12px 20px;
    margin: 8px 0;
    display: inline-block;
    border: 1px solid #ccc;
    box-sizing: border-box;
}

button {
    background-color: #4CAF50;
    color: white;
    padding: 14px 20px;
    margin: 8px 40%;
    border: none;
    cursor: pointer;
    width: 20%;
}

button:hover {
    opacity: 0.8;
}

.cancelbtn {
    width: auto;
    padding: 10px 18px;
    background-color: #f44336;
}


.container {
    padding: 16px;
}

span.psw {
    float: right;
    padding-top: 16px;
}

/* Change styles for span and cancel button on extra small screens */
@media screen and (max-width: 300px) {
    span.psw {
       display: block;
       float: none;
    }
    .cancelbtn {
       width: 100%;
    }
}
</style>
</head>
<body>

<h2>Login Form</h2>

<form action={{url_for("authentication")}} method = "POST">

  <div class="container">

    <label for="domain"><b>Domain</b></label>
    <p>  </p>
    <input type="radio" id="MMT" name="domain" value="MMT" checked="true" onclick="boosterToggle(this);" >
    <label for="MMT">MMT</label>
    <input type="radio" id="GI" name="domain" value="GI" onclick="boosterToggle(this);">
    <label for="GI">GI</label>
    <p>  </p>

    <label for="username"><b>Username</b></label>
    <input type="text" placeholder="Enter Username" name="username" required>

    <label for="password"><b>Password</b></label>
    <input type="password" placeholder="Enter Password" name="password" required>
    <p>  </p>
    <p>  </p>
    <div style="color:red"> {{error}} </div>
    <button type="submit">Login</button>
  </div>
</form>



</body>
</html>