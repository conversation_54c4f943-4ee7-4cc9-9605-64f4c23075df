<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg>
<metadata>
Created by FontForge 20120731 at Thu Sep  1 14:02:24 2016
 By www-data
Copyright (c) 2010-2011 by tyPoland Lukasz Dziedzic with Reserved Font Name "Lato". Licensed under the SIL Open Font License, Version 1.1.
</metadata>
<defs>
<font id="Lato-Bold" horiz-adv-x="1160" >
  <font-face 
    font-family="Lato"
    font-weight="700"
    font-stretch="normal"
    units-per-em="2000"
    panose-1="2 15 8 2 2 2 4 3 2 3"
    ascent="1610"
    descent="-390"
    x-height="1026"
    cap-height="1446"
    bbox="-177 -361 2286 1874"
    underline-thickness="160"
    underline-position="-40"
    unicode-range="U+000D-FB02"
  />
<missing-glyph horiz-adv-x="1094" 
d="M239 1147q57 46 129.5 77.5t171.5 31.5q70 0 126 -19t95 -53.5t59.5 -83t20.5 -107.5q0 -55 -14 -94.5t-35 -69t-46 -50.5t-48 -39.5t-40.5 -36.5t-22.5 -40l-22 -96h-169l-17 114q-6 37 5.5 63.5t32 48t46 40t48 39t38 46t15.5 60.5t-24 57t-70 22q-36 0 -60.5 -7
t-42.5 -15t-31.5 -15t-28.5 -7q-34 0 -50 29zM385 313q0 28 10.5 53t28.5 43.5t43 29t54 10.5q28 0 52.5 -10.5t43 -29t29 -43.5t10.5 -53q0 -29 -10.5 -53.5t-29 -42.5t-43 -28.5t-52.5 -10.5q-29 0 -54 10.5t-43 28.5t-28.5 42.5t-10.5 53.5zM42 1446h1010v-1446h-1010
v1446zM107 69h873v1309h-873v-1309z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="1202" 
d="M176 0v840l-89 14q-29 5 -46.5 20t-17.5 42v101h153v33q0 94 31 174.5t94 139.5t159 92.5t227 33.5q42 0 86 -4.5t76 -14.5l-8 -128q-2 -23 -25 -27.5t-57 -4.5q-97 0 -162.5 -17t-106 -50.5t-58 -84t-17.5 -117.5v-25h636v-1017h-247v841h-381v-841h-247z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="1234" 
d="M176 0v840l-89 14q-29 5 -46.5 20t-17.5 42v101h153v43q0 82 25.5 157t78.5 132.5t134 92t192 34.5q91 0 172.5 -6t162.5 -6h139v-1464h-246v1292q-53 2 -103.5 5t-85.5 3q-112 0 -171 -62.5t-59 -177.5v-43h243v-176h-235v-841h-247z" />
    <glyph glyph-name=".notdef" horiz-adv-x="1094" 
d="M239 1147q57 46 129.5 77.5t171.5 31.5q70 0 126 -19t95 -53.5t59.5 -83t20.5 -107.5q0 -55 -14 -94.5t-35 -69t-46 -50.5t-48 -39.5t-40.5 -36.5t-22.5 -40l-22 -96h-169l-17 114q-6 37 5.5 63.5t32 48t46 40t48 39t38 46t15.5 60.5t-24 57t-70 22q-36 0 -60.5 -7
t-42.5 -15t-31.5 -15t-28.5 -7q-34 0 -50 29zM385 313q0 28 10.5 53t28.5 43.5t43 29t54 10.5q28 0 52.5 -10.5t43 -29t29 -43.5t10.5 -53q0 -29 -10.5 -53.5t-29 -42.5t-43 -28.5t-52.5 -10.5q-29 0 -54 10.5t-43 28.5t-28.5 42.5t-10.5 53.5zM42 1446h1010v-1446h-1010
v1446zM107 69h873v1309h-873v-1309z" />
    <glyph glyph-name="NULL" horiz-adv-x="0" 
 />
    <glyph glyph-name="NULL" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" unicode="&#xd;" horiz-adv-x="0" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="386" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="721" 
d="M480 1446v-572q0 -91 -9 -178.5t-24 -185.5h-167q-15 98 -24 185.5t-9 178.5v572h233zM209 136q0 31 11.5 59t32 48t48.5 32t60 12q31 0 59 -12t48 -32t32 -48t12 -59q0 -32 -12 -59.5t-32 -47.5t-48 -31.5t-59 -11.5q-32 0 -60 11.5t-48.5 31.5t-32 47.5t-11.5 59.5z
" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="844" 
d="M346 1446v-288l-23 -156q-7 -44 -25.5 -67.5t-60.5 -23.5q-36 0 -57 23.5t-27 67.5l-22 156v288h215zM714 1446v-288l-23 -156q-7 -44 -25.5 -67.5t-60.5 -23.5q-36 0 -57 23.5t-27 67.5l-22 156v288h215z" />
    <glyph glyph-name="numbersign" unicode="#" 
d="M831 408l-77 -408h-113q-30 0 -52 24t-22 59q0 5 0.5 9.5t1.5 9.5l59 306h-205l-58 -318q-9 -48 -41 -69t-71 -21h-109l76 408h-115q-32 0 -49 15.5t-17 51.5q0 14 3 32l13 79h190l52 276h-214l19 103q7 38 31 56.5t78 18.5h111l62 322q8 40 37 62t68 22h112l-76 -406
h204l77 406h110q35 0 57 -20t22 -51q0 -10 -1 -15l-62 -320h203l-19 -103q-7 -38 -31.5 -56.5t-77.5 -18.5h-100l-51 -276h142q32 0 48.5 -15.5t16.5 -52.5q0 -14 -3 -32l-12 -78h-217zM448 586h205l51 276h-204z" />
    <glyph glyph-name="dollar" unicode="$" 
d="M463 -9q-117 16 -219.5 64t-174.5 119l76 113q10 15 26.5 24.5t34.5 9.5q22 0 47 -15.5t57 -36.5t73 -42t97 -31l38 448q-71 20 -140.5 47t-125 71.5t-89.5 112.5t-34 170q0 76 30.5 148.5t88.5 129.5t143 94t195 42l11 126q2 24 20 43t47 19h91l-17 -197
q106 -17 183 -60t134 -97l-60 -91q-14 -20 -28 -30t-34 -10q-15 0 -35.5 9.5t-47 23t-58.5 27.5t-71 23l-35 -414q72 -22 142.5 -48.5t127 -68.5t91.5 -104.5t35 -156.5q0 -93 -31 -174.5t-90.5 -144t-146.5 -101.5t-199 -47l-12 -147q-2 -23 -20 -42.5t-47 -19.5h-91z
M848 415q0 38 -13.5 66.5t-38 50.5t-57.5 38t-72 29l-34 -406q106 14 160.5 71.5t54.5 150.5zM362 1069q0 -38 13 -67t36.5 -51.5t55.5 -40t70 -31.5l31 370q-55 -7 -94 -23.5t-64 -40.5t-36.5 -53.5t-11.5 -62.5z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="1606" 
d="M729 1096q0 -82 -27.5 -148t-73.5 -112.5t-107 -71.5t-127 -25q-72 0 -133 25t-105.5 71.5t-69.5 112.5t-25 148q0 84 25 152t69.5 115t105.5 72.5t133 25.5t133.5 -25.5t106.5 -72.5t70 -115t25 -152zM538 1096q0 58 -11 97t-30.5 63t-46 34.5t-56.5 10.5t-56 -10.5
t-45 -34.5t-29.5 -63t-10.5 -97q0 -56 10.5 -93.5t29.5 -60.5t45 -33t56 -10t56.5 10t46 33t30.5 60.5t11 93.5zM1210 1407q12 15 30 27t50 12h179l-1074 -1409q-12 -16 -30.5 -26.5t-44.5 -10.5h-184zM1545 340q0 -82 -27.5 -148t-73.5 -112.5t-107 -72t-127 -25.5
q-72 0 -133 25.5t-105.5 72t-69.5 112.5t-25 148q0 84 25 152t69.5 115t105.5 72.5t133 25.5t133.5 -25.5t106.5 -72.5t70 -115t25 -152zM1355 340q0 58 -11.5 97t-31 63t-46 34.5t-56.5 10.5t-56 -10.5t-44.5 -34.5t-29.5 -63t-11 -97q0 -56 11 -94t29.5 -61t44.5 -33
t56 -10t56.5 10t46 33t31 61t11.5 94z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="1428" 
d="M663 1462q88 0 157.5 -27t119 -72t78 -103t33.5 -120l-157 -33q-3 -1 -6.5 -1h-6.5q-17 0 -30.5 9.5t-21.5 29.5q-9 26 -23 49.5t-34 41t-47 28t-62 10.5q-42 0 -75 -14.5t-56 -39t-35 -57t-12 -68.5q0 -30 6.5 -58t20.5 -56t36.5 -57.5t55.5 -63.5l384 -399
q33 63 53 133t26 142q2 23 15 37t36 14h155q-1 -132 -38 -252t-106 -221l303 -314h-242q-19 0 -33.5 2t-27.5 7.5t-25 14.5t-25 23l-100 103q-96 -78 -213.5 -122t-254.5 -44q-84 0 -164 28.5t-143 82.5t-101.5 130.5t-38.5 172.5q0 67 22 127.5t61 112.5t92.5 94t117.5 72
q-53 75 -77.5 146.5t-24.5 142.5q0 75 27.5 142.5t80 117.5t128.5 79.5t172 29.5zM315 418q0 -55 19 -99t52 -74.5t76.5 -47t92.5 -16.5q88 0 160.5 27.5t132.5 76.5l-380 390q-80 -52 -116.5 -117t-36.5 -140z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="476" 
d="M346 1446v-288l-23 -156q-7 -44 -25.5 -67.5t-60.5 -23.5q-36 0 -57 23.5t-27 67.5l-22 156v288h215z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="600" 
d="M320 627q0 -206 50 -405t143 -374q7 -13 9.5 -24t2.5 -20q0 -20 -10 -32.5t-24 -20.5l-110 -67q-74 114 -126 229t-85 232t-48.5 237t-15.5 245t15.5 245.5t48.5 237.5t85 231.5t126 228.5l110 -66q14 -8 24 -20.5t10 -31.5t-13 -45q-92 -175 -142 -374.5t-50 -405.5z
" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="600" 
d="M280 627q0 206 -50 405.5t-142 374.5q-13 26 -13 45t10 31.5t24 20.5l110 66q74 -114 126 -228.5t85 -231.5t48.5 -237.5t15.5 -245.5t-15.5 -245t-48.5 -237t-85 -232t-126 -229l-110 67q-14 8 -24 20.5t-10 32.5q0 9 2.5 20t9.5 24q93 175 143 374t50 405z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="800" 
d="M340 837v183q0 20 2.5 39t8.5 36q-11 -14 -26 -25t-32 -22l-158 -92l-59 100l159 92q18 11 35.5 18t36.5 10q-19 2 -36.5 10t-35.5 19l-159 93l58 100l159 -94q17 -11 32.5 -22.5t26.5 -25.5q-7 17 -9.5 36t-2.5 39v185h118v-183q0 -21 -2.5 -40.5t-9.5 -36.5
q11 14 26 25.5t33 22.5l158 92l59 -100l-159 -91q-18 -11 -35.5 -18.5t-36.5 -10.5q34 -6 72 -28l159 -93l-58 -100l-159 93q-18 11 -33 22t-27 25q13 -32 13 -74v-184h-118z" />
    <glyph glyph-name="plus" unicode="+" 
d="M678 1173v-410h389v-183h-389v-412h-200v412h-387v183h387v410h200z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="450" 
d="M80 152q0 28 10.5 52.5t29.5 43t46 29t59 10.5q38 0 66 -13.5t47 -37t28 -54t9 -64.5q0 -48 -14.5 -101t-42.5 -106t-69 -103t-94 -92l-43 39q-18 15 -18 37q0 8 5.5 18t13.5 18q11 12 27 29.5t32 40t29.5 49t19.5 56.5q-31 0 -57 11.5t-44.5 31.5t-29 47t-10.5 59z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="718" 
d="M100 707h518v-206h-518v206z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="450" 
d="M73 136q0 31 11.5 59t32 48t48.5 32t60 12q31 0 59 -12t48 -32t32 -48t12 -59q0 -32 -12 -59.5t-32 -47.5t-48 -31.5t-59 -11.5q-32 0 -60 11.5t-48.5 31.5t-32 47.5t-11.5 59.5z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="774" 
d="M218 -3q-9 -23 -23 -40t-32 -28.5t-38 -17.5t-39 -6h-104l584 1494q17 42 49 64.5t77 22.5h105z" />
    <glyph glyph-name="zero" unicode="0" 
d="M1110 723q0 -189 -40.5 -328.5t-112 -230.5t-169 -135.5t-210.5 -44.5t-209.5 44.5t-167.5 135.5t-111 230.5t-40 328.5q0 190 40 329t111 230t167.5 135.5t209.5 44.5t210.5 -44.5t169 -135.5t112 -230t40.5 -329zM855 723q0 157 -23 260t-61.5 164t-88.5 85.5
t-104 24.5q-53 0 -102.5 -24.5t-87.5 -85.5t-60.5 -164t-22.5 -260t22.5 -260t60.5 -164t87.5 -85.5t102.5 -24.5q54 0 104 24.5t88.5 85.5t61.5 164t23 260z" />
    <glyph glyph-name="one" unicode="1" 
d="M269 185h293v843q0 49 3 103l-208 -174q-13 -11 -26.5 -15t-26.5 -4q-20 0 -36.5 8.5t-24.5 19.5l-78 107l441 375h203v-1263h260v-185h-800v185z" />
    <glyph glyph-name="two" unicode="2" 
d="M602 1462q100 0 183 -29.5t142 -83.5t92 -130t33 -169q0 -80 -23.5 -148t-63 -130t-92.5 -120t-111 -118l-326 -333q48 14 94 21.5t88 7.5h371q40 0 63.5 -22.5t23.5 -59.5v-148h-992v82q0 25 10.5 52t33.5 50l439 440q55 56 98.5 107t73 101t45 101.5t15.5 108.5
q0 52 -15 91.5t-43 66.5t-66.5 40.5t-86.5 13.5q-89 0 -146.5 -45t-80.5 -121q-11 -38 -33 -54.5t-56 -16.5q-15 0 -33 3l-130 23q15 104 58 182.5t107.5 131t148 79t179.5 26.5z" />
    <glyph glyph-name="three" unicode="3" 
d="M625 1462q100 0 180.5 -28.5t137.5 -79t87.5 -118.5t30.5 -147q0 -69 -15.5 -121.5t-45 -91.5t-72 -66t-96.5 -45q130 -41 194 -125t64 -211q0 -108 -40 -191t-108 -139.5t-157.5 -85.5t-189.5 -29q-109 0 -190 25t-141 74t-102 120t-72 164l109 45q28 12 57 12
q26 0 46.5 -11t31.5 -32q18 -35 39.5 -69t51.5 -60.5t70.5 -43t96.5 -16.5q63 0 110 20.5t78.5 53.5t47 73.5t15.5 81.5q0 52 -11 94.5t-46 72.5t-100.5 47t-175.5 17v176q91 1 151 17t95.5 44.5t49.5 68.5t14 88q0 103 -56 155.5t-153 52.5q-88 0 -146 -46.5t-81 -119.5
q-12 -38 -33 -54.5t-55 -16.5q-16 0 -34 3l-130 23q15 104 58 182.5t107.5 131t148 79t179.5 26.5z" />
    <glyph glyph-name="four" unicode="4" 
d="M942 545h182v-143q0 -20 -13 -34.5t-38 -14.5h-131v-353h-216v353h-610q-25 0 -44 15.5t-24 38.5l-25 125l685 915h234v-902zM726 1018q0 32 2 69t7 77l-449 -619h440v473z" />
    <glyph glyph-name="five" unicode="5" 
d="M989 1341q0 -53 -33.5 -86t-111.5 -33h-398l-52 -302q98 20 179 20q114 0 200.5 -34t145.5 -94t89 -140.5t30 -173.5q0 -115 -40.5 -210t-112.5 -162.5t-170.5 -104.5t-214.5 -37q-68 0 -129 14t-114.5 37.5t-99 54t-81.5 64.5l76 105q24 34 64 34q25 0 50.5 -16
t59.5 -35t79 -35t109 -16q68 0 120 22t86.5 61.5t52 94t17.5 118.5q0 118 -68.5 184.5t-201.5 66.5q-105 0 -211 -38l-154 44l120 702h714v-105z" />
    <glyph glyph-name="six" unicode="6" 
d="M670 903q82 0 159.5 -27t136.5 -81.5t95 -135.5t36 -189q0 -101 -37 -189.5t-104 -154.5t-161.5 -104t-208.5 -38q-116 0 -208 37t-157 103.5t-99.5 160t-34.5 207.5q0 102 41.5 209.5t128.5 223.5l345 463q18 24 52.5 41t79.5 17h220l-429 -525q-14 -17 -26.5 -32
t-24.5 -31q42 21 90.5 33t105.5 12zM324 454q0 -60 16.5 -109.5t49 -84.5t80.5 -54.5t111 -19.5q59 0 108.5 20t85 56t55.5 84.5t20 105.5q0 62 -19 111.5t-54 84t-84 52.5t-108 18t-107 -20t-82.5 -55.5t-53 -84t-18.5 -104.5z" />
    <glyph glyph-name="seven" unicode="7" 
d="M1096 1446v-107q0 -48 -10.5 -77.5t-20.5 -49.5l-547 -1128q-17 -35 -48 -59.5t-84 -24.5h-179l560 1105q35 68 78 117h-692q-23 0 -40 17t-17 40v167h1000z" />
    <glyph glyph-name="eight" unicode="8" 
d="M580 -16q-112 0 -204.5 30t-158.5 85t-102.5 133t-36.5 174q0 128 63 216.5t196 131.5q-106 44 -158.5 125t-52.5 195q0 82 33.5 153t93.5 123.5t143.5 82t183.5 29.5t183.5 -29.5t143.5 -82t93.5 -123.5t33.5 -153q0 -114 -53 -195t-158 -125q133 -43 196 -131.5
t63 -216.5q0 -96 -36.5 -174t-102.5 -133t-158.5 -85t-204.5 -30zM580 177q61 0 107 17.5t77.5 48.5t47.5 74t16 93q0 119 -66 180t-182 61t-182 -61t-66 -180q0 -50 16 -93t47.5 -74t77.5 -48.5t107 -17.5zM580 845q60 0 101 19t66 50.5t35.5 72t10.5 83.5q0 41 -13 78
t-39 65.5t-66 45.5t-95 17t-95 -17t-66.5 -45.5t-39 -65.5t-12.5 -78q0 -43 10.5 -83.5t35.5 -72t66 -50.5t101 -19z" />
    <glyph glyph-name="nine" unicode="9" 
d="M530 577q-75 0 -147 26t-128.5 78.5t-90.5 130t-34 181.5q0 97 36 182t101 149t156 101t201 37q111 0 199.5 -35t151 -98t96 -151t33.5 -193q0 -67 -11.5 -127t-33 -115.5t-51.5 -108t-67 -105.5l-331 -472q-17 -24 -50.5 -40.5t-76.5 -16.5h-227l449 561q17 21 32 41
t29 40q-50 -32 -110 -48.5t-126 -16.5zM867 1010q0 59 -18 105.5t-51 78.5t-78.5 49t-99.5 17q-56 0 -101 -18.5t-76.5 -51.5t-48.5 -78.5t-17 -99.5q0 -121 62.5 -184.5t177.5 -63.5q60 0 106.5 19t78.5 52t48.5 78t16.5 97z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="530" 
d="M113 136q0 31 11.5 59t32 48t48.5 32t60 12q31 0 59 -12t48 -32t32 -48t12 -59q0 -32 -12 -59.5t-32 -47.5t-48 -31.5t-59 -11.5q-32 0 -60 11.5t-48.5 31.5t-32 47.5t-11.5 59.5zM113 849q0 31 11.5 59t32 48t48.5 32t60 12q31 0 59 -12t48 -32t32 -48t12 -59
q0 -32 -12 -59.5t-32 -47.5t-48 -31.5t-59 -11.5q-32 0 -60 11.5t-48.5 31.5t-32 47.5t-11.5 59.5z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="530" 
d="M120 152q0 28 10.5 52.5t29.5 43t46 29t59 10.5q38 0 66 -13.5t47 -37t28 -54t9 -64.5q0 -48 -14.5 -101t-42.5 -106t-69 -103t-94 -92l-43 39q-18 15 -18 37q0 8 5.5 18t13.5 18q11 12 27 29.5t32 40t29.5 49t19.5 56.5q-31 0 -57 11.5t-44.5 31.5t-29 47t-10.5 59z
M113 849q0 31 11.5 59t32 48t48.5 32t60 12q31 0 59 -12t48 -32t32 -48t12 -59q0 -32 -12 -59.5t-32 -47.5t-48 -31.5t-59 -11.5q-32 0 -60 11.5t-48.5 31.5t-32 47.5t-11.5 59.5z" />
    <glyph glyph-name="less" unicode="&#x3c;" 
d="M139 724l792 413v-176q0 -20 -10 -36.5t-33 -28.5l-363 -186q-23 -12 -48.5 -20.5t-54.5 -15.5q29 -7 54.5 -15.5t48.5 -20.5l363 -187q23 -12 33 -28.5t10 -36.5v-176l-792 414v100z" />
    <glyph glyph-name="equal" unicode="=" 
d="M136 588h886v-184h-886v184zM136 940h886v-183h-886v183z" />
    <glyph glyph-name="greater" unicode="&#x3e;" 
d="M229 210v176q0 20 10 36.5t33 28.5l363 187q44 22 103 36q-29 7 -54.5 15.5t-48.5 20.5l-363 186q-23 12 -33 28.5t-10 36.5v176l792 -413v-100z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="841" 
d="M37 1315q34 30 74.5 57t87.5 47t102 31.5t119 11.5q87 0 158.5 -24t122.5 -68.5t79 -107.5t28 -141q0 -76 -22 -131.5t-55.5 -96.5t-72.5 -71t-74 -56.5t-60.5 -52t-30.5 -57.5l-23 -146h-169l-17 163q-1 5 -1 8.5v8.5q0 44 22 76.5t55 61t71 55.5t71 58.5t55 71.5t22 94
q0 36 -13.5 64.5t-37.5 49t-57.5 31.5t-72.5 11q-57 0 -96.5 -12.5t-67 -28t-46.5 -28t-34 -12.5q-36 0 -52 30zM226 136q0 31 11.5 59t32 48t48.5 32t60 12q31 0 59 -12t48 -32t32 -48t12 -59q0 -32 -12 -59.5t-32 -47.5t-48 -31.5t-59 -11.5q-32 0 -60 11.5t-48.5 31.5
t-32 47.5t-11.5 59.5z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="1645" 
d="M1166 184q-68 0 -121.5 32t-74.5 103q-59 -72 -125 -103t-142 -31q-63 0 -109.5 22t-78 60.5t-47 91t-15.5 114.5q0 57 14.5 115.5t43.5 113t72.5 102t102.5 83t132 56t162 20.5q77 0 132 -12t106 -34l-96 -371q-16 -64 -16 -107q0 -30 7.5 -50t20 -32t30.5 -16.5
t39 -4.5q42 0 79.5 26.5t65.5 74.5t44.5 114t16.5 144q0 130 -40.5 229t-113 165.5t-173.5 100t-221 33.5q-131 0 -243.5 -49.5t-195 -135.5t-129 -203t-46.5 -252q0 -164 51 -288.5t140 -208t209.5 -125.5t260.5 -42q77 0 144 8.5t123 23t102 33t82 39.5q20 11 34 11
q29 0 41 -32l34 -89q-104 -68 -243 -111t-317 -43q-181 0 -335 57t-266.5 163.5t-176 259.5t-63.5 344q0 107 27.5 208t78.5 189.5t123 162.5t160 127t190.5 82.5t214.5 29.5q142 0 272.5 -47.5t231 -136t160.5 -216t60 -287.5q0 -107 -31.5 -200.5t-88 -162.5t-133 -108.5
t-166.5 -39.5zM752 344q26 0 52.5 8t50.5 28.5t44 54.5t33 85l73 282q-32 6 -66 6q-62 0 -116 -27t-93.5 -73t-62.5 -105.5t-23 -123.5t27.5 -99.5t80.5 -35.5z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="1420" 
d="M1417 0h-208q-35 0 -57.5 17.5t-32.5 43.5l-108 295h-599l-108 -295q-8 -23 -31.5 -42t-57.5 -19h-209l568 1446h275zM481 546h461l-176 481q-12 32 -26.5 75.5t-28.5 94.5q-14 -51 -27.5 -95t-26.5 -77z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="1303" 
d="M146 0v1446h499q142 0 242.5 -27t165 -77t94.5 -121t30 -160q0 -51 -15 -97.5t-46.5 -87t-80 -73.5t-115.5 -56q298 -67 298 -322q0 -92 -35 -170t-102 -134.5t-165 -88.5t-224 -32h-546zM415 634v-424h273q75 0 125.5 18t81 48t44 70t13.5 85q0 47 -15 84.5t-47 64
t-82 40.5t-121 14h-272zM415 820h215q137 0 208 50t71 159q0 113 -64 161t-200 48h-230v-418z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="1341" 
d="M1140 341q22 0 38 -17l106 -115q-88 -109 -216.5 -167t-308.5 -58q-161 0 -289.5 55t-219.5 153t-139.5 234t-48.5 297q0 163 54 298.5t152 233.5t234.5 152.5t301.5 54.5q161 0 281.5 -51.5t206.5 -136.5l-90 -125q-8 -12 -20.5 -21t-34.5 -9q-23 0 -47 18t-61 39
t-93.5 39t-143.5 18q-102 0 -187.5 -35.5t-147 -101.5t-96 -160.5t-34.5 -212.5q0 -122 34.5 -217t93.5 -160t139 -99.5t172 -34.5q55 0 99.5 6t82 19t71 33.5t66.5 50.5q10 9 21 14.5t24 5.5z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="1495" 
d="M1430 723q0 -159 -53 -292t-149 -229t-231 -149t-299 -53h-552v1446h552q164 0 299 -53.5t231 -149t149 -228.5t53 -292zM1155 723q0 119 -32 213.5t-91 160t-143.5 100.5t-190.5 35h-281v-1018h281q106 0 190.5 35t143.5 100.5t91 160t32 213.5z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="1145" 
d="M1058 1446v-214h-641v-401h505v-207h-505v-409h641v-215h-912v1446h912z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="1123" 
d="M1058 1446v-214h-641v-428h541v-215h-541v-589h-271v1446h912z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="1446" 
d="M810 198q91 0 158.5 16.5t128.5 45.5v263h-181q-26 0 -41 14.5t-15 35.5v152h481v-585q-55 -40 -114.5 -69.5t-127 -48.5t-144.5 -28.5t-166 -9.5q-158 0 -291 55t-230 153t-151.5 234t-54.5 297q0 163 53 299t150.5 234t236.5 152t312 54q177 0 306.5 -52.5
t219.5 -136.5l-78 -122q-23 -37 -61 -37q-24 0 -49 16q-32 19 -65.5 38t-74 33t-91 23t-116.5 9q-107 0 -193.5 -36t-147.5 -103t-94 -161t-33 -210q0 -125 35 -222.5t98 -164.5t150 -102.5t190 -35.5z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="1512" 
d="M1366 0h-271v632h-678v-632h-271v1446h271v-622h678v622h271v-1446z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="628" 
d="M449 0h-270v1446h270v-1446z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="874" 
d="M728 514q0 -122 -30 -220t-89.5 -167t-148.5 -106t-207 -37q-54 0 -106 6.5t-109 20.5l14 160q2 22 16.5 35.5t42.5 13.5q17 0 44.5 -7t69.5 -7q57 0 101 16.5t73.5 52.5t44.5 93.5t15 139.5v938h269v-932z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="1396" 
d="M424 840h63q38 0 63 10.5t43 33.5l399 505q25 32 52.5 44.5t69.5 12.5h232l-487 -601q-23 -28 -44 -47t-45 -31q33 -12 59.5 -34t51.5 -56l502 -677h-238q-48 0 -71.5 13.5t-39.5 39.5l-409 534q-20 26 -45 37.5t-71 11.5h-85v-636h-269v1447h269v-607z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="1034" 
d="M415 222h579v-222h-848v1446h269v-1224z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="1860" 
d="M872 600q17 -32 31.5 -66.5t28.5 -69.5q14 36 29 71t32 67l428 801q8 15 16.5 24t19 13t23.5 5t31 1h203v-1446h-237v934q0 26 1.5 57t4.5 63l-437 -820q-15 -28 -39 -43.5t-56 -15.5h-37q-32 0 -56 15.5t-39 43.5l-443 823q4 -33 5.5 -64.5t1.5 -58.5v-934h-237v1446
h203q18 0 31 -1t23.5 -5t19.5 -13t17 -24l432 -803v0z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="1512" 
d="M287 1446q18 0 30 -1.5t21.5 -6t18.5 -13t20 -22.5l759 -967q-4 35 -5.5 68.5t-1.5 62.5v879h237v-1446h-139q-32 0 -53 10t-41 36l-756 963q3 -32 4.5 -63.5t1.5 -57.5v-888h-237v1446h141v0z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="1599" 
d="M1533 723q0 -159 -53 -294.5t-149 -234.5t-231 -154.5t-299 -55.5t-299.5 55.5t-232 154.5t-149.5 234.5t-53 294.5t53 294.5t149.5 234.5t232 154.5t299.5 55.5t299 -56t231 -154.5t149 -234t53 -294.5zM1257 723q0 119 -31.5 213.5t-90.5 160.5t-143.5 101t-190.5 35
t-191 -35t-144.5 -101t-91.5 -160.5t-32 -213.5t32 -213.5t91.5 -160t144.5 -100.5t191 -35t190.5 35t143.5 100.5t90.5 160t31.5 213.5z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="1250" 
d="M424 509v-509h-269v1446h471q145 0 251 -34t175 -95t102 -146t33 -186q0 -105 -35 -192.5t-105 -150.5t-175 -98t-246 -35h-202zM424 719h202q74 0 129 18.5t91 53t54 84t18 110.5q0 58 -18 105t-54 80t-91 50.5t-129 17.5h-202v-519z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="1599" 
d="M1533 723q0 -97 -20 -185.5t-57.5 -165.5t-91.5 -141.5t-123 -113.5l367 -400h-222q-48 0 -86.5 13t-70.5 48l-212 234q-51 -14 -104.5 -21t-111.5 -7q-164 0 -299.5 55.5t-232 154.5t-149.5 234.5t-53 294.5t53 294.5t149.5 234.5t232 154.5t299.5 55.5t299 -56
t231 -154.5t149 -234t53 -294.5zM1257 723q0 119 -31.5 213.5t-90.5 160.5t-143.5 101t-190.5 35t-191 -35t-144.5 -101t-91.5 -160.5t-32 -213.5t32 -213.5t91.5 -160t144.5 -100.5t191 -35t190.5 35t143.5 100.5t90.5 160t31.5 213.5z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="1310" 
d="M424 565v-565h-269v1446h441q148 0 253.5 -30.5t173 -85.5t99 -131.5t31.5 -168.5q0 -73 -21.5 -138t-62 -118t-100 -93t-135.5 -64q51 -29 88 -83l362 -534h-242q-35 0 -59.5 14t-41.5 40l-304 463q-17 26 -37.5 37t-60.5 11h-115zM424 758h168q76 0 132.5 19t93 52.5
t54.5 79.5t18 101q0 110 -72.5 169t-221.5 59h-172v-480z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="1073" 
d="M921 1183q-11 -22 -25.5 -31t-34.5 -9t-45 15.5t-59 34.5t-79.5 34.5t-107.5 15.5q-56 0 -97.5 -13.5t-70 -37.5t-42.5 -57.5t-14 -73.5q0 -51 28.5 -85t75.5 -58t107 -43t122.5 -40.5t122.5 -50t107 -72t75.5 -106.5t28.5 -153q0 -98 -33.5 -183.5t-98 -149t-157.5 -100
t-213 -36.5q-69 0 -136 13.5t-128.5 38.5t-115 60t-95.5 78l78 129q11 14 26.5 23.5t34.5 9.5q25 0 54 -20.5t68.5 -45.5t93 -45.5t128.5 -20.5q115 0 178 54.5t63 156.5q0 57 -28.5 93t-75.5 60.5t-107 41.5t-122 37t-122 48t-107 73t-75.5 112.5t-28.5 166.5
q0 79 31.5 154t92 133t148.5 93t201 35q128 0 236 -40t184 -112z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="1190" 
d="M1165 1446v-221h-435v-1225h-269v1225h-437v221h1141z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="1456" 
d="M728 217q78 0 139.5 26t104 73t65 114t22.5 150v866h269v-866q0 -129 -41.5 -238.5t-119.5 -189t-189 -124t-250 -44.5t-250 44.5t-188.5 124t-119 189t-41.5 238.5v866h269v-865q0 -83 22.5 -150t64.5 -114.5t103.5 -73.5t139.5 -26z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="1420" 
d="M4 1446h217q35 0 57 -17t33 -44l340 -882q17 -43 32.5 -94.5t29.5 -108.5q23 115 57 203l339 882q9 23 32 42t57 19h217l-584 -1446h-243z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="2093" 
d="M12 1446h226q35 0 58.5 -16.5t31.5 -44.5l246 -865q9 -32 16.5 -69.5t14.5 -79.5q8 42 17.5 79.5t20.5 69.5l284 865q8 23 31.5 42t57.5 19h79q35 0 58 -16.5t32 -44.5l282 -865q22 -64 38 -142q7 39 14.5 75t15.5 67l246 865q7 25 31 43t58 18h211l-449 -1446h-243
l-316 988q-6 19 -12.5 41t-12.5 47q-6 -25 -12.5 -47t-12.5 -41l-319 -988h-243z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="1358" 
d="M493 744l-461 702h268q28 0 40.5 -7.5t22.5 -24.5l329 -531q5 13 11 25.5t14 25.5l301 475q22 37 57 37h258l-466 -691l479 -755h-269q-27 0 -43.5 14t-27.5 32l-335 554q-4 -12 -9 -22.5t-10 -19.5l-321 -512q-11 -17 -27 -31.5t-40 -14.5h-252z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="1309" 
d="M789 562v-562h-269v562l-527 884h237q35 0 55.5 -17t34.5 -43l265 -483q23 -43 40 -81.5t31 -75.5q13 38 29.5 76.5t39.5 80.5l263 483q11 22 33 41t56 19h238z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="1234" 
d="M1179 1446v-99q0 -46 -26 -84l-742 -1048h750v-215h-1099v106q0 20 7 38.5t18 34.5l744 1053h-722v214h1070z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="600" 
d="M115 -308v1855h410v-97q0 -26 -18.5 -44.5t-47.5 -18.5h-138v-1535h138q29 0 47.5 -18.5t18.5 -44.5v-97h-410z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="781" 
d="M-28 1486h106q44 0 76.5 -22.5t49.5 -64.5l583 -1494h-104q-39 0 -76.5 23t-54.5 69z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="600" 
d="M75 -308v97q0 26 18.5 44.5t47.5 18.5h138v1535h-138q-29 0 -47.5 18.5t-18.5 44.5v97h410v-1855h-410z" />
    <glyph glyph-name="asciicircum" unicode="^" 
d="M1028 777h-181q-23 0 -37.5 12t-24.5 29l-159 289q-15 28 -27.5 53t-20.5 51q-8 -26 -19 -51.5t-26 -52.5l-156 -289q-9 -17 -24 -29t-41 -12h-190l373 669h160z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="788" 
d="M788 -134v-160h-788v160h788z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="638" 
d="M230 1462q41 0 60.5 -13.5t34.5 -39.5l143 -243h-141q-28 0 -45.5 7.5t-35.5 27.5l-247 261h231z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="1047" 
d="M936 0h-111q-35 0 -55 10.5t-30 42.5l-22 73q-39 -35 -76.5 -61.5t-77.5 -44.5t-85 -27t-100 -9q-65 0 -120 17.5t-94.5 52.5t-61.5 87t-22 121q0 58 30.5 114.5t101.5 102t189 75.5t293 34v60q0 103 -43.5 152.5t-126.5 49.5q-60 0 -100 -14t-69.5 -31.5t-54.5 -31.5
t-55 -14q-25 0 -43 13t-29 32l-45 79q177 162 427 162q90 0 160.5 -29.5t119.5 -82t74.5 -125.5t25.5 -160v-648zM456 154q38 0 70 7t60.5 21t55 34.5t53.5 48.5v173q-108 -5 -180.5 -18.5t-116.5 -34.5t-62.5 -49t-18.5 -61q0 -65 38.5 -93t100.5 -28z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="1140" 
d="M135 0v1486h247v-586q61 65 138 103.5t180 38.5q84 0 153.5 -34.5t120 -100.5t78 -163t27.5 -223q0 -115 -31 -213t-88.5 -170t-139 -112.5t-182.5 -40.5q-47 0 -86 9.5t-71 26.5t-59.5 41.5t-52.5 54.5l-11 -69q-6 -26 -20.5 -37t-39.5 -11h-163zM615 850
q-77 0 -131.5 -32.5t-101.5 -91.5v-460q42 -52 91.5 -72.5t107.5 -20.5q56 0 101 21t76.5 64t48.5 108.5t17 154.5q0 90 -14.5 152.5t-41.5 101.5t-65.5 57t-87.5 18z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="954" 
d="M853 809q-11 -14 -21.5 -22t-30.5 -8q-19 0 -37 11.5t-43 26t-59.5 26t-85.5 11.5q-65 0 -114 -23.5t-81.5 -67.5t-48.5 -106.5t-16 -141.5q0 -82 17.5 -146t50.5 -107.5t80 -66t106 -22.5t95.5 14.5t61.5 32t43.5 32t41.5 14.5q30 0 45 -23l71 -90q-41 -48 -89 -80.5
t-99.5 -52t-106.5 -27.5t-109 -8q-95 0 -179 35.5t-146.5 103.5t-99 166.5t-36.5 224.5q0 113 32.5 209.5t95.5 167t156 110.5t214 40q115 0 201.5 -37t155.5 -106z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="1140" 
d="M854 0q-48 0 -63 45l-20 99q-32 -36 -67 -65t-75.5 -50t-87 -32.5t-100.5 -11.5q-84 0 -154 35t-120.5 101.5t-78 164.5t-27.5 224q0 114 31 212t89 170t139 112.5t182 40.5q86 0 147 -27.5t109 -73.5v542h247v-1486h-151zM525 181q77 0 131 32t102 91v460
q-42 51 -91.5 72t-106.5 21q-56 0 -101.5 -21t-77 -63.5t-48.5 -108t-17 -154.5q0 -90 14.5 -152.5t41.5 -102t66 -57t87 -17.5z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="1069" 
d="M556 1042q97 0 178.5 -31t140.5 -90.5t92 -146t33 -197.5q0 -28 -2.5 -46.5t-9 -29t-17.5 -15t-28 -4.5h-634q11 -158 85 -232t196 -74q60 0 103.5 14t76 31t57 31t47.5 14q15 0 26 -6t19 -17l72 -90q-41 -48 -92 -80.5t-106.5 -52t-113 -27.5t-111.5 -8
q-107 0 -199 35.5t-160 105t-107 172t-39 237.5q0 105 34 197.5t97.5 161t155 108.5t206.5 40zM561 865q-108 0 -169 -61t-78 -173h464q0 48 -13 90.5t-40 74.5t-68 50.5t-96 18.5z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="701" 
d="M176 0v840l-89 14q-29 5 -46.5 20t-17.5 42v101h153v76q0 88 26.5 158t76 119t120.5 75t160 26q71 0 132 -19l-5 -124q-2 -29 -27 -36t-58 -7q-44 0 -78.5 -9.5t-58.5 -33t-36.5 -62t-12.5 -95.5v-68h267v-176h-259v-841h-247z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="1035" 
d="M487 1044q66 0 124 -13.5t106 -39.5h295v-92q0 -23 -12 -36t-41 -18l-92 -17q10 -26 15.5 -55t5.5 -61q0 -76 -30.5 -137.5t-84 -104.5t-127 -66.5t-159.5 -23.5q-58 0 -113 11q-48 -29 -48 -65q0 -31 28.5 -45.5t75 -20.5t105.5 -7.5t121 -6.5t121 -17.5t105.5 -39.5
t75 -73.5t28.5 -119.5q0 -68 -33.5 -132t-97 -114t-155.5 -80.5t-210 -30.5q-117 0 -203 22.5t-142.5 60t-84.5 86.5t-28 102q0 72 44 121t122 78q-38 21 -61 56t-23 91q0 23 8 47.5t24.5 48.5t41.5 45.5t59 38.5q-78 42 -122.5 112t-44.5 164q0 76 30.5 137.5t85 105
t129 66.5t162.5 23zM758 -46q0 30 -18 49t-49 29.5t-72.5 15.5t-88 7.5t-96 5t-95.5 8.5q-42 -23 -67.5 -54.5t-25.5 -72.5q0 -27 13.5 -50.5t43 -40.5t76.5 -26.5t115 -9.5q69 0 119 10.5t82.5 29t47.5 44t15 55.5zM487 538q46 0 80 12.5t56.5 34.5t34 53t11.5 68
q0 76 -45.5 120.5t-136.5 44.5t-136.5 -44.5t-45.5 -120.5q0 -36 11.5 -67t34 -53.5t57 -35t79.5 -12.5z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="1137" 
d="M132 0v1486h247v-571q60 57 132 92t169 35q84 0 149 -28.5t108.5 -80t66 -123t22.5 -157.5v-653h-247v653q0 94 -43.5 145.5t-130.5 51.5q-64 0 -120 -29t-106 -79v-742h-247z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="542" 
d="M395 1026v-1026h-247v1026h247zM432 1325q0 -32 -13 -60t-34.5 -49t-50.5 -33.5t-62 -12.5q-32 0 -60.5 12.5t-49.5 33.5t-33.5 49t-12.5 60q0 33 12.5 62t33.5 50t49.5 33.5t60.5 12.5q33 0 62 -12.5t50.5 -33.5t34.5 -50t13 -62z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="538" 
d="M395 1026v-1061q0 -67 -17.5 -126t-56 -103.5t-101 -70t-152.5 -25.5q-35 0 -65 4.5t-61 14.5l8 133q3 20 16 25.5t50 5.5t62.5 7.5t41 24t22 43.5t6.5 67v1061h247zM432 1325q0 -32 -13 -60t-34.5 -49t-50.5 -33.5t-62 -12.5q-32 0 -60.5 12.5t-49.5 33.5t-33.5 49
t-12.5 60q0 33 12.5 62t33.5 50t49.5 33.5t60.5 12.5q33 0 62 -12.5t50.5 -33.5t34.5 -50t13 -62z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="1103" 
d="M382 1486v-851h46q25 0 39 7t30 26l255 315q17 20 36 31.5t50 11.5h226l-319 -381q-17 -21 -35 -38.5t-39 -30.5q21 -15 37 -35t32 -43l342 -498h-223q-29 0 -49 10t-36 34l-261 389q-15 23 -30 30t-45 7h-56v-470h-247v1486h247z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="542" 
d="M395 1486v-1486h-247v1486h247z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="1684" 
d="M132 0v1026h151q48 0 63 -45l16 -76q27 30 56.5 55t63 43t72 28.5t84.5 10.5q97 0 159.5 -52.5t93.5 -139.5q24 51 60 87.5t79 59.5t91.5 34t97.5 11q85 0 151 -26t111 -76t68.5 -122t23.5 -165v-653h-247v653q0 98 -43 147.5t-126 49.5q-38 0 -70.5 -13t-57 -37.5
t-38.5 -61.5t-14 -85v-653h-248v653q0 103 -41.5 150t-122.5 47q-53 0 -99.5 -26.5t-86.5 -72.5v-751h-247z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="1137" 
d="M132 0v1026h151q48 0 63 -45l17 -81q31 32 65.5 58t73 45t82.5 29t96 10q84 0 149 -28.5t108.5 -80t66 -123t22.5 -157.5v-653h-247v653q0 94 -43.5 145.5t-130.5 51.5q-64 0 -120 -29t-106 -79v-742h-247z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="1137" 
d="M570 1042q115 0 208.5 -37t159.5 -105t102 -166t36 -219q0 -122 -36 -220t-102 -167t-159.5 -106t-208.5 -37t-209 37t-160.5 106t-103 167t-36.5 220q0 121 36.5 219t103 166t160.5 105t209 37zM570 175q128 0 189.5 86t61.5 252t-61.5 253t-189.5 87q-130 0 -192 -87.5
t-62 -252.5t62 -251.5t192 -86.5z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="1131" 
d="M132 -335v1361h151q24 0 41 -11t22 -34l20 -95q62 71 142.5 115t188.5 44q84 0 153.5 -35t120 -101.5t78 -164t27.5 -223.5q0 -115 -31 -213t-88.5 -170t-139 -112.5t-182.5 -40.5q-87 0 -147.5 26.5t-108.5 73.5v-420h-247zM612 850q-77 0 -131.5 -32.5t-101.5 -91.5
v-460q42 -52 91.5 -72.5t106.5 -20.5q56 0 101.5 21t77 64t48.5 108.5t17 154.5q0 90 -14.5 152.5t-41.5 101.5t-65.5 57t-87.5 18z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="1140" 
d="M1005 1026v-1361h-247v464q-31 -33 -65.5 -59.5t-73.5 -45t-83 -29t-95 -10.5q-84 0 -154 35t-120.5 101.5t-78 164.5t-27.5 224q0 114 31 212t89 170t139 112.5t182 40.5q48 0 87.5 -8.5t73 -24.5t61.5 -38t54 -50l13 57q5 23 22 34t41 11h151zM525 181q77 0 131 32
t102 91v460q-42 51 -91.5 72t-106.5 21q-56 0 -101.5 -21t-77 -63.5t-48.5 -108t-17 -154.5q0 -90 14.5 -152.5t41.5 -102t66 -57t87 -17.5z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="817" 
d="M132 0v1026h145q38 0 53 -14t20 -48l15 -124q55 95 129 150t166 55q76 0 126 -35l-32 -185q-3 -18 -13 -25.5t-27 -7.5q-15 0 -41 7t-69 7q-77 0 -132 -42.5t-93 -124.5v-639h-247z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="874" 
d="M741 826q-10 -16 -21 -22.5t-28 -6.5q-18 0 -38.5 10t-47.5 22.5t-61.5 22.5t-81.5 10q-73 0 -115 -31t-42 -81q0 -33 21.5 -55.5t57 -39.5t80.5 -30.5t92 -29.5t92 -36.5t80.5 -52t57 -75.5t21.5 -106q0 -74 -27 -136.5t-79 -108t-128.5 -71t-175.5 -25.5
q-53 0 -103.5 9.5t-97 26.5t-86 40t-69.5 50l57 94q11 17 26 26t38 9t43.5 -13t47.5 -28t63.5 -28t92.5 -13q44 0 75.5 10.5t52 27.5t30 39.5t9.5 46.5q0 36 -21.5 59t-57 40t-81 30.5t-93 29.5t-93 37.5t-81 54.5t-57 81t-21.5 116q0 63 25 120t73.5 99.5t121 68
t167.5 25.5q106 0 193 -35t145 -92z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="774" 
d="M469 -16q-133 0 -205 75.5t-72 208.5v573h-104q-20 0 -34.5 13t-14.5 39v98l165 27l52 280q4 20 18.5 31t36.5 11h128v-323h270v-176h-270v-556q0 -48 24 -75t64 -27q23 0 38.5 5.5t27 11.5t20.5 11.5t18 5.5q11 0 18 -5.5t15 -16.5l74 -120q-54 -45 -124 -68t-145 -23z
" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="1137" 
d="M358 1026v-652q0 -94 43.5 -145.5t130.5 -51.5q64 0 120 28.5t106 78.5v742h247v-1026h-151q-48 0 -63 45l-17 82q-32 -32 -66 -58.5t-72.5 -45t-83 -29t-95.5 -10.5q-84 0 -148.5 28.5t-108.5 80.5t-66.5 123.5t-22.5 157.5v652h247z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="1067" 
d="M646 0h-224l-408 1026h205q27 0 45.5 -13t25.5 -33l198 -548q17 -48 28.5 -94t20.5 -92q9 46 20.5 92t29.5 94l203 548q7 20 25 33t43 13h195z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="1582" 
d="M7 1026h196q28 0 47 -13t24 -33l147 -548q12 -45 19.5 -88t14.5 -86q11 43 23.5 86t26.5 88l170 550q6 20 24 33t42 13h109q27 0 45 -13t24 -33l168 -560q13 -43 23.5 -83.5t20.5 -81.5q7 43 15.5 86t21.5 89l152 548q5 20 24 33t44 13h187l-325 -1026h-199q-32 0 -46 44
l-185 593q-9 29 -16.5 58.5t-12.5 58.5q-6 -30 -13 -59.5t-16 -59.5l-187 -591q-14 -44 -54 -44h-189z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="1080" 
d="M375 529l-337 497h238q27 0 40 -7.5t23 -24.5l215 -343q5 17 12.5 34t18.5 34l173 270q12 19 25 28t32 9h227l-338 -486l352 -540h-238q-27 0 -43.5 14t-27.5 32l-218 357q-9 -36 -25 -60l-192 -297q-11 -17 -27 -31.5t-40 -14.5h-221z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="1067" 
d="M496 -282q-11 -26 -28.5 -39.5t-53.5 -13.5h-184l192 411l-415 950h216q30 0 46 -14t24 -32l219 -532q11 -26 19 -54t14 -56q8 29 17.5 56t20.5 55l206 531q8 20 26.5 33t41.5 13h198z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="939" 
d="M874 924q0 -26 -9.5 -50.5t-22.5 -41.5l-488 -642h506v-190h-793v103q0 17 8 40.5t25 44.5l492 649h-496v189h778v-102z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="600" 
d="M149 410q0 61 -28.5 99.5t-91.5 38.5v143q63 0 91.5 38.5t28.5 99.5q0 47 -6.5 93.5t-15 93.5t-15 94.5t-6.5 96.5q0 76 22 138.5t67.5 107.5t115 69.5t163.5 24.5h53v-110q0 -13 -5.5 -22.5t-14 -15.5t-17.5 -9t-16 -3h-9q-70 0 -107 -44t-37 -121q0 -55 6 -105.5
t12.5 -98t12.5 -93.5t6 -93q0 -38 -10.5 -72t-31 -61.5t-49 -48.5t-64.5 -31q36 -11 64.5 -31.5t49 -48.5t31 -62t10.5 -71q0 -47 -6 -93t-12.5 -93.5t-12.5 -98t-6 -105.5q0 -76 37 -120t107 -44h9q7 0 16 -3t17.5 -9t14 -16t5.5 -23v-109h-53q-94 0 -163.5 24.5t-115 69
t-67.5 107t-22 138.5q0 49 6.5 96.5t15 94.5t15 94t6.5 94z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="600" 
d="M204 1547h192v-1882h-192v1882z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="600" 
d="M451 410q0 -47 6.5 -94t15 -94t15 -94.5t6.5 -96.5q0 -76 -22 -138.5t-67.5 -107t-115 -69t-163.5 -24.5h-53v109q0 13 5.5 23t14 16t17.5 9t16 3h9q70 0 107 44t37 120q0 55 -6 105.5t-12.5 98t-12.5 93.5t-6 93q0 37 10.5 71t30.5 62t49 48.5t65 31.5q-36 10 -65 31
t-49 48.5t-30.5 61.5t-10.5 72q0 47 6 93t12.5 93.5t12.5 98t6 105.5q0 77 -37 121t-107 44h-9q-7 0 -16 3t-17.5 9t-14 15.5t-5.5 22.5v110h53q94 0 163.5 -24.5t115 -69.5t67.5 -107.5t22 -138.5q0 -49 -6.5 -96.5t-15 -94.5t-15 -93.5t-6.5 -93.5q0 -61 28.5 -99.5
t91.5 -38.5v-143q-63 0 -91.5 -38.5t-28.5 -99.5z" />
    <glyph glyph-name="asciitilde" unicode="~" 
d="M745 648q56 0 87 36t31 101h199q0 -77 -20.5 -139.5t-59 -107t-94.5 -68.5t-128 -24q-53 0 -101.5 14t-92 30.5t-81.5 30.5t-70 14q-56 0 -87 -36t-31 -101h-199q0 77 20.5 139.5t59 107t94.5 68.5t128 24q53 0 101.5 -14t92 -30.5t81.5 -30.5t70 -14z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="386" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="721" 
d="M247 -335v533q0 91 9 178.5t24 185.5h167q15 -98 24 -185.5t9 -178.5v-533h-233zM209 892q0 32 11.5 59.5t31.5 47.5t48 31.5t60 11.5t59.5 -11.5t48 -31.5t32.5 -47.5t12 -59.5t-12 -59.5t-32.5 -47.5t-48 -32t-59.5 -12t-60 12t-48 32t-31.5 47.5t-11.5 59.5z" />
    <glyph glyph-name="cent" unicode="&#xa2;" 
d="M539 -8q-89 13 -165 53.5t-132 107t-87.5 157t-31.5 203.5q0 109 33 203.5t97.5 164.5t159 112t216.5 47l14 157q2 23 20 42.5t47 19.5h91l-20 -229q79 -14 143 -47.5t118 -83.5l-64 -87q-10 -14 -20 -21t-30 -7q-14 0 -29 6.5t-34.5 16.5t-43.5 20t-56 18l-58 -673
q53 5 88.5 19.5t61 29.5t44.5 27t39 12q30 0 46 -21l68 -88q-35 -41 -77 -70t-88 -48.5t-96 -30t-102 -14.5l-12 -147q-2 -24 -20 -43.5t-47 -19.5h-91zM367 513q0 -134 49 -216.5t140 -111.5l57 667q-126 -16 -186 -104t-60 -235z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" 
d="M39 679q0 34 21 58.5t62 24.5h108v236q0 94 28 178.5t85 148t143 100.5t202 37q80 0 143.5 -20.5t113 -55.5t86 -82.5t62.5 -102.5l-99 -63q-32 -16 -57 -16q-37 0 -66 33q-19 22 -37.5 40.5t-40 31.5t-47 20t-58.5 7q-103 0 -154 -67.5t-51 -187.5v-237h409v-98
q0 -24 -19.5 -44t-50.5 -20h-339v-194q0 -66 -24.5 -117.5t-67.5 -95.5q74 17 147 17h586v-104q0 -18 -7.5 -37t-21.5 -34.5t-33.5 -25t-43.5 -9.5h-959v155q34 8 65 22.5t54.5 37t37.5 53t14 71.5v261h-191v79z" />
    <glyph glyph-name="currency" unicode="&#xa4;" 
d="M211 673q0 53 13.5 100.5t38.5 89.5l-153 152l125 122l150 -150q43 26 92 40.5t103 14.5q52 0 100 -13.5t90 -38.5l152 152l123 -123l-151 -151q26 -43 40.5 -92t14.5 -103q0 -53 -13.5 -100t-38.5 -89l153 -152l-125 -123l-151 150q-42 -26 -91 -40t-103 -14
q-52 0 -99.5 13t-89.5 38l-153 -152l-123 124l151 150q-26 43 -40.5 92t-14.5 103zM393 673q0 -38 14.5 -72.5t40 -60t59.5 -40.5t73 -15t73.5 15t60 40.5t40.5 60t15 72.5q0 40 -15 74.5t-40.5 60t-60 40.5t-73.5 15t-73 -15t-59.5 -40.5t-40 -60t-14.5 -74.5z" />
    <glyph glyph-name="yen" unicode="&#xa5;" 
d="M133 633h276l-398 813h205q35 0 57 -16.5t34 -43.5l219 -488q20 -45 32 -83t20 -75q8 37 19.5 75.5t31.5 82.5l217 488q11 23 33 41.5t56 18.5h207l-399 -813h276v-138h-319v-95h319v-137h-319v-263h-247v263h-320v137h320v95h-320v138z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="600" 
d="M204 1547h192v-809h-192v809zM204 473h192v-808h-192v808z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="1010" 
d="M827 1245q-10 -16 -21 -23t-28 -7q-19 0 -39.5 10t-47.5 22.5t-61.5 22.5t-81.5 10q-42 0 -74 -9.5t-53.5 -26t-33 -38.5t-11.5 -47q0 -32 23.5 -56t62.5 -45.5t88.5 -41.5t101.5 -42.5t101.5 -49.5t88.5 -62t62.5 -81t23.5 -106q0 -81 -37.5 -146.5t-120.5 -105.5
q45 -37 73.5 -86t28.5 -117q0 -74 -26.5 -136.5t-78.5 -108t-128.5 -71t-176.5 -25.5q-53 0 -103.5 9.5t-97 26.5t-86 40t-69.5 50l58 94q11 17 25.5 26t37.5 9t43.5 -13t48.5 -28t67 -28t98 -13q82 0 127.5 34t45.5 94q0 40 -24 69t-64 51.5t-90.5 41t-103 39.5t-103 46.5
t-90.5 60.5t-64 83t-24 114q0 79 41.5 141.5t126.5 99.5q-46 39 -75 92.5t-29 129.5q0 63 24.5 119.5t73.5 99.5t121 68.5t167 25.5q106 0 193.5 -35t144.5 -92zM313 725q0 -42 29 -72.5t75.5 -55t104.5 -47t115 -48.5q40 20 58 50.5t18 66.5q0 44 -28 74.5t-73 55.5
t-101.5 47t-112.5 48q-45 -24 -65 -52.5t-20 -66.5z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="638" 
d="M270 1292q0 -28 -11 -52.5t-30.5 -42.5t-45 -28.5t-53.5 -10.5q-27 0 -51.5 10.5t-43.5 28.5t-30 42.5t-11 52.5q0 29 11 54.5t30 44.5t43.5 30t51.5 11q28 0 53.5 -11t45 -30t30.5 -44.5t11 -54.5zM644 1292q0 -28 -11 -52.5t-30 -42.5t-44.5 -28.5t-53.5 -10.5
t-53 10.5t-43.5 28.5t-29.5 42.5t-11 52.5q0 29 11 54.5t29.5 44.5t43.5 30t53 11t53.5 -11t44.5 -30t30 -44.5t11 -54.5z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="1583" 
d="M1017 512q11 0 20 -4.5t15 -12.5l82 -87q-56 -71 -141 -108.5t-200 -37.5q-100 0 -181 35.5t-138.5 98t-88.5 147t-31 183.5q0 102 35 187t97 146.5t145.5 95.5t181.5 34q112 0 191.5 -37t133.5 -96l-65 -89q-6 -8 -16 -16t-27 -8t-32 10t-36 22t-53 22t-83 10
q-60 0 -107 -19.5t-79.5 -56t-49.5 -88.5t-17 -117q0 -68 17.5 -120t48.5 -87.5t73.5 -54t92.5 -18.5q49 0 80.5 8t53.5 19t39 22t39 17zM53 723q0 102 26.5 196.5t74.5 176.5t115.5 149.5t149.5 115.5t176 74.5t196 26.5t196.5 -26.5t176.5 -74.5t149.5 -115.5
t115.5 -149.5t74.5 -176.5t26.5 -196.5q0 -101 -26.5 -195.5t-74.5 -176t-115.5 -149t-149.5 -115.5t-176.5 -74.5t-196.5 -26.5t-196 26.5t-176 74.5t-149.5 115.5t-115.5 149t-74.5 175.5t-26.5 196zM188 723q0 -130 46.5 -243t128 -196.5t191.5 -131.5t237 -48t238 48
t193 131.5t129 196.5t47 243q0 87 -21.5 167t-60.5 149.5t-94.5 126t-122.5 97t-145 62.5t-163 22t-162.5 -22t-144.5 -62.5t-121.5 -97t-93.5 -126t-60 -149.5t-21 -167z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="696" 
d="M622 841h-83q-24 0 -38 6.5t-22 29.5l-12 36q-24 -20 -46 -35t-45 -25t-49.5 -15t-59.5 -5q-42 0 -76 11t-58.5 32.5t-38 53t-13.5 72.5q0 33 17 67.5t59 62.5t113 46.5t179 21.5v25q0 54 -23.5 76.5t-68.5 22.5q-34 0 -56 -7t-39 -16t-32 -16t-36 -7q-19 0 -32 10
t-20 23l-31 57q56 51 123 74.5t145 23.5q56 0 101 -18t76.5 -50.5t48.5 -76.5t17 -96v-384zM323 954q38 0 67 13.5t59 42.5v81q-59 -2 -97 -9t-60.5 -17.5t-31.5 -24t-9 -29.5q0 -32 18.5 -44.5t53.5 -12.5z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="972" 
d="M123 522v32l256 396l81 -38q20 -9 29 -23t9 -31q0 -21 -13 -43l-138 -235q-14 -26 -32 -42q16 -14 32 -42l138 -236q13 -22 13 -44q0 -34 -38 -52l-81 -38zM452 522v32l256 396l81 -38q20 -9 29 -23t9 -31q0 -21 -13 -43l-138 -235q-14 -26 -32 -42q16 -14 32 -42
l138 -236q13 -22 13 -44q0 -34 -38 -52l-81 -38z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" 
d="M136 763h886v-466h-209v283h-677v183z" />
    <glyph glyph-name="uni00AD" unicode="&#xad;" horiz-adv-x="718" 
d="M100 707h518v-206h-518v206z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="1583" 
d="M53 723q0 102 26.5 196.5t74.5 176.5t115.5 149.5t149.5 115.5t176 74.5t196 26.5t196.5 -26.5t176.5 -74.5t149.5 -115.5t115.5 -149.5t74.5 -176.5t26.5 -196.5q0 -101 -26.5 -195.5t-74.5 -176t-115.5 -149t-149.5 -115.5t-176.5 -74.5t-196.5 -26.5t-196 26.5
t-176 74.5t-149.5 115.5t-115.5 149t-74.5 175.5t-26.5 196zM188 723q0 -130 46.5 -243t128 -196.5t191.5 -131.5t237 -48t238 48t193 131.5t129 196.5t47 243q0 87 -21.5 167t-60.5 149.5t-94.5 126t-122.5 97t-145 62.5t-163 22t-162.5 -22t-144.5 -62.5t-121.5 -97
t-93.5 -126t-60 -149.5t-21 -167zM679 602v-325h-214v897h321q187 0 274 -67t87 -192q0 -89 -46 -155.5t-143 -97.5q23 -13 38 -32.5t30 -45.5l181 -307h-206q-45 0 -63 33l-144 263q-10 14 -22.5 21.5t-37.5 7.5h-55zM679 755h89q51 0 84 8.5t51.5 25.5t25.5 42t7 57
q0 31 -6 54.5t-22.5 39t-46 23t-75.5 7.5h-107v-257z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="638" 
d="M20 1372h598v-158h-598v158z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="803" 
d="M55 1123q0 71 26.5 133.5t73 108.5t109.5 72.5t136 26.5t136.5 -26.5t110.5 -72.5t74 -108.5t27 -133.5q0 -69 -27 -131t-74 -108.5t-110.5 -73.5t-136.5 -27t-136 27t-109.5 73.5t-73 108.5t-26.5 131zM227 1121q0 -37 13 -69t36.5 -55.5t55 -37t68.5 -13.5t69 13.5
t55.5 37t36.5 55.5t13 69q0 38 -13 70.5t-36.5 56.5t-55.5 38t-69 14t-68.5 -14t-55 -38t-36.5 -56.5t-13 -70.5z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" 
d="M678 1241v-354h389v-184h-389v-341h-200v341h-387v184h387v354h200zM91 263h976v-183h-976v183z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="666" 
d="M350 1649q58 0 103.5 -17t77 -46t47.5 -68.5t16 -85.5q0 -41 -12.5 -75t-33 -65t-47.5 -60t-56 -59l-128 -130q28 8 55 12.5t50 4.5h129q28 0 43.5 -15t15.5 -40v-105h-541v57q0 17 6.5 36t22.5 35l209 206q22 22 42 46.5t34 49t22.5 49t8.5 47.5q0 36 -19.5 58.5
t-54.5 22.5q-33 0 -53 -16.5t-33 -47.5q-11 -18 -23.5 -27t-35.5 -9q-5 0 -10.5 0.5t-12.5 1.5l-99 15q17 115 91 170t186 55z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="666" 
d="M360 1649q57 0 101 -16.5t75 -43.5t47 -61.5t16 -71.5q0 -63 -26 -106.5t-83 -67.5q60 -20 91 -56.5t31 -95.5q0 -62 -23 -106.5t-60 -74t-84 -43.5t-95 -14q-54 0 -97 10.5t-76.5 34t-58.5 62t-44 94.5l77 31q22 8 40 8q37 0 51 -28q6 -11 14 -23t20 -21.5t28 -15.5
t37 -6q45 0 69 25t24 60q0 27 -7 45t-23.5 29t-45 15.5t-70.5 4.5v116q42 0 69.5 6.5t43 18t22 28.5t6.5 38q0 37 -19.5 58.5t-58.5 21.5q-35 0 -55.5 -16.5t-31.5 -42.5q-9 -20 -21 -29.5t-32 -9.5q-11 0 -26 3l-91 15q8 57 31.5 99.5t58.5 70.5t80 41.5t96 13.5z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="638" 
d="M665 1462l-247 -261q-19 -20 -36.5 -27.5t-45.5 -7.5h-148l142 243q15 26 35 39.5t60 13.5h240z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="1137" 
d="M358 1026v-652q0 -94 43.5 -145.5t130.5 -51.5q64 0 120 28.5t106 78.5v742h247v-1026h-151q-48 0 -63 45l-17 83q-31 -31 -61 -52.5t-61 -35t-64.5 -19.5t-71.5 -6q-59 0 -107 18t-85 51q11 -44 14.5 -91t3.5 -88v-240h-122q-52 0 -80.5 26.5t-28.5 76.5v1258h247z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="1401" 
d="M1370 1446v-207h-214v-1448h-216v1448h-235v-1448h-216v839q-104 0 -188 32.5t-143.5 88t-92 129.5t-32.5 157q0 91 32.5 166t92 129t143.5 84t188 30h881z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="559" 
d="M91 595q0 39 14.5 73.5t40 60t59.5 40t73 14.5q40 0 74.5 -14.5t60 -40t40.5 -60t15 -73.5t-15 -72.5t-40.5 -59t-60 -40t-74.5 -14.5q-39 0 -73 14.5t-59.5 40t-40 59t-14.5 72.5z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="638" 
d="M183 -227q7 0 14.5 -2t17 -5t21 -5t27.5 -2q33 0 49.5 12.5t16.5 30.5q0 29 -36 41.5t-111 22.5l45 148h154l-20 -69q89 -22 125 -59.5t36 -88.5q0 -33 -17.5 -60t-49.5 -46t-77 -29.5t-99 -10.5q-41 0 -76.5 6t-70.5 17l23 76q6 23 28 23z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="666" 
d="M161 1014h139v360l5 51l-77 -61q-16 -12 -34 -12q-15 0 -27 6t-17 14l-55 75l233 194h149v-627h115v-114h-431v114z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="776" 
d="M390 1464q72 0 132 -22t103 -62.5t66.5 -99t23.5 -131.5q0 -74 -23.5 -133.5t-66.5 -101t-103 -63.5t-132 -22q-74 0 -134.5 22t-103.5 63.5t-67 101t-24 133.5q0 73 24 131.5t67 99t103.5 62.5t134.5 22zM390 973q67 0 99.5 42.5t32.5 131.5t-32.5 131t-99.5 42
q-71 0 -103.5 -42t-32.5 -131t32.5 -131.5t103.5 -42.5z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="972" 
d="M263 126l-81 38q-20 9 -29 23t-9 31q0 20 13 42l138 236q16 28 32 42q-18 16 -32 42l-138 235q-13 22 -13 43q0 36 38 54l81 38l256 -396v-32zM592 126l-81 38q-20 9 -29 23t-9 31q0 20 13 42l138 236q16 28 32 42q-18 16 -32 42l-138 235q-13 22 -13 43q0 36 38 54
l81 38l256 -396v-32z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="1424" 
d="M455 71q-26 -41 -55.5 -56t-67.5 -15h-106l834 1365q23 38 54 59.5t76 21.5h105zM1320 282h94v-88q0 -13 -9 -23t-25 -10h-60v-161h-148v161h-289q-24 0 -37.5 10.5t-16.5 26.5l-15 76l335 467h171v-459zM142 820h139v360l5 51l-77 -61q-16 -12 -34 -12q-15 0 -27 6
t-17 14l-55 75l233 194h149v-627h115v-114h-431v114zM1172 450q0 23 1.5 51t5.5 58l-202 -277h195v168z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="1424" 
d="M414 71q-26 -41 -55.5 -56t-67.5 -15h-106l834 1365q23 38 54 59.5t76 21.5h105zM1128 749q58 0 103.5 -17t77 -46t47.5 -68.5t16 -85.5q0 -41 -12.5 -75t-33 -65t-47.5 -60t-56 -59l-128 -130q28 8 55 12.5t50 4.5h129q28 0 43.5 -15t15.5 -40v-105h-541v57q0 17 6.5 36
t22.5 35l209 206q22 22 42 46.5t34 49t22.5 49t8.5 47.5q0 36 -19.5 58.5t-54.5 22.5q-33 0 -52 -15.5t-34 -48.5q-16 -36 -59 -36q-5 0 -10.5 0.5t-12.5 1.5l-99 15q17 115 91 170t186 55zM142 820h139v360l5 51l-77 -61q-16 -12 -34 -12q-15 0 -27 6t-17 14l-55 75
l233 194h149v-627h115v-114h-431v114z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="1425" 
d="M458 71q-26 -41 -55.5 -56t-67.5 -15h-106l834 1365q23 38 54 59.5t76 21.5h105zM1320 282h94v-88q0 -13 -9 -23t-25 -10h-60v-161h-148v161h-289q-24 0 -37.5 10.5t-16.5 26.5l-15 76l335 467h171v-459zM341 1455q57 0 101 -16.5t75 -43.5t47 -61.5t16 -71.5
q0 -63 -26 -106.5t-83 -67.5q60 -20 91 -56.5t31 -95.5q0 -62 -23 -106.5t-60 -74t-84 -43.5t-95 -14q-54 0 -97 10.5t-76.5 34t-58.5 62t-44 94.5l77 31q22 8 40 8q37 0 51 -28q6 -11 14 -23t20 -21.5t28 -15.5t37 -6q45 0 69 25t24 60q0 27 -7 45t-23.5 29t-45 15.5
t-70.5 4.5v116q42 0 69.5 6.5t43 18t22 28.5t6.5 38q0 37 -19.5 58.5t-58.5 21.5q-35 0 -55.5 -16t-31.5 -43q-10 -20 -21.5 -29.5t-31.5 -9.5q-11 0 -26 3l-91 15q8 57 31.5 99.5t58.5 70.5t80 41.5t96 13.5zM1172 450q0 23 1.5 51t5.5 58l-202 -277h195v168z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="841" 
d="M820 -203q-35 -30 -75.5 -56.5t-87.5 -47t-102 -32t-119 -11.5q-87 0 -158.5 23t-122.5 66t-79 104.5t-28 139.5q0 76 22 129t55.5 90.5t73 63.5t74.5 49t60.5 46.5t30.5 55.5l23 145h169l17 -163q1 -5 1 -9.5v-9.5q0 -46 -22 -77t-55 -55t-71 -45.5t-71 -48t-55 -62
t-22 -87.5q0 -36 13.5 -64.5t37.5 -49t57 -31.5t72 -11q57 0 96.5 13t67 28t46.5 28t35 13q35 0 51 -31zM324 892q0 31 11.5 59t32 48t48.5 32t60 12q31 0 59 -12t48 -32t32 -48t12 -59q0 -32 -12 -59.5t-32 -47.5t-48 -31.5t-59 -11.5q-32 0 -60 11.5t-48.5 31.5t-32 47.5
t-11.5 59.5z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="1420" 
d="M1417 0h-208q-35 0 -57.5 17.5t-32.5 43.5l-108 295h-599l-108 -295q-8 -23 -31.5 -42t-57.5 -19h-209l568 1446h275zM481 546h461l-176 481q-12 32 -26.5 75.5t-28.5 94.5q-14 -51 -27.5 -95t-26.5 -77zM568 1791q20 0 34 -1.5t25.5 -6t21.5 -12.5t22 -20l197 -202h-195
q-14 0 -24.5 0.5t-19.5 3t-18 7.5t-20 13l-297 218h274z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="1420" 
d="M1417 0h-208q-35 0 -57.5 17.5t-32.5 43.5l-108 295h-599l-108 -295q-8 -23 -31.5 -42t-57.5 -19h-209l568 1446h275zM481 546h461l-176 481q-12 32 -26.5 75.5t-28.5 94.5q-14 -51 -27.5 -95t-26.5 -77zM1095 1791l-296 -216q-11 -8 -20.5 -13t-19 -8t-20 -4t-24.5 -1
h-194l196 202q12 12 22.5 20t21.5 12.5t25 6t34 1.5h275z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="1420" 
d="M1417 0h-208q-35 0 -57.5 17.5t-32.5 43.5l-108 295h-599l-108 -295q-8 -23 -31.5 -42t-57.5 -19h-209l568 1446h275zM481 546h461l-176 481q-12 32 -26.5 75.5t-28.5 94.5q-14 -51 -27.5 -95t-26.5 -77zM1076 1549h-187q-15 0 -33 4t-29 12l-101 67q-4 2 -7.5 5t-7.5 6
q-2 -2 -6 -5t-9 -6l-101 -67q-11 -8 -29 -12t-33 -4h-187l242 219h246z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="1420" 
d="M1417 0h-208q-35 0 -57.5 17.5t-32.5 43.5l-108 295h-599l-108 -295q-8 -23 -31.5 -42t-57.5 -19h-209l568 1446h275zM481 546h461l-176 481q-12 32 -26.5 75.5t-28.5 94.5q-14 -51 -27.5 -95t-26.5 -77zM834 1699q27 0 43.5 15.5t17.5 47.5h134q0 -50 -13 -91t-37 -70
t-59 -45.5t-80 -16.5q-36 0 -69.5 11t-64 24t-57 24t-47.5 11q-27 0 -43 -16.5t-17 -48.5h-136q0 49 13 90.5t38 71.5t60 46.5t79 16.5q36 0 70 -11t64.5 -24t56.5 -24t47 -11z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="1420" 
d="M1417 0h-208q-35 0 -57.5 17.5t-32.5 43.5l-108 295h-599l-108 -295q-8 -23 -31.5 -42t-57.5 -19h-209l568 1446h275zM481 546h461l-176 481q-12 32 -26.5 75.5t-28.5 94.5q-14 -51 -27.5 -95t-26.5 -77zM629 1667q0 -27 -11 -51t-30 -42t-44 -28t-53 -10q-26 0 -49.5 10
t-41.5 28t-28.5 42t-10.5 51t10.5 51.5t28.5 43t41.5 29.5t49.5 11q28 0 53 -11t44 -29.5t30 -43t11 -51.5zM1061 1667q0 -27 -10.5 -51t-28.5 -42t-42.5 -28t-52.5 -10q-27 0 -51.5 10t-43 28t-29 42t-10.5 51t10.5 51.5t29 43t43 29.5t51.5 11q28 0 52.5 -11t42.5 -29.5
t28.5 -43t10.5 -51.5z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="1420" 
d="M1417 0h-208q-35 0 -57.5 17.5t-32.5 43.5l-108 295h-599l-108 -295q-8 -23 -31.5 -42t-57.5 -19h-209l568 1446h275zM481 546h461l-176 481q-12 32 -26.5 75.5t-28.5 94.5q-14 -51 -27.5 -95t-26.5 -77zM505 1683q0 43 17 78t45 60t65 39t78 14q43 0 81 -14t67 -39
t46 -60t17 -78q0 -42 -17 -76.5t-46 -59.5t-67 -38.5t-81 -13.5q-41 0 -78 13.5t-65 38.5t-45 59.5t-17 76.5zM623 1683q0 -39 23.5 -64t67.5 -25q40 0 64 25t24 64q0 42 -24 66t-64 24q-44 0 -67.5 -24t-23.5 -66z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="1868" 
d="M1780 1232h-694l50 -401h509v-207h-484l50 -409h569v-215h-797l-44 356h-520l-142 -296q-13 -27 -39 -43.5t-62 -16.5h-204l735 1446h1073v-214zM511 546h405l-86 700q-16 -51 -33.5 -96t-34.5 -83z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="1341" 
d="M632 -227q7 0 14.5 -2t17 -5t21 -5t27.5 -2q33 0 49.5 12.5t16.5 30.5q0 29 -36 41.5t-111 22.5l37 123q-141 15 -253 75.5t-190.5 157t-120.5 224t-42 277.5q0 163 54 298.5t152 233.5t234.5 152.5t301.5 54.5q161 0 281.5 -51.5t206.5 -136.5l-90 -125
q-8 -12 -20.5 -21t-34.5 -9q-23 0 -47 18t-61 39t-93.5 39t-143.5 18q-102 0 -187.5 -35.5t-147 -101.5t-96 -160.5t-34.5 -212.5q0 -122 34.5 -217t93.5 -160t139 -99.5t172 -34.5q55 0 99.5 6t82 19t71 33.5t66.5 50.5q10 9 21 14.5t24 5.5q22 0 38 -17l106 -115
q-80 -100 -193.5 -156.5t-268.5 -66.5l-12 -41q89 -22 125 -59.5t36 -88.5q0 -33 -17.5 -60t-49.5 -46t-77 -29.5t-99 -10.5q-41 0 -76.5 6t-70.5 17l23 76q6 23 28 23z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="1145" 
d="M1058 1446v-214h-641v-401h505v-207h-505v-409h641v-215h-912v1446h912zM474 1791q20 0 34 -1.5t25.5 -6t21.5 -12.5t22 -20l197 -202h-195q-14 0 -24.5 0.5t-19.5 3t-18 7.5t-20 13l-297 218h274z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="1145" 
d="M1058 1446v-214h-641v-401h505v-207h-505v-409h641v-215h-912v1446h912zM1001 1791l-296 -216q-11 -8 -20.5 -13t-19 -8t-20 -4t-24.5 -1h-194l196 202q12 12 22.5 20t21.5 12.5t25 6t34 1.5h275z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="1145" 
d="M1058 1446v-214h-641v-401h505v-207h-505v-409h641v-215h-912v1446h912zM982 1549h-187q-15 0 -33 4t-29 12l-101 67q-4 2 -7.5 5t-7.5 6q-2 -2 -6 -5t-9 -6l-101 -67q-11 -8 -29 -12t-33 -4h-187l242 219h246z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="1145" 
d="M1058 1446v-214h-641v-401h505v-207h-505v-409h641v-215h-912v1446h912zM535 1667q0 -27 -11 -51t-30 -42t-44 -28t-53 -10q-26 0 -49.5 10t-41.5 28t-28.5 42t-10.5 51t10.5 51.5t28.5 43t41.5 29.5t49.5 11q28 0 53 -11t44 -29.5t30 -43t11 -51.5zM967 1667
q0 -27 -10.5 -51t-28.5 -42t-42.5 -28t-52.5 -10q-27 0 -51.5 10t-43 28t-29 42t-10.5 51t10.5 51.5t29 43t43 29.5t51.5 11q28 0 52.5 -11t42.5 -29.5t28.5 -43t10.5 -51.5z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="628" 
d="M449 0h-270v1446h270v-1446zM176 1791q20 0 34 -1.5t25.5 -6t21.5 -12.5t22 -20l197 -202h-195q-14 0 -24.5 0.5t-19.5 3t-18 7.5t-20 13l-297 218h274z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="628" 
d="M449 0h-270v1446h270v-1446zM703 1791l-296 -216q-11 -8 -20.5 -13t-19 -8t-20 -4t-24.5 -1h-194l196 202q12 12 22.5 20t21.5 12.5t25 6t34 1.5h275z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="628" 
d="M449 0h-270v1446h270v-1446zM683 1549h-187q-15 0 -33 4t-29 12l-101 67q-4 2 -7.5 5t-7.5 6q-2 -2 -6 -5t-9 -6l-101 -67q-11 -8 -29 -12t-33 -4h-187l242 219h246z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="628" 
d="M449 0h-270v1446h270v-1446zM236 1667q0 -27 -11 -51t-30 -42t-44 -28t-53 -10q-26 0 -49.5 10t-41.5 28t-28.5 42t-10.5 51t10.5 51.5t28.5 43t41.5 29.5t49.5 11q28 0 53 -11t44 -29.5t30 -43t11 -51.5zM668 1667q0 -27 -10.5 -51t-28.5 -42t-42.5 -28t-52.5 -10
q-27 0 -51.5 10t-43 28t-29 42t-10.5 51t10.5 51.5t29 43t43 29.5t51.5 11q28 0 52.5 -11t42.5 -29.5t28.5 -43t10.5 -51.5z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="1581" 
d="M53 804h180v642h552q164 0 299 -53.5t231 -149t149 -228.5t53 -292t-53 -292t-149 -229t-231 -149t-299 -53h-552v651h-180v153zM1241 723q0 119 -31.5 213.5t-90.5 160t-143.5 100.5t-190.5 35h-281v-428h361v-153h-361v-437h281q106 0 190.5 35t143.5 100.5t90.5 160
t31.5 213.5z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="1512" 
d="M287 1446q18 0 30 -1.5t21.5 -6t18.5 -13t20 -22.5l759 -967q-4 35 -5.5 68.5t-1.5 62.5v879h237v-1446h-139q-32 0 -53 10t-41 36l-756 963q3 -32 4.5 -63.5t1.5 -57.5v-888h-237v1446h141v0zM895 1699q27 0 43.5 15.5t17.5 47.5h134q0 -50 -13 -91t-37 -70t-59 -45.5
t-80 -16.5q-36 0 -69.5 11t-64 24t-57 24t-47.5 11q-27 0 -43 -16.5t-17 -48.5h-136q0 49 13 90.5t38 71.5t60 46.5t79 16.5q36 0 70 -11t64.5 -24t56.5 -24t47 -11z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="1599" 
d="M1533 723q0 -159 -53 -294.5t-149 -234.5t-231 -154.5t-299 -55.5t-299.5 55.5t-232 154.5t-149.5 234.5t-53 294.5t53 294.5t149.5 234.5t232 154.5t299.5 55.5t299 -56t231 -154.5t149 -234t53 -294.5zM1257 723q0 119 -31.5 213.5t-90.5 160.5t-143.5 101t-190.5 35
t-191 -35t-144.5 -101t-91.5 -160.5t-32 -213.5t32 -213.5t91.5 -160t144.5 -100.5t191 -35t190.5 35t143.5 100.5t90.5 160t31.5 213.5zM660 1791q20 0 34 -1.5t25.5 -6t21.5 -12.5t22 -20l197 -202h-195q-14 0 -24.5 0.5t-19.5 3t-18 7.5t-20 13l-297 218h274z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="1599" 
d="M1533 723q0 -159 -53 -294.5t-149 -234.5t-231 -154.5t-299 -55.5t-299.5 55.5t-232 154.5t-149.5 234.5t-53 294.5t53 294.5t149.5 234.5t232 154.5t299.5 55.5t299 -56t231 -154.5t149 -234t53 -294.5zM1257 723q0 119 -31.5 213.5t-90.5 160.5t-143.5 101t-190.5 35
t-191 -35t-144.5 -101t-91.5 -160.5t-32 -213.5t32 -213.5t91.5 -160t144.5 -100.5t191 -35t190.5 35t143.5 100.5t90.5 160t31.5 213.5zM1187 1791l-296 -216q-11 -8 -20.5 -13t-19 -8t-20 -4t-24.5 -1h-194l196 202q12 12 22.5 20t21.5 12.5t25 6t34 1.5h275z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="1599" 
d="M1533 723q0 -159 -53 -294.5t-149 -234.5t-231 -154.5t-299 -55.5t-299.5 55.5t-232 154.5t-149.5 234.5t-53 294.5t53 294.5t149.5 234.5t232 154.5t299.5 55.5t299 -56t231 -154.5t149 -234t53 -294.5zM1257 723q0 119 -31.5 213.5t-90.5 160.5t-143.5 101t-190.5 35
t-191 -35t-144.5 -101t-91.5 -160.5t-32 -213.5t32 -213.5t91.5 -160t144.5 -100.5t191 -35t190.5 35t143.5 100.5t90.5 160t31.5 213.5zM1168 1549h-187q-15 0 -33 4t-29 12l-101 67q-4 2 -7.5 5t-7.5 6q-2 -2 -6 -5t-9 -6l-101 -67q-11 -8 -29 -12t-33 -4h-187l242 219
h246z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="1599" 
d="M1533 723q0 -159 -53 -294.5t-149 -234.5t-231 -154.5t-299 -55.5t-299.5 55.5t-232 154.5t-149.5 234.5t-53 294.5t53 294.5t149.5 234.5t232 154.5t299.5 55.5t299 -56t231 -154.5t149 -234t53 -294.5zM1257 723q0 119 -31.5 213.5t-90.5 160.5t-143.5 101t-190.5 35
t-191 -35t-144.5 -101t-91.5 -160.5t-32 -213.5t32 -213.5t91.5 -160t144.5 -100.5t191 -35t190.5 35t143.5 100.5t90.5 160t31.5 213.5zM926 1699q27 0 43.5 15.5t17.5 47.5h134q0 -50 -13 -91t-37 -70t-59 -45.5t-80 -16.5q-36 0 -69.5 11t-64 24t-57 24t-47.5 11
q-27 0 -43 -16.5t-17 -48.5h-136q0 49 13 90.5t38 71.5t60 46.5t79 16.5q36 0 70 -11t64.5 -24t56.5 -24t47 -11z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="1599" 
d="M1533 723q0 -159 -53 -294.5t-149 -234.5t-231 -154.5t-299 -55.5t-299.5 55.5t-232 154.5t-149.5 234.5t-53 294.5t53 294.5t149.5 234.5t232 154.5t299.5 55.5t299 -56t231 -154.5t149 -234t53 -294.5zM1257 723q0 119 -31.5 213.5t-90.5 160.5t-143.5 101t-190.5 35
t-191 -35t-144.5 -101t-91.5 -160.5t-32 -213.5t32 -213.5t91.5 -160t144.5 -100.5t191 -35t190.5 35t143.5 100.5t90.5 160t31.5 213.5zM721 1667q0 -27 -11 -51t-30 -42t-44 -28t-53 -10q-26 0 -49.5 10t-41.5 28t-28.5 42t-10.5 51t10.5 51.5t28.5 43t41.5 29.5t49.5 11
q28 0 53 -11t44 -29.5t30 -43t11 -51.5zM1153 1667q0 -27 -10.5 -51t-28.5 -42t-42.5 -28t-52.5 -10q-27 0 -51.5 10t-43 28t-29 42t-10.5 51t10.5 51.5t29 43t43 29.5t51.5 11q28 0 52.5 -11t42.5 -29.5t28.5 -43t10.5 -51.5z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" 
d="M1033 996l-325 -325l341 -340l-131 -129l-340 339l-341 -341l-131 129l342 342l-327 327l130 130l327 -327l324 325z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="1599" 
d="M1533 723q0 -159 -53 -294.5t-149 -234.5t-231 -154.5t-299 -55.5q-101 0 -190 21t-167 61l-76 -105q-29 -40 -69.5 -56t-78.5 -16h-106l195 268q-115 100 -178.5 245t-63.5 321q0 159 53 294.5t149.5 234.5t232 154.5t299.5 55.5q110 0 206.5 -25t178.5 -73l59 82
q13 18 24 31t23.5 20.5t27.5 11t37 3.5h138l-179 -246q104 -100 160.5 -238.5t56.5 -304.5zM342 723q0 -115 29 -206t85 -157l589 810q-104 63 -244 63q-106 0 -191 -35t-144.5 -101t-91.5 -160.5t-32 -213.5zM1257 723q0 103 -23.5 187t-68.5 148l-580 -798q95 -46 216 -46
q106 0 190.5 35t143.5 100.5t90.5 160t31.5 213.5z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="1456" 
d="M728 217q78 0 139.5 26t104 73t65 114t22.5 150v866h269v-866q0 -129 -41.5 -238.5t-119.5 -189t-189 -124t-250 -44.5t-250 44.5t-188.5 124t-119 189t-41.5 238.5v866h269v-865q0 -83 22.5 -150t64.5 -114.5t103.5 -73.5t139.5 -26zM586 1791q20 0 34 -1.5t25.5 -6
t21.5 -12.5t22 -20l197 -202h-195q-14 0 -24.5 0.5t-19.5 3t-18 7.5t-20 13l-297 218h274z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="1456" 
d="M728 217q78 0 139.5 26t104 73t65 114t22.5 150v866h269v-866q0 -129 -41.5 -238.5t-119.5 -189t-189 -124t-250 -44.5t-250 44.5t-188.5 124t-119 189t-41.5 238.5v866h269v-865q0 -83 22.5 -150t64.5 -114.5t103.5 -73.5t139.5 -26zM1113 1791l-296 -216
q-11 -8 -20.5 -13t-19 -8t-20 -4t-24.5 -1h-194l196 202q12 12 22.5 20t21.5 12.5t25 6t34 1.5h275z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="1456" 
d="M728 217q78 0 139.5 26t104 73t65 114t22.5 150v866h269v-866q0 -129 -41.5 -238.5t-119.5 -189t-189 -124t-250 -44.5t-250 44.5t-188.5 124t-119 189t-41.5 238.5v866h269v-865q0 -83 22.5 -150t64.5 -114.5t103.5 -73.5t139.5 -26zM1093 1549h-187q-15 0 -33 4t-29 12
l-101 67q-4 2 -7.5 5t-7.5 6q-2 -2 -6 -5t-9 -6l-101 -67q-11 -8 -29 -12t-33 -4h-187l242 219h246z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="1456" 
d="M728 217q78 0 139.5 26t104 73t65 114t22.5 150v866h269v-866q0 -129 -41.5 -238.5t-119.5 -189t-189 -124t-250 -44.5t-250 44.5t-188.5 124t-119 189t-41.5 238.5v866h269v-865q0 -83 22.5 -150t64.5 -114.5t103.5 -73.5t139.5 -26zM646 1667q0 -27 -11 -51t-30 -42
t-44 -28t-53 -10q-26 0 -49.5 10t-41.5 28t-28.5 42t-10.5 51t10.5 51.5t28.5 43t41.5 29.5t49.5 11q28 0 53 -11t44 -29.5t30 -43t11 -51.5zM1078 1667q0 -27 -10.5 -51t-28.5 -42t-42.5 -28t-52.5 -10q-27 0 -51.5 10t-43 28t-29 42t-10.5 51t10.5 51.5t29 43t43 29.5
t51.5 11q28 0 52.5 -11t42.5 -29.5t28.5 -43t10.5 -51.5z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="1309" 
d="M789 562v-562h-269v562l-527 884h237q35 0 55.5 -17t34.5 -43l265 -483q23 -43 40 -81.5t31 -75.5q13 38 29.5 76.5t39.5 80.5l263 483q11 22 33 41t56 19h238zM1041 1791l-296 -216q-11 -8 -20.5 -13t-19 -8t-20 -4t-24.5 -1h-194l196 202q12 12 22.5 20t21.5 12.5t25 6
t34 1.5h275z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="1250" 
d="M424 261v-261h-269v1446h269v-248h202q145 0 251 -34t175 -95t102 -146t33 -186q0 -105 -35 -192.5t-105 -150.5t-175 -98t-246 -35h-202zM424 471h202q74 0 129 18.5t91 53t54 84t18 110.5q0 58 -18 105t-54 80t-91 50.5t-129 17.5h-202v-519z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="1270" 
d="M700 1471q117 0 200 -34t135 -85t76 -110t24 -110q0 -58 -19.5 -99t-49 -72t-63.5 -54t-63.5 -44t-49 -43.5t-19.5 -51.5q0 -33 25 -57t62 -47t81 -49t81 -63.5t62 -90.5t25 -130q0 -85 -31.5 -149.5t-85.5 -108.5t-125.5 -66.5t-151.5 -22.5q-46 0 -91 9.5t-86.5 26.5
t-78.5 40t-67 50l58 94q10 17 25 26t38 9t44 -13t46 -28t57 -28t77 -13q63 0 102 37.5t39 97.5q0 47 -27 78t-67.5 55.5t-87.5 48t-87.5 55t-67.5 76.5t-27 114q0 57 21 98t52.5 73t68.5 57.5t68.5 52.5t52.5 58t21 73q0 33 -11.5 62.5t-36.5 51t-64.5 34.5t-94.5 13
q-131 0 -198.5 -80.5t-67.5 -235.5v-976h-247v984q0 106 36.5 195t104 154t164.5 101.5t219 36.5z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="1047" 
d="M936 0h-111q-35 0 -55 10.5t-30 42.5l-22 73q-39 -35 -76.5 -61.5t-77.5 -44.5t-85 -27t-100 -9q-65 0 -120 17.5t-94.5 52.5t-61.5 87t-22 121q0 58 30.5 114.5t101.5 102t189 75.5t293 34v60q0 103 -43.5 152.5t-126.5 49.5q-60 0 -100 -14t-69.5 -31.5t-54.5 -31.5
t-55 -14q-25 0 -43 13t-29 32l-45 79q177 162 427 162q90 0 160.5 -29.5t119.5 -82t74.5 -125.5t25.5 -160v-648zM456 154q38 0 70 7t60.5 21t55 34.5t53.5 48.5v173q-108 -5 -180.5 -18.5t-116.5 -34.5t-62.5 -49t-18.5 -61q0 -65 38.5 -93t100.5 -28zM457 1462
q41 0 60.5 -13.5t34.5 -39.5l143 -243h-141q-28 0 -45.5 7.5t-35.5 27.5l-247 261h231z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="1047" 
d="M936 0h-111q-35 0 -55 10.5t-30 42.5l-22 73q-39 -35 -76.5 -61.5t-77.5 -44.5t-85 -27t-100 -9q-65 0 -120 17.5t-94.5 52.5t-61.5 87t-22 121q0 58 30.5 114.5t101.5 102t189 75.5t293 34v60q0 103 -43.5 152.5t-126.5 49.5q-60 0 -100 -14t-69.5 -31.5t-54.5 -31.5
t-55 -14q-25 0 -43 13t-29 32l-45 79q177 162 427 162q90 0 160.5 -29.5t119.5 -82t74.5 -125.5t25.5 -160v-648zM456 154q38 0 70 7t60.5 21t55 34.5t53.5 48.5v173q-108 -5 -180.5 -18.5t-116.5 -34.5t-62.5 -49t-18.5 -61q0 -65 38.5 -93t100.5 -28zM892 1462l-247 -261
q-19 -20 -36.5 -27.5t-45.5 -7.5h-148l142 243q15 26 35 39.5t60 13.5h240z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="1047" 
d="M936 0h-111q-35 0 -55 10.5t-30 42.5l-22 73q-39 -35 -76.5 -61.5t-77.5 -44.5t-85 -27t-100 -9q-65 0 -120 17.5t-94.5 52.5t-61.5 87t-22 121q0 58 30.5 114.5t101.5 102t189 75.5t293 34v60q0 103 -43.5 152.5t-126.5 49.5q-60 0 -100 -14t-69.5 -31.5t-54.5 -31.5
t-55 -14q-25 0 -43 13t-29 32l-45 79q177 162 427 162q90 0 160.5 -29.5t119.5 -82t74.5 -125.5t25.5 -160v-648zM456 154q38 0 70 7t60.5 21t55 34.5t53.5 48.5v173q-108 -5 -180.5 -18.5t-116.5 -34.5t-62.5 -49t-18.5 -61q0 -65 38.5 -93t100.5 -28zM891 1168h-165
q-31 0 -50 18l-105 95q-5 5 -10.5 10t-10.5 12q-5 -7 -10.5 -12t-10.5 -10l-107 -95q-8 -7 -21.5 -12.5t-28.5 -5.5h-171l229 278h232z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="1047" 
d="M936 0h-111q-35 0 -55 10.5t-30 42.5l-22 73q-39 -35 -76.5 -61.5t-77.5 -44.5t-85 -27t-100 -9q-65 0 -120 17.5t-94.5 52.5t-61.5 87t-22 121q0 58 30.5 114.5t101.5 102t189 75.5t293 34v60q0 103 -43.5 152.5t-126.5 49.5q-60 0 -100 -14t-69.5 -31.5t-54.5 -31.5
t-55 -14q-25 0 -43 13t-29 32l-45 79q177 162 427 162q90 0 160.5 -29.5t119.5 -82t74.5 -125.5t25.5 -160v-648zM456 154q38 0 70 7t60.5 21t55 34.5t53.5 48.5v173q-108 -5 -180.5 -18.5t-116.5 -34.5t-62.5 -49t-18.5 -61q0 -65 38.5 -93t100.5 -28zM653 1359
q30 0 47 16.5t17 59.5h150q0 -56 -15.5 -101.5t-42.5 -78t-64.5 -50t-82.5 -17.5q-36 0 -67 13t-58 28t-50.5 28t-43.5 13q-29 0 -45.5 -17.5t-16.5 -59.5h-153q0 56 15.5 101.5t43.5 78t65.5 50.5t81.5 18q36 0 67.5 -13t58.5 -28t50 -28t43 -13z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="1047" 
d="M936 0h-111q-35 0 -55 10.5t-30 42.5l-22 73q-39 -35 -76.5 -61.5t-77.5 -44.5t-85 -27t-100 -9q-65 0 -120 17.5t-94.5 52.5t-61.5 87t-22 121q0 58 30.5 114.5t101.5 102t189 75.5t293 34v60q0 103 -43.5 152.5t-126.5 49.5q-60 0 -100 -14t-69.5 -31.5t-54.5 -31.5
t-55 -14q-25 0 -43 13t-29 32l-45 79q177 162 427 162q90 0 160.5 -29.5t119.5 -82t74.5 -125.5t25.5 -160v-648zM456 154q38 0 70 7t60.5 21t55 34.5t53.5 48.5v173q-108 -5 -180.5 -18.5t-116.5 -34.5t-62.5 -49t-18.5 -61q0 -65 38.5 -93t100.5 -28zM497 1292
q0 -28 -11 -52.5t-30.5 -42.5t-45 -28.5t-53.5 -10.5q-27 0 -51.5 10.5t-43.5 28.5t-30 42.5t-11 52.5q0 29 11 54.5t30 44.5t43.5 30t51.5 11q28 0 53.5 -11t45 -30t30.5 -44.5t11 -54.5zM871 1292q0 -28 -11 -52.5t-30 -42.5t-44.5 -28.5t-53.5 -10.5t-53 10.5t-43.5 28.5
t-29.5 42.5t-11 52.5q0 29 11 54.5t29.5 44.5t43.5 30t53 11t53.5 -11t44.5 -30t30 -44.5t11 -54.5z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="1047" 
d="M936 0h-111q-35 0 -55 10.5t-30 42.5l-22 73q-39 -35 -76.5 -61.5t-77.5 -44.5t-85 -27t-100 -9q-65 0 -120 17.5t-94.5 52.5t-61.5 87t-22 121q0 58 30.5 114.5t101.5 102t189 75.5t293 34v60q0 103 -43.5 152.5t-126.5 49.5q-60 0 -100 -14t-69.5 -31.5t-54.5 -31.5
t-55 -14q-25 0 -43 13t-29 32l-45 79q177 162 427 162q90 0 160.5 -29.5t119.5 -82t74.5 -125.5t25.5 -160v-648zM456 154q38 0 70 7t60.5 21t55 34.5t53.5 48.5v173q-108 -5 -180.5 -18.5t-116.5 -34.5t-62.5 -49t-18.5 -61q0 -65 38.5 -93t100.5 -28zM324 1325q0 46 18 84
t48.5 65t70.5 42t84 15q45 0 86 -15t72 -42t49.5 -65t18.5 -84q0 -45 -18.5 -82t-49.5 -63.5t-72 -41t-86 -14.5q-44 0 -84 14.5t-70.5 41t-48.5 63.5t-18 82zM458 1325q0 -38 23.5 -63.5t67.5 -25.5q40 0 64 25.5t24 63.5q0 42 -24 66.5t-64 24.5q-44 0 -67.5 -24.5
t-23.5 -66.5z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="1651" 
d="M1174 1042q87 0 162 -33.5t129.5 -96.5t85.5 -153t31 -202q0 -28 -2.5 -46.5t-8.5 -29t-16.5 -15t-27.5 -4.5h-598q14 -147 83.5 -216.5t179.5 -69.5q68 0 110.5 13t70.5 29t47 29t40 13q18 0 30 -6t20 -17l66 -84q-41 -48 -90 -80.5t-102.5 -52t-108.5 -27.5t-107 -8
q-109 0 -203.5 49t-155.5 153q-30 -54 -74 -92t-97 -63t-113 -36.5t-121 -11.5q-75 0 -134.5 19t-101.5 55.5t-64.5 91t-22.5 125.5q0 58 30.5 116.5t101.5 106.5t189 79.5t293 35.5v35q0 103 -43.5 156t-126.5 53q-60 0 -100 -15.5t-69.5 -33.5t-54.5 -33.5t-55 -15.5
q-25 0 -43 13t-29 32l-45 79q89 81 185.5 121.5t214.5 40.5q116 0 190 -44t113 -121q57 75 142 118.5t200 43.5zM695 462q-108 -5 -180.5 -20.5t-116.5 -38.5t-62.5 -53t-18.5 -63q0 -69 39 -101t107 -32q49 0 91.5 15t73.5 46.5t49 81.5t18 119v46zM1163 865
q-105 0 -162.5 -66.5t-70.5 -187.5h433q0 49 -12 95t-36.5 81.5t-62.5 56.5t-89 21z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="954" 
d="M405 -227q7 0 14.5 -2t17 -5t21 -5t27.5 -2q33 0 49.5 12.5t16.5 30.5q0 29 -36 41.5t-111 22.5l38 126q-80 14 -149.5 55.5t-120.5 108t-80 157t-29 202.5q0 113 32.5 209.5t95.5 167t156 110.5t214 40q115 0 201.5 -37t155.5 -106l-65 -90q-11 -14 -21.5 -22t-30.5 -8
q-19 0 -37 11.5t-43 26t-59.5 26t-85.5 11.5q-65 0 -114 -23.5t-81.5 -67.5t-48.5 -106.5t-16 -141.5q0 -82 17.5 -146t50.5 -107.5t80 -66t106 -22.5t95.5 14.5t61.5 32t43.5 32t41.5 14.5q27 0 45 -23l71 -90q-69 -81 -155 -118.5t-178 -45.5l-12 -44q89 -22 125 -59.5
t36 -88.5q0 -33 -17.5 -60t-49.5 -46t-77 -29.5t-99 -10.5q-41 0 -76.5 6t-70.5 17l23 76q6 23 28 23z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="1069" 
d="M556 1042q97 0 178.5 -31t140.5 -90.5t92 -146t33 -197.5q0 -28 -2.5 -46.5t-9 -29t-17.5 -15t-28 -4.5h-634q11 -158 85 -232t196 -74q60 0 103.5 14t76 31t57 31t47.5 14q15 0 26 -6t19 -17l72 -90q-41 -48 -92 -80.5t-106.5 -52t-113 -27.5t-111.5 -8
q-107 0 -199 35.5t-160 105t-107 172t-39 237.5q0 105 34 197.5t97.5 161t155 108.5t206.5 40zM561 865q-108 0 -169 -61t-78 -173h464q0 48 -13 90.5t-40 74.5t-68 50.5t-96 18.5zM473 1462q41 0 60.5 -13.5t34.5 -39.5l143 -243h-141q-28 0 -45.5 7.5t-35.5 27.5l-247 261
h231z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="1069" 
d="M556 1042q97 0 178.5 -31t140.5 -90.5t92 -146t33 -197.5q0 -28 -2.5 -46.5t-9 -29t-17.5 -15t-28 -4.5h-634q11 -158 85 -232t196 -74q60 0 103.5 14t76 31t57 31t47.5 14q15 0 26 -6t19 -17l72 -90q-41 -48 -92 -80.5t-106.5 -52t-113 -27.5t-111.5 -8
q-107 0 -199 35.5t-160 105t-107 172t-39 237.5q0 105 34 197.5t97.5 161t155 108.5t206.5 40zM561 865q-108 0 -169 -61t-78 -173h464q0 48 -13 90.5t-40 74.5t-68 50.5t-96 18.5zM908 1462l-247 -261q-19 -20 -36.5 -27.5t-45.5 -7.5h-148l142 243q15 26 35 39.5t60 13.5
h240z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="1069" 
d="M556 1042q97 0 178.5 -31t140.5 -90.5t92 -146t33 -197.5q0 -28 -2.5 -46.5t-9 -29t-17.5 -15t-28 -4.5h-634q11 -158 85 -232t196 -74q60 0 103.5 14t76 31t57 31t47.5 14q15 0 26 -6t19 -17l72 -90q-41 -48 -92 -80.5t-106.5 -52t-113 -27.5t-111.5 -8
q-107 0 -199 35.5t-160 105t-107 172t-39 237.5q0 105 34 197.5t97.5 161t155 108.5t206.5 40zM561 865q-108 0 -169 -61t-78 -173h464q0 48 -13 90.5t-40 74.5t-68 50.5t-96 18.5zM907 1168h-165q-31 0 -50 18l-105 95q-5 5 -10.5 10t-10.5 12q-5 -7 -10.5 -12t-10.5 -10
l-107 -95q-8 -7 -21.5 -12.5t-28.5 -5.5h-171l229 278h232z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="1069" 
d="M556 1042q97 0 178.5 -31t140.5 -90.5t92 -146t33 -197.5q0 -28 -2.5 -46.5t-9 -29t-17.5 -15t-28 -4.5h-634q11 -158 85 -232t196 -74q60 0 103.5 14t76 31t57 31t47.5 14q15 0 26 -6t19 -17l72 -90q-41 -48 -92 -80.5t-106.5 -52t-113 -27.5t-111.5 -8
q-107 0 -199 35.5t-160 105t-107 172t-39 237.5q0 105 34 197.5t97.5 161t155 108.5t206.5 40zM561 865q-108 0 -169 -61t-78 -173h464q0 48 -13 90.5t-40 74.5t-68 50.5t-96 18.5zM513 1292q0 -28 -11 -52.5t-30.5 -42.5t-45 -28.5t-53.5 -10.5q-27 0 -51.5 10.5
t-43.5 28.5t-30 42.5t-11 52.5q0 29 11 54.5t30 44.5t43.5 30t51.5 11q28 0 53.5 -11t45 -30t30.5 -44.5t11 -54.5zM887 1292q0 -28 -11 -52.5t-30 -42.5t-44.5 -28.5t-53.5 -10.5t-53 10.5t-43.5 28.5t-29.5 42.5t-11 52.5q0 29 11 54.5t29.5 44.5t43.5 30t53 11t53.5 -11
t44.5 -30t30 -44.5t11 -54.5z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="542" 
d="M395 1026v-1026h-247v1026h247zM190 1462q41 0 60.5 -13.5t34.5 -39.5l143 -243h-141q-28 0 -45.5 7.5t-35.5 27.5l-247 261h231z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="542" 
d="M395 1026v-1026h-247v1026h247zM625 1462l-247 -261q-19 -20 -36.5 -27.5t-45.5 -7.5h-148l142 243q15 26 35 39.5t60 13.5h240z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="542" 
d="M395 1026v-1026h-247v1026h247zM624 1168h-165q-31 0 -50 18l-105 95q-5 5 -10.5 10t-10.5 12q-5 -7 -10.5 -12t-10.5 -10l-107 -95q-8 -7 -21.5 -12.5t-28.5 -5.5h-171l229 278h232z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="542" 
d="M395 1026v-1026h-247v1026h247zM230 1292q0 -28 -11 -52.5t-30.5 -42.5t-45 -28.5t-53.5 -10.5q-27 0 -51.5 10.5t-43.5 28.5t-30 42.5t-11 52.5q0 29 11 54.5t30 44.5t43.5 30t51.5 11q28 0 53.5 -11t45 -30t30.5 -44.5t11 -54.5zM604 1292q0 -28 -11 -52.5t-30 -42.5
t-44.5 -28.5t-53.5 -10.5t-53 10.5t-43.5 28.5t-29.5 42.5t-11 52.5q0 29 11 54.5t29.5 44.5t43.5 30t53 11t53.5 -11t44.5 -30t30 -44.5t11 -54.5z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="1136" 
d="M386 1062q-10 16 -10 33q0 30 32 48l75 43q-32 13 -67.5 24t-74.5 22q-25 7 -41.5 25t-16.5 48q0 16 8 42l29 85q101 -17 194.5 -48t176.5 -80l176 114l49 -80q10 -17 10 -31t-7.5 -26t-21.5 -20l-81 -46q57 -51 103 -113.5t79 -137.5t50.5 -164t17.5 -192
q0 -147 -33.5 -262.5t-98.5 -196t-161 -122.5t-220 -42q-104 0 -193 33.5t-154 96.5t-102.5 153.5t-37.5 205.5q0 92 32.5 175t92 145.5t143.5 99.5t188 37q95 0 174 -34.5t140 -100.5q-21 106 -71 182.5t-135 133.5l-198 -128zM559 173q58 0 107 21t85 67.5t57.5 120.5
t24.5 180q-15 37 -38.5 70t-55.5 57.5t-73.5 39t-93.5 14.5q-64 0 -112.5 -21t-81.5 -58t-50 -86t-17 -106q0 -73 19.5 -128.5t53 -93.5t78.5 -57.5t97 -19.5z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="1137" 
d="M132 0v1026h151q48 0 63 -45l17 -81q31 32 65.5 58t73 45t82.5 29t96 10q84 0 149 -28.5t108.5 -80t66 -123t22.5 -157.5v-653h-247v653q0 94 -43.5 145.5t-130.5 51.5q-64 0 -120 -29t-106 -79v-742h-247zM685 1359q30 0 47 16.5t17 59.5h150q0 -56 -15.5 -101.5
t-42.5 -78t-64.5 -50t-82.5 -17.5q-36 0 -67 13t-58 28t-50.5 28t-43.5 13q-29 0 -45.5 -17.5t-16.5 -59.5h-153q0 56 15.5 101.5t43.5 78t65.5 50.5t81.5 18q36 0 67.5 -13t58.5 -28t50 -28t43 -13z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="1137" 
d="M570 1042q115 0 208.5 -37t159.5 -105t102 -166t36 -219q0 -122 -36 -220t-102 -167t-159.5 -106t-208.5 -37t-209 37t-160.5 106t-103 167t-36.5 220q0 121 36.5 219t103 166t160.5 105t209 37zM570 175q128 0 189.5 86t61.5 252t-61.5 253t-189.5 87q-130 0 -192 -87.5
t-62 -252.5t62 -251.5t192 -86.5zM482 1462q41 0 60.5 -13.5t34.5 -39.5l143 -243h-141q-28 0 -45.5 7.5t-35.5 27.5l-247 261h231z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="1137" 
d="M570 1042q115 0 208.5 -37t159.5 -105t102 -166t36 -219q0 -122 -36 -220t-102 -167t-159.5 -106t-208.5 -37t-209 37t-160.5 106t-103 167t-36.5 220q0 121 36.5 219t103 166t160.5 105t209 37zM570 175q128 0 189.5 86t61.5 252t-61.5 253t-189.5 87q-130 0 -192 -87.5
t-62 -252.5t62 -251.5t192 -86.5zM917 1462l-247 -261q-19 -20 -36.5 -27.5t-45.5 -7.5h-148l142 243q15 26 35 39.5t60 13.5h240z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="1137" 
d="M570 1042q115 0 208.5 -37t159.5 -105t102 -166t36 -219q0 -122 -36 -220t-102 -167t-159.5 -106t-208.5 -37t-209 37t-160.5 106t-103 167t-36.5 220q0 121 36.5 219t103 166t160.5 105t209 37zM570 175q128 0 189.5 86t61.5 252t-61.5 253t-189.5 87q-130 0 -192 -87.5
t-62 -252.5t62 -251.5t192 -86.5zM916 1168h-165q-31 0 -50 18l-105 95q-5 5 -10.5 10t-10.5 12q-5 -7 -10.5 -12t-10.5 -10l-107 -95q-8 -7 -21.5 -12.5t-28.5 -5.5h-171l229 278h232z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="1137" 
d="M570 1042q115 0 208.5 -37t159.5 -105t102 -166t36 -219q0 -122 -36 -220t-102 -167t-159.5 -106t-208.5 -37t-209 37t-160.5 106t-103 167t-36.5 220q0 121 36.5 219t103 166t160.5 105t209 37zM570 175q128 0 189.5 86t61.5 252t-61.5 253t-189.5 87q-130 0 -192 -87.5
t-62 -252.5t62 -251.5t192 -86.5zM678 1359q30 0 47 16.5t17 59.5h150q0 -56 -15.5 -101.5t-42.5 -78t-64.5 -50t-82.5 -17.5q-36 0 -67 13t-58 28t-50.5 28t-43.5 13q-29 0 -45.5 -17.5t-16.5 -59.5h-153q0 56 15.5 101.5t43.5 78t65.5 50.5t81.5 18q36 0 67.5 -13
t58.5 -28t50 -28t43 -13z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="1137" 
d="M570 1042q115 0 208.5 -37t159.5 -105t102 -166t36 -219q0 -122 -36 -220t-102 -167t-159.5 -106t-208.5 -37t-209 37t-160.5 106t-103 167t-36.5 220q0 121 36.5 219t103 166t160.5 105t209 37zM570 175q128 0 189.5 86t61.5 252t-61.5 253t-189.5 87q-130 0 -192 -87.5
t-62 -252.5t62 -251.5t192 -86.5zM522 1292q0 -28 -11 -52.5t-30.5 -42.5t-45 -28.5t-53.5 -10.5q-27 0 -51.5 10.5t-43.5 28.5t-30 42.5t-11 52.5q0 29 11 54.5t30 44.5t43.5 30t51.5 11q28 0 53.5 -11t45 -30t30.5 -44.5t11 -54.5zM896 1292q0 -28 -11 -52.5t-30 -42.5
t-44.5 -28.5t-53.5 -10.5t-53 10.5t-43.5 28.5t-29.5 42.5t-11 52.5q0 29 11 54.5t29.5 44.5t43.5 30t53 11t53.5 -11t44.5 -30t30 -44.5t11 -54.5z" />
    <glyph glyph-name="divide" unicode="&#xf7;" 
d="M91 763h976v-183h-976v183zM427 1027q0 31 11.5 59t31.5 48t48 32t60 12q31 0 59 -12t48.5 -32t32.5 -48t12 -59q0 -32 -12 -59.5t-32.5 -47.5t-48.5 -31.5t-59 -11.5q-32 0 -60 11.5t-48 31.5t-31.5 47.5t-11.5 59.5zM427 315q0 31 11.5 59t31.5 48t48 32t60 12
q31 0 59 -12t48.5 -32t32.5 -48t12 -59q0 -32 -12 -59.5t-32.5 -47.5t-48.5 -31.5t-59 -11.5q-32 0 -60 11.5t-48 31.5t-31.5 47.5t-11.5 59.5z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="1137" 
d="M943 893q64 -69 98.5 -164t34.5 -214q0 -122 -36 -220t-102 -167t-159.5 -106t-208.5 -37q-72 0 -135.5 14.5t-118.5 42.5l-34 -47q-29 -39 -69.5 -55.5t-78.5 -16.5h-91l154 209q-66 69 -101 165.5t-35 217.5t36.5 219t103 166t160.5 105t209 37q72 0 136 -15t118 -43
l56 75q13 18 24 30.5t23.5 20.5t27.5 11.5t37 3.5h122zM300 513q0 -119 32 -197l376 510q-56 34 -138 34q-131 0 -200.5 -90.5t-69.5 -256.5zM570 167q128 0 197.5 90t69.5 256q0 59 -8 107t-23 86l-374 -507q55 -32 138 -32z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="1137" 
d="M358 1026v-652q0 -94 43.5 -145.5t130.5 -51.5q64 0 120 28.5t106 78.5v742h247v-1026h-151q-48 0 -63 45l-17 82q-32 -32 -66 -58.5t-72.5 -45t-83 -29t-95.5 -10.5q-84 0 -148.5 28.5t-108.5 80.5t-66.5 123.5t-22.5 157.5v652h247zM477 1462q41 0 60.5 -13.5
t34.5 -39.5l143 -243h-141q-28 0 -45.5 7.5t-35.5 27.5l-247 261h231z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="1137" 
d="M358 1026v-652q0 -94 43.5 -145.5t130.5 -51.5q64 0 120 28.5t106 78.5v742h247v-1026h-151q-48 0 -63 45l-17 82q-32 -32 -66 -58.5t-72.5 -45t-83 -29t-95.5 -10.5q-84 0 -148.5 28.5t-108.5 80.5t-66.5 123.5t-22.5 157.5v652h247zM912 1462l-247 -261
q-19 -20 -36.5 -27.5t-45.5 -7.5h-148l142 243q15 26 35 39.5t60 13.5h240z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="1137" 
d="M358 1026v-652q0 -94 43.5 -145.5t130.5 -51.5q64 0 120 28.5t106 78.5v742h247v-1026h-151q-48 0 -63 45l-17 82q-32 -32 -66 -58.5t-72.5 -45t-83 -29t-95.5 -10.5q-84 0 -148.5 28.5t-108.5 80.5t-66.5 123.5t-22.5 157.5v652h247zM911 1168h-165q-31 0 -50 18
l-105 95q-5 5 -10.5 10t-10.5 12q-5 -7 -10.5 -12t-10.5 -10l-107 -95q-8 -7 -21.5 -12.5t-28.5 -5.5h-171l229 278h232z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="1137" 
d="M358 1026v-652q0 -94 43.5 -145.5t130.5 -51.5q64 0 120 28.5t106 78.5v742h247v-1026h-151q-48 0 -63 45l-17 82q-32 -32 -66 -58.5t-72.5 -45t-83 -29t-95.5 -10.5q-84 0 -148.5 28.5t-108.5 80.5t-66.5 123.5t-22.5 157.5v652h247zM517 1292q0 -28 -11 -52.5
t-30.5 -42.5t-45 -28.5t-53.5 -10.5q-27 0 -51.5 10.5t-43.5 28.5t-30 42.5t-11 52.5q0 29 11 54.5t30 44.5t43.5 30t51.5 11q28 0 53.5 -11t45 -30t30.5 -44.5t11 -54.5zM891 1292q0 -28 -11 -52.5t-30 -42.5t-44.5 -28.5t-53.5 -10.5t-53 10.5t-43.5 28.5t-29.5 42.5
t-11 52.5q0 29 11 54.5t29.5 44.5t43.5 30t53 11t53.5 -11t44.5 -30t30 -44.5t11 -54.5z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="1067" 
d="M496 -282q-11 -26 -28.5 -39.5t-53.5 -13.5h-184l192 411l-415 950h216q30 0 46 -14t24 -32l219 -532q11 -26 19 -54t14 -56q8 29 17.5 56t20.5 55l206 531q8 20 26.5 33t41.5 13h198zM899 1462l-247 -261q-19 -20 -36.5 -27.5t-45.5 -7.5h-148l142 243q15 26 35 39.5
t60 13.5h240z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="1131" 
d="M132 -335v1821h247v-586q61 65 138 103.5t180 38.5q84 0 153.5 -34.5t120 -100.5t78 -163t27.5 -223q0 -115 -31 -213t-88.5 -170t-139 -112.5t-182.5 -40.5q-44 0 -81 8t-68 23.5t-57 37t-50 48.5v-437h-247zM612 850q-77 0 -131.5 -32.5t-101.5 -91.5v-460
q42 -52 91.5 -72.5t106.5 -20.5q56 0 101.5 21t77 64t48.5 108.5t17 154.5q0 90 -14.5 152.5t-41.5 101.5t-65.5 57t-87.5 18z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="1067" 
d="M496 -282q-11 -26 -28.5 -39.5t-53.5 -13.5h-184l192 411l-415 950h216q30 0 46 -14t24 -32l219 -532q11 -26 19 -54t14 -56q8 29 17.5 56t20.5 55l206 531q8 20 26.5 33t41.5 13h198zM504 1292q0 -28 -11 -52.5t-30.5 -42.5t-45 -28.5t-53.5 -10.5q-27 0 -51.5 10.5
t-43.5 28.5t-30 42.5t-11 52.5q0 29 11 54.5t30 44.5t43.5 30t51.5 11q28 0 53.5 -11t45 -30t30.5 -44.5t11 -54.5zM878 1292q0 -28 -11 -52.5t-30 -42.5t-44.5 -28.5t-53.5 -10.5t-53 10.5t-43.5 28.5t-29.5 42.5t-11 52.5q0 29 11 54.5t29.5 44.5t43.5 30t53 11t53.5 -11
t44.5 -30t30 -44.5t11 -54.5z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="1420" 
d="M1404 -187q18 0 23 -16l38 -91q-33 -22 -82.5 -36.5t-104.5 -14.5q-98 0 -151 41t-53 106q0 55 36.5 106t104.5 92h-6q-35 0 -57.5 17.5t-32.5 43.5l-108 295h-599l-108 -295q-8 -23 -31.5 -42t-57.5 -19h-209l568 1446h275l568 -1446h-53q-20 -11 -39 -25t-34 -31.5
t-24 -37.5t-9 -42q0 -30 19 -47.5t53 -17.5q19 0 31 2t19.5 5t13 5t10.5 2zM481 546h461l-176 481q-12 32 -26.5 75.5t-28.5 94.5q-14 -51 -28 -95t-26 -77z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="1047" 
d="M936 0q-20 -11 -39 -25t-34 -31.5t-24 -37.5t-9 -42q0 -30 19 -47.5t53 -17.5q19 0 31 2t19.5 5t13 5t10.5 2q18 0 23 -16l38 -91q-33 -22 -82.5 -36.5t-104.5 -14.5q-98 0 -151 41t-53 106q0 56 38 107.5t108 93.5q-20 4 -32.5 15.5t-19.5 34.5l-22 73
q-39 -35 -76.5 -61.5t-77.5 -44.5t-85 -27t-100 -9q-65 0 -120 17.5t-94.5 52.5t-61.5 87t-22 121q0 58 30.5 114.5t101.5 102t189 75.5t293 34v60q0 103 -43.5 152.5t-126.5 49.5q-60 0 -100 -14t-69.5 -31.5t-54.5 -31.5t-55 -14q-25 0 -43 13.5t-29 31.5l-45 79
q177 162 427 162q90 0 160.5 -29.5t119.5 -82t74.5 -125.5t25.5 -160v-648zM456 154q38 0 70 7t60.5 21t55 34.5t53.5 48.5v173q-108 -5 -180.5 -18.5t-116.5 -34.5t-62.5 -49t-18.5 -61q0 -65 38.5 -93t100.5 -28z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="1341" 
d="M1140 341q22 0 38 -17l106 -115q-88 -109 -216.5 -167t-308.5 -58q-161 0 -289.5 55t-219.5 153t-139.5 234t-48.5 297q0 163 54 298.5t152 233.5t234.5 152.5t301.5 54.5q161 0 281.5 -51.5t206.5 -136.5l-90 -125q-8 -12 -20.5 -21t-34.5 -9q-23 0 -47 18t-61 39
t-93.5 39t-143.5 18q-102 0 -187.5 -35.5t-147 -101.5t-96 -160.5t-34.5 -212.5q0 -122 34.5 -217t93.5 -160t139 -99.5t172 -34.5q55 0 99.5 6t82 19t71 33.5t66.5 50.5q10 9 21 14.5t24 5.5zM1181 1791l-296 -216q-11 -8 -20.5 -13t-19 -8t-20 -4t-24.5 -1h-194l196 202
q12 12 22.5 20t21.5 12.5t25 6t34 1.5h275z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="954" 
d="M853 809q-11 -14 -21.5 -22t-30.5 -8q-19 0 -37 11.5t-43 26t-59.5 26t-85.5 11.5q-65 0 -114 -23.5t-81.5 -67.5t-48.5 -106.5t-16 -141.5q0 -82 17.5 -146t50.5 -107.5t80 -66t106 -22.5t95.5 14.5t61.5 32t43.5 32t41.5 14.5q30 0 45 -23l71 -90q-41 -48 -89 -80.5
t-99.5 -52t-106.5 -27.5t-109 -8q-95 0 -179 35.5t-146.5 103.5t-99 166.5t-36.5 224.5q0 113 32.5 209.5t95.5 167t156 110.5t214 40q115 0 201.5 -37t155.5 -106zM905 1462l-247 -261q-19 -20 -36.5 -27.5t-45.5 -7.5h-148l142 243q15 26 35 39.5t60 13.5h240z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="1158" 
d="M1007 -187q18 0 23 -16l38 -91q-33 -22 -82.5 -36.5t-104.5 -14.5q-98 0 -151 41t-53 106q0 55 36.5 106t104.5 92h-672v1446h912v-214h-641v-401h505v-207h-505v-409h641v-215h-91q-20 -11 -39 -25t-34 -31.5t-24 -37.5t-9 -42q0 -30 19 -47.5t53 -17.5q19 0 31 2
t19.5 5t13 5t10.5 2z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="1069" 
d="M745 -187q18 0 23 -16l38 -91q-33 -22 -82.5 -36.5t-104.5 -14.5q-98 0 -151 41t-53 106q0 51 31 98t88 86q-101 5 -187 43t-149 107.5t-99 169.5t-36 229q0 105 34 197.5t97.5 161t155 108.5t206.5 40q97 0 178.5 -31t140.5 -90.5t92 -146t33 -197.5q0 -28 -2.5 -46.5
t-9 -29t-17.5 -15t-28 -4.5h-634q11 -158 85 -232t196 -74q60 0 103.5 14t76 31t57 31t47.5 14q31 0 45 -23l72 -90q-59 -68 -136 -105t-157 -51q-19 -11 -37 -25t-31.5 -31t-22 -36.5t-8.5 -40.5q0 -30 19 -47.5t53 -17.5q19 0 31 2t19.5 5t13 5t10.5 2zM561 865
q-108 0 -169 -61t-78 -173h464q0 48 -13 90.5t-40 74.5t-68 50.5t-96 18.5z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="542" 
d="M395 1026v-1026h-247v1026h247z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="1101" 
d="M483 870l358 179v-180q0 -37 -33 -54l-325 -169v-424h579v-222h-848v528l-180 -90v185q0 32 31 48l149 77v698h269v-576z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="678" 
d="M463 1486v-557l163 66v-142q0 -41 -34 -54l-129 -55v-744h-247v657l-164 -66v146q0 35 31 48l133 56v645h247z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="1512" 
d="M287 1446q18 0 30 -1.5t21.5 -6t18.5 -13t20 -22.5l759 -967q-4 35 -5.5 68.5t-1.5 62.5v879h237v-1446h-139q-32 0 -53 10t-41 36l-756 963q3 -32 4.5 -63.5t1.5 -57.5v-888h-237v1446h141v0zM1157 1791l-296 -216q-11 -8 -20.5 -13t-19 -8t-20 -4t-24.5 -1h-194
l196 202q12 12 22.5 20t21.5 12.5t25 6t34 1.5h275z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="1137" 
d="M132 0v1026h151q48 0 63 -45l17 -81q31 32 65.5 58t73 45t82.5 29t96 10q84 0 149 -28.5t108.5 -80t66 -123t22.5 -157.5v-653h-247v653q0 94 -43.5 145.5t-130.5 51.5q-64 0 -120 -29t-106 -79v-742h-247zM930 1462l-247 -261q-19 -20 -36.5 -27.5t-45.5 -7.5h-148
l142 243q15 26 35 39.5t60 13.5h240z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="2163" 
d="M2075 1446v-214h-641v-401h506v-207h-506v-409h641v-215h-880v185q-87 -94 -204.5 -147t-263.5 -53q-148 0 -269.5 55.5t-208.5 154t-134.5 234t-47.5 294.5t47.5 294.5t134.5 234.5t208.5 154.5t269.5 55.5q145 0 263 -53t205 -148v185h880zM1164 723q0 119 -28 214.5
t-81 163t-129 103.5t-171 36q-96 0 -173 -36t-130 -103.5t-81.5 -163t-28.5 -214.5t28.5 -214.5t81.5 -162.5t130 -103t173 -36q95 0 171 36t129 103t81 162.5t28 214.5z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="1749" 
d="M1272 1042q87 0 162 -33.5t129.5 -96.5t85.5 -153t31 -202q0 -28 -2.5 -46.5t-8.5 -29t-16.5 -15t-27.5 -4.5h-598q14 -147 83.5 -216.5t179.5 -69.5q56 0 96 14t70.5 31t54.5 31t47 14q26 0 44 -23l72 -90q-41 -48 -90 -80.5t-102.5 -52t-108.5 -27.5t-107 -8
q-108 0 -202.5 48.5t-155.5 152.5q-60 -97 -156.5 -149t-224.5 -52q-102 0 -187.5 37t-147.5 106t-96.5 167t-34.5 220q0 121 35 219t97.5 166t150 105t192.5 37q124 0 217.5 -51.5t152.5 -146.5q55 90 147 144t219 54zM550 175q118 0 175 86t57 252t-57 253t-175 87
q-120 0 -177 -87.5t-57 -252.5q0 -166 56.5 -252t177.5 -86zM1261 865q-105 0 -162.5 -66.5t-70.5 -187.5h433q0 49 -12 95t-36.5 81.5t-62.5 56.5t-89 21z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="1107" 
d="M921 1183q-11 -22 -25.5 -31t-34.5 -9t-45 15.5t-59 34.5t-79.5 34.5t-107.5 15.5q-56 0 -97.5 -13.5t-70 -37.5t-42.5 -57.5t-14 -73.5q0 -51 28.5 -85t75.5 -58t107 -43t122.5 -40.5t122.5 -50t107 -72t75.5 -106.5t28.5 -153q0 -98 -33.5 -183.5t-98 -149t-157.5 -100
t-213 -36.5q-69 0 -136 13.5t-128.5 38.5t-115 60t-95.5 78l78 129q11 14 26.5 23.5t34.5 9.5q25 0 54 -20.5t68.5 -45.5t93 -45.5t128.5 -20.5q115 0 178 54.5t63 156.5q0 57 -28.5 93t-75.5 60.5t-107 41.5t-122 37t-122 48t-107 73t-75.5 112.5t-28.5 166.5
q0 79 31.5 154t92 133t148.5 93t201 35q128 0 236 -40t184 -112zM977 1791l-296 -216q-11 -8 -20.5 -13t-19 -8t-20 -4t-24.5 -1h-194l196 202q12 12 22.5 20t21.5 12.5t25 6t34 1.5h275z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="874" 
d="M741 826q-10 -16 -21 -22.5t-28 -6.5q-18 0 -38.5 10t-47.5 22.5t-61.5 22.5t-81.5 10q-73 0 -115 -31t-42 -81q0 -33 21.5 -55.5t57 -39.5t80.5 -30.5t92 -29.5t92 -36.5t80.5 -52t57 -75.5t21.5 -106q0 -74 -27 -136.5t-79 -108t-128.5 -71t-175.5 -25.5
q-53 0 -103.5 9.5t-97 26.5t-86 40t-69.5 50l57 94q11 17 26 26t38 9t43.5 -13t47.5 -28t63.5 -28t92.5 -13q44 0 75.5 10.5t52 27.5t30 39.5t9.5 46.5q0 36 -21.5 59t-57 40t-81 30.5t-93 29.5t-93 37.5t-81 54.5t-57 81t-21.5 116q0 63 25 120t73.5 99.5t121 68
t167.5 25.5q106 0 193 -35t145 -92zM815 1462l-247 -261q-19 -20 -36.5 -27.5t-45.5 -7.5h-148l142 243q15 26 35 39.5t60 13.5h240z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="1073" 
d="M921 1183q-11 -22 -25.5 -31t-34.5 -9t-45 15.5t-59 34.5t-79.5 34.5t-107.5 15.5q-56 0 -97.5 -13.5t-70 -37.5t-42.5 -57.5t-14 -73.5q0 -51 28.5 -85t75.5 -58t107 -43t122.5 -40.5t122.5 -50t107 -72t75.5 -106.5t28.5 -153q0 -98 -33.5 -183.5t-98 -149t-157.5 -100
t-213 -36.5q-69 0 -136 13.5t-128.5 38.5t-115 60t-95.5 78l78 129q11 14 26.5 23.5t34.5 9.5q25 0 54 -20.5t68.5 -45.5t93 -45.5t128.5 -20.5q115 0 178 54.5t63 156.5q0 57 -28.5 93t-75.5 60.5t-107 41.5t-122 37t-122 48t-107 73t-75.5 112.5t-28.5 166.5
q0 79 31.5 154t92 133t148.5 93t201 35q128 0 236 -40t184 -112zM217 1768h187q15 0 33 -4t29 -12l101 -67q4 -2 7.5 -4.5t7.5 -5.5q4 3 7.5 5.5t7.5 4.5l101 67q11 8 29 12t33 4h187l-242 -219h-246z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="874" 
d="M741 826q-10 -16 -21 -22.5t-28 -6.5q-18 0 -38.5 10t-47.5 22.5t-61.5 22.5t-81.5 10q-73 0 -115 -31t-42 -81q0 -33 21.5 -55.5t57 -39.5t80.5 -30.5t92 -29.5t92 -36.5t80.5 -52t57 -75.5t21.5 -106q0 -74 -27 -136.5t-79 -108t-128.5 -71t-175.5 -25.5
q-53 0 -103.5 9.5t-97 26.5t-86 40t-69.5 50l57 94q11 17 26 26t38 9t43.5 -13t47.5 -28t63.5 -28t92.5 -13q44 0 75.5 10.5t52 27.5t30 39.5t9.5 46.5q0 36 -21.5 59t-57 40t-81 30.5t-93 29.5t-93 37.5t-81 54.5t-57 81t-21.5 116q0 63 25 120t73.5 99.5t121 68
t167.5 25.5q106 0 193 -35t145 -92zM576 1168h-232l-229 278h171q15 0 28.5 -5t21.5 -12l106 -96q9 -8 21 -23q5 7 11 12.5t11 10.5l105 96q8 7 22 12t28 5h165z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="1309" 
d="M789 562v-562h-269v562l-527 884h237q35 0 55.5 -17t34.5 -43l265 -483q23 -43 40 -81.5t31 -75.5q13 38 29.5 76.5t39.5 80.5l263 483q11 22 33 41t56 19h238zM575 1667q0 -27 -11 -51t-30 -42t-44 -28t-53 -10q-26 0 -49.5 10t-41.5 28t-28.5 42t-10.5 51t10.5 51.5
t28.5 43t41.5 29.5t49.5 11q28 0 53 -11t44 -29.5t30 -43t11 -51.5zM1007 1667q0 -27 -10.5 -51t-28.5 -42t-42.5 -28t-52.5 -10q-27 0 -51.5 10t-43 28t-29 42t-10.5 51t10.5 51.5t29 43t43 29.5t51.5 11q28 0 52.5 -11t42.5 -29.5t28.5 -43t10.5 -51.5z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="1234" 
d="M1179 1446v-99q0 -46 -26 -84l-742 -1048h750v-215h-1099v106q0 20 7 38.5t18 34.5l744 1053h-722v214h1070zM1032 1791l-296 -216q-11 -8 -20.5 -13t-19 -8t-20 -4t-24.5 -1h-194l196 202q12 12 22.5 20t21.5 12.5t25 6t34 1.5h275z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="939" 
d="M874 924q0 -26 -9.5 -50.5t-22.5 -41.5l-488 -642h506v-190h-793v103q0 17 8 40.5t25 44.5l492 649h-496v189h778v-102zM844 1462l-247 -261q-19 -20 -36.5 -27.5t-45.5 -7.5h-148l142 243q15 26 35 39.5t60 13.5h240z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="1234" 
d="M1179 1446v-99q0 -46 -26 -84l-742 -1048h750v-215h-1099v106q0 20 7 38.5t18 34.5l744 1053h-722v214h1070zM799 1695q0 -30 -12.5 -56.5t-33.5 -47t-49 -32.5t-59 -12q-29 0 -56 12t-47.5 32.5t-32.5 47t-12 56.5q0 31 12 58.5t32.5 48t47.5 32.5t56 12q31 0 59 -12
t49 -32.5t33.5 -48t12.5 -58.5z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="939" 
d="M874 924q0 -26 -9.5 -50.5t-22.5 -41.5l-488 -642h506v-190h-793v103q0 17 8 40.5t25 44.5l492 649h-496v189h778v-102zM658 1329q0 -32 -13 -60.5t-34.5 -49.5t-51 -33.5t-62.5 -12.5q-32 0 -60 12.5t-49 33.5t-33.5 49.5t-12.5 60.5q0 33 12.5 61.5t33.5 50t49 34
t60 12.5q33 0 62.5 -12.5t51 -34t34.5 -50t13 -61.5z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="1234" 
d="M1179 1446v-99q0 -46 -26 -84l-742 -1048h750v-215h-1099v106q0 20 7 38.5t18 34.5l744 1053h-722v214h1070zM283 1767h187q15 0 33 -4t29 -12l101 -67q4 -2 7.5 -4.5t7.5 -5.5q4 3 7.5 5.5t7.5 4.5l101 67q11 8 29 12t33 4h187l-242 -219h-246z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="939" 
d="M874 924q0 -26 -9.5 -50.5t-22.5 -41.5l-488 -642h506v-190h-793v103q0 17 8 40.5t25 44.5l492 649h-496v189h778v-102zM614 1168h-232l-229 278h171q15 0 28.5 -5t21.5 -12l106 -96q9 -8 21 -23q5 7 11 12.5t11 10.5l105 96q8 7 22 12t28 5h165z" />
    <glyph glyph-name="florin" unicode="&#x192;" 
d="M706 745l-83 -688q-13 -106 -50.5 -182t-102 -125t-157 -72t-215.5 -23v129q0 76 79 76q40 0 74 11t60 35t43 63t24 95l83 676l-123 18q-56 12 -56 62v101h201l18 148q25 200 152 301t373 101v-135q0 -39 -18.5 -54.5t-59.5 -15.5t-75.5 -11t-61.5 -35t-45 -63t-25 -95
l-19 -141h294v-176h-310z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="638" 
d="M664 1168h-165q-31 0 -50 18l-105 95q-5 5 -10.5 10t-10.5 12q-5 -7 -10.5 -12t-10.5 -10l-107 -95q-8 -7 -21.5 -12.5t-28.5 -5.5h-171l229 278h232z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="638" 
d="M435 1168h-232l-229 278h171q15 0 28.5 -5t21.5 -12l106 -96q9 -8 21 -23q5 7 11 12.5t11 10.5l105 96q8 7 22 12t28 5h165z" />
    <glyph glyph-name="uni02C9" unicode="&#x2c9;" horiz-adv-x="638" 
d="M20 1372h598v-158h-598v158z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="638" 
d="M319 1161q-86 0 -143.5 22.5t-93 61.5t-50.5 91t-15 110h173q0 -30 5.5 -54t20 -40.5t39.5 -25.5t64 -9t64 9t39.5 25.5t20 40.5t5.5 54h173q0 -58 -15 -110t-50.5 -91t-93.5 -61.5t-143 -22.5z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="638" 
d="M478 1329q0 -32 -13 -60.5t-34.5 -49.5t-51 -33.5t-62.5 -12.5q-32 0 -60 12.5t-49 33.5t-33.5 49.5t-12.5 60.5q0 33 12.5 61.5t33.5 50t49 34t60 12.5q33 0 62.5 -12.5t51 -34t34.5 -50t13 -61.5z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="638" 
d="M96 1325q0 46 18 84t48.5 65t70.5 42t84 15q45 0 86 -15t72 -42t49.5 -65t18.5 -84q0 -45 -18.5 -82t-49.5 -63.5t-72 -41t-86 -14.5q-44 0 -84 14.5t-70.5 41t-48.5 63.5t-18 82zM230 1325q0 -38 23.5 -63.5t67.5 -25.5q40 0 64 25.5t24 63.5q0 42 -24 66.5t-64 24.5
q-44 0 -67.5 -24.5t-23.5 -66.5z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="638" 
d="M461 -187q18 0 23 -16l38 -91q-33 -22 -82.5 -36.5t-104.5 -14.5q-98 0 -151 41t-53 106q0 59 42 113.5t120 96.5l128 -12q-20 -11 -39 -25t-34 -31.5t-24 -37.5t-9 -42q0 -30 19 -47.5t53 -17.5q19 0 31 2t19.5 5t13 5t10.5 2z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="638" 
d="M426 1359q30 0 47 16.5t17 59.5h150q0 -56 -15.5 -101.5t-42.5 -78t-64.5 -50t-82.5 -17.5q-36 0 -67 13t-58 28t-50.5 28t-43.5 13q-29 0 -45.5 -17.5t-16.5 -59.5h-153q0 56 15.5 101.5t43.5 78t65.5 50.5t81.5 18q36 0 67.5 -13t58.5 -28t50 -28t43 -13z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="638" 
d="M451 1462l-199 -261q-16 -20 -35 -27.5t-47 -7.5h-101l126 243q14 26 34.5 39.5t60.5 13.5h161zM800 1462l-252 -261q-19 -19 -36 -27t-46 -8h-116l177 243q9 13 17.5 22.5t19 16.5t24.5 10.5t34 3.5h178z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="1288" 
d="M1251 1026v-98q0 -32 -19.5 -55t-57.5 -23h-107v-850h-247v850h-339v-584q0 -63 -15.5 -114.5t-49 -88.5t-86.5 -57t-128 -20q-36 0 -75.5 7t-74.5 26l7 104q3 20 15.5 30t52.5 10q61 0 84 24.5t23 81.5v581h-179v88q0 15 6 30.5t18 28t29 21t39 8.5h1104z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="1137" 
d="M163 687h811v-175h-811v175z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="1684" 
d="M163 687h1357v-175h-1357v175z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="450" 
d="M166 977q-68 111 -68 224q0 100 50.5 191.5t146.5 167.5l77 -47q10 -6 14 -14.5t4 -16.5q0 -9 -4 -17t-9 -14q-13 -15 -27.5 -35t-26.5 -43.5t-20 -51.5t-8 -60q0 -34 11 -72.5t37 -80.5q9 -14 9 -28q0 -32 -36 -45z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="450" 
d="M309 1532q35 -56 51.5 -112t16.5 -112q0 -100 -50.5 -191t-145.5 -168l-77 47q-10 6 -14 14.5t-4 16.5q0 19 13 32q13 15 27.5 34.5t26.5 43t19.5 51.5t7.5 60q0 34 -11 72.5t-37 81.5q-9 13 -9 27q0 32 37 45z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="450" 
d="M309 291q35 -56 51.5 -112t16.5 -112q0 -100 -50.5 -191t-145.5 -168l-77 47q-10 6 -14 14.5t-4 16.5q0 19 13 32q13 15 27.5 34.5t26.5 43t19.5 51.5t7.5 60q0 34 -11 72.5t-37 81.5q-9 13 -9 27q0 32 37 45z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="782" 
d="M166 977q-68 111 -68 224q0 100 50.5 191.5t146.5 167.5l77 -47q10 -6 14 -14.5t4 -16.5q0 -9 -4 -17t-9 -14q-13 -15 -27.5 -35t-26.5 -43.5t-20 -51.5t-8 -60q0 -34 11 -72.5t37 -80.5q9 -14 9 -28q0 -32 -36 -45zM498 977q-68 111 -68 224q0 100 50.5 191.5
t146.5 167.5l77 -47q10 -6 14 -14.5t4 -16.5q0 -9 -4 -17t-9 -14q-13 -15 -27.5 -35t-26.5 -43.5t-20 -51.5t-8 -60q0 -34 11 -72.5t37 -80.5q9 -14 9 -28q0 -32 -36 -45z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="782" 
d="M309 1532q35 -56 51.5 -112t16.5 -112q0 -100 -50.5 -191t-145.5 -168l-77 47q-10 6 -14 14.5t-4 16.5q0 19 13 32q13 15 27.5 34t26.5 43t19.5 52t7.5 60q0 34 -11 72.5t-37 81.5q-9 13 -9 27q0 32 37 45zM641 1532q35 -56 51.5 -112t16.5 -112q0 -100 -50.5 -191
t-145.5 -168l-77 47q-10 6 -14 14.5t-4 16.5q0 19 13 32q13 15 27.5 34t26.5 43t19.5 52t7.5 60q0 34 -11 72.5t-37 81.5q-9 13 -9 27q0 32 37 45z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="782" 
d="M309 291q35 -56 51.5 -112t16.5 -112q0 -100 -50.5 -191t-145.5 -168l-77 47q-10 6 -14 14.5t-4 16.5q0 19 13 32q13 15 27.5 34t26.5 43t19.5 52t7.5 60q0 34 -11 72.5t-37 81.5q-9 13 -9 27q0 32 37 45zM641 291q35 -56 51.5 -112t16.5 -112q0 -100 -50.5 -191
t-145.5 -168l-77 47q-10 6 -14 14.5t-4 16.5q0 19 13 32q13 15 27.5 34t26.5 43t19.5 52t7.5 60q0 34 -11 72.5t-37 81.5q-9 13 -9 27q0 32 37 45z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" 
d="M84 913q0 21 6.5 40t21 34t36 24t51.5 9q63 -1 137 -17t149 -27l-31 489q55 32 128 32q36 0 69 -8t59 -24l-32 -489q75 11 149.5 27t137.5 17q30 0 52 -9t36 -24t21 -34t7 -40v-82h-403v-354l32 -800q-26 -16 -59 -24t-69 -8q-73 0 -128 32l32 800v354h-402v82z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" 
d="M84 913q0 21 6.5 40t21 34t36 24t51.5 9q63 -1 137 -17t149 -27l-31 489q55 32 128 32q36 0 69 -8t59 -24l-32 -489q75 11 149.5 27t137.5 17q30 0 52 -9t36 -24t21 -34t7 -40v-82h-403v-520h403v-82q0 -21 -7 -40t-21 -34t-36 -24t-52 -9q-63 1 -137.5 16.5t-149.5 26.5
l32 -488q-26 -16 -59 -24t-69 -8q-73 0 -128 32l31 488q-75 -11 -149 -26.5t-137 -16.5q-30 0 -51.5 9t-36 24t-21 34t-6.5 40v82h402v520h-402v82z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" 
d="M143 593q0 90 34.5 169t93.5 138.5t138 93.5t169 34t170 -34t139 -93.5t93.5 -138.5t34.5 -169q0 -89 -34.5 -167.5t-93.5 -137.5t-139 -93t-170 -34t-169 34t-138 93t-93.5 137.5t-34.5 167.5z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="1517" 
d="M73 136q0 31 11.5 59t32 48t48.5 32t60 12q31 0 59 -12t48 -32t32 -48t12 -59q0 -32 -12 -59.5t-32 -47.5t-48 -31.5t-59 -11.5q-32 0 -60 11.5t-48.5 31.5t-32 47.5t-11.5 59.5zM1141 136q0 31 11.5 59t32 48t48.5 32t60 12q31 0 59 -12t48 -32t32 -48t12 -59
q0 -32 -12 -59.5t-32 -47.5t-48 -31.5t-59 -11.5q-32 0 -60 11.5t-48.5 31.5t-32 47.5t-11.5 59.5zM607 136q0 31 11.5 59t32 48t48.5 32t60 12q31 0 59 -12t48 -32t32 -48t12 -59q0 -32 -12 -59.5t-32 -47.5t-48 -31.5t-59 -11.5q-32 0 -60 11.5t-48.5 31.5t-32 47.5
t-11.5 59.5z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="2347" 
d="M729 1096q0 -82 -27.5 -148t-73.5 -112.5t-107 -71.5t-127 -25q-72 0 -133 25t-105.5 71.5t-69.5 112.5t-25 148q0 84 25 152t69.5 115t105.5 72.5t133 25.5t133.5 -25.5t106.5 -72.5t70 -115t25 -152zM538 1096q0 58 -11 97t-30.5 63t-46 34.5t-56.5 10.5t-56 -10.5
t-45 -34.5t-29.5 -63t-10.5 -97q0 -56 10.5 -93.5t29.5 -60.5t45 -33t56 -10t56.5 10t46 33t30.5 60.5t11 93.5zM1210 1407q12 15 30 27t50 12h179l-1074 -1409q-12 -16 -30.5 -26.5t-44.5 -10.5h-184zM1545 340q0 -82 -27.5 -148t-73.5 -112.5t-107 -72t-127 -25.5
q-72 0 -133 25.5t-105.5 72t-69.5 112.5t-25 148q0 84 25 152t69.5 115t105.5 72.5t133 25.5t133.5 -25.5t106.5 -72.5t70 -115t25 -152zM1355 340q0 58 -11.5 97t-31 63t-46 34.5t-56.5 10.5t-56 -10.5t-44.5 -34.5t-29.5 -63t-11 -97q0 -56 11 -94t29.5 -61t44.5 -33
t56 -10t56.5 10t46 33t31 61t11.5 94zM2286 340q0 -82 -27.5 -148t-73.5 -112.5t-107 -72t-127 -25.5q-72 0 -133 25.5t-105.5 72t-69.5 112.5t-25 148q0 84 25 152t69.5 115t105.5 72.5t133 25.5t133.5 -25.5t106.5 -72.5t70 -115t25 -152zM2096 340q0 58 -11.5 97t-31 63
t-46 34.5t-56.5 10.5t-56 -10.5t-44.5 -34.5t-29.5 -63t-11 -97q0 -56 11 -94t29.5 -61t44.5 -33t56 -10t56.5 10t46 33t31 61t11.5 94z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="642" 
d="M123 522v32l256 396l81 -38q20 -9 29 -23t9 -31q0 -21 -13 -43l-138 -235q-14 -26 -32 -42q16 -14 32 -42l138 -236q13 -22 13 -43q0 -35 -38 -53l-81 -38z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="642" 
d="M263 126l-81 38q-20 9 -29 23t-9 31q0 20 13 42l138 236q16 28 32 42q-18 16 -32 42l-138 235q-13 22 -13 43q0 36 38 54l81 38l256 -396v-32z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="711" 
d="M52 71q-26 -41 -55.5 -56t-67.5 -15h-106l834 1365q23 38 54 59.5t76 21.5h105z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" 
d="M37 928h133q23 122 73 220.5t122 167.5t163 106.5t198 37.5q139 0 238 -53.5t166 -144.5l-87 -94q-10 -11 -21 -19.5t-31 -8.5q-14 0 -25.5 8t-25.5 20t-31 26t-40.5 26t-55.5 20t-76 8q-118 0 -199 -81t-113 -239h502v-76q0 -23 -18.5 -42.5t-50.5 -19.5h-450
q-1 -17 -1 -33.5v-33.5v-24t1 -23h424v-75q0 -23 -18.5 -42.5t-49.5 -19.5h-344q28 -172 107.5 -256.5t197.5 -84.5q72 0 115.5 19.5t71 43.5t46 43.5t40.5 19.5q11 0 19.5 -3.5t16.5 -13.5l107 -98q-70 -108 -178 -165.5t-251 -57.5q-117 0 -210.5 39.5t-162.5 111.5
t-113 174t-62 228h-127v137h116q-1 11 -1 23v24v33.5t1 33.5h-116v138z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="1452" 
d="M960 1165q8 -18 14.5 -35.5t11.5 -35.5q6 18 13 36t16 35l133 255q10 19 22 22.5t33 3.5h147v-603h-150v302l11 99l-153 -302q-19 -37 -58 -37h-24q-41 0 -59 37l-151 297l11 -94v-302h-150v603h147q22 0 33.5 -3t21.5 -23zM555 1446v-140h-164v-463h-171v463h-163v140
h498z" />
    <glyph glyph-name="Omega" unicode="&#x2126;" horiz-adv-x="1480" 
d="M811 0v497q69 11 128 38t102.5 72t68 109t24.5 150q0 93 -27.5 164t-79 119.5t-124.5 73.5t-163 25t-163 -25t-124 -73.5t-78.5 -119.5t-27.5 -164q0 -86 24.5 -150t67.5 -109t102 -72t128 -38v-497h-507q-42 0 -66 23.5t-24 60.5v137h389v139q-93 24 -166 73t-122.5 117
t-75.5 151.5t-26 178.5q0 121 48.5 227t136.5 185t211 124.5t273 45.5t273 -45.5t211 -124.5t136.5 -185t48.5 -227q0 -95 -26 -178.5t-75.5 -151.5t-122.5 -117t-166 -73v-139h390v-137q0 -37 -24.5 -60.5t-65.5 -23.5h-508z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" 
d="M306 1335q40 30 80.5 53.5t84 39t92.5 24t108 8.5q93 0 171.5 -38t134.5 -109.5t87.5 -174t31.5 -232.5q0 -207 -38 -377t-114 -291t-190.5 -187t-267.5 -66q-91 0 -168 30t-132.5 85.5t-87 134.5t-31.5 177q0 117 40 217.5t109.5 174.5t165 116t206.5 42q98 0 167.5 -35
t116.5 -99q1 20 1 39.5v37.5q0 90 -17 157.5t-48 112.5t-74.5 67.5t-96.5 22.5q-38 0 -71.5 -10t-62 -22t-51 -22t-38.5 -10q-14 0 -27 8t-27 32zM515 177q54 0 103.5 22t91.5 70t74.5 124t51.5 184q-10 38 -27 73t-43.5 62t-63.5 43t-88 16q-74 0 -128.5 -26.5t-91 -74
t-54.5 -112.5t-18 -143q0 -114 51.5 -176t141.5 -62z" />
    <glyph glyph-name="Delta" unicode="&#x2206;" horiz-adv-x="1467" 
d="M612 1446h243l599 -1446h-1442zM370 213h727l-316 814q-11 28 -23 65t-24 80q-12 -44 -24 -81t-23 -66z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="1372" 
d="M1321 1446v-207h-167v-1574h-255v1574h-426v-1574h-254v1574h-168v207h1270z" />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="1372" 
d="M82 1446h1208v-207h-826l514 -648v-71l-514 -648h826v-207h-1208v86q0 18 6 37.5t20 36.5l595 733l-595 728q-14 17 -20 36.5t-6 37.5v86z" />
    <glyph glyph-name="minus" unicode="&#x2212;" 
d="M136 763h886v-183h-886v183z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="1165" 
d="M267 642h-121q-38 0 -65.5 22.5t-27.5 76.5v79h352q28 0 46.5 -13t24.5 -33l102 -311q14 -42 22.5 -84t13.5 -85q5 33 12.5 67.5t18.5 70.5l386 1228q6 20 24.5 33t43.5 13h163l-550 -1706h-208z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="1372" 
d="M1000 232q-55 0 -100.5 15t-83.5 40.5t-69.5 59.5t-58.5 72q-27 -38 -59 -72t-70 -59.5t-83.5 -40.5t-100.5 -15q-66 0 -126 26.5t-105 74t-71.5 113.5t-26.5 146q0 79 26.5 145t71.5 113.5t105 74t126 26.5q55 0 100.5 -15t83.5 -40.5t70 -59.5t59 -72q27 38 58.5 72
t69.5 59.5t83.5 40.5t100.5 15q66 0 126 -26.5t105 -74t71.5 -113.5t26.5 -145q0 -80 -26.5 -146t-71.5 -113.5t-105 -74t-126 -26.5zM385 431q30 0 56.5 12.5t50 34t45 51t42.5 62.5q-21 34 -42.5 63t-45 51t-50 34.5t-56.5 12.5q-29 0 -55 -9t-46 -28.5t-32 -50t-12 -72.5
q0 -43 12 -73.5t32 -50t46 -28.5t55 -9zM990 431q29 0 55.5 9t46.5 28.5t31.5 50t11.5 73.5q0 42 -12 72.5t-32 50t-46 28.5t-55 9q-31 0 -57 -12.5t-49.5 -34.5t-44.5 -51t-42 -63q21 -33 42 -62.5t44.5 -51t49.5 -34t57 -12.5z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="827" 
d="M359 1094q21 174 111.5 271t249.5 97q38 0 78.5 -7.5t76.5 -26.5l-12 -119q-1 -10 -4.5 -20t-11.5 -18t-21.5 -13t-34.5 -5q-48 0 -82.5 -11.5t-57.5 -35.5t-37 -60t-20 -84l-128 -1005q-13 -104 -47.5 -179.5t-86 -124t-119.5 -72t-148 -23.5q-36 0 -77.5 7.5
t-76.5 26.5l13 103q2 13 6 22.5t13 15.5t24 9t40 3q58 0 97.5 11.5t65.5 36t40 63t20 93.5z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" 
d="M792 896q31 0 60.5 7t54 17.5t42 22t24.5 20.5l30 -157q-38 -50 -100 -73.5t-127 -23.5q-53 0 -105.5 15.5t-103 34.5t-98.5 34.5t-91 15.5q-33 0 -63 -7.5t-54.5 -18t-42.5 -23t-25 -21.5l-36 150q19 28 45 48t57 33t65 19t69 6q53 0 106 -15.5t103.5 -34t98.5 -34
t91 -15.5zM792 540q31 0 60.5 7t54 17.5t42 22t24.5 20.5l30 -157q-38 -50 -100 -73.5t-127 -23.5q-53 0 -105.5 15.5t-103 34.5t-98.5 34.5t-91 15.5q-33 0 -63 -7.5t-54.5 -18t-42.5 -23t-25 -21.5l-36 150q19 28 45 48t57 33t65 19t69 6q53 0 106 -15.5t103.5 -34
t98.5 -34t91 -15.5z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" 
d="M136 940h502l110 221h177l-111 -221h208v-183h-299l-85 -169h384v-184h-476l-117 -235h-176l117 235h-234v184h326l84 169h-410v183z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" 
d="M139 839l792 379v-168q0 -20 -11 -37.5t-43 -31.5l-352 -158q-26 -11 -54 -18.5t-60 -14.5q32 -7 60.5 -15t53.5 -19l351 -161q32 -15 43.5 -32t11.5 -37v-168l-792 382v99zM139 263h792v-183h-792v183z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" 
d="M1021 839v-99l-792 -382v168q0 20 11.5 37t43.5 32l351 161q25 11 53 19t60 15q-31 7 -59.5 14.5t-53.5 18.5l-352 158q-32 14 -43 31.5t-11 37.5v168zM1021 80h-792v183h792v-183z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" 
d="M113 720l381 811h172l381 -811l-381 -811h-172zM315 720l231 -501q12 -26 20 -49.5t14 -45.5q6 22 14 45.5t20 49.5l235 501l-235 501q-12 26 -19.5 49.5t-14.5 45.5q-7 -22 -14.5 -45.5t-19.5 -49.5z" />
    <glyph glyph-name="uni2669" unicode="&#x2669;" horiz-adv-x="0" 
d="M-2 1486h4v-1821h-4v1821z" />
    <glyph glyph-name="undercommaaccent" horiz-adv-x="638" 
d="M399 -72q28 0 34.5 -13t6.5 -25q0 -9 -2 -22.5t-9 -36t-20.5 -57t-37.5 -84.5q-14 -29 -34 -37t-52 -8h-87l50 283h151z" />
    <glyph glyph-name="grave.case" horiz-adv-x="638" 
d="M192 1791q20 0 34 -1.5t25.5 -6t21.5 -12.5t22 -20l197 -202h-195q-14 0 -24.5 0.5t-19.5 3t-18 7.5t-20 13l-297 218h274z" />
    <glyph glyph-name="dieresis.case" horiz-adv-x="638" 
d="M237 1667q0 -27 -11 -51t-30 -42t-44 -28t-53 -10q-26 0 -49.5 10t-41.5 28t-28.5 42t-10.5 51t10.5 51.5t28.5 43t41.5 29.5t49.5 11q28 0 53 -11t44 -29.5t30 -43t11 -51.5zM669 1667q0 -27 -10.5 -51t-28.5 -42t-42.5 -28t-52.5 -10q-27 0 -51.5 10t-43 28t-29 42
t-10.5 51t10.5 51.5t29 43t43 29.5t51.5 11q28 0 52.5 -11t42.5 -29.5t28.5 -43t10.5 -51.5z" />
    <glyph glyph-name="macron.case" horiz-adv-x="638" 
d="M53 1705h532v-142h-532v142z" />
    <glyph glyph-name="acute.case" horiz-adv-x="638" 
d="M719 1791l-296 -216q-11 -8 -20.5 -13t-19 -8t-20 -4t-24.5 -1h-194l196 202q12 12 22.5 20t21.5 12.5t25 6t34 1.5h275z" />
    <glyph glyph-name="circumflex.case" horiz-adv-x="638" 
d="M684 1549h-187q-15 0 -33 4t-29 12l-101 67q-4 2 -7.5 5t-7.5 6q-2 -2 -6 -5t-9 -6l-101 -67q-11 -8 -29 -12t-33 -4h-187l242 219h246z" />
    <glyph glyph-name="caron.case" horiz-adv-x="638" 
d="M-46 1768h187q15 0 33 -4t29 -12l101 -67q4 -2 7.5 -4.5t7.5 -5.5q4 3 7.5 5.5t7.5 4.5l101 67q11 8 29 12t33 4h187l-242 -219h-246z" />
    <glyph glyph-name="breve.case" horiz-adv-x="638" 
d="M319 1526q-149 0 -229.5 60t-80.5 182h157q0 -50 37.5 -72.5t115.5 -22.5t115.5 22.5t37.5 72.5h157q0 -55 -20.5 -100t-60.5 -76.5t-97.5 -48.5t-131.5 -17z" />
    <glyph glyph-name="dotaccent.case" horiz-adv-x="638" 
d="M470 1696q0 -30 -12.5 -56.5t-33.5 -47t-49 -32.5t-59 -12q-29 0 -56 12t-47.5 32.5t-32.5 47t-12 56.5q0 31 12 58.5t32.5 48t47.5 32.5t56 12q31 0 59 -12t49 -32.5t33.5 -48t12.5 -58.5z" />
    <glyph glyph-name="ring.case" horiz-adv-x="638" 
d="M112 1683q0 43 17 78t45 60t65 39t78 14q43 0 81 -14t67 -39t46 -60t17 -78q0 -42 -17 -76.5t-46 -59.5t-67 -38.5t-81 -13.5q-41 0 -78 13.5t-65 38.5t-45 59.5t-17 76.5zM230 1683q0 -39 23.5 -64t67.5 -25q40 0 64 25t24 64q0 42 -24 66t-64 24q-44 0 -67.5 -24
t-23.5 -66z" />
    <glyph glyph-name="tilde.case" horiz-adv-x="638" 
d="M442 1699q27 0 43.5 15.5t17.5 47.5h134q0 -50 -13 -91t-37 -70t-59 -45.5t-80 -16.5q-36 0 -69.5 11t-64 24t-57 24t-47.5 11q-27 0 -43 -16.5t-17 -48.5h-136q0 49 13 90.5t38 71.5t60 46.5t79 16.5q36 0 70 -11t64.5 -24t56.5 -24t47 -11z" />
    <glyph glyph-name="hungarumlaut.case" horiz-adv-x="638" 
d="M456 1775l-201 -190q-19 -18 -36 -27t-45 -9h-117l132 174q17 23 41.5 37.5t64.5 14.5h161zM821 1775l-253 -190q-22 -17 -41.5 -26.5t-47.5 -9.5h-133l177 174q22 22 47.5 37t65.5 15h185z" />
    <glyph glyph-name="caron.salt" horiz-adv-x="638" 
d="M421 1488q28 0 34.5 -12.5t6.5 -24.5q0 -9 -4 -24.5t-14.5 -45t-29 -76.5t-48.5 -118q-12 -29 -32.5 -37t-52.5 -8h-88l62 346h166z" />
    <hkern u1="&#x22;" u2="&#x2206;" k="191" />
    <hkern u1="&#x22;" u2="&#x203a;" k="169" />
    <hkern u1="&#x22;" u2="&#x2039;" k="169" />
    <hkern u1="&#x22;" u2="&#x2022;" k="169" />
    <hkern u1="&#x22;" u2="&#x201e;" k="213" />
    <hkern u1="&#x22;" u2="&#x201a;" k="213" />
    <hkern u1="&#x22;" u2="&#x2014;" k="169" />
    <hkern u1="&#x22;" u2="&#x2013;" k="169" />
    <hkern u1="&#x22;" u2="&#x178;" k="-36" />
    <hkern u1="&#x22;" u2="&#x153;" k="96" />
    <hkern u1="&#x22;" u2="&#x152;" k="42" />
    <hkern u1="&#x22;" u2="&#x119;" k="96" />
    <hkern u1="&#x22;" u2="&#x107;" k="96" />
    <hkern u1="&#x22;" u2="&#x106;" k="42" />
    <hkern u1="&#x22;" u2="&#x105;" k="66" />
    <hkern u1="&#x22;" u2="&#x104;" k="191" />
    <hkern u1="&#x22;" u2="&#xf8;" k="96" />
    <hkern u1="&#x22;" u2="&#xf6;" k="96" />
    <hkern u1="&#x22;" u2="&#xf5;" k="96" />
    <hkern u1="&#x22;" u2="&#xf4;" k="96" />
    <hkern u1="&#x22;" u2="&#xf3;" k="96" />
    <hkern u1="&#x22;" u2="&#xf2;" k="96" />
    <hkern u1="&#x22;" u2="&#xf0;" k="96" />
    <hkern u1="&#x22;" u2="&#xeb;" k="96" />
    <hkern u1="&#x22;" u2="&#xea;" k="96" />
    <hkern u1="&#x22;" u2="&#xe9;" k="96" />
    <hkern u1="&#x22;" u2="&#xe8;" k="96" />
    <hkern u1="&#x22;" u2="&#xe7;" k="96" />
    <hkern u1="&#x22;" u2="&#xe6;" k="66" />
    <hkern u1="&#x22;" u2="&#xe5;" k="66" />
    <hkern u1="&#x22;" u2="&#xe4;" k="66" />
    <hkern u1="&#x22;" u2="&#xe3;" k="66" />
    <hkern u1="&#x22;" u2="&#xe2;" k="66" />
    <hkern u1="&#x22;" u2="&#xe1;" k="66" />
    <hkern u1="&#x22;" u2="&#xe0;" k="66" />
    <hkern u1="&#x22;" u2="&#xdd;" k="-36" />
    <hkern u1="&#x22;" u2="&#xd8;" k="42" />
    <hkern u1="&#x22;" u2="&#xd6;" k="42" />
    <hkern u1="&#x22;" u2="&#xd5;" k="42" />
    <hkern u1="&#x22;" u2="&#xd4;" k="42" />
    <hkern u1="&#x22;" u2="&#xd3;" k="42" />
    <hkern u1="&#x22;" u2="&#xd2;" k="42" />
    <hkern u1="&#x22;" u2="&#xc7;" k="42" />
    <hkern u1="&#x22;" u2="&#xc6;" k="191" />
    <hkern u1="&#x22;" u2="&#xc5;" k="191" />
    <hkern u1="&#x22;" u2="&#xc4;" k="191" />
    <hkern u1="&#x22;" u2="&#xc3;" k="191" />
    <hkern u1="&#x22;" u2="&#xc2;" k="191" />
    <hkern u1="&#x22;" u2="&#xc1;" k="191" />
    <hkern u1="&#x22;" u2="&#xc0;" k="191" />
    <hkern u1="&#x22;" u2="&#xbb;" k="169" />
    <hkern u1="&#x22;" u2="&#xb7;" k="169" />
    <hkern u1="&#x22;" u2="&#xae;" k="42" />
    <hkern u1="&#x22;" u2="&#xad;" k="169" />
    <hkern u1="&#x22;" u2="&#xab;" k="169" />
    <hkern u1="&#x22;" u2="&#xa9;" k="42" />
    <hkern u1="&#x22;" u2="q" k="96" />
    <hkern u1="&#x22;" u2="o" k="96" />
    <hkern u1="&#x22;" u2="e" k="96" />
    <hkern u1="&#x22;" u2="d" k="96" />
    <hkern u1="&#x22;" u2="c" k="96" />
    <hkern u1="&#x22;" u2="a" k="66" />
    <hkern u1="&#x22;" u2="\" k="-44" />
    <hkern u1="&#x22;" u2="Y" k="-36" />
    <hkern u1="&#x22;" u2="W" k="-44" />
    <hkern u1="&#x22;" u2="V" k="-44" />
    <hkern u1="&#x22;" u2="Q" k="42" />
    <hkern u1="&#x22;" u2="O" k="42" />
    <hkern u1="&#x22;" u2="G" k="42" />
    <hkern u1="&#x22;" u2="C" k="42" />
    <hkern u1="&#x22;" u2="A" k="191" />
    <hkern u1="&#x22;" u2="&#x40;" k="42" />
    <hkern u1="&#x22;" u2="&#x2f;" k="191" />
    <hkern u1="&#x22;" u2="&#x2e;" k="213" />
    <hkern u1="&#x22;" u2="&#x2d;" k="169" />
    <hkern u1="&#x22;" u2="&#x2c;" k="213" />
    <hkern u1="&#x22;" u2="&#x26;" k="191" />
    <hkern u1="&#x27;" u2="&#x2206;" k="191" />
    <hkern u1="&#x27;" u2="&#x203a;" k="169" />
    <hkern u1="&#x27;" u2="&#x2039;" k="169" />
    <hkern u1="&#x27;" u2="&#x2022;" k="169" />
    <hkern u1="&#x27;" u2="&#x201e;" k="213" />
    <hkern u1="&#x27;" u2="&#x201a;" k="213" />
    <hkern u1="&#x27;" u2="&#x2014;" k="169" />
    <hkern u1="&#x27;" u2="&#x2013;" k="169" />
    <hkern u1="&#x27;" u2="&#x178;" k="-36" />
    <hkern u1="&#x27;" u2="&#x153;" k="96" />
    <hkern u1="&#x27;" u2="&#x152;" k="42" />
    <hkern u1="&#x27;" u2="&#x119;" k="96" />
    <hkern u1="&#x27;" u2="&#x107;" k="96" />
    <hkern u1="&#x27;" u2="&#x106;" k="42" />
    <hkern u1="&#x27;" u2="&#x105;" k="66" />
    <hkern u1="&#x27;" u2="&#x104;" k="191" />
    <hkern u1="&#x27;" u2="&#xf8;" k="96" />
    <hkern u1="&#x27;" u2="&#xf6;" k="96" />
    <hkern u1="&#x27;" u2="&#xf5;" k="96" />
    <hkern u1="&#x27;" u2="&#xf4;" k="96" />
    <hkern u1="&#x27;" u2="&#xf3;" k="96" />
    <hkern u1="&#x27;" u2="&#xf2;" k="96" />
    <hkern u1="&#x27;" u2="&#xf0;" k="96" />
    <hkern u1="&#x27;" u2="&#xeb;" k="96" />
    <hkern u1="&#x27;" u2="&#xea;" k="96" />
    <hkern u1="&#x27;" u2="&#xe9;" k="96" />
    <hkern u1="&#x27;" u2="&#xe8;" k="96" />
    <hkern u1="&#x27;" u2="&#xe7;" k="96" />
    <hkern u1="&#x27;" u2="&#xe6;" k="66" />
    <hkern u1="&#x27;" u2="&#xe5;" k="66" />
    <hkern u1="&#x27;" u2="&#xe4;" k="66" />
    <hkern u1="&#x27;" u2="&#xe3;" k="66" />
    <hkern u1="&#x27;" u2="&#xe2;" k="66" />
    <hkern u1="&#x27;" u2="&#xe1;" k="66" />
    <hkern u1="&#x27;" u2="&#xe0;" k="66" />
    <hkern u1="&#x27;" u2="&#xdd;" k="-36" />
    <hkern u1="&#x27;" u2="&#xd8;" k="42" />
    <hkern u1="&#x27;" u2="&#xd6;" k="42" />
    <hkern u1="&#x27;" u2="&#xd5;" k="42" />
    <hkern u1="&#x27;" u2="&#xd4;" k="42" />
    <hkern u1="&#x27;" u2="&#xd3;" k="42" />
    <hkern u1="&#x27;" u2="&#xd2;" k="42" />
    <hkern u1="&#x27;" u2="&#xc7;" k="42" />
    <hkern u1="&#x27;" u2="&#xc6;" k="191" />
    <hkern u1="&#x27;" u2="&#xc5;" k="191" />
    <hkern u1="&#x27;" u2="&#xc4;" k="191" />
    <hkern u1="&#x27;" u2="&#xc3;" k="191" />
    <hkern u1="&#x27;" u2="&#xc2;" k="191" />
    <hkern u1="&#x27;" u2="&#xc1;" k="191" />
    <hkern u1="&#x27;" u2="&#xc0;" k="191" />
    <hkern u1="&#x27;" u2="&#xbb;" k="169" />
    <hkern u1="&#x27;" u2="&#xb7;" k="169" />
    <hkern u1="&#x27;" u2="&#xae;" k="42" />
    <hkern u1="&#x27;" u2="&#xad;" k="169" />
    <hkern u1="&#x27;" u2="&#xab;" k="169" />
    <hkern u1="&#x27;" u2="&#xa9;" k="42" />
    <hkern u1="&#x27;" u2="q" k="96" />
    <hkern u1="&#x27;" u2="o" k="96" />
    <hkern u1="&#x27;" u2="e" k="96" />
    <hkern u1="&#x27;" u2="d" k="96" />
    <hkern u1="&#x27;" u2="c" k="96" />
    <hkern u1="&#x27;" u2="a" k="66" />
    <hkern u1="&#x27;" u2="\" k="-44" />
    <hkern u1="&#x27;" u2="Y" k="-36" />
    <hkern u1="&#x27;" u2="W" k="-44" />
    <hkern u1="&#x27;" u2="V" k="-44" />
    <hkern u1="&#x27;" u2="Q" k="42" />
    <hkern u1="&#x27;" u2="O" k="42" />
    <hkern u1="&#x27;" u2="G" k="42" />
    <hkern u1="&#x27;" u2="C" k="42" />
    <hkern u1="&#x27;" u2="A" k="191" />
    <hkern u1="&#x27;" u2="&#x40;" k="42" />
    <hkern u1="&#x27;" u2="&#x2f;" k="191" />
    <hkern u1="&#x27;" u2="&#x2e;" k="213" />
    <hkern u1="&#x27;" u2="&#x2d;" k="169" />
    <hkern u1="&#x27;" u2="&#x2c;" k="213" />
    <hkern u1="&#x27;" u2="&#x26;" k="191" />
    <hkern u1="&#x28;" u2="&#x153;" k="36" />
    <hkern u1="&#x28;" u2="&#x152;" k="40" />
    <hkern u1="&#x28;" u2="&#x119;" k="36" />
    <hkern u1="&#x28;" u2="&#x107;" k="36" />
    <hkern u1="&#x28;" u2="&#x106;" k="40" />
    <hkern u1="&#x28;" u2="&#xf8;" k="36" />
    <hkern u1="&#x28;" u2="&#xf6;" k="36" />
    <hkern u1="&#x28;" u2="&#xf5;" k="36" />
    <hkern u1="&#x28;" u2="&#xf4;" k="36" />
    <hkern u1="&#x28;" u2="&#xf3;" k="36" />
    <hkern u1="&#x28;" u2="&#xf2;" k="36" />
    <hkern u1="&#x28;" u2="&#xf0;" k="36" />
    <hkern u1="&#x28;" u2="&#xeb;" k="36" />
    <hkern u1="&#x28;" u2="&#xea;" k="36" />
    <hkern u1="&#x28;" u2="&#xe9;" k="36" />
    <hkern u1="&#x28;" u2="&#xe8;" k="36" />
    <hkern u1="&#x28;" u2="&#xe7;" k="36" />
    <hkern u1="&#x28;" u2="&#xd8;" k="40" />
    <hkern u1="&#x28;" u2="&#xd6;" k="40" />
    <hkern u1="&#x28;" u2="&#xd5;" k="40" />
    <hkern u1="&#x28;" u2="&#xd4;" k="40" />
    <hkern u1="&#x28;" u2="&#xd3;" k="40" />
    <hkern u1="&#x28;" u2="&#xd2;" k="40" />
    <hkern u1="&#x28;" u2="&#xc7;" k="40" />
    <hkern u1="&#x28;" u2="&#xae;" k="40" />
    <hkern u1="&#x28;" u2="&#xa9;" k="40" />
    <hkern u1="&#x28;" u2="q" k="36" />
    <hkern u1="&#x28;" u2="o" k="36" />
    <hkern u1="&#x28;" u2="e" k="36" />
    <hkern u1="&#x28;" u2="d" k="36" />
    <hkern u1="&#x28;" u2="c" k="36" />
    <hkern u1="&#x28;" u2="Q" k="40" />
    <hkern u1="&#x28;" u2="O" k="40" />
    <hkern u1="&#x28;" u2="G" k="40" />
    <hkern u1="&#x28;" u2="C" k="40" />
    <hkern u1="&#x28;" u2="&#x40;" k="40" />
    <hkern u1="&#x2a;" u2="&#x2206;" k="191" />
    <hkern u1="&#x2a;" u2="&#x203a;" k="169" />
    <hkern u1="&#x2a;" u2="&#x2039;" k="169" />
    <hkern u1="&#x2a;" u2="&#x2022;" k="169" />
    <hkern u1="&#x2a;" u2="&#x201e;" k="213" />
    <hkern u1="&#x2a;" u2="&#x201a;" k="213" />
    <hkern u1="&#x2a;" u2="&#x2014;" k="169" />
    <hkern u1="&#x2a;" u2="&#x2013;" k="169" />
    <hkern u1="&#x2a;" u2="&#x178;" k="-36" />
    <hkern u1="&#x2a;" u2="&#x153;" k="96" />
    <hkern u1="&#x2a;" u2="&#x152;" k="42" />
    <hkern u1="&#x2a;" u2="&#x119;" k="96" />
    <hkern u1="&#x2a;" u2="&#x107;" k="96" />
    <hkern u1="&#x2a;" u2="&#x106;" k="42" />
    <hkern u1="&#x2a;" u2="&#x105;" k="66" />
    <hkern u1="&#x2a;" u2="&#x104;" k="191" />
    <hkern u1="&#x2a;" u2="&#xf8;" k="96" />
    <hkern u1="&#x2a;" u2="&#xf6;" k="96" />
    <hkern u1="&#x2a;" u2="&#xf5;" k="96" />
    <hkern u1="&#x2a;" u2="&#xf4;" k="96" />
    <hkern u1="&#x2a;" u2="&#xf3;" k="96" />
    <hkern u1="&#x2a;" u2="&#xf2;" k="96" />
    <hkern u1="&#x2a;" u2="&#xf0;" k="96" />
    <hkern u1="&#x2a;" u2="&#xeb;" k="96" />
    <hkern u1="&#x2a;" u2="&#xea;" k="96" />
    <hkern u1="&#x2a;" u2="&#xe9;" k="96" />
    <hkern u1="&#x2a;" u2="&#xe8;" k="96" />
    <hkern u1="&#x2a;" u2="&#xe7;" k="96" />
    <hkern u1="&#x2a;" u2="&#xe6;" k="66" />
    <hkern u1="&#x2a;" u2="&#xe5;" k="66" />
    <hkern u1="&#x2a;" u2="&#xe4;" k="66" />
    <hkern u1="&#x2a;" u2="&#xe3;" k="66" />
    <hkern u1="&#x2a;" u2="&#xe2;" k="66" />
    <hkern u1="&#x2a;" u2="&#xe1;" k="66" />
    <hkern u1="&#x2a;" u2="&#xe0;" k="66" />
    <hkern u1="&#x2a;" u2="&#xdd;" k="-36" />
    <hkern u1="&#x2a;" u2="&#xd8;" k="42" />
    <hkern u1="&#x2a;" u2="&#xd6;" k="42" />
    <hkern u1="&#x2a;" u2="&#xd5;" k="42" />
    <hkern u1="&#x2a;" u2="&#xd4;" k="42" />
    <hkern u1="&#x2a;" u2="&#xd3;" k="42" />
    <hkern u1="&#x2a;" u2="&#xd2;" k="42" />
    <hkern u1="&#x2a;" u2="&#xc7;" k="42" />
    <hkern u1="&#x2a;" u2="&#xc6;" k="191" />
    <hkern u1="&#x2a;" u2="&#xc5;" k="191" />
    <hkern u1="&#x2a;" u2="&#xc4;" k="191" />
    <hkern u1="&#x2a;" u2="&#xc3;" k="191" />
    <hkern u1="&#x2a;" u2="&#xc2;" k="191" />
    <hkern u1="&#x2a;" u2="&#xc1;" k="191" />
    <hkern u1="&#x2a;" u2="&#xc0;" k="191" />
    <hkern u1="&#x2a;" u2="&#xbb;" k="169" />
    <hkern u1="&#x2a;" u2="&#xb7;" k="169" />
    <hkern u1="&#x2a;" u2="&#xae;" k="42" />
    <hkern u1="&#x2a;" u2="&#xad;" k="169" />
    <hkern u1="&#x2a;" u2="&#xab;" k="169" />
    <hkern u1="&#x2a;" u2="&#xa9;" k="42" />
    <hkern u1="&#x2a;" u2="q" k="96" />
    <hkern u1="&#x2a;" u2="o" k="96" />
    <hkern u1="&#x2a;" u2="e" k="96" />
    <hkern u1="&#x2a;" u2="d" k="96" />
    <hkern u1="&#x2a;" u2="c" k="96" />
    <hkern u1="&#x2a;" u2="a" k="66" />
    <hkern u1="&#x2a;" u2="\" k="-44" />
    <hkern u1="&#x2a;" u2="Y" k="-36" />
    <hkern u1="&#x2a;" u2="W" k="-44" />
    <hkern u1="&#x2a;" u2="V" k="-44" />
    <hkern u1="&#x2a;" u2="Q" k="42" />
    <hkern u1="&#x2a;" u2="O" k="42" />
    <hkern u1="&#x2a;" u2="G" k="42" />
    <hkern u1="&#x2a;" u2="C" k="42" />
    <hkern u1="&#x2a;" u2="A" k="191" />
    <hkern u1="&#x2a;" u2="&#x40;" k="42" />
    <hkern u1="&#x2a;" u2="&#x2f;" k="191" />
    <hkern u1="&#x2a;" u2="&#x2e;" k="213" />
    <hkern u1="&#x2a;" u2="&#x2d;" k="169" />
    <hkern u1="&#x2a;" u2="&#x2c;" k="213" />
    <hkern u1="&#x2a;" u2="&#x26;" k="191" />
    <hkern u1="&#x2c;" u2="&#x2122;" k="213" />
    <hkern u1="&#x2c;" u2="&#x203a;" k="132" />
    <hkern u1="&#x2c;" u2="&#x2039;" k="132" />
    <hkern u1="&#x2c;" u2="&#x2022;" k="132" />
    <hkern u1="&#x2c;" u2="&#x201d;" k="213" />
    <hkern u1="&#x2c;" u2="&#x201c;" k="213" />
    <hkern u1="&#x2c;" u2="&#x2019;" k="213" />
    <hkern u1="&#x2c;" u2="&#x2018;" k="213" />
    <hkern u1="&#x2c;" u2="&#x2014;" k="132" />
    <hkern u1="&#x2c;" u2="&#x2013;" k="132" />
    <hkern u1="&#x2c;" u2="&#x178;" k="167" />
    <hkern u1="&#x2c;" u2="&#x152;" k="52" />
    <hkern u1="&#x2c;" u2="&#x106;" k="52" />
    <hkern u1="&#x2c;" u2="&#xff;" k="136" />
    <hkern u1="&#x2c;" u2="&#xfd;" k="136" />
    <hkern u1="&#x2c;" u2="&#xdd;" k="167" />
    <hkern u1="&#x2c;" u2="&#xd8;" k="52" />
    <hkern u1="&#x2c;" u2="&#xd6;" k="52" />
    <hkern u1="&#x2c;" u2="&#xd5;" k="52" />
    <hkern u1="&#x2c;" u2="&#xd4;" k="52" />
    <hkern u1="&#x2c;" u2="&#xd3;" k="52" />
    <hkern u1="&#x2c;" u2="&#xd2;" k="52" />
    <hkern u1="&#x2c;" u2="&#xc7;" k="52" />
    <hkern u1="&#x2c;" u2="&#xbb;" k="132" />
    <hkern u1="&#x2c;" u2="&#xba;" k="213" />
    <hkern u1="&#x2c;" u2="&#xb7;" k="132" />
    <hkern u1="&#x2c;" u2="&#xb0;" k="213" />
    <hkern u1="&#x2c;" u2="&#xae;" k="52" />
    <hkern u1="&#x2c;" u2="&#xad;" k="132" />
    <hkern u1="&#x2c;" u2="&#xab;" k="132" />
    <hkern u1="&#x2c;" u2="&#xaa;" k="213" />
    <hkern u1="&#x2c;" u2="&#xa9;" k="52" />
    <hkern u1="&#x2c;" u2="y" k="136" />
    <hkern u1="&#x2c;" u2="w" k="71" />
    <hkern u1="&#x2c;" u2="v" k="136" />
    <hkern u1="&#x2c;" u2="\" k="180" />
    <hkern u1="&#x2c;" u2="Y" k="167" />
    <hkern u1="&#x2c;" u2="W" k="131" />
    <hkern u1="&#x2c;" u2="V" k="180" />
    <hkern u1="&#x2c;" u2="T" k="180" />
    <hkern u1="&#x2c;" u2="Q" k="52" />
    <hkern u1="&#x2c;" u2="O" k="52" />
    <hkern u1="&#x2c;" u2="G" k="52" />
    <hkern u1="&#x2c;" u2="C" k="52" />
    <hkern u1="&#x2c;" u2="&#x40;" k="52" />
    <hkern u1="&#x2c;" u2="&#x2d;" k="132" />
    <hkern u1="&#x2c;" u2="&#x2a;" k="213" />
    <hkern u1="&#x2c;" u2="&#x27;" k="213" />
    <hkern u1="&#x2c;" u2="&#x22;" k="213" />
    <hkern u1="&#x2d;" u2="&#x2206;" k="67" />
    <hkern u1="&#x2d;" u2="&#x2122;" k="169" />
    <hkern u1="&#x2d;" u2="&#x201e;" k="132" />
    <hkern u1="&#x2d;" u2="&#x201d;" k="169" />
    <hkern u1="&#x2d;" u2="&#x201c;" k="169" />
    <hkern u1="&#x2d;" u2="&#x201a;" k="132" />
    <hkern u1="&#x2d;" u2="&#x2019;" k="169" />
    <hkern u1="&#x2d;" u2="&#x2018;" k="169" />
    <hkern u1="&#x2d;" u2="&#x17d;" k="48" />
    <hkern u1="&#x2d;" u2="&#x17b;" k="48" />
    <hkern u1="&#x2d;" u2="&#x179;" k="48" />
    <hkern u1="&#x2d;" u2="&#x178;" k="160" />
    <hkern u1="&#x2d;" u2="&#x104;" k="67" />
    <hkern u1="&#x2d;" u2="&#xdd;" k="160" />
    <hkern u1="&#x2d;" u2="&#xc6;" k="67" />
    <hkern u1="&#x2d;" u2="&#xc5;" k="67" />
    <hkern u1="&#x2d;" u2="&#xc4;" k="67" />
    <hkern u1="&#x2d;" u2="&#xc3;" k="67" />
    <hkern u1="&#x2d;" u2="&#xc2;" k="67" />
    <hkern u1="&#x2d;" u2="&#xc1;" k="67" />
    <hkern u1="&#x2d;" u2="&#xc0;" k="67" />
    <hkern u1="&#x2d;" u2="&#xba;" k="169" />
    <hkern u1="&#x2d;" u2="&#xb0;" k="169" />
    <hkern u1="&#x2d;" u2="&#xaa;" k="169" />
    <hkern u1="&#x2d;" u2="\" k="116" />
    <hkern u1="&#x2d;" u2="Z" k="48" />
    <hkern u1="&#x2d;" u2="Y" k="160" />
    <hkern u1="&#x2d;" u2="X" k="66" />
    <hkern u1="&#x2d;" u2="W" k="36" />
    <hkern u1="&#x2d;" u2="V" k="116" />
    <hkern u1="&#x2d;" u2="T" k="180" />
    <hkern u1="&#x2d;" u2="A" k="67" />
    <hkern u1="&#x2d;" u2="&#x2f;" k="67" />
    <hkern u1="&#x2d;" u2="&#x2e;" k="132" />
    <hkern u1="&#x2d;" u2="&#x2c;" k="132" />
    <hkern u1="&#x2d;" u2="&#x2a;" k="169" />
    <hkern u1="&#x2d;" u2="&#x27;" k="169" />
    <hkern u1="&#x2d;" u2="&#x26;" k="67" />
    <hkern u1="&#x2d;" u2="&#x22;" k="169" />
    <hkern u1="&#x2e;" u2="&#x2122;" k="213" />
    <hkern u1="&#x2e;" u2="&#x203a;" k="132" />
    <hkern u1="&#x2e;" u2="&#x2039;" k="132" />
    <hkern u1="&#x2e;" u2="&#x2022;" k="132" />
    <hkern u1="&#x2e;" u2="&#x201d;" k="213" />
    <hkern u1="&#x2e;" u2="&#x201c;" k="213" />
    <hkern u1="&#x2e;" u2="&#x2019;" k="213" />
    <hkern u1="&#x2e;" u2="&#x2018;" k="213" />
    <hkern u1="&#x2e;" u2="&#x2014;" k="132" />
    <hkern u1="&#x2e;" u2="&#x2013;" k="132" />
    <hkern u1="&#x2e;" u2="&#x178;" k="167" />
    <hkern u1="&#x2e;" u2="&#x152;" k="52" />
    <hkern u1="&#x2e;" u2="&#x106;" k="52" />
    <hkern u1="&#x2e;" u2="&#xff;" k="136" />
    <hkern u1="&#x2e;" u2="&#xfd;" k="136" />
    <hkern u1="&#x2e;" u2="&#xdd;" k="167" />
    <hkern u1="&#x2e;" u2="&#xd8;" k="52" />
    <hkern u1="&#x2e;" u2="&#xd6;" k="52" />
    <hkern u1="&#x2e;" u2="&#xd5;" k="52" />
    <hkern u1="&#x2e;" u2="&#xd4;" k="52" />
    <hkern u1="&#x2e;" u2="&#xd3;" k="52" />
    <hkern u1="&#x2e;" u2="&#xd2;" k="52" />
    <hkern u1="&#x2e;" u2="&#xc7;" k="52" />
    <hkern u1="&#x2e;" u2="&#xbb;" k="132" />
    <hkern u1="&#x2e;" u2="&#xba;" k="213" />
    <hkern u1="&#x2e;" u2="&#xb7;" k="132" />
    <hkern u1="&#x2e;" u2="&#xb0;" k="213" />
    <hkern u1="&#x2e;" u2="&#xae;" k="52" />
    <hkern u1="&#x2e;" u2="&#xad;" k="132" />
    <hkern u1="&#x2e;" u2="&#xab;" k="132" />
    <hkern u1="&#x2e;" u2="&#xaa;" k="213" />
    <hkern u1="&#x2e;" u2="&#xa9;" k="52" />
    <hkern u1="&#x2e;" u2="y" k="136" />
    <hkern u1="&#x2e;" u2="w" k="71" />
    <hkern u1="&#x2e;" u2="v" k="136" />
    <hkern u1="&#x2e;" u2="\" k="180" />
    <hkern u1="&#x2e;" u2="Y" k="167" />
    <hkern u1="&#x2e;" u2="W" k="131" />
    <hkern u1="&#x2e;" u2="V" k="180" />
    <hkern u1="&#x2e;" u2="T" k="180" />
    <hkern u1="&#x2e;" u2="Q" k="52" />
    <hkern u1="&#x2e;" u2="O" k="52" />
    <hkern u1="&#x2e;" u2="G" k="52" />
    <hkern u1="&#x2e;" u2="C" k="52" />
    <hkern u1="&#x2e;" u2="&#x40;" k="52" />
    <hkern u1="&#x2e;" u2="&#x2d;" k="132" />
    <hkern u1="&#x2e;" u2="&#x2a;" k="213" />
    <hkern u1="&#x2e;" u2="&#x27;" k="213" />
    <hkern u1="&#x2e;" u2="&#x22;" k="213" />
    <hkern u1="&#x2f;" u2="&#x2206;" k="169" />
    <hkern u1="&#x2f;" u2="&#x2122;" k="-44" />
    <hkern u1="&#x2f;" u2="&#x203a;" k="116" />
    <hkern u1="&#x2f;" u2="&#x2039;" k="116" />
    <hkern u1="&#x2f;" u2="&#x2022;" k="116" />
    <hkern u1="&#x2f;" u2="&#x201e;" k="196" />
    <hkern u1="&#x2f;" u2="&#x201d;" k="-44" />
    <hkern u1="&#x2f;" u2="&#x201c;" k="-44" />
    <hkern u1="&#x2f;" u2="&#x201a;" k="196" />
    <hkern u1="&#x2f;" u2="&#x2019;" k="-44" />
    <hkern u1="&#x2f;" u2="&#x2018;" k="-44" />
    <hkern u1="&#x2f;" u2="&#x2014;" k="116" />
    <hkern u1="&#x2f;" u2="&#x2013;" k="116" />
    <hkern u1="&#x2f;" u2="&#x17e;" k="91" />
    <hkern u1="&#x2f;" u2="&#x17c;" k="91" />
    <hkern u1="&#x2f;" u2="&#x17a;" k="91" />
    <hkern u1="&#x2f;" u2="&#x161;" k="108" />
    <hkern u1="&#x2f;" u2="&#x15b;" k="108" />
    <hkern u1="&#x2f;" u2="&#x153;" k="123" />
    <hkern u1="&#x2f;" u2="&#x152;" k="56" />
    <hkern u1="&#x2f;" u2="&#x144;" k="99" />
    <hkern u1="&#x2f;" u2="&#x131;" k="99" />
    <hkern u1="&#x2f;" u2="&#x119;" k="123" />
    <hkern u1="&#x2f;" u2="&#x107;" k="123" />
    <hkern u1="&#x2f;" u2="&#x106;" k="56" />
    <hkern u1="&#x2f;" u2="&#x105;" k="123" />
    <hkern u1="&#x2f;" u2="&#x104;" k="169" />
    <hkern u1="&#x2f;" u2="&#xff;" k="59" />
    <hkern u1="&#x2f;" u2="&#xfd;" k="59" />
    <hkern u1="&#x2f;" u2="&#xfc;" k="99" />
    <hkern u1="&#x2f;" u2="&#xfb;" k="99" />
    <hkern u1="&#x2f;" u2="&#xfa;" k="99" />
    <hkern u1="&#x2f;" u2="&#xf9;" k="99" />
    <hkern u1="&#x2f;" u2="&#xf8;" k="123" />
    <hkern u1="&#x2f;" u2="&#xf6;" k="123" />
    <hkern u1="&#x2f;" u2="&#xf5;" k="123" />
    <hkern u1="&#x2f;" u2="&#xf4;" k="123" />
    <hkern u1="&#x2f;" u2="&#xf3;" k="123" />
    <hkern u1="&#x2f;" u2="&#xf2;" k="123" />
    <hkern u1="&#x2f;" u2="&#xf1;" k="99" />
    <hkern u1="&#x2f;" u2="&#xf0;" k="123" />
    <hkern u1="&#x2f;" u2="&#xeb;" k="123" />
    <hkern u1="&#x2f;" u2="&#xea;" k="123" />
    <hkern u1="&#x2f;" u2="&#xe9;" k="123" />
    <hkern u1="&#x2f;" u2="&#xe8;" k="123" />
    <hkern u1="&#x2f;" u2="&#xe7;" k="123" />
    <hkern u1="&#x2f;" u2="&#xe6;" k="123" />
    <hkern u1="&#x2f;" u2="&#xe5;" k="123" />
    <hkern u1="&#x2f;" u2="&#xe4;" k="123" />
    <hkern u1="&#x2f;" u2="&#xe3;" k="123" />
    <hkern u1="&#x2f;" u2="&#xe2;" k="123" />
    <hkern u1="&#x2f;" u2="&#xe1;" k="123" />
    <hkern u1="&#x2f;" u2="&#xe0;" k="123" />
    <hkern u1="&#x2f;" u2="&#xd8;" k="56" />
    <hkern u1="&#x2f;" u2="&#xd6;" k="56" />
    <hkern u1="&#x2f;" u2="&#xd5;" k="56" />
    <hkern u1="&#x2f;" u2="&#xd4;" k="56" />
    <hkern u1="&#x2f;" u2="&#xd3;" k="56" />
    <hkern u1="&#x2f;" u2="&#xd2;" k="56" />
    <hkern u1="&#x2f;" u2="&#xc7;" k="56" />
    <hkern u1="&#x2f;" u2="&#xc6;" k="169" />
    <hkern u1="&#x2f;" u2="&#xc5;" k="169" />
    <hkern u1="&#x2f;" u2="&#xc4;" k="169" />
    <hkern u1="&#x2f;" u2="&#xc3;" k="169" />
    <hkern u1="&#x2f;" u2="&#xc2;" k="169" />
    <hkern u1="&#x2f;" u2="&#xc1;" k="169" />
    <hkern u1="&#x2f;" u2="&#xc0;" k="169" />
    <hkern u1="&#x2f;" u2="&#xbb;" k="116" />
    <hkern u1="&#x2f;" u2="&#xba;" k="-44" />
    <hkern u1="&#x2f;" u2="&#xb9;" k="-49" />
    <hkern u1="&#x2f;" u2="&#xb7;" k="116" />
    <hkern u1="&#x2f;" u2="&#xb5;" k="99" />
    <hkern u1="&#x2f;" u2="&#xb3;" k="-49" />
    <hkern u1="&#x2f;" u2="&#xb2;" k="-49" />
    <hkern u1="&#x2f;" u2="&#xb0;" k="-44" />
    <hkern u1="&#x2f;" u2="&#xae;" k="56" />
    <hkern u1="&#x2f;" u2="&#xad;" k="116" />
    <hkern u1="&#x2f;" u2="&#xab;" k="116" />
    <hkern u1="&#x2f;" u2="&#xaa;" k="-44" />
    <hkern u1="&#x2f;" u2="&#xa9;" k="56" />
    <hkern u1="&#x2f;" u2="z" k="91" />
    <hkern u1="&#x2f;" u2="y" k="59" />
    <hkern u1="&#x2f;" u2="x" k="67" />
    <hkern u1="&#x2f;" u2="v" k="59" />
    <hkern u1="&#x2f;" u2="u" k="99" />
    <hkern u1="&#x2f;" u2="t" k="46" />
    <hkern u1="&#x2f;" u2="s" k="108" />
    <hkern u1="&#x2f;" u2="r" k="99" />
    <hkern u1="&#x2f;" u2="q" k="123" />
    <hkern u1="&#x2f;" u2="p" k="99" />
    <hkern u1="&#x2f;" u2="o" k="123" />
    <hkern u1="&#x2f;" u2="n" k="99" />
    <hkern u1="&#x2f;" u2="m" k="99" />
    <hkern u1="&#x2f;" u2="g" k="138" />
    <hkern u1="&#x2f;" u2="f" k="30" />
    <hkern u1="&#x2f;" u2="e" k="123" />
    <hkern u1="&#x2f;" u2="d" k="123" />
    <hkern u1="&#x2f;" u2="c" k="123" />
    <hkern u1="&#x2f;" u2="a" k="123" />
    <hkern u1="&#x2f;" u2="Q" k="56" />
    <hkern u1="&#x2f;" u2="O" k="56" />
    <hkern u1="&#x2f;" u2="J" k="156" />
    <hkern u1="&#x2f;" u2="G" k="56" />
    <hkern u1="&#x2f;" u2="C" k="56" />
    <hkern u1="&#x2f;" u2="A" k="169" />
    <hkern u1="&#x2f;" u2="&#x40;" k="56" />
    <hkern u1="&#x2f;" u2="&#x3f;" k="-39" />
    <hkern u1="&#x2f;" u2="&#x3b;" k="99" />
    <hkern u1="&#x2f;" u2="&#x3a;" k="99" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="169" />
    <hkern u1="&#x2f;" u2="&#x2e;" k="196" />
    <hkern u1="&#x2f;" u2="&#x2d;" k="116" />
    <hkern u1="&#x2f;" u2="&#x2c;" k="196" />
    <hkern u1="&#x2f;" u2="&#x2a;" k="-44" />
    <hkern u1="&#x2f;" u2="&#x27;" k="-44" />
    <hkern u1="&#x2f;" u2="&#x26;" k="169" />
    <hkern u1="&#x2f;" u2="&#x22;" k="-44" />
    <hkern u1="&#x40;" u2="&#x2206;" k="51" />
    <hkern u1="&#x40;" u2="&#x2122;" k="42" />
    <hkern u1="&#x40;" u2="&#x201e;" k="52" />
    <hkern u1="&#x40;" u2="&#x201d;" k="42" />
    <hkern u1="&#x40;" u2="&#x201c;" k="42" />
    <hkern u1="&#x40;" u2="&#x201a;" k="52" />
    <hkern u1="&#x40;" u2="&#x2019;" k="42" />
    <hkern u1="&#x40;" u2="&#x2018;" k="42" />
    <hkern u1="&#x40;" u2="&#x17d;" k="64" />
    <hkern u1="&#x40;" u2="&#x17b;" k="64" />
    <hkern u1="&#x40;" u2="&#x179;" k="64" />
    <hkern u1="&#x40;" u2="&#x178;" k="80" />
    <hkern u1="&#x40;" u2="&#x104;" k="51" />
    <hkern u1="&#x40;" u2="&#xdd;" k="80" />
    <hkern u1="&#x40;" u2="&#xc6;" k="51" />
    <hkern u1="&#x40;" u2="&#xc5;" k="51" />
    <hkern u1="&#x40;" u2="&#xc4;" k="51" />
    <hkern u1="&#x40;" u2="&#xc3;" k="51" />
    <hkern u1="&#x40;" u2="&#xc2;" k="51" />
    <hkern u1="&#x40;" u2="&#xc1;" k="51" />
    <hkern u1="&#x40;" u2="&#xc0;" k="51" />
    <hkern u1="&#x40;" u2="&#xba;" k="42" />
    <hkern u1="&#x40;" u2="&#xb0;" k="42" />
    <hkern u1="&#x40;" u2="&#xaa;" k="42" />
    <hkern u1="&#x40;" u2="&#x7d;" k="40" />
    <hkern u1="&#x40;" u2="]" k="40" />
    <hkern u1="&#x40;" u2="\" k="56" />
    <hkern u1="&#x40;" u2="Z" k="64" />
    <hkern u1="&#x40;" u2="Y" k="80" />
    <hkern u1="&#x40;" u2="X" k="30" />
    <hkern u1="&#x40;" u2="V" k="56" />
    <hkern u1="&#x40;" u2="T" k="78" />
    <hkern u1="&#x40;" u2="A" k="51" />
    <hkern u1="&#x40;" u2="&#x2f;" k="51" />
    <hkern u1="&#x40;" u2="&#x2e;" k="52" />
    <hkern u1="&#x40;" u2="&#x2c;" k="52" />
    <hkern u1="&#x40;" u2="&#x2a;" k="42" />
    <hkern u1="&#x40;" u2="&#x29;" k="40" />
    <hkern u1="&#x40;" u2="&#x27;" k="42" />
    <hkern u1="&#x40;" u2="&#x26;" k="51" />
    <hkern u1="&#x40;" u2="&#x22;" k="42" />
    <hkern u1="A" u2="&#x2122;" k="191" />
    <hkern u1="A" u2="&#x203a;" k="67" />
    <hkern u1="A" u2="&#x2039;" k="67" />
    <hkern u1="A" u2="&#x2022;" k="67" />
    <hkern u1="A" u2="&#x201d;" k="191" />
    <hkern u1="A" u2="&#x201c;" k="191" />
    <hkern u1="A" u2="&#x2019;" k="191" />
    <hkern u1="A" u2="&#x2018;" k="191" />
    <hkern u1="A" u2="&#x2014;" k="67" />
    <hkern u1="A" u2="&#x2013;" k="67" />
    <hkern u1="A" u2="&#x178;" k="182" />
    <hkern u1="A" u2="&#x152;" k="51" />
    <hkern u1="A" u2="&#x106;" k="51" />
    <hkern u1="A" u2="&#xff;" k="91" />
    <hkern u1="A" u2="&#xfd;" k="91" />
    <hkern u1="A" u2="&#xdd;" k="182" />
    <hkern u1="A" u2="&#xdc;" k="52" />
    <hkern u1="A" u2="&#xdb;" k="52" />
    <hkern u1="A" u2="&#xda;" k="52" />
    <hkern u1="A" u2="&#xd9;" k="52" />
    <hkern u1="A" u2="&#xd8;" k="51" />
    <hkern u1="A" u2="&#xd6;" k="51" />
    <hkern u1="A" u2="&#xd5;" k="51" />
    <hkern u1="A" u2="&#xd4;" k="51" />
    <hkern u1="A" u2="&#xd3;" k="51" />
    <hkern u1="A" u2="&#xd2;" k="51" />
    <hkern u1="A" u2="&#xc7;" k="51" />
    <hkern u1="A" u2="&#xbb;" k="67" />
    <hkern u1="A" u2="&#xba;" k="191" />
    <hkern u1="A" u2="&#xb9;" k="202" />
    <hkern u1="A" u2="&#xb7;" k="67" />
    <hkern u1="A" u2="&#xb3;" k="202" />
    <hkern u1="A" u2="&#xb2;" k="202" />
    <hkern u1="A" u2="&#xb0;" k="191" />
    <hkern u1="A" u2="&#xae;" k="51" />
    <hkern u1="A" u2="&#xad;" k="67" />
    <hkern u1="A" u2="&#xab;" k="67" />
    <hkern u1="A" u2="&#xaa;" k="191" />
    <hkern u1="A" u2="&#xa9;" k="51" />
    <hkern u1="A" u2="y" k="91" />
    <hkern u1="A" u2="v" k="91" />
    <hkern u1="A" u2="\" k="169" />
    <hkern u1="A" u2="Y" k="182" />
    <hkern u1="A" u2="W" k="102" />
    <hkern u1="A" u2="V" k="169" />
    <hkern u1="A" u2="U" k="52" />
    <hkern u1="A" u2="T" k="147" />
    <hkern u1="A" u2="Q" k="51" />
    <hkern u1="A" u2="O" k="51" />
    <hkern u1="A" u2="J" k="-56" />
    <hkern u1="A" u2="G" k="51" />
    <hkern u1="A" u2="C" k="51" />
    <hkern u1="A" u2="&#x40;" k="51" />
    <hkern u1="A" u2="&#x3f;" k="63" />
    <hkern u1="A" u2="&#x2d;" k="67" />
    <hkern u1="A" u2="&#x2a;" k="191" />
    <hkern u1="A" u2="&#x27;" k="191" />
    <hkern u1="A" u2="&#x22;" k="191" />
    <hkern u1="C" u2="&#x203a;" k="144" />
    <hkern u1="C" u2="&#x2039;" k="144" />
    <hkern u1="C" u2="&#x2022;" k="144" />
    <hkern u1="C" u2="&#x2014;" k="144" />
    <hkern u1="C" u2="&#x2013;" k="144" />
    <hkern u1="C" u2="&#xbb;" k="144" />
    <hkern u1="C" u2="&#xb7;" k="144" />
    <hkern u1="C" u2="&#xad;" k="144" />
    <hkern u1="C" u2="&#xab;" k="144" />
    <hkern u1="C" u2="&#x2d;" k="144" />
    <hkern u1="D" u2="&#x2206;" k="51" />
    <hkern u1="D" u2="&#x2122;" k="42" />
    <hkern u1="D" u2="&#x201e;" k="52" />
    <hkern u1="D" u2="&#x201d;" k="42" />
    <hkern u1="D" u2="&#x201c;" k="42" />
    <hkern u1="D" u2="&#x201a;" k="52" />
    <hkern u1="D" u2="&#x2019;" k="42" />
    <hkern u1="D" u2="&#x2018;" k="42" />
    <hkern u1="D" u2="&#x17d;" k="64" />
    <hkern u1="D" u2="&#x17b;" k="64" />
    <hkern u1="D" u2="&#x179;" k="64" />
    <hkern u1="D" u2="&#x178;" k="80" />
    <hkern u1="D" u2="&#x104;" k="51" />
    <hkern u1="D" u2="&#xdd;" k="80" />
    <hkern u1="D" u2="&#xc6;" k="51" />
    <hkern u1="D" u2="&#xc5;" k="51" />
    <hkern u1="D" u2="&#xc4;" k="51" />
    <hkern u1="D" u2="&#xc3;" k="51" />
    <hkern u1="D" u2="&#xc2;" k="51" />
    <hkern u1="D" u2="&#xc1;" k="51" />
    <hkern u1="D" u2="&#xc0;" k="51" />
    <hkern u1="D" u2="&#xba;" k="42" />
    <hkern u1="D" u2="&#xb0;" k="42" />
    <hkern u1="D" u2="&#xaa;" k="42" />
    <hkern u1="D" u2="&#x7d;" k="40" />
    <hkern u1="D" u2="]" k="40" />
    <hkern u1="D" u2="\" k="56" />
    <hkern u1="D" u2="Z" k="64" />
    <hkern u1="D" u2="Y" k="80" />
    <hkern u1="D" u2="X" k="30" />
    <hkern u1="D" u2="V" k="56" />
    <hkern u1="D" u2="T" k="78" />
    <hkern u1="D" u2="A" k="51" />
    <hkern u1="D" u2="&#x2f;" k="51" />
    <hkern u1="D" u2="&#x2e;" k="52" />
    <hkern u1="D" u2="&#x2c;" k="52" />
    <hkern u1="D" u2="&#x2a;" k="42" />
    <hkern u1="D" u2="&#x29;" k="40" />
    <hkern u1="D" u2="&#x27;" k="42" />
    <hkern u1="D" u2="&#x26;" k="51" />
    <hkern u1="D" u2="&#x22;" k="42" />
    <hkern u1="F" u2="&#x2206;" k="147" />
    <hkern u1="F" u2="&#x201e;" k="180" />
    <hkern u1="F" u2="&#x201a;" k="180" />
    <hkern u1="F" u2="&#x153;" k="64" />
    <hkern u1="F" u2="&#x144;" k="60" />
    <hkern u1="F" u2="&#x131;" k="60" />
    <hkern u1="F" u2="&#x119;" k="64" />
    <hkern u1="F" u2="&#x107;" k="64" />
    <hkern u1="F" u2="&#x104;" k="147" />
    <hkern u1="F" u2="&#xfc;" k="60" />
    <hkern u1="F" u2="&#xfb;" k="60" />
    <hkern u1="F" u2="&#xfa;" k="60" />
    <hkern u1="F" u2="&#xf9;" k="60" />
    <hkern u1="F" u2="&#xf8;" k="64" />
    <hkern u1="F" u2="&#xf6;" k="64" />
    <hkern u1="F" u2="&#xf5;" k="64" />
    <hkern u1="F" u2="&#xf4;" k="64" />
    <hkern u1="F" u2="&#xf3;" k="64" />
    <hkern u1="F" u2="&#xf2;" k="64" />
    <hkern u1="F" u2="&#xf1;" k="60" />
    <hkern u1="F" u2="&#xf0;" k="64" />
    <hkern u1="F" u2="&#xeb;" k="64" />
    <hkern u1="F" u2="&#xea;" k="64" />
    <hkern u1="F" u2="&#xe9;" k="64" />
    <hkern u1="F" u2="&#xe8;" k="64" />
    <hkern u1="F" u2="&#xe7;" k="64" />
    <hkern u1="F" u2="&#xc6;" k="147" />
    <hkern u1="F" u2="&#xc5;" k="147" />
    <hkern u1="F" u2="&#xc4;" k="147" />
    <hkern u1="F" u2="&#xc3;" k="147" />
    <hkern u1="F" u2="&#xc2;" k="147" />
    <hkern u1="F" u2="&#xc1;" k="147" />
    <hkern u1="F" u2="&#xc0;" k="147" />
    <hkern u1="F" u2="&#xb5;" k="60" />
    <hkern u1="F" u2="u" k="60" />
    <hkern u1="F" u2="r" k="60" />
    <hkern u1="F" u2="q" k="64" />
    <hkern u1="F" u2="p" k="60" />
    <hkern u1="F" u2="o" k="64" />
    <hkern u1="F" u2="n" k="60" />
    <hkern u1="F" u2="m" k="60" />
    <hkern u1="F" u2="e" k="64" />
    <hkern u1="F" u2="d" k="64" />
    <hkern u1="F" u2="c" k="64" />
    <hkern u1="F" u2="J" k="189" />
    <hkern u1="F" u2="A" k="147" />
    <hkern u1="F" u2="&#x3f;" k="-30" />
    <hkern u1="F" u2="&#x3b;" k="60" />
    <hkern u1="F" u2="&#x3a;" k="60" />
    <hkern u1="F" u2="&#x2f;" k="147" />
    <hkern u1="F" u2="&#x2e;" k="180" />
    <hkern u1="F" u2="&#x2c;" k="180" />
    <hkern u1="F" u2="&#x26;" k="147" />
    <hkern u1="J" u2="&#x2206;" k="52" />
    <hkern u1="J" u2="&#x201e;" k="50" />
    <hkern u1="J" u2="&#x201a;" k="50" />
    <hkern u1="J" u2="&#x104;" k="52" />
    <hkern u1="J" u2="&#xc6;" k="52" />
    <hkern u1="J" u2="&#xc5;" k="52" />
    <hkern u1="J" u2="&#xc4;" k="52" />
    <hkern u1="J" u2="&#xc3;" k="52" />
    <hkern u1="J" u2="&#xc2;" k="52" />
    <hkern u1="J" u2="&#xc1;" k="52" />
    <hkern u1="J" u2="&#xc0;" k="52" />
    <hkern u1="J" u2="A" k="52" />
    <hkern u1="J" u2="&#x2f;" k="52" />
    <hkern u1="J" u2="&#x2e;" k="50" />
    <hkern u1="J" u2="&#x2c;" k="50" />
    <hkern u1="J" u2="&#x26;" k="52" />
    <hkern u1="K" u2="&#x203a;" k="66" />
    <hkern u1="K" u2="&#x2039;" k="66" />
    <hkern u1="K" u2="&#x2022;" k="66" />
    <hkern u1="K" u2="&#x2014;" k="66" />
    <hkern u1="K" u2="&#x2013;" k="66" />
    <hkern u1="K" u2="&#x153;" k="43" />
    <hkern u1="K" u2="&#x152;" k="30" />
    <hkern u1="K" u2="&#x119;" k="43" />
    <hkern u1="K" u2="&#x107;" k="43" />
    <hkern u1="K" u2="&#x106;" k="30" />
    <hkern u1="K" u2="&#xff;" k="73" />
    <hkern u1="K" u2="&#xfd;" k="73" />
    <hkern u1="K" u2="&#xf8;" k="43" />
    <hkern u1="K" u2="&#xf6;" k="43" />
    <hkern u1="K" u2="&#xf5;" k="43" />
    <hkern u1="K" u2="&#xf4;" k="43" />
    <hkern u1="K" u2="&#xf3;" k="43" />
    <hkern u1="K" u2="&#xf2;" k="43" />
    <hkern u1="K" u2="&#xf0;" k="43" />
    <hkern u1="K" u2="&#xeb;" k="43" />
    <hkern u1="K" u2="&#xea;" k="43" />
    <hkern u1="K" u2="&#xe9;" k="43" />
    <hkern u1="K" u2="&#xe8;" k="43" />
    <hkern u1="K" u2="&#xe7;" k="43" />
    <hkern u1="K" u2="&#xd8;" k="30" />
    <hkern u1="K" u2="&#xd6;" k="30" />
    <hkern u1="K" u2="&#xd5;" k="30" />
    <hkern u1="K" u2="&#xd4;" k="30" />
    <hkern u1="K" u2="&#xd3;" k="30" />
    <hkern u1="K" u2="&#xd2;" k="30" />
    <hkern u1="K" u2="&#xc7;" k="30" />
    <hkern u1="K" u2="&#xbb;" k="66" />
    <hkern u1="K" u2="&#xb7;" k="66" />
    <hkern u1="K" u2="&#xae;" k="30" />
    <hkern u1="K" u2="&#xad;" k="66" />
    <hkern u1="K" u2="&#xab;" k="66" />
    <hkern u1="K" u2="&#xa9;" k="30" />
    <hkern u1="K" u2="y" k="73" />
    <hkern u1="K" u2="w" k="52" />
    <hkern u1="K" u2="v" k="73" />
    <hkern u1="K" u2="t" k="91" />
    <hkern u1="K" u2="q" k="43" />
    <hkern u1="K" u2="o" k="43" />
    <hkern u1="K" u2="f" k="56" />
    <hkern u1="K" u2="e" k="43" />
    <hkern u1="K" u2="d" k="43" />
    <hkern u1="K" u2="c" k="43" />
    <hkern u1="K" u2="Q" k="30" />
    <hkern u1="K" u2="O" k="30" />
    <hkern u1="K" u2="G" k="30" />
    <hkern u1="K" u2="C" k="30" />
    <hkern u1="K" u2="&#x40;" k="30" />
    <hkern u1="K" u2="&#x2d;" k="66" />
    <hkern u1="L" u2="&#x2122;" k="284" />
    <hkern u1="L" u2="&#x203a;" k="178" />
    <hkern u1="L" u2="&#x2039;" k="178" />
    <hkern u1="L" u2="&#x2022;" k="178" />
    <hkern u1="L" u2="&#x201e;" k="-58" />
    <hkern u1="L" u2="&#x201d;" k="284" />
    <hkern u1="L" u2="&#x201c;" k="284" />
    <hkern u1="L" u2="&#x201a;" k="-58" />
    <hkern u1="L" u2="&#x2019;" k="284" />
    <hkern u1="L" u2="&#x2018;" k="284" />
    <hkern u1="L" u2="&#x2014;" k="178" />
    <hkern u1="L" u2="&#x2013;" k="178" />
    <hkern u1="L" u2="&#x178;" k="227" />
    <hkern u1="L" u2="&#x153;" k="38" />
    <hkern u1="L" u2="&#x152;" k="80" />
    <hkern u1="L" u2="&#x119;" k="38" />
    <hkern u1="L" u2="&#x107;" k="38" />
    <hkern u1="L" u2="&#x106;" k="80" />
    <hkern u1="L" u2="&#xff;" k="119" />
    <hkern u1="L" u2="&#xfd;" k="119" />
    <hkern u1="L" u2="&#xf8;" k="38" />
    <hkern u1="L" u2="&#xf6;" k="38" />
    <hkern u1="L" u2="&#xf5;" k="38" />
    <hkern u1="L" u2="&#xf4;" k="38" />
    <hkern u1="L" u2="&#xf3;" k="38" />
    <hkern u1="L" u2="&#xf2;" k="38" />
    <hkern u1="L" u2="&#xf0;" k="38" />
    <hkern u1="L" u2="&#xeb;" k="38" />
    <hkern u1="L" u2="&#xea;" k="38" />
    <hkern u1="L" u2="&#xe9;" k="38" />
    <hkern u1="L" u2="&#xe8;" k="38" />
    <hkern u1="L" u2="&#xe7;" k="38" />
    <hkern u1="L" u2="&#xdd;" k="227" />
    <hkern u1="L" u2="&#xd8;" k="80" />
    <hkern u1="L" u2="&#xd6;" k="80" />
    <hkern u1="L" u2="&#xd5;" k="80" />
    <hkern u1="L" u2="&#xd4;" k="80" />
    <hkern u1="L" u2="&#xd3;" k="80" />
    <hkern u1="L" u2="&#xd2;" k="80" />
    <hkern u1="L" u2="&#xc7;" k="80" />
    <hkern u1="L" u2="&#xbb;" k="178" />
    <hkern u1="L" u2="&#xba;" k="284" />
    <hkern u1="L" u2="&#xb9;" k="211" />
    <hkern u1="L" u2="&#xb7;" k="178" />
    <hkern u1="L" u2="&#xb3;" k="211" />
    <hkern u1="L" u2="&#xb2;" k="211" />
    <hkern u1="L" u2="&#xb0;" k="284" />
    <hkern u1="L" u2="&#xae;" k="80" />
    <hkern u1="L" u2="&#xad;" k="178" />
    <hkern u1="L" u2="&#xab;" k="178" />
    <hkern u1="L" u2="&#xaa;" k="284" />
    <hkern u1="L" u2="&#xa9;" k="80" />
    <hkern u1="L" u2="y" k="119" />
    <hkern u1="L" u2="w" k="80" />
    <hkern u1="L" u2="v" k="119" />
    <hkern u1="L" u2="q" k="38" />
    <hkern u1="L" u2="o" k="38" />
    <hkern u1="L" u2="e" k="38" />
    <hkern u1="L" u2="d" k="38" />
    <hkern u1="L" u2="c" k="38" />
    <hkern u1="L" u2="\" k="191" />
    <hkern u1="L" u2="Y" k="227" />
    <hkern u1="L" u2="W" k="167" />
    <hkern u1="L" u2="V" k="191" />
    <hkern u1="L" u2="T" k="176" />
    <hkern u1="L" u2="Q" k="80" />
    <hkern u1="L" u2="O" k="80" />
    <hkern u1="L" u2="G" k="80" />
    <hkern u1="L" u2="C" k="80" />
    <hkern u1="L" u2="&#x40;" k="80" />
    <hkern u1="L" u2="&#x3f;" k="50" />
    <hkern u1="L" u2="&#x2e;" k="-58" />
    <hkern u1="L" u2="&#x2d;" k="178" />
    <hkern u1="L" u2="&#x2c;" k="-58" />
    <hkern u1="L" u2="&#x2a;" k="284" />
    <hkern u1="L" u2="&#x27;" k="284" />
    <hkern u1="L" u2="&#x22;" k="284" />
    <hkern u1="O" u2="&#x2206;" k="51" />
    <hkern u1="O" u2="&#x2122;" k="42" />
    <hkern u1="O" u2="&#x201e;" k="52" />
    <hkern u1="O" u2="&#x201d;" k="42" />
    <hkern u1="O" u2="&#x201c;" k="42" />
    <hkern u1="O" u2="&#x201a;" k="52" />
    <hkern u1="O" u2="&#x2019;" k="42" />
    <hkern u1="O" u2="&#x2018;" k="42" />
    <hkern u1="O" u2="&#x17d;" k="64" />
    <hkern u1="O" u2="&#x17b;" k="64" />
    <hkern u1="O" u2="&#x179;" k="64" />
    <hkern u1="O" u2="&#x178;" k="80" />
    <hkern u1="O" u2="&#x104;" k="51" />
    <hkern u1="O" u2="&#xdd;" k="80" />
    <hkern u1="O" u2="&#xc6;" k="51" />
    <hkern u1="O" u2="&#xc5;" k="51" />
    <hkern u1="O" u2="&#xc4;" k="51" />
    <hkern u1="O" u2="&#xc3;" k="51" />
    <hkern u1="O" u2="&#xc2;" k="51" />
    <hkern u1="O" u2="&#xc1;" k="51" />
    <hkern u1="O" u2="&#xc0;" k="51" />
    <hkern u1="O" u2="&#xba;" k="42" />
    <hkern u1="O" u2="&#xb0;" k="42" />
    <hkern u1="O" u2="&#xaa;" k="42" />
    <hkern u1="O" u2="&#x7d;" k="40" />
    <hkern u1="O" u2="]" k="40" />
    <hkern u1="O" u2="\" k="56" />
    <hkern u1="O" u2="Z" k="64" />
    <hkern u1="O" u2="Y" k="80" />
    <hkern u1="O" u2="X" k="30" />
    <hkern u1="O" u2="V" k="56" />
    <hkern u1="O" u2="T" k="78" />
    <hkern u1="O" u2="A" k="51" />
    <hkern u1="O" u2="&#x2f;" k="51" />
    <hkern u1="O" u2="&#x2e;" k="52" />
    <hkern u1="O" u2="&#x2c;" k="52" />
    <hkern u1="O" u2="&#x2a;" k="42" />
    <hkern u1="O" u2="&#x29;" k="40" />
    <hkern u1="O" u2="&#x27;" k="42" />
    <hkern u1="O" u2="&#x26;" k="51" />
    <hkern u1="O" u2="&#x22;" k="42" />
    <hkern u1="P" u2="&#x2206;" k="155" />
    <hkern u1="P" u2="&#x201e;" k="265" />
    <hkern u1="P" u2="&#x201a;" k="265" />
    <hkern u1="P" u2="&#x153;" k="30" />
    <hkern u1="P" u2="&#x119;" k="30" />
    <hkern u1="P" u2="&#x107;" k="30" />
    <hkern u1="P" u2="&#x105;" k="50" />
    <hkern u1="P" u2="&#x104;" k="155" />
    <hkern u1="P" u2="&#xf8;" k="30" />
    <hkern u1="P" u2="&#xf6;" k="30" />
    <hkern u1="P" u2="&#xf5;" k="30" />
    <hkern u1="P" u2="&#xf4;" k="30" />
    <hkern u1="P" u2="&#xf3;" k="30" />
    <hkern u1="P" u2="&#xf2;" k="30" />
    <hkern u1="P" u2="&#xf0;" k="30" />
    <hkern u1="P" u2="&#xeb;" k="30" />
    <hkern u1="P" u2="&#xea;" k="30" />
    <hkern u1="P" u2="&#xe9;" k="30" />
    <hkern u1="P" u2="&#xe8;" k="30" />
    <hkern u1="P" u2="&#xe7;" k="30" />
    <hkern u1="P" u2="&#xe6;" k="50" />
    <hkern u1="P" u2="&#xe5;" k="50" />
    <hkern u1="P" u2="&#xe4;" k="50" />
    <hkern u1="P" u2="&#xe3;" k="50" />
    <hkern u1="P" u2="&#xe2;" k="50" />
    <hkern u1="P" u2="&#xe1;" k="50" />
    <hkern u1="P" u2="&#xe0;" k="50" />
    <hkern u1="P" u2="&#xc6;" k="155" />
    <hkern u1="P" u2="&#xc5;" k="155" />
    <hkern u1="P" u2="&#xc4;" k="155" />
    <hkern u1="P" u2="&#xc3;" k="155" />
    <hkern u1="P" u2="&#xc2;" k="155" />
    <hkern u1="P" u2="&#xc1;" k="155" />
    <hkern u1="P" u2="&#xc0;" k="155" />
    <hkern u1="P" u2="q" k="30" />
    <hkern u1="P" u2="o" k="30" />
    <hkern u1="P" u2="e" k="30" />
    <hkern u1="P" u2="d" k="30" />
    <hkern u1="P" u2="c" k="30" />
    <hkern u1="P" u2="a" k="50" />
    <hkern u1="P" u2="J" k="191" />
    <hkern u1="P" u2="A" k="155" />
    <hkern u1="P" u2="&#x2f;" k="155" />
    <hkern u1="P" u2="&#x2e;" k="265" />
    <hkern u1="P" u2="&#x2c;" k="265" />
    <hkern u1="P" u2="&#x26;" k="155" />
    <hkern u1="Q" u2="&#x2206;" k="51" />
    <hkern u1="Q" u2="&#x2122;" k="42" />
    <hkern u1="Q" u2="&#x201e;" k="52" />
    <hkern u1="Q" u2="&#x201d;" k="42" />
    <hkern u1="Q" u2="&#x201c;" k="42" />
    <hkern u1="Q" u2="&#x201a;" k="52" />
    <hkern u1="Q" u2="&#x2019;" k="42" />
    <hkern u1="Q" u2="&#x2018;" k="42" />
    <hkern u1="Q" u2="&#x17d;" k="64" />
    <hkern u1="Q" u2="&#x17b;" k="64" />
    <hkern u1="Q" u2="&#x179;" k="64" />
    <hkern u1="Q" u2="&#x178;" k="80" />
    <hkern u1="Q" u2="&#x104;" k="51" />
    <hkern u1="Q" u2="&#xdd;" k="80" />
    <hkern u1="Q" u2="&#xc6;" k="51" />
    <hkern u1="Q" u2="&#xc5;" k="51" />
    <hkern u1="Q" u2="&#xc4;" k="51" />
    <hkern u1="Q" u2="&#xc3;" k="51" />
    <hkern u1="Q" u2="&#xc2;" k="51" />
    <hkern u1="Q" u2="&#xc1;" k="51" />
    <hkern u1="Q" u2="&#xc0;" k="51" />
    <hkern u1="Q" u2="&#xba;" k="42" />
    <hkern u1="Q" u2="&#xb0;" k="42" />
    <hkern u1="Q" u2="&#xaa;" k="42" />
    <hkern u1="Q" u2="&#x7d;" k="40" />
    <hkern u1="Q" u2="]" k="40" />
    <hkern u1="Q" u2="\" k="56" />
    <hkern u1="Q" u2="Z" k="64" />
    <hkern u1="Q" u2="Y" k="80" />
    <hkern u1="Q" u2="X" k="30" />
    <hkern u1="Q" u2="V" k="56" />
    <hkern u1="Q" u2="T" k="78" />
    <hkern u1="Q" u2="A" k="51" />
    <hkern u1="Q" u2="&#x2f;" k="51" />
    <hkern u1="Q" u2="&#x2e;" k="52" />
    <hkern u1="Q" u2="&#x2c;" k="52" />
    <hkern u1="Q" u2="&#x2a;" k="42" />
    <hkern u1="Q" u2="&#x29;" k="40" />
    <hkern u1="Q" u2="&#x27;" k="42" />
    <hkern u1="Q" u2="&#x26;" k="51" />
    <hkern u1="Q" u2="&#x22;" k="42" />
    <hkern u1="R" u2="&#x152;" k="48" />
    <hkern u1="R" u2="&#x106;" k="48" />
    <hkern u1="R" u2="&#xdc;" k="51" />
    <hkern u1="R" u2="&#xdb;" k="51" />
    <hkern u1="R" u2="&#xda;" k="51" />
    <hkern u1="R" u2="&#xd9;" k="51" />
    <hkern u1="R" u2="&#xd8;" k="48" />
    <hkern u1="R" u2="&#xd6;" k="48" />
    <hkern u1="R" u2="&#xd5;" k="48" />
    <hkern u1="R" u2="&#xd4;" k="48" />
    <hkern u1="R" u2="&#xd3;" k="48" />
    <hkern u1="R" u2="&#xd2;" k="48" />
    <hkern u1="R" u2="&#xc7;" k="48" />
    <hkern u1="R" u2="&#xae;" k="48" />
    <hkern u1="R" u2="&#xa9;" k="48" />
    <hkern u1="R" u2="U" k="51" />
    <hkern u1="R" u2="T" k="56" />
    <hkern u1="R" u2="Q" k="48" />
    <hkern u1="R" u2="O" k="48" />
    <hkern u1="R" u2="G" k="48" />
    <hkern u1="R" u2="C" k="48" />
    <hkern u1="R" u2="&#x40;" k="48" />
    <hkern u1="T" u2="&#x2206;" k="147" />
    <hkern u1="T" u2="&#x203a;" k="180" />
    <hkern u1="T" u2="&#x2039;" k="180" />
    <hkern u1="T" u2="&#x2022;" k="180" />
    <hkern u1="T" u2="&#x201e;" k="180" />
    <hkern u1="T" u2="&#x201a;" k="180" />
    <hkern u1="T" u2="&#x2014;" k="180" />
    <hkern u1="T" u2="&#x2013;" k="180" />
    <hkern u1="T" u2="&#x17e;" k="120" />
    <hkern u1="T" u2="&#x17c;" k="120" />
    <hkern u1="T" u2="&#x17a;" k="120" />
    <hkern u1="T" u2="&#x161;" k="140" />
    <hkern u1="T" u2="&#x15b;" k="140" />
    <hkern u1="T" u2="&#x153;" k="204" />
    <hkern u1="T" u2="&#x152;" k="78" />
    <hkern u1="T" u2="&#x144;" k="160" />
    <hkern u1="T" u2="&#x131;" k="160" />
    <hkern u1="T" u2="&#x119;" k="204" />
    <hkern u1="T" u2="&#x107;" k="204" />
    <hkern u1="T" u2="&#x106;" k="78" />
    <hkern u1="T" u2="&#x105;" k="244" />
    <hkern u1="T" u2="&#x104;" k="147" />
    <hkern u1="T" u2="&#xff;" k="180" />
    <hkern u1="T" u2="&#xfd;" k="180" />
    <hkern u1="T" u2="&#xfc;" k="160" />
    <hkern u1="T" u2="&#xfb;" k="160" />
    <hkern u1="T" u2="&#xfa;" k="160" />
    <hkern u1="T" u2="&#xf9;" k="160" />
    <hkern u1="T" u2="&#xf8;" k="204" />
    <hkern u1="T" u2="&#xf6;" k="204" />
    <hkern u1="T" u2="&#xf5;" k="204" />
    <hkern u1="T" u2="&#xf4;" k="204" />
    <hkern u1="T" u2="&#xf3;" k="204" />
    <hkern u1="T" u2="&#xf2;" k="204" />
    <hkern u1="T" u2="&#xf1;" k="160" />
    <hkern u1="T" u2="&#xf0;" k="204" />
    <hkern u1="T" u2="&#xeb;" k="204" />
    <hkern u1="T" u2="&#xea;" k="204" />
    <hkern u1="T" u2="&#xe9;" k="204" />
    <hkern u1="T" u2="&#xe8;" k="204" />
    <hkern u1="T" u2="&#xe7;" k="204" />
    <hkern u1="T" u2="&#xe6;" k="244" />
    <hkern u1="T" u2="&#xe5;" k="244" />
    <hkern u1="T" u2="&#xe4;" k="244" />
    <hkern u1="T" u2="&#xe3;" k="244" />
    <hkern u1="T" u2="&#xe2;" k="244" />
    <hkern u1="T" u2="&#xe1;" k="244" />
    <hkern u1="T" u2="&#xe0;" k="244" />
    <hkern u1="T" u2="&#xd8;" k="78" />
    <hkern u1="T" u2="&#xd6;" k="78" />
    <hkern u1="T" u2="&#xd5;" k="78" />
    <hkern u1="T" u2="&#xd4;" k="78" />
    <hkern u1="T" u2="&#xd3;" k="78" />
    <hkern u1="T" u2="&#xd2;" k="78" />
    <hkern u1="T" u2="&#xc7;" k="78" />
    <hkern u1="T" u2="&#xc6;" k="147" />
    <hkern u1="T" u2="&#xc5;" k="147" />
    <hkern u1="T" u2="&#xc4;" k="147" />
    <hkern u1="T" u2="&#xc3;" k="147" />
    <hkern u1="T" u2="&#xc2;" k="147" />
    <hkern u1="T" u2="&#xc1;" k="147" />
    <hkern u1="T" u2="&#xc0;" k="147" />
    <hkern u1="T" u2="&#xbb;" k="180" />
    <hkern u1="T" u2="&#xb7;" k="180" />
    <hkern u1="T" u2="&#xb5;" k="160" />
    <hkern u1="T" u2="&#xae;" k="78" />
    <hkern u1="T" u2="&#xad;" k="180" />
    <hkern u1="T" u2="&#xab;" k="180" />
    <hkern u1="T" u2="&#xa9;" k="78" />
    <hkern u1="T" u2="z" k="120" />
    <hkern u1="T" u2="y" k="180" />
    <hkern u1="T" u2="x" k="137" />
    <hkern u1="T" u2="w" k="140" />
    <hkern u1="T" u2="v" k="180" />
    <hkern u1="T" u2="u" k="160" />
    <hkern u1="T" u2="s" k="140" />
    <hkern u1="T" u2="r" k="160" />
    <hkern u1="T" u2="q" k="204" />
    <hkern u1="T" u2="p" k="160" />
    <hkern u1="T" u2="o" k="204" />
    <hkern u1="T" u2="n" k="160" />
    <hkern u1="T" u2="m" k="160" />
    <hkern u1="T" u2="g" k="181" />
    <hkern u1="T" u2="e" k="204" />
    <hkern u1="T" u2="d" k="204" />
    <hkern u1="T" u2="c" k="204" />
    <hkern u1="T" u2="a" k="244" />
    <hkern u1="T" u2="Q" k="78" />
    <hkern u1="T" u2="O" k="78" />
    <hkern u1="T" u2="J" k="200" />
    <hkern u1="T" u2="G" k="78" />
    <hkern u1="T" u2="C" k="78" />
    <hkern u1="T" u2="A" k="147" />
    <hkern u1="T" u2="&#x40;" k="78" />
    <hkern u1="T" u2="&#x3b;" k="160" />
    <hkern u1="T" u2="&#x3a;" k="160" />
    <hkern u1="T" u2="&#x2f;" k="147" />
    <hkern u1="T" u2="&#x2e;" k="180" />
    <hkern u1="T" u2="&#x2d;" k="180" />
    <hkern u1="T" u2="&#x2c;" k="180" />
    <hkern u1="T" u2="&#x26;" k="147" />
    <hkern u1="U" u2="&#x2206;" k="52" />
    <hkern u1="U" u2="&#x201e;" k="50" />
    <hkern u1="U" u2="&#x201a;" k="50" />
    <hkern u1="U" u2="&#x104;" k="52" />
    <hkern u1="U" u2="&#xc6;" k="52" />
    <hkern u1="U" u2="&#xc5;" k="52" />
    <hkern u1="U" u2="&#xc4;" k="52" />
    <hkern u1="U" u2="&#xc3;" k="52" />
    <hkern u1="U" u2="&#xc2;" k="52" />
    <hkern u1="U" u2="&#xc1;" k="52" />
    <hkern u1="U" u2="&#xc0;" k="52" />
    <hkern u1="U" u2="A" k="52" />
    <hkern u1="U" u2="&#x2f;" k="52" />
    <hkern u1="U" u2="&#x2e;" k="50" />
    <hkern u1="U" u2="&#x2c;" k="50" />
    <hkern u1="U" u2="&#x26;" k="52" />
    <hkern u1="V" u2="&#x2206;" k="169" />
    <hkern u1="V" u2="&#x2122;" k="-44" />
    <hkern u1="V" u2="&#x203a;" k="116" />
    <hkern u1="V" u2="&#x2039;" k="116" />
    <hkern u1="V" u2="&#x2022;" k="116" />
    <hkern u1="V" u2="&#x201e;" k="196" />
    <hkern u1="V" u2="&#x201d;" k="-44" />
    <hkern u1="V" u2="&#x201c;" k="-44" />
    <hkern u1="V" u2="&#x201a;" k="196" />
    <hkern u1="V" u2="&#x2019;" k="-44" />
    <hkern u1="V" u2="&#x2018;" k="-44" />
    <hkern u1="V" u2="&#x2014;" k="116" />
    <hkern u1="V" u2="&#x2013;" k="116" />
    <hkern u1="V" u2="&#x17e;" k="91" />
    <hkern u1="V" u2="&#x17c;" k="91" />
    <hkern u1="V" u2="&#x17a;" k="91" />
    <hkern u1="V" u2="&#x161;" k="108" />
    <hkern u1="V" u2="&#x15b;" k="108" />
    <hkern u1="V" u2="&#x153;" k="123" />
    <hkern u1="V" u2="&#x152;" k="56" />
    <hkern u1="V" u2="&#x144;" k="99" />
    <hkern u1="V" u2="&#x131;" k="99" />
    <hkern u1="V" u2="&#x119;" k="123" />
    <hkern u1="V" u2="&#x107;" k="123" />
    <hkern u1="V" u2="&#x106;" k="56" />
    <hkern u1="V" u2="&#x105;" k="123" />
    <hkern u1="V" u2="&#x104;" k="169" />
    <hkern u1="V" u2="&#xff;" k="59" />
    <hkern u1="V" u2="&#xfd;" k="59" />
    <hkern u1="V" u2="&#xfc;" k="99" />
    <hkern u1="V" u2="&#xfb;" k="99" />
    <hkern u1="V" u2="&#xfa;" k="99" />
    <hkern u1="V" u2="&#xf9;" k="99" />
    <hkern u1="V" u2="&#xf8;" k="123" />
    <hkern u1="V" u2="&#xf6;" k="123" />
    <hkern u1="V" u2="&#xf5;" k="123" />
    <hkern u1="V" u2="&#xf4;" k="123" />
    <hkern u1="V" u2="&#xf3;" k="123" />
    <hkern u1="V" u2="&#xf2;" k="123" />
    <hkern u1="V" u2="&#xf1;" k="99" />
    <hkern u1="V" u2="&#xf0;" k="123" />
    <hkern u1="V" u2="&#xeb;" k="123" />
    <hkern u1="V" u2="&#xea;" k="123" />
    <hkern u1="V" u2="&#xe9;" k="123" />
    <hkern u1="V" u2="&#xe8;" k="123" />
    <hkern u1="V" u2="&#xe7;" k="123" />
    <hkern u1="V" u2="&#xe6;" k="123" />
    <hkern u1="V" u2="&#xe5;" k="123" />
    <hkern u1="V" u2="&#xe4;" k="123" />
    <hkern u1="V" u2="&#xe3;" k="123" />
    <hkern u1="V" u2="&#xe2;" k="123" />
    <hkern u1="V" u2="&#xe1;" k="123" />
    <hkern u1="V" u2="&#xe0;" k="123" />
    <hkern u1="V" u2="&#xd8;" k="56" />
    <hkern u1="V" u2="&#xd6;" k="56" />
    <hkern u1="V" u2="&#xd5;" k="56" />
    <hkern u1="V" u2="&#xd4;" k="56" />
    <hkern u1="V" u2="&#xd3;" k="56" />
    <hkern u1="V" u2="&#xd2;" k="56" />
    <hkern u1="V" u2="&#xc7;" k="56" />
    <hkern u1="V" u2="&#xc6;" k="169" />
    <hkern u1="V" u2="&#xc5;" k="169" />
    <hkern u1="V" u2="&#xc4;" k="169" />
    <hkern u1="V" u2="&#xc3;" k="169" />
    <hkern u1="V" u2="&#xc2;" k="169" />
    <hkern u1="V" u2="&#xc1;" k="169" />
    <hkern u1="V" u2="&#xc0;" k="169" />
    <hkern u1="V" u2="&#xbb;" k="116" />
    <hkern u1="V" u2="&#xba;" k="-44" />
    <hkern u1="V" u2="&#xb9;" k="-49" />
    <hkern u1="V" u2="&#xb7;" k="116" />
    <hkern u1="V" u2="&#xb5;" k="99" />
    <hkern u1="V" u2="&#xb3;" k="-49" />
    <hkern u1="V" u2="&#xb2;" k="-49" />
    <hkern u1="V" u2="&#xb0;" k="-44" />
    <hkern u1="V" u2="&#xae;" k="56" />
    <hkern u1="V" u2="&#xad;" k="116" />
    <hkern u1="V" u2="&#xab;" k="116" />
    <hkern u1="V" u2="&#xaa;" k="-44" />
    <hkern u1="V" u2="&#xa9;" k="56" />
    <hkern u1="V" u2="z" k="91" />
    <hkern u1="V" u2="y" k="59" />
    <hkern u1="V" u2="x" k="67" />
    <hkern u1="V" u2="v" k="59" />
    <hkern u1="V" u2="u" k="99" />
    <hkern u1="V" u2="t" k="46" />
    <hkern u1="V" u2="s" k="108" />
    <hkern u1="V" u2="r" k="99" />
    <hkern u1="V" u2="q" k="123" />
    <hkern u1="V" u2="p" k="99" />
    <hkern u1="V" u2="o" k="123" />
    <hkern u1="V" u2="n" k="99" />
    <hkern u1="V" u2="m" k="99" />
    <hkern u1="V" u2="g" k="138" />
    <hkern u1="V" u2="f" k="30" />
    <hkern u1="V" u2="e" k="123" />
    <hkern u1="V" u2="d" k="123" />
    <hkern u1="V" u2="c" k="123" />
    <hkern u1="V" u2="a" k="123" />
    <hkern u1="V" u2="Q" k="56" />
    <hkern u1="V" u2="O" k="56" />
    <hkern u1="V" u2="J" k="156" />
    <hkern u1="V" u2="G" k="56" />
    <hkern u1="V" u2="C" k="56" />
    <hkern u1="V" u2="A" k="169" />
    <hkern u1="V" u2="&#x40;" k="56" />
    <hkern u1="V" u2="&#x3f;" k="-39" />
    <hkern u1="V" u2="&#x3b;" k="99" />
    <hkern u1="V" u2="&#x3a;" k="99" />
    <hkern u1="V" u2="&#x2f;" k="169" />
    <hkern u1="V" u2="&#x2e;" k="196" />
    <hkern u1="V" u2="&#x2d;" k="116" />
    <hkern u1="V" u2="&#x2c;" k="196" />
    <hkern u1="V" u2="&#x2a;" k="-44" />
    <hkern u1="V" u2="&#x27;" k="-44" />
    <hkern u1="V" u2="&#x26;" k="169" />
    <hkern u1="V" u2="&#x22;" k="-44" />
    <hkern u1="W" u2="&#x2206;" k="118" />
    <hkern u1="W" u2="&#x2122;" k="-44" />
    <hkern u1="W" u2="&#x203a;" k="36" />
    <hkern u1="W" u2="&#x2039;" k="36" />
    <hkern u1="W" u2="&#x2022;" k="36" />
    <hkern u1="W" u2="&#x201e;" k="131" />
    <hkern u1="W" u2="&#x201d;" k="-44" />
    <hkern u1="W" u2="&#x201c;" k="-44" />
    <hkern u1="W" u2="&#x201a;" k="131" />
    <hkern u1="W" u2="&#x2019;" k="-44" />
    <hkern u1="W" u2="&#x2018;" k="-44" />
    <hkern u1="W" u2="&#x2014;" k="36" />
    <hkern u1="W" u2="&#x2013;" k="36" />
    <hkern u1="W" u2="&#x161;" k="53" />
    <hkern u1="W" u2="&#x15b;" k="53" />
    <hkern u1="W" u2="&#x153;" k="41" />
    <hkern u1="W" u2="&#x119;" k="41" />
    <hkern u1="W" u2="&#x107;" k="41" />
    <hkern u1="W" u2="&#x105;" k="99" />
    <hkern u1="W" u2="&#x104;" k="118" />
    <hkern u1="W" u2="&#xf8;" k="41" />
    <hkern u1="W" u2="&#xf6;" k="41" />
    <hkern u1="W" u2="&#xf5;" k="41" />
    <hkern u1="W" u2="&#xf4;" k="41" />
    <hkern u1="W" u2="&#xf3;" k="41" />
    <hkern u1="W" u2="&#xf2;" k="41" />
    <hkern u1="W" u2="&#xf0;" k="41" />
    <hkern u1="W" u2="&#xeb;" k="41" />
    <hkern u1="W" u2="&#xea;" k="41" />
    <hkern u1="W" u2="&#xe9;" k="41" />
    <hkern u1="W" u2="&#xe8;" k="41" />
    <hkern u1="W" u2="&#xe7;" k="41" />
    <hkern u1="W" u2="&#xe6;" k="99" />
    <hkern u1="W" u2="&#xe5;" k="99" />
    <hkern u1="W" u2="&#xe4;" k="99" />
    <hkern u1="W" u2="&#xe3;" k="99" />
    <hkern u1="W" u2="&#xe2;" k="99" />
    <hkern u1="W" u2="&#xe1;" k="99" />
    <hkern u1="W" u2="&#xe0;" k="99" />
    <hkern u1="W" u2="&#xc6;" k="118" />
    <hkern u1="W" u2="&#xc5;" k="118" />
    <hkern u1="W" u2="&#xc4;" k="118" />
    <hkern u1="W" u2="&#xc3;" k="118" />
    <hkern u1="W" u2="&#xc2;" k="118" />
    <hkern u1="W" u2="&#xc1;" k="118" />
    <hkern u1="W" u2="&#xc0;" k="118" />
    <hkern u1="W" u2="&#xbb;" k="36" />
    <hkern u1="W" u2="&#xba;" k="-44" />
    <hkern u1="W" u2="&#xb9;" k="-44" />
    <hkern u1="W" u2="&#xb7;" k="36" />
    <hkern u1="W" u2="&#xb3;" k="-44" />
    <hkern u1="W" u2="&#xb2;" k="-44" />
    <hkern u1="W" u2="&#xb0;" k="-44" />
    <hkern u1="W" u2="&#xad;" k="36" />
    <hkern u1="W" u2="&#xab;" k="36" />
    <hkern u1="W" u2="&#xaa;" k="-44" />
    <hkern u1="W" u2="s" k="53" />
    <hkern u1="W" u2="q" k="41" />
    <hkern u1="W" u2="o" k="41" />
    <hkern u1="W" u2="g" k="96" />
    <hkern u1="W" u2="e" k="41" />
    <hkern u1="W" u2="d" k="41" />
    <hkern u1="W" u2="c" k="41" />
    <hkern u1="W" u2="a" k="99" />
    <hkern u1="W" u2="J" k="111" />
    <hkern u1="W" u2="A" k="118" />
    <hkern u1="W" u2="&#x3f;" k="-32" />
    <hkern u1="W" u2="&#x2f;" k="118" />
    <hkern u1="W" u2="&#x2e;" k="131" />
    <hkern u1="W" u2="&#x2d;" k="36" />
    <hkern u1="W" u2="&#x2c;" k="131" />
    <hkern u1="W" u2="&#x2a;" k="-44" />
    <hkern u1="W" u2="&#x27;" k="-44" />
    <hkern u1="W" u2="&#x26;" k="118" />
    <hkern u1="W" u2="&#x22;" k="-44" />
    <hkern u1="X" u2="&#x203a;" k="66" />
    <hkern u1="X" u2="&#x2039;" k="66" />
    <hkern u1="X" u2="&#x2022;" k="66" />
    <hkern u1="X" u2="&#x2014;" k="66" />
    <hkern u1="X" u2="&#x2013;" k="66" />
    <hkern u1="X" u2="&#x153;" k="43" />
    <hkern u1="X" u2="&#x152;" k="30" />
    <hkern u1="X" u2="&#x119;" k="43" />
    <hkern u1="X" u2="&#x107;" k="43" />
    <hkern u1="X" u2="&#x106;" k="30" />
    <hkern u1="X" u2="&#xff;" k="73" />
    <hkern u1="X" u2="&#xfd;" k="73" />
    <hkern u1="X" u2="&#xf8;" k="43" />
    <hkern u1="X" u2="&#xf6;" k="43" />
    <hkern u1="X" u2="&#xf5;" k="43" />
    <hkern u1="X" u2="&#xf4;" k="43" />
    <hkern u1="X" u2="&#xf3;" k="43" />
    <hkern u1="X" u2="&#xf2;" k="43" />
    <hkern u1="X" u2="&#xf0;" k="43" />
    <hkern u1="X" u2="&#xeb;" k="43" />
    <hkern u1="X" u2="&#xea;" k="43" />
    <hkern u1="X" u2="&#xe9;" k="43" />
    <hkern u1="X" u2="&#xe8;" k="43" />
    <hkern u1="X" u2="&#xe7;" k="43" />
    <hkern u1="X" u2="&#xd8;" k="30" />
    <hkern u1="X" u2="&#xd6;" k="30" />
    <hkern u1="X" u2="&#xd5;" k="30" />
    <hkern u1="X" u2="&#xd4;" k="30" />
    <hkern u1="X" u2="&#xd3;" k="30" />
    <hkern u1="X" u2="&#xd2;" k="30" />
    <hkern u1="X" u2="&#xc7;" k="30" />
    <hkern u1="X" u2="&#xbb;" k="66" />
    <hkern u1="X" u2="&#xb7;" k="66" />
    <hkern u1="X" u2="&#xae;" k="30" />
    <hkern u1="X" u2="&#xad;" k="66" />
    <hkern u1="X" u2="&#xab;" k="66" />
    <hkern u1="X" u2="&#xa9;" k="30" />
    <hkern u1="X" u2="y" k="73" />
    <hkern u1="X" u2="w" k="52" />
    <hkern u1="X" u2="v" k="73" />
    <hkern u1="X" u2="t" k="91" />
    <hkern u1="X" u2="q" k="43" />
    <hkern u1="X" u2="o" k="43" />
    <hkern u1="X" u2="f" k="56" />
    <hkern u1="X" u2="e" k="43" />
    <hkern u1="X" u2="d" k="43" />
    <hkern u1="X" u2="c" k="43" />
    <hkern u1="X" u2="Q" k="30" />
    <hkern u1="X" u2="O" k="30" />
    <hkern u1="X" u2="G" k="30" />
    <hkern u1="X" u2="C" k="30" />
    <hkern u1="X" u2="&#x40;" k="30" />
    <hkern u1="X" u2="&#x2d;" k="66" />
    <hkern u1="Y" u2="&#x2206;" k="182" />
    <hkern u1="Y" u2="&#x2122;" k="-36" />
    <hkern u1="Y" u2="&#x203a;" k="160" />
    <hkern u1="Y" u2="&#x2039;" k="160" />
    <hkern u1="Y" u2="&#x2022;" k="160" />
    <hkern u1="Y" u2="&#x201e;" k="167" />
    <hkern u1="Y" u2="&#x201d;" k="-36" />
    <hkern u1="Y" u2="&#x201c;" k="-36" />
    <hkern u1="Y" u2="&#x201a;" k="167" />
    <hkern u1="Y" u2="&#x2019;" k="-36" />
    <hkern u1="Y" u2="&#x2018;" k="-36" />
    <hkern u1="Y" u2="&#x2014;" k="160" />
    <hkern u1="Y" u2="&#x2013;" k="160" />
    <hkern u1="Y" u2="&#x161;" k="139" />
    <hkern u1="Y" u2="&#x15b;" k="139" />
    <hkern u1="Y" u2="&#x153;" k="160" />
    <hkern u1="Y" u2="&#x152;" k="80" />
    <hkern u1="Y" u2="&#x144;" k="131" />
    <hkern u1="Y" u2="&#x131;" k="131" />
    <hkern u1="Y" u2="&#x119;" k="160" />
    <hkern u1="Y" u2="&#x107;" k="160" />
    <hkern u1="Y" u2="&#x106;" k="80" />
    <hkern u1="Y" u2="&#x105;" k="145" />
    <hkern u1="Y" u2="&#x104;" k="182" />
    <hkern u1="Y" u2="&#xff;" k="100" />
    <hkern u1="Y" u2="&#xfd;" k="100" />
    <hkern u1="Y" u2="&#xfc;" k="131" />
    <hkern u1="Y" u2="&#xfb;" k="131" />
    <hkern u1="Y" u2="&#xfa;" k="131" />
    <hkern u1="Y" u2="&#xf9;" k="131" />
    <hkern u1="Y" u2="&#xf8;" k="160" />
    <hkern u1="Y" u2="&#xf6;" k="160" />
    <hkern u1="Y" u2="&#xf5;" k="160" />
    <hkern u1="Y" u2="&#xf4;" k="160" />
    <hkern u1="Y" u2="&#xf3;" k="160" />
    <hkern u1="Y" u2="&#xf2;" k="160" />
    <hkern u1="Y" u2="&#xf1;" k="131" />
    <hkern u1="Y" u2="&#xf0;" k="160" />
    <hkern u1="Y" u2="&#xeb;" k="160" />
    <hkern u1="Y" u2="&#xea;" k="160" />
    <hkern u1="Y" u2="&#xe9;" k="160" />
    <hkern u1="Y" u2="&#xe8;" k="160" />
    <hkern u1="Y" u2="&#xe7;" k="160" />
    <hkern u1="Y" u2="&#xe6;" k="145" />
    <hkern u1="Y" u2="&#xe5;" k="145" />
    <hkern u1="Y" u2="&#xe4;" k="145" />
    <hkern u1="Y" u2="&#xe3;" k="145" />
    <hkern u1="Y" u2="&#xe2;" k="145" />
    <hkern u1="Y" u2="&#xe1;" k="145" />
    <hkern u1="Y" u2="&#xe0;" k="145" />
    <hkern u1="Y" u2="&#xd8;" k="80" />
    <hkern u1="Y" u2="&#xd6;" k="80" />
    <hkern u1="Y" u2="&#xd5;" k="80" />
    <hkern u1="Y" u2="&#xd4;" k="80" />
    <hkern u1="Y" u2="&#xd3;" k="80" />
    <hkern u1="Y" u2="&#xd2;" k="80" />
    <hkern u1="Y" u2="&#xc7;" k="80" />
    <hkern u1="Y" u2="&#xc6;" k="182" />
    <hkern u1="Y" u2="&#xc5;" k="182" />
    <hkern u1="Y" u2="&#xc4;" k="182" />
    <hkern u1="Y" u2="&#xc3;" k="182" />
    <hkern u1="Y" u2="&#xc2;" k="182" />
    <hkern u1="Y" u2="&#xc1;" k="182" />
    <hkern u1="Y" u2="&#xc0;" k="182" />
    <hkern u1="Y" u2="&#xbb;" k="160" />
    <hkern u1="Y" u2="&#xba;" k="-36" />
    <hkern u1="Y" u2="&#xb9;" k="-56" />
    <hkern u1="Y" u2="&#xb7;" k="160" />
    <hkern u1="Y" u2="&#xb5;" k="131" />
    <hkern u1="Y" u2="&#xb3;" k="-56" />
    <hkern u1="Y" u2="&#xb2;" k="-56" />
    <hkern u1="Y" u2="&#xb0;" k="-36" />
    <hkern u1="Y" u2="&#xae;" k="80" />
    <hkern u1="Y" u2="&#xad;" k="160" />
    <hkern u1="Y" u2="&#xab;" k="160" />
    <hkern u1="Y" u2="&#xaa;" k="-36" />
    <hkern u1="Y" u2="&#xa9;" k="80" />
    <hkern u1="Y" u2="y" k="100" />
    <hkern u1="Y" u2="x" k="136" />
    <hkern u1="Y" u2="w" k="96" />
    <hkern u1="Y" u2="v" k="100" />
    <hkern u1="Y" u2="u" k="131" />
    <hkern u1="Y" u2="s" k="139" />
    <hkern u1="Y" u2="r" k="131" />
    <hkern u1="Y" u2="q" k="160" />
    <hkern u1="Y" u2="p" k="131" />
    <hkern u1="Y" u2="o" k="160" />
    <hkern u1="Y" u2="n" k="131" />
    <hkern u1="Y" u2="m" k="131" />
    <hkern u1="Y" u2="g" k="176" />
    <hkern u1="Y" u2="e" k="160" />
    <hkern u1="Y" u2="d" k="160" />
    <hkern u1="Y" u2="c" k="160" />
    <hkern u1="Y" u2="a" k="145" />
    <hkern u1="Y" u2="Q" k="80" />
    <hkern u1="Y" u2="O" k="80" />
    <hkern u1="Y" u2="J" k="200" />
    <hkern u1="Y" u2="G" k="80" />
    <hkern u1="Y" u2="C" k="80" />
    <hkern u1="Y" u2="A" k="182" />
    <hkern u1="Y" u2="&#x40;" k="80" />
    <hkern u1="Y" u2="&#x3f;" k="-32" />
    <hkern u1="Y" u2="&#x3b;" k="131" />
    <hkern u1="Y" u2="&#x3a;" k="131" />
    <hkern u1="Y" u2="&#x2f;" k="182" />
    <hkern u1="Y" u2="&#x2e;" k="167" />
    <hkern u1="Y" u2="&#x2d;" k="160" />
    <hkern u1="Y" u2="&#x2c;" k="167" />
    <hkern u1="Y" u2="&#x2a;" k="-36" />
    <hkern u1="Y" u2="&#x27;" k="-36" />
    <hkern u1="Y" u2="&#x26;" k="182" />
    <hkern u1="Y" u2="&#x22;" k="-36" />
    <hkern u1="Z" u2="&#x203a;" k="64" />
    <hkern u1="Z" u2="&#x2039;" k="64" />
    <hkern u1="Z" u2="&#x2022;" k="64" />
    <hkern u1="Z" u2="&#x2014;" k="64" />
    <hkern u1="Z" u2="&#x2013;" k="64" />
    <hkern u1="Z" u2="&#x161;" k="19" />
    <hkern u1="Z" u2="&#x15b;" k="19" />
    <hkern u1="Z" u2="&#x153;" k="29" />
    <hkern u1="Z" u2="&#x152;" k="49" />
    <hkern u1="Z" u2="&#x119;" k="29" />
    <hkern u1="Z" u2="&#x107;" k="29" />
    <hkern u1="Z" u2="&#x106;" k="49" />
    <hkern u1="Z" u2="&#xff;" k="34" />
    <hkern u1="Z" u2="&#xfd;" k="34" />
    <hkern u1="Z" u2="&#xf8;" k="29" />
    <hkern u1="Z" u2="&#xf6;" k="29" />
    <hkern u1="Z" u2="&#xf5;" k="29" />
    <hkern u1="Z" u2="&#xf4;" k="29" />
    <hkern u1="Z" u2="&#xf3;" k="29" />
    <hkern u1="Z" u2="&#xf2;" k="29" />
    <hkern u1="Z" u2="&#xf0;" k="29" />
    <hkern u1="Z" u2="&#xeb;" k="29" />
    <hkern u1="Z" u2="&#xea;" k="29" />
    <hkern u1="Z" u2="&#xe9;" k="29" />
    <hkern u1="Z" u2="&#xe8;" k="29" />
    <hkern u1="Z" u2="&#xe7;" k="29" />
    <hkern u1="Z" u2="&#xd8;" k="49" />
    <hkern u1="Z" u2="&#xd6;" k="49" />
    <hkern u1="Z" u2="&#xd5;" k="49" />
    <hkern u1="Z" u2="&#xd4;" k="49" />
    <hkern u1="Z" u2="&#xd3;" k="49" />
    <hkern u1="Z" u2="&#xd2;" k="49" />
    <hkern u1="Z" u2="&#xc7;" k="49" />
    <hkern u1="Z" u2="&#xbb;" k="64" />
    <hkern u1="Z" u2="&#xb7;" k="64" />
    <hkern u1="Z" u2="&#xae;" k="49" />
    <hkern u1="Z" u2="&#xad;" k="64" />
    <hkern u1="Z" u2="&#xab;" k="64" />
    <hkern u1="Z" u2="&#xa9;" k="49" />
    <hkern u1="Z" u2="y" k="34" />
    <hkern u1="Z" u2="v" k="34" />
    <hkern u1="Z" u2="s" k="19" />
    <hkern u1="Z" u2="q" k="29" />
    <hkern u1="Z" u2="o" k="29" />
    <hkern u1="Z" u2="e" k="29" />
    <hkern u1="Z" u2="d" k="29" />
    <hkern u1="Z" u2="c" k="29" />
    <hkern u1="Z" u2="Q" k="49" />
    <hkern u1="Z" u2="O" k="49" />
    <hkern u1="Z" u2="G" k="49" />
    <hkern u1="Z" u2="C" k="49" />
    <hkern u1="Z" u2="&#x40;" k="49" />
    <hkern u1="Z" u2="&#x3f;" k="-32" />
    <hkern u1="Z" u2="&#x2d;" k="64" />
    <hkern u1="[" u2="&#x153;" k="36" />
    <hkern u1="[" u2="&#x152;" k="40" />
    <hkern u1="[" u2="&#x119;" k="36" />
    <hkern u1="[" u2="&#x107;" k="36" />
    <hkern u1="[" u2="&#x106;" k="40" />
    <hkern u1="[" u2="&#xf8;" k="36" />
    <hkern u1="[" u2="&#xf6;" k="36" />
    <hkern u1="[" u2="&#xf5;" k="36" />
    <hkern u1="[" u2="&#xf4;" k="36" />
    <hkern u1="[" u2="&#xf3;" k="36" />
    <hkern u1="[" u2="&#xf2;" k="36" />
    <hkern u1="[" u2="&#xf0;" k="36" />
    <hkern u1="[" u2="&#xeb;" k="36" />
    <hkern u1="[" u2="&#xea;" k="36" />
    <hkern u1="[" u2="&#xe9;" k="36" />
    <hkern u1="[" u2="&#xe8;" k="36" />
    <hkern u1="[" u2="&#xe7;" k="36" />
    <hkern u1="[" u2="&#xd8;" k="40" />
    <hkern u1="[" u2="&#xd6;" k="40" />
    <hkern u1="[" u2="&#xd5;" k="40" />
    <hkern u1="[" u2="&#xd4;" k="40" />
    <hkern u1="[" u2="&#xd3;" k="40" />
    <hkern u1="[" u2="&#xd2;" k="40" />
    <hkern u1="[" u2="&#xc7;" k="40" />
    <hkern u1="[" u2="&#xae;" k="40" />
    <hkern u1="[" u2="&#xa9;" k="40" />
    <hkern u1="[" u2="q" k="36" />
    <hkern u1="[" u2="o" k="36" />
    <hkern u1="[" u2="e" k="36" />
    <hkern u1="[" u2="d" k="36" />
    <hkern u1="[" u2="c" k="36" />
    <hkern u1="[" u2="Q" k="40" />
    <hkern u1="[" u2="O" k="40" />
    <hkern u1="[" u2="G" k="40" />
    <hkern u1="[" u2="C" k="40" />
    <hkern u1="[" u2="&#x40;" k="40" />
    <hkern u1="\" u2="&#x2122;" k="191" />
    <hkern u1="\" u2="&#x203a;" k="67" />
    <hkern u1="\" u2="&#x2039;" k="67" />
    <hkern u1="\" u2="&#x2022;" k="67" />
    <hkern u1="\" u2="&#x201d;" k="191" />
    <hkern u1="\" u2="&#x201c;" k="191" />
    <hkern u1="\" u2="&#x2019;" k="191" />
    <hkern u1="\" u2="&#x2018;" k="191" />
    <hkern u1="\" u2="&#x2014;" k="67" />
    <hkern u1="\" u2="&#x2013;" k="67" />
    <hkern u1="\" u2="&#x178;" k="182" />
    <hkern u1="\" u2="&#x152;" k="51" />
    <hkern u1="\" u2="&#x106;" k="51" />
    <hkern u1="\" u2="&#xff;" k="91" />
    <hkern u1="\" u2="&#xfd;" k="91" />
    <hkern u1="\" u2="&#xdd;" k="182" />
    <hkern u1="\" u2="&#xdc;" k="52" />
    <hkern u1="\" u2="&#xdb;" k="52" />
    <hkern u1="\" u2="&#xda;" k="52" />
    <hkern u1="\" u2="&#xd9;" k="52" />
    <hkern u1="\" u2="&#xd8;" k="51" />
    <hkern u1="\" u2="&#xd6;" k="51" />
    <hkern u1="\" u2="&#xd5;" k="51" />
    <hkern u1="\" u2="&#xd4;" k="51" />
    <hkern u1="\" u2="&#xd3;" k="51" />
    <hkern u1="\" u2="&#xd2;" k="51" />
    <hkern u1="\" u2="&#xc7;" k="51" />
    <hkern u1="\" u2="&#xbb;" k="67" />
    <hkern u1="\" u2="&#xba;" k="191" />
    <hkern u1="\" u2="&#xb9;" k="202" />
    <hkern u1="\" u2="&#xb7;" k="67" />
    <hkern u1="\" u2="&#xb3;" k="202" />
    <hkern u1="\" u2="&#xb2;" k="202" />
    <hkern u1="\" u2="&#xb0;" k="191" />
    <hkern u1="\" u2="&#xae;" k="51" />
    <hkern u1="\" u2="&#xad;" k="67" />
    <hkern u1="\" u2="&#xab;" k="67" />
    <hkern u1="\" u2="&#xaa;" k="191" />
    <hkern u1="\" u2="&#xa9;" k="51" />
    <hkern u1="\" u2="y" k="91" />
    <hkern u1="\" u2="v" k="91" />
    <hkern u1="\" u2="\" k="169" />
    <hkern u1="\" u2="Y" k="182" />
    <hkern u1="\" u2="W" k="102" />
    <hkern u1="\" u2="V" k="169" />
    <hkern u1="\" u2="U" k="52" />
    <hkern u1="\" u2="T" k="147" />
    <hkern u1="\" u2="Q" k="51" />
    <hkern u1="\" u2="O" k="51" />
    <hkern u1="\" u2="J" k="-56" />
    <hkern u1="\" u2="G" k="51" />
    <hkern u1="\" u2="C" k="51" />
    <hkern u1="\" u2="&#x40;" k="51" />
    <hkern u1="\" u2="&#x3f;" k="63" />
    <hkern u1="\" u2="&#x2d;" k="67" />
    <hkern u1="\" u2="&#x2a;" k="191" />
    <hkern u1="\" u2="&#x27;" k="191" />
    <hkern u1="\" u2="&#x22;" k="191" />
    <hkern u1="a" u2="&#x2122;" k="76" />
    <hkern u1="a" u2="&#x201d;" k="76" />
    <hkern u1="a" u2="&#x201c;" k="76" />
    <hkern u1="a" u2="&#x2019;" k="76" />
    <hkern u1="a" u2="&#x2018;" k="76" />
    <hkern u1="a" u2="&#xff;" k="36" />
    <hkern u1="a" u2="&#xfd;" k="36" />
    <hkern u1="a" u2="&#xba;" k="76" />
    <hkern u1="a" u2="&#xb9;" k="76" />
    <hkern u1="a" u2="&#xb3;" k="76" />
    <hkern u1="a" u2="&#xb2;" k="76" />
    <hkern u1="a" u2="&#xb0;" k="76" />
    <hkern u1="a" u2="&#xaa;" k="76" />
    <hkern u1="a" u2="y" k="36" />
    <hkern u1="a" u2="w" k="18" />
    <hkern u1="a" u2="v" k="36" />
    <hkern u1="a" u2="&#x2a;" k="76" />
    <hkern u1="a" u2="&#x27;" k="76" />
    <hkern u1="a" u2="&#x22;" k="76" />
    <hkern u1="b" u2="&#x2122;" k="96" />
    <hkern u1="b" u2="&#x201d;" k="96" />
    <hkern u1="b" u2="&#x201c;" k="96" />
    <hkern u1="b" u2="&#x2019;" k="96" />
    <hkern u1="b" u2="&#x2018;" k="96" />
    <hkern u1="b" u2="&#xff;" k="33" />
    <hkern u1="b" u2="&#xfd;" k="33" />
    <hkern u1="b" u2="&#xba;" k="96" />
    <hkern u1="b" u2="&#xb0;" k="96" />
    <hkern u1="b" u2="&#xaa;" k="96" />
    <hkern u1="b" u2="&#x7d;" k="36" />
    <hkern u1="b" u2="y" k="33" />
    <hkern u1="b" u2="x" k="60" />
    <hkern u1="b" u2="v" k="33" />
    <hkern u1="b" u2="]" k="36" />
    <hkern u1="b" u2="\" k="123" />
    <hkern u1="b" u2="W" k="41" />
    <hkern u1="b" u2="V" k="123" />
    <hkern u1="b" u2="&#x2a;" k="96" />
    <hkern u1="b" u2="&#x29;" k="36" />
    <hkern u1="b" u2="&#x27;" k="96" />
    <hkern u1="b" u2="&#x22;" k="96" />
    <hkern u1="e" u2="&#x2122;" k="96" />
    <hkern u1="e" u2="&#x201d;" k="96" />
    <hkern u1="e" u2="&#x201c;" k="96" />
    <hkern u1="e" u2="&#x2019;" k="96" />
    <hkern u1="e" u2="&#x2018;" k="96" />
    <hkern u1="e" u2="&#xff;" k="33" />
    <hkern u1="e" u2="&#xfd;" k="33" />
    <hkern u1="e" u2="&#xba;" k="96" />
    <hkern u1="e" u2="&#xb0;" k="96" />
    <hkern u1="e" u2="&#xaa;" k="96" />
    <hkern u1="e" u2="&#x7d;" k="36" />
    <hkern u1="e" u2="y" k="33" />
    <hkern u1="e" u2="x" k="60" />
    <hkern u1="e" u2="v" k="33" />
    <hkern u1="e" u2="]" k="36" />
    <hkern u1="e" u2="\" k="123" />
    <hkern u1="e" u2="W" k="41" />
    <hkern u1="e" u2="V" k="123" />
    <hkern u1="e" u2="&#x2a;" k="96" />
    <hkern u1="e" u2="&#x29;" k="36" />
    <hkern u1="e" u2="&#x27;" k="96" />
    <hkern u1="e" u2="&#x22;" k="96" />
    <hkern u1="f" u2="&#x2122;" k="-64" />
    <hkern u1="f" u2="&#x201e;" k="124" />
    <hkern u1="f" u2="&#x201d;" k="-64" />
    <hkern u1="f" u2="&#x201c;" k="-64" />
    <hkern u1="f" u2="&#x201a;" k="124" />
    <hkern u1="f" u2="&#x2019;" k="-64" />
    <hkern u1="f" u2="&#x2018;" k="-64" />
    <hkern u1="f" u2="&#xba;" k="-64" />
    <hkern u1="f" u2="&#xb9;" k="-100" />
    <hkern u1="f" u2="&#xb3;" k="-100" />
    <hkern u1="f" u2="&#xb2;" k="-100" />
    <hkern u1="f" u2="&#xb0;" k="-64" />
    <hkern u1="f" u2="&#xaa;" k="-64" />
    <hkern u1="f" u2="&#x2e;" k="124" />
    <hkern u1="f" u2="&#x2c;" k="124" />
    <hkern u1="f" u2="&#x2a;" k="-64" />
    <hkern u1="f" u2="&#x27;" k="-64" />
    <hkern u1="f" u2="&#x22;" k="-64" />
    <hkern u1="h" u2="&#x2122;" k="76" />
    <hkern u1="h" u2="&#x201d;" k="76" />
    <hkern u1="h" u2="&#x201c;" k="76" />
    <hkern u1="h" u2="&#x2019;" k="76" />
    <hkern u1="h" u2="&#x2018;" k="76" />
    <hkern u1="h" u2="&#xff;" k="36" />
    <hkern u1="h" u2="&#xfd;" k="36" />
    <hkern u1="h" u2="&#xba;" k="76" />
    <hkern u1="h" u2="&#xb9;" k="76" />
    <hkern u1="h" u2="&#xb3;" k="76" />
    <hkern u1="h" u2="&#xb2;" k="76" />
    <hkern u1="h" u2="&#xb0;" k="76" />
    <hkern u1="h" u2="&#xaa;" k="76" />
    <hkern u1="h" u2="y" k="36" />
    <hkern u1="h" u2="w" k="18" />
    <hkern u1="h" u2="v" k="36" />
    <hkern u1="h" u2="&#x2a;" k="76" />
    <hkern u1="h" u2="&#x27;" k="76" />
    <hkern u1="h" u2="&#x22;" k="76" />
    <hkern u1="k" u2="&#x153;" k="60" />
    <hkern u1="k" u2="&#x119;" k="60" />
    <hkern u1="k" u2="&#x107;" k="60" />
    <hkern u1="k" u2="&#xf8;" k="60" />
    <hkern u1="k" u2="&#xf6;" k="60" />
    <hkern u1="k" u2="&#xf5;" k="60" />
    <hkern u1="k" u2="&#xf4;" k="60" />
    <hkern u1="k" u2="&#xf3;" k="60" />
    <hkern u1="k" u2="&#xf2;" k="60" />
    <hkern u1="k" u2="&#xf0;" k="60" />
    <hkern u1="k" u2="&#xeb;" k="60" />
    <hkern u1="k" u2="&#xea;" k="60" />
    <hkern u1="k" u2="&#xe9;" k="60" />
    <hkern u1="k" u2="&#xe8;" k="60" />
    <hkern u1="k" u2="&#xe7;" k="60" />
    <hkern u1="k" u2="q" k="60" />
    <hkern u1="k" u2="o" k="60" />
    <hkern u1="k" u2="e" k="60" />
    <hkern u1="k" u2="d" k="60" />
    <hkern u1="k" u2="c" k="60" />
    <hkern u1="m" u2="&#x2122;" k="76" />
    <hkern u1="m" u2="&#x201d;" k="76" />
    <hkern u1="m" u2="&#x201c;" k="76" />
    <hkern u1="m" u2="&#x2019;" k="76" />
    <hkern u1="m" u2="&#x2018;" k="76" />
    <hkern u1="m" u2="&#xff;" k="36" />
    <hkern u1="m" u2="&#xfd;" k="36" />
    <hkern u1="m" u2="&#xba;" k="76" />
    <hkern u1="m" u2="&#xb9;" k="76" />
    <hkern u1="m" u2="&#xb3;" k="76" />
    <hkern u1="m" u2="&#xb2;" k="76" />
    <hkern u1="m" u2="&#xb0;" k="76" />
    <hkern u1="m" u2="&#xaa;" k="76" />
    <hkern u1="m" u2="y" k="36" />
    <hkern u1="m" u2="w" k="18" />
    <hkern u1="m" u2="v" k="36" />
    <hkern u1="m" u2="&#x2a;" k="76" />
    <hkern u1="m" u2="&#x27;" k="76" />
    <hkern u1="m" u2="&#x22;" k="76" />
    <hkern u1="n" u2="&#x2122;" k="76" />
    <hkern u1="n" u2="&#x201d;" k="76" />
    <hkern u1="n" u2="&#x201c;" k="76" />
    <hkern u1="n" u2="&#x2019;" k="76" />
    <hkern u1="n" u2="&#x2018;" k="76" />
    <hkern u1="n" u2="&#xff;" k="36" />
    <hkern u1="n" u2="&#xfd;" k="36" />
    <hkern u1="n" u2="&#xba;" k="76" />
    <hkern u1="n" u2="&#xb9;" k="76" />
    <hkern u1="n" u2="&#xb3;" k="76" />
    <hkern u1="n" u2="&#xb2;" k="76" />
    <hkern u1="n" u2="&#xb0;" k="76" />
    <hkern u1="n" u2="&#xaa;" k="76" />
    <hkern u1="n" u2="y" k="36" />
    <hkern u1="n" u2="w" k="18" />
    <hkern u1="n" u2="v" k="36" />
    <hkern u1="n" u2="&#x2a;" k="76" />
    <hkern u1="n" u2="&#x27;" k="76" />
    <hkern u1="n" u2="&#x22;" k="76" />
    <hkern u1="o" u2="&#x2122;" k="96" />
    <hkern u1="o" u2="&#x201d;" k="96" />
    <hkern u1="o" u2="&#x201c;" k="96" />
    <hkern u1="o" u2="&#x2019;" k="96" />
    <hkern u1="o" u2="&#x2018;" k="96" />
    <hkern u1="o" u2="&#xff;" k="33" />
    <hkern u1="o" u2="&#xfd;" k="33" />
    <hkern u1="o" u2="&#xba;" k="96" />
    <hkern u1="o" u2="&#xb0;" k="96" />
    <hkern u1="o" u2="&#xaa;" k="96" />
    <hkern u1="o" u2="&#x7d;" k="36" />
    <hkern u1="o" u2="y" k="33" />
    <hkern u1="o" u2="x" k="60" />
    <hkern u1="o" u2="v" k="33" />
    <hkern u1="o" u2="]" k="36" />
    <hkern u1="o" u2="\" k="123" />
    <hkern u1="o" u2="W" k="41" />
    <hkern u1="o" u2="V" k="123" />
    <hkern u1="o" u2="&#x2a;" k="96" />
    <hkern u1="o" u2="&#x29;" k="36" />
    <hkern u1="o" u2="&#x27;" k="96" />
    <hkern u1="o" u2="&#x22;" k="96" />
    <hkern u1="p" u2="&#x2122;" k="96" />
    <hkern u1="p" u2="&#x201d;" k="96" />
    <hkern u1="p" u2="&#x201c;" k="96" />
    <hkern u1="p" u2="&#x2019;" k="96" />
    <hkern u1="p" u2="&#x2018;" k="96" />
    <hkern u1="p" u2="&#xff;" k="33" />
    <hkern u1="p" u2="&#xfd;" k="33" />
    <hkern u1="p" u2="&#xba;" k="96" />
    <hkern u1="p" u2="&#xb0;" k="96" />
    <hkern u1="p" u2="&#xaa;" k="96" />
    <hkern u1="p" u2="&#x7d;" k="36" />
    <hkern u1="p" u2="y" k="33" />
    <hkern u1="p" u2="x" k="60" />
    <hkern u1="p" u2="v" k="33" />
    <hkern u1="p" u2="]" k="36" />
    <hkern u1="p" u2="\" k="123" />
    <hkern u1="p" u2="W" k="41" />
    <hkern u1="p" u2="V" k="123" />
    <hkern u1="p" u2="&#x2a;" k="96" />
    <hkern u1="p" u2="&#x29;" k="36" />
    <hkern u1="p" u2="&#x27;" k="96" />
    <hkern u1="p" u2="&#x22;" k="96" />
    <hkern u1="r" u2="&#x201e;" k="136" />
    <hkern u1="r" u2="&#x201a;" k="136" />
    <hkern u1="r" u2="&#x105;" k="29" />
    <hkern u1="r" u2="&#xe6;" k="29" />
    <hkern u1="r" u2="&#xe5;" k="29" />
    <hkern u1="r" u2="&#xe4;" k="29" />
    <hkern u1="r" u2="&#xe3;" k="29" />
    <hkern u1="r" u2="&#xe2;" k="29" />
    <hkern u1="r" u2="&#xe1;" k="29" />
    <hkern u1="r" u2="&#xe0;" k="29" />
    <hkern u1="r" u2="a" k="29" />
    <hkern u1="r" u2="&#x2e;" k="136" />
    <hkern u1="r" u2="&#x2c;" k="136" />
    <hkern u1="v" u2="&#x2206;" k="91" />
    <hkern u1="v" u2="&#x201e;" k="136" />
    <hkern u1="v" u2="&#x201a;" k="136" />
    <hkern u1="v" u2="&#x153;" k="33" />
    <hkern u1="v" u2="&#x119;" k="33" />
    <hkern u1="v" u2="&#x107;" k="33" />
    <hkern u1="v" u2="&#x104;" k="91" />
    <hkern u1="v" u2="&#xf8;" k="33" />
    <hkern u1="v" u2="&#xf6;" k="33" />
    <hkern u1="v" u2="&#xf5;" k="33" />
    <hkern u1="v" u2="&#xf4;" k="33" />
    <hkern u1="v" u2="&#xf3;" k="33" />
    <hkern u1="v" u2="&#xf2;" k="33" />
    <hkern u1="v" u2="&#xf0;" k="33" />
    <hkern u1="v" u2="&#xeb;" k="33" />
    <hkern u1="v" u2="&#xea;" k="33" />
    <hkern u1="v" u2="&#xe9;" k="33" />
    <hkern u1="v" u2="&#xe8;" k="33" />
    <hkern u1="v" u2="&#xe7;" k="33" />
    <hkern u1="v" u2="&#xc6;" k="91" />
    <hkern u1="v" u2="&#xc5;" k="91" />
    <hkern u1="v" u2="&#xc4;" k="91" />
    <hkern u1="v" u2="&#xc3;" k="91" />
    <hkern u1="v" u2="&#xc2;" k="91" />
    <hkern u1="v" u2="&#xc1;" k="91" />
    <hkern u1="v" u2="&#xc0;" k="91" />
    <hkern u1="v" u2="q" k="33" />
    <hkern u1="v" u2="o" k="33" />
    <hkern u1="v" u2="e" k="33" />
    <hkern u1="v" u2="d" k="33" />
    <hkern u1="v" u2="c" k="33" />
    <hkern u1="v" u2="A" k="91" />
    <hkern u1="v" u2="&#x2f;" k="91" />
    <hkern u1="v" u2="&#x2e;" k="136" />
    <hkern u1="v" u2="&#x2c;" k="136" />
    <hkern u1="v" u2="&#x26;" k="91" />
    <hkern u1="w" u2="&#x201e;" k="71" />
    <hkern u1="w" u2="&#x201a;" k="71" />
    <hkern u1="w" u2="&#x2e;" k="71" />
    <hkern u1="w" u2="&#x2c;" k="71" />
    <hkern u1="x" u2="&#x153;" k="60" />
    <hkern u1="x" u2="&#x119;" k="60" />
    <hkern u1="x" u2="&#x107;" k="60" />
    <hkern u1="x" u2="&#xf8;" k="60" />
    <hkern u1="x" u2="&#xf6;" k="60" />
    <hkern u1="x" u2="&#xf5;" k="60" />
    <hkern u1="x" u2="&#xf4;" k="60" />
    <hkern u1="x" u2="&#xf3;" k="60" />
    <hkern u1="x" u2="&#xf2;" k="60" />
    <hkern u1="x" u2="&#xf0;" k="60" />
    <hkern u1="x" u2="&#xeb;" k="60" />
    <hkern u1="x" u2="&#xea;" k="60" />
    <hkern u1="x" u2="&#xe9;" k="60" />
    <hkern u1="x" u2="&#xe8;" k="60" />
    <hkern u1="x" u2="&#xe7;" k="60" />
    <hkern u1="x" u2="q" k="60" />
    <hkern u1="x" u2="o" k="60" />
    <hkern u1="x" u2="e" k="60" />
    <hkern u1="x" u2="d" k="60" />
    <hkern u1="x" u2="c" k="60" />
    <hkern u1="y" u2="&#x2206;" k="91" />
    <hkern u1="y" u2="&#x201e;" k="136" />
    <hkern u1="y" u2="&#x201a;" k="136" />
    <hkern u1="y" u2="&#x153;" k="33" />
    <hkern u1="y" u2="&#x119;" k="33" />
    <hkern u1="y" u2="&#x107;" k="33" />
    <hkern u1="y" u2="&#x104;" k="91" />
    <hkern u1="y" u2="&#xf8;" k="33" />
    <hkern u1="y" u2="&#xf6;" k="33" />
    <hkern u1="y" u2="&#xf5;" k="33" />
    <hkern u1="y" u2="&#xf4;" k="33" />
    <hkern u1="y" u2="&#xf3;" k="33" />
    <hkern u1="y" u2="&#xf2;" k="33" />
    <hkern u1="y" u2="&#xf0;" k="33" />
    <hkern u1="y" u2="&#xeb;" k="33" />
    <hkern u1="y" u2="&#xea;" k="33" />
    <hkern u1="y" u2="&#xe9;" k="33" />
    <hkern u1="y" u2="&#xe8;" k="33" />
    <hkern u1="y" u2="&#xe7;" k="33" />
    <hkern u1="y" u2="&#xc6;" k="91" />
    <hkern u1="y" u2="&#xc5;" k="91" />
    <hkern u1="y" u2="&#xc4;" k="91" />
    <hkern u1="y" u2="&#xc3;" k="91" />
    <hkern u1="y" u2="&#xc2;" k="91" />
    <hkern u1="y" u2="&#xc1;" k="91" />
    <hkern u1="y" u2="&#xc0;" k="91" />
    <hkern u1="y" u2="q" k="33" />
    <hkern u1="y" u2="o" k="33" />
    <hkern u1="y" u2="e" k="33" />
    <hkern u1="y" u2="d" k="33" />
    <hkern u1="y" u2="c" k="33" />
    <hkern u1="y" u2="A" k="91" />
    <hkern u1="y" u2="&#x2f;" k="91" />
    <hkern u1="y" u2="&#x2e;" k="136" />
    <hkern u1="y" u2="&#x2c;" k="136" />
    <hkern u1="y" u2="&#x26;" k="91" />
    <hkern u1="&#x7b;" u2="&#x153;" k="36" />
    <hkern u1="&#x7b;" u2="&#x152;" k="40" />
    <hkern u1="&#x7b;" u2="&#x119;" k="36" />
    <hkern u1="&#x7b;" u2="&#x107;" k="36" />
    <hkern u1="&#x7b;" u2="&#x106;" k="40" />
    <hkern u1="&#x7b;" u2="&#xf8;" k="36" />
    <hkern u1="&#x7b;" u2="&#xf6;" k="36" />
    <hkern u1="&#x7b;" u2="&#xf5;" k="36" />
    <hkern u1="&#x7b;" u2="&#xf4;" k="36" />
    <hkern u1="&#x7b;" u2="&#xf3;" k="36" />
    <hkern u1="&#x7b;" u2="&#xf2;" k="36" />
    <hkern u1="&#x7b;" u2="&#xf0;" k="36" />
    <hkern u1="&#x7b;" u2="&#xeb;" k="36" />
    <hkern u1="&#x7b;" u2="&#xea;" k="36" />
    <hkern u1="&#x7b;" u2="&#xe9;" k="36" />
    <hkern u1="&#x7b;" u2="&#xe8;" k="36" />
    <hkern u1="&#x7b;" u2="&#xe7;" k="36" />
    <hkern u1="&#x7b;" u2="&#xd8;" k="40" />
    <hkern u1="&#x7b;" u2="&#xd6;" k="40" />
    <hkern u1="&#x7b;" u2="&#xd5;" k="40" />
    <hkern u1="&#x7b;" u2="&#xd4;" k="40" />
    <hkern u1="&#x7b;" u2="&#xd3;" k="40" />
    <hkern u1="&#x7b;" u2="&#xd2;" k="40" />
    <hkern u1="&#x7b;" u2="&#xc7;" k="40" />
    <hkern u1="&#x7b;" u2="&#xae;" k="40" />
    <hkern u1="&#x7b;" u2="&#xa9;" k="40" />
    <hkern u1="&#x7b;" u2="q" k="36" />
    <hkern u1="&#x7b;" u2="o" k="36" />
    <hkern u1="&#x7b;" u2="e" k="36" />
    <hkern u1="&#x7b;" u2="d" k="36" />
    <hkern u1="&#x7b;" u2="c" k="36" />
    <hkern u1="&#x7b;" u2="Q" k="40" />
    <hkern u1="&#x7b;" u2="O" k="40" />
    <hkern u1="&#x7b;" u2="G" k="40" />
    <hkern u1="&#x7b;" u2="C" k="40" />
    <hkern u1="&#x7b;" u2="&#x40;" k="40" />
    <hkern u1="&#xa9;" u2="&#x2206;" k="51" />
    <hkern u1="&#xa9;" u2="&#x2122;" k="42" />
    <hkern u1="&#xa9;" u2="&#x201e;" k="52" />
    <hkern u1="&#xa9;" u2="&#x201d;" k="42" />
    <hkern u1="&#xa9;" u2="&#x201c;" k="42" />
    <hkern u1="&#xa9;" u2="&#x201a;" k="52" />
    <hkern u1="&#xa9;" u2="&#x2019;" k="42" />
    <hkern u1="&#xa9;" u2="&#x2018;" k="42" />
    <hkern u1="&#xa9;" u2="&#x17d;" k="64" />
    <hkern u1="&#xa9;" u2="&#x17b;" k="64" />
    <hkern u1="&#xa9;" u2="&#x179;" k="64" />
    <hkern u1="&#xa9;" u2="&#x178;" k="80" />
    <hkern u1="&#xa9;" u2="&#x104;" k="51" />
    <hkern u1="&#xa9;" u2="&#xdd;" k="80" />
    <hkern u1="&#xa9;" u2="&#xc6;" k="51" />
    <hkern u1="&#xa9;" u2="&#xc5;" k="51" />
    <hkern u1="&#xa9;" u2="&#xc4;" k="51" />
    <hkern u1="&#xa9;" u2="&#xc3;" k="51" />
    <hkern u1="&#xa9;" u2="&#xc2;" k="51" />
    <hkern u1="&#xa9;" u2="&#xc1;" k="51" />
    <hkern u1="&#xa9;" u2="&#xc0;" k="51" />
    <hkern u1="&#xa9;" u2="&#xba;" k="42" />
    <hkern u1="&#xa9;" u2="&#xb0;" k="42" />
    <hkern u1="&#xa9;" u2="&#xaa;" k="42" />
    <hkern u1="&#xa9;" u2="&#x7d;" k="40" />
    <hkern u1="&#xa9;" u2="]" k="40" />
    <hkern u1="&#xa9;" u2="\" k="56" />
    <hkern u1="&#xa9;" u2="Z" k="64" />
    <hkern u1="&#xa9;" u2="Y" k="80" />
    <hkern u1="&#xa9;" u2="X" k="30" />
    <hkern u1="&#xa9;" u2="V" k="56" />
    <hkern u1="&#xa9;" u2="T" k="78" />
    <hkern u1="&#xa9;" u2="A" k="51" />
    <hkern u1="&#xa9;" u2="&#x2f;" k="51" />
    <hkern u1="&#xa9;" u2="&#x2e;" k="52" />
    <hkern u1="&#xa9;" u2="&#x2c;" k="52" />
    <hkern u1="&#xa9;" u2="&#x2a;" k="42" />
    <hkern u1="&#xa9;" u2="&#x29;" k="40" />
    <hkern u1="&#xa9;" u2="&#x27;" k="42" />
    <hkern u1="&#xa9;" u2="&#x26;" k="51" />
    <hkern u1="&#xa9;" u2="&#x22;" k="42" />
    <hkern u1="&#xaa;" u2="&#x2206;" k="191" />
    <hkern u1="&#xaa;" u2="&#x203a;" k="169" />
    <hkern u1="&#xaa;" u2="&#x2039;" k="169" />
    <hkern u1="&#xaa;" u2="&#x2022;" k="169" />
    <hkern u1="&#xaa;" u2="&#x201e;" k="213" />
    <hkern u1="&#xaa;" u2="&#x201a;" k="213" />
    <hkern u1="&#xaa;" u2="&#x2014;" k="169" />
    <hkern u1="&#xaa;" u2="&#x2013;" k="169" />
    <hkern u1="&#xaa;" u2="&#x178;" k="-36" />
    <hkern u1="&#xaa;" u2="&#x153;" k="96" />
    <hkern u1="&#xaa;" u2="&#x152;" k="42" />
    <hkern u1="&#xaa;" u2="&#x119;" k="96" />
    <hkern u1="&#xaa;" u2="&#x107;" k="96" />
    <hkern u1="&#xaa;" u2="&#x106;" k="42" />
    <hkern u1="&#xaa;" u2="&#x105;" k="66" />
    <hkern u1="&#xaa;" u2="&#x104;" k="191" />
    <hkern u1="&#xaa;" u2="&#xf8;" k="96" />
    <hkern u1="&#xaa;" u2="&#xf6;" k="96" />
    <hkern u1="&#xaa;" u2="&#xf5;" k="96" />
    <hkern u1="&#xaa;" u2="&#xf4;" k="96" />
    <hkern u1="&#xaa;" u2="&#xf3;" k="96" />
    <hkern u1="&#xaa;" u2="&#xf2;" k="96" />
    <hkern u1="&#xaa;" u2="&#xf0;" k="96" />
    <hkern u1="&#xaa;" u2="&#xeb;" k="96" />
    <hkern u1="&#xaa;" u2="&#xea;" k="96" />
    <hkern u1="&#xaa;" u2="&#xe9;" k="96" />
    <hkern u1="&#xaa;" u2="&#xe8;" k="96" />
    <hkern u1="&#xaa;" u2="&#xe7;" k="96" />
    <hkern u1="&#xaa;" u2="&#xe6;" k="66" />
    <hkern u1="&#xaa;" u2="&#xe5;" k="66" />
    <hkern u1="&#xaa;" u2="&#xe4;" k="66" />
    <hkern u1="&#xaa;" u2="&#xe3;" k="66" />
    <hkern u1="&#xaa;" u2="&#xe2;" k="66" />
    <hkern u1="&#xaa;" u2="&#xe1;" k="66" />
    <hkern u1="&#xaa;" u2="&#xe0;" k="66" />
    <hkern u1="&#xaa;" u2="&#xdd;" k="-36" />
    <hkern u1="&#xaa;" u2="&#xd8;" k="42" />
    <hkern u1="&#xaa;" u2="&#xd6;" k="42" />
    <hkern u1="&#xaa;" u2="&#xd5;" k="42" />
    <hkern u1="&#xaa;" u2="&#xd4;" k="42" />
    <hkern u1="&#xaa;" u2="&#xd3;" k="42" />
    <hkern u1="&#xaa;" u2="&#xd2;" k="42" />
    <hkern u1="&#xaa;" u2="&#xc7;" k="42" />
    <hkern u1="&#xaa;" u2="&#xc6;" k="191" />
    <hkern u1="&#xaa;" u2="&#xc5;" k="191" />
    <hkern u1="&#xaa;" u2="&#xc4;" k="191" />
    <hkern u1="&#xaa;" u2="&#xc3;" k="191" />
    <hkern u1="&#xaa;" u2="&#xc2;" k="191" />
    <hkern u1="&#xaa;" u2="&#xc1;" k="191" />
    <hkern u1="&#xaa;" u2="&#xc0;" k="191" />
    <hkern u1="&#xaa;" u2="&#xbb;" k="169" />
    <hkern u1="&#xaa;" u2="&#xb7;" k="169" />
    <hkern u1="&#xaa;" u2="&#xae;" k="42" />
    <hkern u1="&#xaa;" u2="&#xad;" k="169" />
    <hkern u1="&#xaa;" u2="&#xab;" k="169" />
    <hkern u1="&#xaa;" u2="&#xa9;" k="42" />
    <hkern u1="&#xaa;" u2="q" k="96" />
    <hkern u1="&#xaa;" u2="o" k="96" />
    <hkern u1="&#xaa;" u2="e" k="96" />
    <hkern u1="&#xaa;" u2="d" k="96" />
    <hkern u1="&#xaa;" u2="c" k="96" />
    <hkern u1="&#xaa;" u2="a" k="66" />
    <hkern u1="&#xaa;" u2="\" k="-44" />
    <hkern u1="&#xaa;" u2="Y" k="-36" />
    <hkern u1="&#xaa;" u2="W" k="-44" />
    <hkern u1="&#xaa;" u2="V" k="-44" />
    <hkern u1="&#xaa;" u2="Q" k="42" />
    <hkern u1="&#xaa;" u2="O" k="42" />
    <hkern u1="&#xaa;" u2="G" k="42" />
    <hkern u1="&#xaa;" u2="C" k="42" />
    <hkern u1="&#xaa;" u2="A" k="191" />
    <hkern u1="&#xaa;" u2="&#x40;" k="42" />
    <hkern u1="&#xaa;" u2="&#x2f;" k="191" />
    <hkern u1="&#xaa;" u2="&#x2e;" k="213" />
    <hkern u1="&#xaa;" u2="&#x2d;" k="169" />
    <hkern u1="&#xaa;" u2="&#x2c;" k="213" />
    <hkern u1="&#xaa;" u2="&#x26;" k="191" />
    <hkern u1="&#xab;" u2="&#x2206;" k="67" />
    <hkern u1="&#xab;" u2="&#x2122;" k="169" />
    <hkern u1="&#xab;" u2="&#x201e;" k="132" />
    <hkern u1="&#xab;" u2="&#x201d;" k="169" />
    <hkern u1="&#xab;" u2="&#x201c;" k="169" />
    <hkern u1="&#xab;" u2="&#x201a;" k="132" />
    <hkern u1="&#xab;" u2="&#x2019;" k="169" />
    <hkern u1="&#xab;" u2="&#x2018;" k="169" />
    <hkern u1="&#xab;" u2="&#x17d;" k="48" />
    <hkern u1="&#xab;" u2="&#x17b;" k="48" />
    <hkern u1="&#xab;" u2="&#x179;" k="48" />
    <hkern u1="&#xab;" u2="&#x178;" k="160" />
    <hkern u1="&#xab;" u2="&#x104;" k="67" />
    <hkern u1="&#xab;" u2="&#xdd;" k="160" />
    <hkern u1="&#xab;" u2="&#xc6;" k="67" />
    <hkern u1="&#xab;" u2="&#xc5;" k="67" />
    <hkern u1="&#xab;" u2="&#xc4;" k="67" />
    <hkern u1="&#xab;" u2="&#xc3;" k="67" />
    <hkern u1="&#xab;" u2="&#xc2;" k="67" />
    <hkern u1="&#xab;" u2="&#xc1;" k="67" />
    <hkern u1="&#xab;" u2="&#xc0;" k="67" />
    <hkern u1="&#xab;" u2="&#xba;" k="169" />
    <hkern u1="&#xab;" u2="&#xb0;" k="169" />
    <hkern u1="&#xab;" u2="&#xaa;" k="169" />
    <hkern u1="&#xab;" u2="\" k="116" />
    <hkern u1="&#xab;" u2="Z" k="48" />
    <hkern u1="&#xab;" u2="Y" k="160" />
    <hkern u1="&#xab;" u2="X" k="66" />
    <hkern u1="&#xab;" u2="W" k="36" />
    <hkern u1="&#xab;" u2="V" k="116" />
    <hkern u1="&#xab;" u2="T" k="180" />
    <hkern u1="&#xab;" u2="A" k="67" />
    <hkern u1="&#xab;" u2="&#x2f;" k="67" />
    <hkern u1="&#xab;" u2="&#x2e;" k="132" />
    <hkern u1="&#xab;" u2="&#x2c;" k="132" />
    <hkern u1="&#xab;" u2="&#x2a;" k="169" />
    <hkern u1="&#xab;" u2="&#x27;" k="169" />
    <hkern u1="&#xab;" u2="&#x26;" k="67" />
    <hkern u1="&#xab;" u2="&#x22;" k="169" />
    <hkern u1="&#xad;" u2="&#x2206;" k="67" />
    <hkern u1="&#xad;" u2="&#x2122;" k="169" />
    <hkern u1="&#xad;" u2="&#x201e;" k="132" />
    <hkern u1="&#xad;" u2="&#x201d;" k="169" />
    <hkern u1="&#xad;" u2="&#x201c;" k="169" />
    <hkern u1="&#xad;" u2="&#x201a;" k="132" />
    <hkern u1="&#xad;" u2="&#x2019;" k="169" />
    <hkern u1="&#xad;" u2="&#x2018;" k="169" />
    <hkern u1="&#xad;" u2="&#x17d;" k="48" />
    <hkern u1="&#xad;" u2="&#x17b;" k="48" />
    <hkern u1="&#xad;" u2="&#x179;" k="48" />
    <hkern u1="&#xad;" u2="&#x178;" k="160" />
    <hkern u1="&#xad;" u2="&#x104;" k="67" />
    <hkern u1="&#xad;" u2="&#xdd;" k="160" />
    <hkern u1="&#xad;" u2="&#xc6;" k="67" />
    <hkern u1="&#xad;" u2="&#xc5;" k="67" />
    <hkern u1="&#xad;" u2="&#xc4;" k="67" />
    <hkern u1="&#xad;" u2="&#xc3;" k="67" />
    <hkern u1="&#xad;" u2="&#xc2;" k="67" />
    <hkern u1="&#xad;" u2="&#xc1;" k="67" />
    <hkern u1="&#xad;" u2="&#xc0;" k="67" />
    <hkern u1="&#xad;" u2="&#xba;" k="169" />
    <hkern u1="&#xad;" u2="&#xb0;" k="169" />
    <hkern u1="&#xad;" u2="&#xaa;" k="169" />
    <hkern u1="&#xad;" u2="\" k="116" />
    <hkern u1="&#xad;" u2="Z" k="48" />
    <hkern u1="&#xad;" u2="Y" k="160" />
    <hkern u1="&#xad;" u2="X" k="66" />
    <hkern u1="&#xad;" u2="W" k="36" />
    <hkern u1="&#xad;" u2="V" k="116" />
    <hkern u1="&#xad;" u2="T" k="180" />
    <hkern u1="&#xad;" u2="A" k="67" />
    <hkern u1="&#xad;" u2="&#x2f;" k="67" />
    <hkern u1="&#xad;" u2="&#x2e;" k="132" />
    <hkern u1="&#xad;" u2="&#x2c;" k="132" />
    <hkern u1="&#xad;" u2="&#x2a;" k="169" />
    <hkern u1="&#xad;" u2="&#x27;" k="169" />
    <hkern u1="&#xad;" u2="&#x26;" k="67" />
    <hkern u1="&#xad;" u2="&#x22;" k="169" />
    <hkern u1="&#xae;" u2="&#x2206;" k="51" />
    <hkern u1="&#xae;" u2="&#x2122;" k="42" />
    <hkern u1="&#xae;" u2="&#x201e;" k="52" />
    <hkern u1="&#xae;" u2="&#x201d;" k="42" />
    <hkern u1="&#xae;" u2="&#x201c;" k="42" />
    <hkern u1="&#xae;" u2="&#x201a;" k="52" />
    <hkern u1="&#xae;" u2="&#x2019;" k="42" />
    <hkern u1="&#xae;" u2="&#x2018;" k="42" />
    <hkern u1="&#xae;" u2="&#x17d;" k="64" />
    <hkern u1="&#xae;" u2="&#x17b;" k="64" />
    <hkern u1="&#xae;" u2="&#x179;" k="64" />
    <hkern u1="&#xae;" u2="&#x178;" k="80" />
    <hkern u1="&#xae;" u2="&#x104;" k="51" />
    <hkern u1="&#xae;" u2="&#xdd;" k="80" />
    <hkern u1="&#xae;" u2="&#xc6;" k="51" />
    <hkern u1="&#xae;" u2="&#xc5;" k="51" />
    <hkern u1="&#xae;" u2="&#xc4;" k="51" />
    <hkern u1="&#xae;" u2="&#xc3;" k="51" />
    <hkern u1="&#xae;" u2="&#xc2;" k="51" />
    <hkern u1="&#xae;" u2="&#xc1;" k="51" />
    <hkern u1="&#xae;" u2="&#xc0;" k="51" />
    <hkern u1="&#xae;" u2="&#xba;" k="42" />
    <hkern u1="&#xae;" u2="&#xb0;" k="42" />
    <hkern u1="&#xae;" u2="&#xaa;" k="42" />
    <hkern u1="&#xae;" u2="&#x7d;" k="40" />
    <hkern u1="&#xae;" u2="]" k="40" />
    <hkern u1="&#xae;" u2="\" k="56" />
    <hkern u1="&#xae;" u2="Z" k="64" />
    <hkern u1="&#xae;" u2="Y" k="80" />
    <hkern u1="&#xae;" u2="X" k="30" />
    <hkern u1="&#xae;" u2="V" k="56" />
    <hkern u1="&#xae;" u2="T" k="78" />
    <hkern u1="&#xae;" u2="A" k="51" />
    <hkern u1="&#xae;" u2="&#x2f;" k="51" />
    <hkern u1="&#xae;" u2="&#x2e;" k="52" />
    <hkern u1="&#xae;" u2="&#x2c;" k="52" />
    <hkern u1="&#xae;" u2="&#x2a;" k="42" />
    <hkern u1="&#xae;" u2="&#x29;" k="40" />
    <hkern u1="&#xae;" u2="&#x27;" k="42" />
    <hkern u1="&#xae;" u2="&#x26;" k="51" />
    <hkern u1="&#xae;" u2="&#x22;" k="42" />
    <hkern u1="&#xb0;" u2="&#x2206;" k="191" />
    <hkern u1="&#xb0;" u2="&#x203a;" k="169" />
    <hkern u1="&#xb0;" u2="&#x2039;" k="169" />
    <hkern u1="&#xb0;" u2="&#x2022;" k="169" />
    <hkern u1="&#xb0;" u2="&#x201e;" k="213" />
    <hkern u1="&#xb0;" u2="&#x201a;" k="213" />
    <hkern u1="&#xb0;" u2="&#x2014;" k="169" />
    <hkern u1="&#xb0;" u2="&#x2013;" k="169" />
    <hkern u1="&#xb0;" u2="&#x178;" k="-36" />
    <hkern u1="&#xb0;" u2="&#x153;" k="96" />
    <hkern u1="&#xb0;" u2="&#x152;" k="42" />
    <hkern u1="&#xb0;" u2="&#x119;" k="96" />
    <hkern u1="&#xb0;" u2="&#x107;" k="96" />
    <hkern u1="&#xb0;" u2="&#x106;" k="42" />
    <hkern u1="&#xb0;" u2="&#x105;" k="66" />
    <hkern u1="&#xb0;" u2="&#x104;" k="191" />
    <hkern u1="&#xb0;" u2="&#xf8;" k="96" />
    <hkern u1="&#xb0;" u2="&#xf6;" k="96" />
    <hkern u1="&#xb0;" u2="&#xf5;" k="96" />
    <hkern u1="&#xb0;" u2="&#xf4;" k="96" />
    <hkern u1="&#xb0;" u2="&#xf3;" k="96" />
    <hkern u1="&#xb0;" u2="&#xf2;" k="96" />
    <hkern u1="&#xb0;" u2="&#xf0;" k="96" />
    <hkern u1="&#xb0;" u2="&#xeb;" k="96" />
    <hkern u1="&#xb0;" u2="&#xea;" k="96" />
    <hkern u1="&#xb0;" u2="&#xe9;" k="96" />
    <hkern u1="&#xb0;" u2="&#xe8;" k="96" />
    <hkern u1="&#xb0;" u2="&#xe7;" k="96" />
    <hkern u1="&#xb0;" u2="&#xe6;" k="66" />
    <hkern u1="&#xb0;" u2="&#xe5;" k="66" />
    <hkern u1="&#xb0;" u2="&#xe4;" k="66" />
    <hkern u1="&#xb0;" u2="&#xe3;" k="66" />
    <hkern u1="&#xb0;" u2="&#xe2;" k="66" />
    <hkern u1="&#xb0;" u2="&#xe1;" k="66" />
    <hkern u1="&#xb0;" u2="&#xe0;" k="66" />
    <hkern u1="&#xb0;" u2="&#xdd;" k="-36" />
    <hkern u1="&#xb0;" u2="&#xd8;" k="42" />
    <hkern u1="&#xb0;" u2="&#xd6;" k="42" />
    <hkern u1="&#xb0;" u2="&#xd5;" k="42" />
    <hkern u1="&#xb0;" u2="&#xd4;" k="42" />
    <hkern u1="&#xb0;" u2="&#xd3;" k="42" />
    <hkern u1="&#xb0;" u2="&#xd2;" k="42" />
    <hkern u1="&#xb0;" u2="&#xc7;" k="42" />
    <hkern u1="&#xb0;" u2="&#xc6;" k="191" />
    <hkern u1="&#xb0;" u2="&#xc5;" k="191" />
    <hkern u1="&#xb0;" u2="&#xc4;" k="191" />
    <hkern u1="&#xb0;" u2="&#xc3;" k="191" />
    <hkern u1="&#xb0;" u2="&#xc2;" k="191" />
    <hkern u1="&#xb0;" u2="&#xc1;" k="191" />
    <hkern u1="&#xb0;" u2="&#xc0;" k="191" />
    <hkern u1="&#xb0;" u2="&#xbb;" k="169" />
    <hkern u1="&#xb0;" u2="&#xb7;" k="169" />
    <hkern u1="&#xb0;" u2="&#xae;" k="42" />
    <hkern u1="&#xb0;" u2="&#xad;" k="169" />
    <hkern u1="&#xb0;" u2="&#xab;" k="169" />
    <hkern u1="&#xb0;" u2="&#xa9;" k="42" />
    <hkern u1="&#xb0;" u2="q" k="96" />
    <hkern u1="&#xb0;" u2="o" k="96" />
    <hkern u1="&#xb0;" u2="e" k="96" />
    <hkern u1="&#xb0;" u2="d" k="96" />
    <hkern u1="&#xb0;" u2="c" k="96" />
    <hkern u1="&#xb0;" u2="a" k="66" />
    <hkern u1="&#xb0;" u2="\" k="-44" />
    <hkern u1="&#xb0;" u2="Y" k="-36" />
    <hkern u1="&#xb0;" u2="W" k="-44" />
    <hkern u1="&#xb0;" u2="V" k="-44" />
    <hkern u1="&#xb0;" u2="Q" k="42" />
    <hkern u1="&#xb0;" u2="O" k="42" />
    <hkern u1="&#xb0;" u2="G" k="42" />
    <hkern u1="&#xb0;" u2="C" k="42" />
    <hkern u1="&#xb0;" u2="A" k="191" />
    <hkern u1="&#xb0;" u2="&#x40;" k="42" />
    <hkern u1="&#xb0;" u2="&#x2f;" k="191" />
    <hkern u1="&#xb0;" u2="&#x2e;" k="213" />
    <hkern u1="&#xb0;" u2="&#x2d;" k="169" />
    <hkern u1="&#xb0;" u2="&#x2c;" k="213" />
    <hkern u1="&#xb0;" u2="&#x26;" k="191" />
    <hkern u1="&#xb2;" u2="&#x2206;" k="202" />
    <hkern u1="&#xb2;" u2="&#x178;" k="-40" />
    <hkern u1="&#xb2;" u2="&#x104;" k="202" />
    <hkern u1="&#xb2;" u2="&#xdd;" k="-40" />
    <hkern u1="&#xb2;" u2="&#xc6;" k="202" />
    <hkern u1="&#xb2;" u2="&#xc5;" k="202" />
    <hkern u1="&#xb2;" u2="&#xc4;" k="202" />
    <hkern u1="&#xb2;" u2="&#xc3;" k="202" />
    <hkern u1="&#xb2;" u2="&#xc2;" k="202" />
    <hkern u1="&#xb2;" u2="&#xc1;" k="202" />
    <hkern u1="&#xb2;" u2="&#xc0;" k="202" />
    <hkern u1="&#xb2;" u2="\" k="-49" />
    <hkern u1="&#xb2;" u2="Y" k="-40" />
    <hkern u1="&#xb2;" u2="W" k="-49" />
    <hkern u1="&#xb2;" u2="V" k="-49" />
    <hkern u1="&#xb2;" u2="A" k="202" />
    <hkern u1="&#xb2;" u2="&#x2f;" k="202" />
    <hkern u1="&#xb2;" u2="&#x26;" k="202" />
    <hkern u1="&#xb3;" u2="&#x2206;" k="202" />
    <hkern u1="&#xb3;" u2="&#x178;" k="-40" />
    <hkern u1="&#xb3;" u2="&#x104;" k="202" />
    <hkern u1="&#xb3;" u2="&#xdd;" k="-40" />
    <hkern u1="&#xb3;" u2="&#xc6;" k="202" />
    <hkern u1="&#xb3;" u2="&#xc5;" k="202" />
    <hkern u1="&#xb3;" u2="&#xc4;" k="202" />
    <hkern u1="&#xb3;" u2="&#xc3;" k="202" />
    <hkern u1="&#xb3;" u2="&#xc2;" k="202" />
    <hkern u1="&#xb3;" u2="&#xc1;" k="202" />
    <hkern u1="&#xb3;" u2="&#xc0;" k="202" />
    <hkern u1="&#xb3;" u2="\" k="-49" />
    <hkern u1="&#xb3;" u2="Y" k="-40" />
    <hkern u1="&#xb3;" u2="W" k="-49" />
    <hkern u1="&#xb3;" u2="V" k="-49" />
    <hkern u1="&#xb3;" u2="A" k="202" />
    <hkern u1="&#xb3;" u2="&#x2f;" k="202" />
    <hkern u1="&#xb3;" u2="&#x26;" k="202" />
    <hkern u1="&#xb7;" u2="&#x2206;" k="67" />
    <hkern u1="&#xb7;" u2="&#x2122;" k="169" />
    <hkern u1="&#xb7;" u2="&#x201e;" k="132" />
    <hkern u1="&#xb7;" u2="&#x201d;" k="169" />
    <hkern u1="&#xb7;" u2="&#x201c;" k="169" />
    <hkern u1="&#xb7;" u2="&#x201a;" k="132" />
    <hkern u1="&#xb7;" u2="&#x2019;" k="169" />
    <hkern u1="&#xb7;" u2="&#x2018;" k="169" />
    <hkern u1="&#xb7;" u2="&#x17d;" k="48" />
    <hkern u1="&#xb7;" u2="&#x17b;" k="48" />
    <hkern u1="&#xb7;" u2="&#x179;" k="48" />
    <hkern u1="&#xb7;" u2="&#x178;" k="160" />
    <hkern u1="&#xb7;" u2="&#x104;" k="67" />
    <hkern u1="&#xb7;" u2="&#xdd;" k="160" />
    <hkern u1="&#xb7;" u2="&#xc6;" k="67" />
    <hkern u1="&#xb7;" u2="&#xc5;" k="67" />
    <hkern u1="&#xb7;" u2="&#xc4;" k="67" />
    <hkern u1="&#xb7;" u2="&#xc3;" k="67" />
    <hkern u1="&#xb7;" u2="&#xc2;" k="67" />
    <hkern u1="&#xb7;" u2="&#xc1;" k="67" />
    <hkern u1="&#xb7;" u2="&#xc0;" k="67" />
    <hkern u1="&#xb7;" u2="&#xba;" k="169" />
    <hkern u1="&#xb7;" u2="&#xb0;" k="169" />
    <hkern u1="&#xb7;" u2="&#xaa;" k="169" />
    <hkern u1="&#xb7;" u2="\" k="116" />
    <hkern u1="&#xb7;" u2="Z" k="48" />
    <hkern u1="&#xb7;" u2="Y" k="160" />
    <hkern u1="&#xb7;" u2="X" k="66" />
    <hkern u1="&#xb7;" u2="W" k="36" />
    <hkern u1="&#xb7;" u2="V" k="116" />
    <hkern u1="&#xb7;" u2="T" k="180" />
    <hkern u1="&#xb7;" u2="A" k="67" />
    <hkern u1="&#xb7;" u2="&#x2f;" k="67" />
    <hkern u1="&#xb7;" u2="&#x2e;" k="132" />
    <hkern u1="&#xb7;" u2="&#x2c;" k="132" />
    <hkern u1="&#xb7;" u2="&#x2a;" k="169" />
    <hkern u1="&#xb7;" u2="&#x27;" k="169" />
    <hkern u1="&#xb7;" u2="&#x26;" k="67" />
    <hkern u1="&#xb7;" u2="&#x22;" k="169" />
    <hkern u1="&#xb9;" u2="&#x2206;" k="202" />
    <hkern u1="&#xb9;" u2="&#x178;" k="-40" />
    <hkern u1="&#xb9;" u2="&#x104;" k="202" />
    <hkern u1="&#xb9;" u2="&#xdd;" k="-40" />
    <hkern u1="&#xb9;" u2="&#xc6;" k="202" />
    <hkern u1="&#xb9;" u2="&#xc5;" k="202" />
    <hkern u1="&#xb9;" u2="&#xc4;" k="202" />
    <hkern u1="&#xb9;" u2="&#xc3;" k="202" />
    <hkern u1="&#xb9;" u2="&#xc2;" k="202" />
    <hkern u1="&#xb9;" u2="&#xc1;" k="202" />
    <hkern u1="&#xb9;" u2="&#xc0;" k="202" />
    <hkern u1="&#xb9;" u2="\" k="-49" />
    <hkern u1="&#xb9;" u2="Y" k="-40" />
    <hkern u1="&#xb9;" u2="W" k="-49" />
    <hkern u1="&#xb9;" u2="V" k="-49" />
    <hkern u1="&#xb9;" u2="A" k="202" />
    <hkern u1="&#xb9;" u2="&#x2f;" k="202" />
    <hkern u1="&#xb9;" u2="&#x26;" k="202" />
    <hkern u1="&#xba;" u2="&#x2206;" k="191" />
    <hkern u1="&#xba;" u2="&#x203a;" k="169" />
    <hkern u1="&#xba;" u2="&#x2039;" k="169" />
    <hkern u1="&#xba;" u2="&#x2022;" k="169" />
    <hkern u1="&#xba;" u2="&#x201e;" k="213" />
    <hkern u1="&#xba;" u2="&#x201a;" k="213" />
    <hkern u1="&#xba;" u2="&#x2014;" k="169" />
    <hkern u1="&#xba;" u2="&#x2013;" k="169" />
    <hkern u1="&#xba;" u2="&#x178;" k="-36" />
    <hkern u1="&#xba;" u2="&#x153;" k="96" />
    <hkern u1="&#xba;" u2="&#x152;" k="42" />
    <hkern u1="&#xba;" u2="&#x119;" k="96" />
    <hkern u1="&#xba;" u2="&#x107;" k="96" />
    <hkern u1="&#xba;" u2="&#x106;" k="42" />
    <hkern u1="&#xba;" u2="&#x105;" k="66" />
    <hkern u1="&#xba;" u2="&#x104;" k="191" />
    <hkern u1="&#xba;" u2="&#xf8;" k="96" />
    <hkern u1="&#xba;" u2="&#xf6;" k="96" />
    <hkern u1="&#xba;" u2="&#xf5;" k="96" />
    <hkern u1="&#xba;" u2="&#xf4;" k="96" />
    <hkern u1="&#xba;" u2="&#xf3;" k="96" />
    <hkern u1="&#xba;" u2="&#xf2;" k="96" />
    <hkern u1="&#xba;" u2="&#xf0;" k="96" />
    <hkern u1="&#xba;" u2="&#xeb;" k="96" />
    <hkern u1="&#xba;" u2="&#xea;" k="96" />
    <hkern u1="&#xba;" u2="&#xe9;" k="96" />
    <hkern u1="&#xba;" u2="&#xe8;" k="96" />
    <hkern u1="&#xba;" u2="&#xe7;" k="96" />
    <hkern u1="&#xba;" u2="&#xe6;" k="66" />
    <hkern u1="&#xba;" u2="&#xe5;" k="66" />
    <hkern u1="&#xba;" u2="&#xe4;" k="66" />
    <hkern u1="&#xba;" u2="&#xe3;" k="66" />
    <hkern u1="&#xba;" u2="&#xe2;" k="66" />
    <hkern u1="&#xba;" u2="&#xe1;" k="66" />
    <hkern u1="&#xba;" u2="&#xe0;" k="66" />
    <hkern u1="&#xba;" u2="&#xdd;" k="-36" />
    <hkern u1="&#xba;" u2="&#xd8;" k="42" />
    <hkern u1="&#xba;" u2="&#xd6;" k="42" />
    <hkern u1="&#xba;" u2="&#xd5;" k="42" />
    <hkern u1="&#xba;" u2="&#xd4;" k="42" />
    <hkern u1="&#xba;" u2="&#xd3;" k="42" />
    <hkern u1="&#xba;" u2="&#xd2;" k="42" />
    <hkern u1="&#xba;" u2="&#xc7;" k="42" />
    <hkern u1="&#xba;" u2="&#xc6;" k="191" />
    <hkern u1="&#xba;" u2="&#xc5;" k="191" />
    <hkern u1="&#xba;" u2="&#xc4;" k="191" />
    <hkern u1="&#xba;" u2="&#xc3;" k="191" />
    <hkern u1="&#xba;" u2="&#xc2;" k="191" />
    <hkern u1="&#xba;" u2="&#xc1;" k="191" />
    <hkern u1="&#xba;" u2="&#xc0;" k="191" />
    <hkern u1="&#xba;" u2="&#xbb;" k="169" />
    <hkern u1="&#xba;" u2="&#xb7;" k="169" />
    <hkern u1="&#xba;" u2="&#xae;" k="42" />
    <hkern u1="&#xba;" u2="&#xad;" k="169" />
    <hkern u1="&#xba;" u2="&#xab;" k="169" />
    <hkern u1="&#xba;" u2="&#xa9;" k="42" />
    <hkern u1="&#xba;" u2="q" k="96" />
    <hkern u1="&#xba;" u2="o" k="96" />
    <hkern u1="&#xba;" u2="e" k="96" />
    <hkern u1="&#xba;" u2="d" k="96" />
    <hkern u1="&#xba;" u2="c" k="96" />
    <hkern u1="&#xba;" u2="a" k="66" />
    <hkern u1="&#xba;" u2="\" k="-44" />
    <hkern u1="&#xba;" u2="Y" k="-36" />
    <hkern u1="&#xba;" u2="W" k="-44" />
    <hkern u1="&#xba;" u2="V" k="-44" />
    <hkern u1="&#xba;" u2="Q" k="42" />
    <hkern u1="&#xba;" u2="O" k="42" />
    <hkern u1="&#xba;" u2="G" k="42" />
    <hkern u1="&#xba;" u2="C" k="42" />
    <hkern u1="&#xba;" u2="A" k="191" />
    <hkern u1="&#xba;" u2="&#x40;" k="42" />
    <hkern u1="&#xba;" u2="&#x2f;" k="191" />
    <hkern u1="&#xba;" u2="&#x2e;" k="213" />
    <hkern u1="&#xba;" u2="&#x2d;" k="169" />
    <hkern u1="&#xba;" u2="&#x2c;" k="213" />
    <hkern u1="&#xba;" u2="&#x26;" k="191" />
    <hkern u1="&#xbb;" u2="&#x2206;" k="67" />
    <hkern u1="&#xbb;" u2="&#x2122;" k="169" />
    <hkern u1="&#xbb;" u2="&#x201e;" k="132" />
    <hkern u1="&#xbb;" u2="&#x201d;" k="169" />
    <hkern u1="&#xbb;" u2="&#x201c;" k="169" />
    <hkern u1="&#xbb;" u2="&#x201a;" k="132" />
    <hkern u1="&#xbb;" u2="&#x2019;" k="169" />
    <hkern u1="&#xbb;" u2="&#x2018;" k="169" />
    <hkern u1="&#xbb;" u2="&#x17d;" k="48" />
    <hkern u1="&#xbb;" u2="&#x17b;" k="48" />
    <hkern u1="&#xbb;" u2="&#x179;" k="48" />
    <hkern u1="&#xbb;" u2="&#x178;" k="160" />
    <hkern u1="&#xbb;" u2="&#x104;" k="67" />
    <hkern u1="&#xbb;" u2="&#xdd;" k="160" />
    <hkern u1="&#xbb;" u2="&#xc6;" k="67" />
    <hkern u1="&#xbb;" u2="&#xc5;" k="67" />
    <hkern u1="&#xbb;" u2="&#xc4;" k="67" />
    <hkern u1="&#xbb;" u2="&#xc3;" k="67" />
    <hkern u1="&#xbb;" u2="&#xc2;" k="67" />
    <hkern u1="&#xbb;" u2="&#xc1;" k="67" />
    <hkern u1="&#xbb;" u2="&#xc0;" k="67" />
    <hkern u1="&#xbb;" u2="&#xba;" k="169" />
    <hkern u1="&#xbb;" u2="&#xb0;" k="169" />
    <hkern u1="&#xbb;" u2="&#xaa;" k="169" />
    <hkern u1="&#xbb;" u2="\" k="116" />
    <hkern u1="&#xbb;" u2="Z" k="48" />
    <hkern u1="&#xbb;" u2="Y" k="160" />
    <hkern u1="&#xbb;" u2="X" k="66" />
    <hkern u1="&#xbb;" u2="W" k="36" />
    <hkern u1="&#xbb;" u2="V" k="116" />
    <hkern u1="&#xbb;" u2="T" k="180" />
    <hkern u1="&#xbb;" u2="A" k="67" />
    <hkern u1="&#xbb;" u2="&#x2f;" k="67" />
    <hkern u1="&#xbb;" u2="&#x2e;" k="132" />
    <hkern u1="&#xbb;" u2="&#x2c;" k="132" />
    <hkern u1="&#xbb;" u2="&#x2a;" k="169" />
    <hkern u1="&#xbb;" u2="&#x27;" k="169" />
    <hkern u1="&#xbb;" u2="&#x26;" k="67" />
    <hkern u1="&#xbb;" u2="&#x22;" k="169" />
    <hkern u1="&#xc0;" u2="&#x2122;" k="191" />
    <hkern u1="&#xc0;" u2="&#x203a;" k="67" />
    <hkern u1="&#xc0;" u2="&#x2039;" k="67" />
    <hkern u1="&#xc0;" u2="&#x2022;" k="67" />
    <hkern u1="&#xc0;" u2="&#x201d;" k="191" />
    <hkern u1="&#xc0;" u2="&#x201c;" k="191" />
    <hkern u1="&#xc0;" u2="&#x2019;" k="191" />
    <hkern u1="&#xc0;" u2="&#x2018;" k="191" />
    <hkern u1="&#xc0;" u2="&#x2014;" k="67" />
    <hkern u1="&#xc0;" u2="&#x2013;" k="67" />
    <hkern u1="&#xc0;" u2="&#x178;" k="182" />
    <hkern u1="&#xc0;" u2="&#x152;" k="51" />
    <hkern u1="&#xc0;" u2="&#x106;" k="51" />
    <hkern u1="&#xc0;" u2="&#xff;" k="91" />
    <hkern u1="&#xc0;" u2="&#xfd;" k="91" />
    <hkern u1="&#xc0;" u2="&#xdd;" k="182" />
    <hkern u1="&#xc0;" u2="&#xdc;" k="52" />
    <hkern u1="&#xc0;" u2="&#xdb;" k="52" />
    <hkern u1="&#xc0;" u2="&#xda;" k="52" />
    <hkern u1="&#xc0;" u2="&#xd9;" k="52" />
    <hkern u1="&#xc0;" u2="&#xd8;" k="51" />
    <hkern u1="&#xc0;" u2="&#xd6;" k="51" />
    <hkern u1="&#xc0;" u2="&#xd5;" k="51" />
    <hkern u1="&#xc0;" u2="&#xd4;" k="51" />
    <hkern u1="&#xc0;" u2="&#xd3;" k="51" />
    <hkern u1="&#xc0;" u2="&#xd2;" k="51" />
    <hkern u1="&#xc0;" u2="&#xc7;" k="51" />
    <hkern u1="&#xc0;" u2="&#xbb;" k="67" />
    <hkern u1="&#xc0;" u2="&#xba;" k="191" />
    <hkern u1="&#xc0;" u2="&#xb9;" k="202" />
    <hkern u1="&#xc0;" u2="&#xb7;" k="67" />
    <hkern u1="&#xc0;" u2="&#xb3;" k="202" />
    <hkern u1="&#xc0;" u2="&#xb2;" k="202" />
    <hkern u1="&#xc0;" u2="&#xb0;" k="191" />
    <hkern u1="&#xc0;" u2="&#xae;" k="51" />
    <hkern u1="&#xc0;" u2="&#xad;" k="67" />
    <hkern u1="&#xc0;" u2="&#xab;" k="67" />
    <hkern u1="&#xc0;" u2="&#xaa;" k="191" />
    <hkern u1="&#xc0;" u2="&#xa9;" k="51" />
    <hkern u1="&#xc0;" u2="y" k="91" />
    <hkern u1="&#xc0;" u2="v" k="91" />
    <hkern u1="&#xc0;" u2="\" k="169" />
    <hkern u1="&#xc0;" u2="Y" k="182" />
    <hkern u1="&#xc0;" u2="W" k="102" />
    <hkern u1="&#xc0;" u2="V" k="169" />
    <hkern u1="&#xc0;" u2="U" k="52" />
    <hkern u1="&#xc0;" u2="T" k="147" />
    <hkern u1="&#xc0;" u2="Q" k="51" />
    <hkern u1="&#xc0;" u2="O" k="51" />
    <hkern u1="&#xc0;" u2="J" k="-56" />
    <hkern u1="&#xc0;" u2="G" k="51" />
    <hkern u1="&#xc0;" u2="C" k="51" />
    <hkern u1="&#xc0;" u2="&#x40;" k="51" />
    <hkern u1="&#xc0;" u2="&#x3f;" k="63" />
    <hkern u1="&#xc0;" u2="&#x2d;" k="67" />
    <hkern u1="&#xc0;" u2="&#x2a;" k="191" />
    <hkern u1="&#xc0;" u2="&#x27;" k="191" />
    <hkern u1="&#xc0;" u2="&#x22;" k="191" />
    <hkern u1="&#xc1;" u2="&#x2122;" k="191" />
    <hkern u1="&#xc1;" u2="&#x203a;" k="67" />
    <hkern u1="&#xc1;" u2="&#x2039;" k="67" />
    <hkern u1="&#xc1;" u2="&#x2022;" k="67" />
    <hkern u1="&#xc1;" u2="&#x201d;" k="191" />
    <hkern u1="&#xc1;" u2="&#x201c;" k="191" />
    <hkern u1="&#xc1;" u2="&#x2019;" k="191" />
    <hkern u1="&#xc1;" u2="&#x2018;" k="191" />
    <hkern u1="&#xc1;" u2="&#x2014;" k="67" />
    <hkern u1="&#xc1;" u2="&#x2013;" k="67" />
    <hkern u1="&#xc1;" u2="&#x178;" k="182" />
    <hkern u1="&#xc1;" u2="&#x152;" k="51" />
    <hkern u1="&#xc1;" u2="&#x106;" k="51" />
    <hkern u1="&#xc1;" u2="&#xff;" k="91" />
    <hkern u1="&#xc1;" u2="&#xfd;" k="91" />
    <hkern u1="&#xc1;" u2="&#xdd;" k="182" />
    <hkern u1="&#xc1;" u2="&#xdc;" k="52" />
    <hkern u1="&#xc1;" u2="&#xdb;" k="52" />
    <hkern u1="&#xc1;" u2="&#xda;" k="52" />
    <hkern u1="&#xc1;" u2="&#xd9;" k="52" />
    <hkern u1="&#xc1;" u2="&#xd8;" k="51" />
    <hkern u1="&#xc1;" u2="&#xd6;" k="51" />
    <hkern u1="&#xc1;" u2="&#xd5;" k="51" />
    <hkern u1="&#xc1;" u2="&#xd4;" k="51" />
    <hkern u1="&#xc1;" u2="&#xd3;" k="51" />
    <hkern u1="&#xc1;" u2="&#xd2;" k="51" />
    <hkern u1="&#xc1;" u2="&#xc7;" k="51" />
    <hkern u1="&#xc1;" u2="&#xbb;" k="67" />
    <hkern u1="&#xc1;" u2="&#xba;" k="191" />
    <hkern u1="&#xc1;" u2="&#xb9;" k="202" />
    <hkern u1="&#xc1;" u2="&#xb7;" k="67" />
    <hkern u1="&#xc1;" u2="&#xb3;" k="202" />
    <hkern u1="&#xc1;" u2="&#xb2;" k="202" />
    <hkern u1="&#xc1;" u2="&#xb0;" k="191" />
    <hkern u1="&#xc1;" u2="&#xae;" k="51" />
    <hkern u1="&#xc1;" u2="&#xad;" k="67" />
    <hkern u1="&#xc1;" u2="&#xab;" k="67" />
    <hkern u1="&#xc1;" u2="&#xaa;" k="191" />
    <hkern u1="&#xc1;" u2="&#xa9;" k="51" />
    <hkern u1="&#xc1;" u2="y" k="91" />
    <hkern u1="&#xc1;" u2="v" k="91" />
    <hkern u1="&#xc1;" u2="\" k="169" />
    <hkern u1="&#xc1;" u2="Y" k="182" />
    <hkern u1="&#xc1;" u2="W" k="102" />
    <hkern u1="&#xc1;" u2="V" k="169" />
    <hkern u1="&#xc1;" u2="U" k="52" />
    <hkern u1="&#xc1;" u2="T" k="147" />
    <hkern u1="&#xc1;" u2="Q" k="51" />
    <hkern u1="&#xc1;" u2="O" k="51" />
    <hkern u1="&#xc1;" u2="J" k="-56" />
    <hkern u1="&#xc1;" u2="G" k="51" />
    <hkern u1="&#xc1;" u2="C" k="51" />
    <hkern u1="&#xc1;" u2="&#x40;" k="51" />
    <hkern u1="&#xc1;" u2="&#x3f;" k="63" />
    <hkern u1="&#xc1;" u2="&#x2d;" k="67" />
    <hkern u1="&#xc1;" u2="&#x2a;" k="191" />
    <hkern u1="&#xc1;" u2="&#x27;" k="191" />
    <hkern u1="&#xc1;" u2="&#x22;" k="191" />
    <hkern u1="&#xc2;" u2="&#x2122;" k="191" />
    <hkern u1="&#xc2;" u2="&#x203a;" k="67" />
    <hkern u1="&#xc2;" u2="&#x2039;" k="67" />
    <hkern u1="&#xc2;" u2="&#x2022;" k="67" />
    <hkern u1="&#xc2;" u2="&#x201d;" k="191" />
    <hkern u1="&#xc2;" u2="&#x201c;" k="191" />
    <hkern u1="&#xc2;" u2="&#x2019;" k="191" />
    <hkern u1="&#xc2;" u2="&#x2018;" k="191" />
    <hkern u1="&#xc2;" u2="&#x2014;" k="67" />
    <hkern u1="&#xc2;" u2="&#x2013;" k="67" />
    <hkern u1="&#xc2;" u2="&#x178;" k="182" />
    <hkern u1="&#xc2;" u2="&#x152;" k="51" />
    <hkern u1="&#xc2;" u2="&#x106;" k="51" />
    <hkern u1="&#xc2;" u2="&#xff;" k="91" />
    <hkern u1="&#xc2;" u2="&#xfd;" k="91" />
    <hkern u1="&#xc2;" u2="&#xdd;" k="182" />
    <hkern u1="&#xc2;" u2="&#xdc;" k="52" />
    <hkern u1="&#xc2;" u2="&#xdb;" k="52" />
    <hkern u1="&#xc2;" u2="&#xda;" k="52" />
    <hkern u1="&#xc2;" u2="&#xd9;" k="52" />
    <hkern u1="&#xc2;" u2="&#xd8;" k="51" />
    <hkern u1="&#xc2;" u2="&#xd6;" k="51" />
    <hkern u1="&#xc2;" u2="&#xd5;" k="51" />
    <hkern u1="&#xc2;" u2="&#xd4;" k="51" />
    <hkern u1="&#xc2;" u2="&#xd3;" k="51" />
    <hkern u1="&#xc2;" u2="&#xd2;" k="51" />
    <hkern u1="&#xc2;" u2="&#xc7;" k="51" />
    <hkern u1="&#xc2;" u2="&#xbb;" k="67" />
    <hkern u1="&#xc2;" u2="&#xba;" k="191" />
    <hkern u1="&#xc2;" u2="&#xb9;" k="202" />
    <hkern u1="&#xc2;" u2="&#xb7;" k="67" />
    <hkern u1="&#xc2;" u2="&#xb3;" k="202" />
    <hkern u1="&#xc2;" u2="&#xb2;" k="202" />
    <hkern u1="&#xc2;" u2="&#xb0;" k="191" />
    <hkern u1="&#xc2;" u2="&#xae;" k="51" />
    <hkern u1="&#xc2;" u2="&#xad;" k="67" />
    <hkern u1="&#xc2;" u2="&#xab;" k="67" />
    <hkern u1="&#xc2;" u2="&#xaa;" k="191" />
    <hkern u1="&#xc2;" u2="&#xa9;" k="51" />
    <hkern u1="&#xc2;" u2="y" k="91" />
    <hkern u1="&#xc2;" u2="v" k="91" />
    <hkern u1="&#xc2;" u2="\" k="169" />
    <hkern u1="&#xc2;" u2="Y" k="182" />
    <hkern u1="&#xc2;" u2="W" k="102" />
    <hkern u1="&#xc2;" u2="V" k="169" />
    <hkern u1="&#xc2;" u2="U" k="52" />
    <hkern u1="&#xc2;" u2="T" k="147" />
    <hkern u1="&#xc2;" u2="Q" k="51" />
    <hkern u1="&#xc2;" u2="O" k="51" />
    <hkern u1="&#xc2;" u2="J" k="-56" />
    <hkern u1="&#xc2;" u2="G" k="51" />
    <hkern u1="&#xc2;" u2="C" k="51" />
    <hkern u1="&#xc2;" u2="&#x40;" k="51" />
    <hkern u1="&#xc2;" u2="&#x3f;" k="63" />
    <hkern u1="&#xc2;" u2="&#x2d;" k="67" />
    <hkern u1="&#xc2;" u2="&#x2a;" k="191" />
    <hkern u1="&#xc2;" u2="&#x27;" k="191" />
    <hkern u1="&#xc2;" u2="&#x22;" k="191" />
    <hkern u1="&#xc3;" u2="&#x2122;" k="191" />
    <hkern u1="&#xc3;" u2="&#x203a;" k="67" />
    <hkern u1="&#xc3;" u2="&#x2039;" k="67" />
    <hkern u1="&#xc3;" u2="&#x2022;" k="67" />
    <hkern u1="&#xc3;" u2="&#x201d;" k="191" />
    <hkern u1="&#xc3;" u2="&#x201c;" k="191" />
    <hkern u1="&#xc3;" u2="&#x2019;" k="191" />
    <hkern u1="&#xc3;" u2="&#x2018;" k="191" />
    <hkern u1="&#xc3;" u2="&#x2014;" k="67" />
    <hkern u1="&#xc3;" u2="&#x2013;" k="67" />
    <hkern u1="&#xc3;" u2="&#x178;" k="182" />
    <hkern u1="&#xc3;" u2="&#x152;" k="51" />
    <hkern u1="&#xc3;" u2="&#x106;" k="51" />
    <hkern u1="&#xc3;" u2="&#xff;" k="91" />
    <hkern u1="&#xc3;" u2="&#xfd;" k="91" />
    <hkern u1="&#xc3;" u2="&#xdd;" k="182" />
    <hkern u1="&#xc3;" u2="&#xdc;" k="52" />
    <hkern u1="&#xc3;" u2="&#xdb;" k="52" />
    <hkern u1="&#xc3;" u2="&#xda;" k="52" />
    <hkern u1="&#xc3;" u2="&#xd9;" k="52" />
    <hkern u1="&#xc3;" u2="&#xd8;" k="51" />
    <hkern u1="&#xc3;" u2="&#xd6;" k="51" />
    <hkern u1="&#xc3;" u2="&#xd5;" k="51" />
    <hkern u1="&#xc3;" u2="&#xd4;" k="51" />
    <hkern u1="&#xc3;" u2="&#xd3;" k="51" />
    <hkern u1="&#xc3;" u2="&#xd2;" k="51" />
    <hkern u1="&#xc3;" u2="&#xc7;" k="51" />
    <hkern u1="&#xc3;" u2="&#xbb;" k="67" />
    <hkern u1="&#xc3;" u2="&#xba;" k="191" />
    <hkern u1="&#xc3;" u2="&#xb9;" k="202" />
    <hkern u1="&#xc3;" u2="&#xb7;" k="67" />
    <hkern u1="&#xc3;" u2="&#xb3;" k="202" />
    <hkern u1="&#xc3;" u2="&#xb2;" k="202" />
    <hkern u1="&#xc3;" u2="&#xb0;" k="191" />
    <hkern u1="&#xc3;" u2="&#xae;" k="51" />
    <hkern u1="&#xc3;" u2="&#xad;" k="67" />
    <hkern u1="&#xc3;" u2="&#xab;" k="67" />
    <hkern u1="&#xc3;" u2="&#xaa;" k="191" />
    <hkern u1="&#xc3;" u2="&#xa9;" k="51" />
    <hkern u1="&#xc3;" u2="y" k="91" />
    <hkern u1="&#xc3;" u2="v" k="91" />
    <hkern u1="&#xc3;" u2="\" k="169" />
    <hkern u1="&#xc3;" u2="Y" k="182" />
    <hkern u1="&#xc3;" u2="W" k="102" />
    <hkern u1="&#xc3;" u2="V" k="169" />
    <hkern u1="&#xc3;" u2="U" k="52" />
    <hkern u1="&#xc3;" u2="T" k="147" />
    <hkern u1="&#xc3;" u2="Q" k="51" />
    <hkern u1="&#xc3;" u2="O" k="51" />
    <hkern u1="&#xc3;" u2="J" k="-56" />
    <hkern u1="&#xc3;" u2="G" k="51" />
    <hkern u1="&#xc3;" u2="C" k="51" />
    <hkern u1="&#xc3;" u2="&#x40;" k="51" />
    <hkern u1="&#xc3;" u2="&#x3f;" k="63" />
    <hkern u1="&#xc3;" u2="&#x2d;" k="67" />
    <hkern u1="&#xc3;" u2="&#x2a;" k="191" />
    <hkern u1="&#xc3;" u2="&#x27;" k="191" />
    <hkern u1="&#xc3;" u2="&#x22;" k="191" />
    <hkern u1="&#xc4;" u2="&#x2122;" k="191" />
    <hkern u1="&#xc4;" u2="&#x203a;" k="67" />
    <hkern u1="&#xc4;" u2="&#x2039;" k="67" />
    <hkern u1="&#xc4;" u2="&#x2022;" k="67" />
    <hkern u1="&#xc4;" u2="&#x201d;" k="191" />
    <hkern u1="&#xc4;" u2="&#x201c;" k="191" />
    <hkern u1="&#xc4;" u2="&#x2019;" k="191" />
    <hkern u1="&#xc4;" u2="&#x2018;" k="191" />
    <hkern u1="&#xc4;" u2="&#x2014;" k="67" />
    <hkern u1="&#xc4;" u2="&#x2013;" k="67" />
    <hkern u1="&#xc4;" u2="&#x178;" k="182" />
    <hkern u1="&#xc4;" u2="&#x152;" k="51" />
    <hkern u1="&#xc4;" u2="&#x106;" k="51" />
    <hkern u1="&#xc4;" u2="&#xff;" k="91" />
    <hkern u1="&#xc4;" u2="&#xfd;" k="91" />
    <hkern u1="&#xc4;" u2="&#xdd;" k="182" />
    <hkern u1="&#xc4;" u2="&#xdc;" k="52" />
    <hkern u1="&#xc4;" u2="&#xdb;" k="52" />
    <hkern u1="&#xc4;" u2="&#xda;" k="52" />
    <hkern u1="&#xc4;" u2="&#xd9;" k="52" />
    <hkern u1="&#xc4;" u2="&#xd8;" k="51" />
    <hkern u1="&#xc4;" u2="&#xd6;" k="51" />
    <hkern u1="&#xc4;" u2="&#xd5;" k="51" />
    <hkern u1="&#xc4;" u2="&#xd4;" k="51" />
    <hkern u1="&#xc4;" u2="&#xd3;" k="51" />
    <hkern u1="&#xc4;" u2="&#xd2;" k="51" />
    <hkern u1="&#xc4;" u2="&#xc7;" k="51" />
    <hkern u1="&#xc4;" u2="&#xbb;" k="67" />
    <hkern u1="&#xc4;" u2="&#xba;" k="191" />
    <hkern u1="&#xc4;" u2="&#xb9;" k="202" />
    <hkern u1="&#xc4;" u2="&#xb7;" k="67" />
    <hkern u1="&#xc4;" u2="&#xb3;" k="202" />
    <hkern u1="&#xc4;" u2="&#xb2;" k="202" />
    <hkern u1="&#xc4;" u2="&#xb0;" k="191" />
    <hkern u1="&#xc4;" u2="&#xae;" k="51" />
    <hkern u1="&#xc4;" u2="&#xad;" k="67" />
    <hkern u1="&#xc4;" u2="&#xab;" k="67" />
    <hkern u1="&#xc4;" u2="&#xaa;" k="191" />
    <hkern u1="&#xc4;" u2="&#xa9;" k="51" />
    <hkern u1="&#xc4;" u2="y" k="91" />
    <hkern u1="&#xc4;" u2="v" k="91" />
    <hkern u1="&#xc4;" u2="\" k="169" />
    <hkern u1="&#xc4;" u2="Y" k="182" />
    <hkern u1="&#xc4;" u2="W" k="102" />
    <hkern u1="&#xc4;" u2="V" k="169" />
    <hkern u1="&#xc4;" u2="U" k="52" />
    <hkern u1="&#xc4;" u2="T" k="147" />
    <hkern u1="&#xc4;" u2="Q" k="51" />
    <hkern u1="&#xc4;" u2="O" k="51" />
    <hkern u1="&#xc4;" u2="J" k="-56" />
    <hkern u1="&#xc4;" u2="G" k="51" />
    <hkern u1="&#xc4;" u2="C" k="51" />
    <hkern u1="&#xc4;" u2="&#x40;" k="51" />
    <hkern u1="&#xc4;" u2="&#x3f;" k="63" />
    <hkern u1="&#xc4;" u2="&#x2d;" k="67" />
    <hkern u1="&#xc4;" u2="&#x2a;" k="191" />
    <hkern u1="&#xc4;" u2="&#x27;" k="191" />
    <hkern u1="&#xc4;" u2="&#x22;" k="191" />
    <hkern u1="&#xc5;" u2="&#x2122;" k="191" />
    <hkern u1="&#xc5;" u2="&#x203a;" k="67" />
    <hkern u1="&#xc5;" u2="&#x2039;" k="67" />
    <hkern u1="&#xc5;" u2="&#x2022;" k="67" />
    <hkern u1="&#xc5;" u2="&#x201d;" k="191" />
    <hkern u1="&#xc5;" u2="&#x201c;" k="191" />
    <hkern u1="&#xc5;" u2="&#x2019;" k="191" />
    <hkern u1="&#xc5;" u2="&#x2018;" k="191" />
    <hkern u1="&#xc5;" u2="&#x2014;" k="67" />
    <hkern u1="&#xc5;" u2="&#x2013;" k="67" />
    <hkern u1="&#xc5;" u2="&#x178;" k="182" />
    <hkern u1="&#xc5;" u2="&#x152;" k="51" />
    <hkern u1="&#xc5;" u2="&#x106;" k="51" />
    <hkern u1="&#xc5;" u2="&#xff;" k="91" />
    <hkern u1="&#xc5;" u2="&#xfd;" k="91" />
    <hkern u1="&#xc5;" u2="&#xdd;" k="182" />
    <hkern u1="&#xc5;" u2="&#xdc;" k="52" />
    <hkern u1="&#xc5;" u2="&#xdb;" k="52" />
    <hkern u1="&#xc5;" u2="&#xda;" k="52" />
    <hkern u1="&#xc5;" u2="&#xd9;" k="52" />
    <hkern u1="&#xc5;" u2="&#xd8;" k="51" />
    <hkern u1="&#xc5;" u2="&#xd6;" k="51" />
    <hkern u1="&#xc5;" u2="&#xd5;" k="51" />
    <hkern u1="&#xc5;" u2="&#xd4;" k="51" />
    <hkern u1="&#xc5;" u2="&#xd3;" k="51" />
    <hkern u1="&#xc5;" u2="&#xd2;" k="51" />
    <hkern u1="&#xc5;" u2="&#xc7;" k="51" />
    <hkern u1="&#xc5;" u2="&#xbb;" k="67" />
    <hkern u1="&#xc5;" u2="&#xba;" k="191" />
    <hkern u1="&#xc5;" u2="&#xb9;" k="202" />
    <hkern u1="&#xc5;" u2="&#xb7;" k="67" />
    <hkern u1="&#xc5;" u2="&#xb3;" k="202" />
    <hkern u1="&#xc5;" u2="&#xb2;" k="202" />
    <hkern u1="&#xc5;" u2="&#xb0;" k="191" />
    <hkern u1="&#xc5;" u2="&#xae;" k="51" />
    <hkern u1="&#xc5;" u2="&#xad;" k="67" />
    <hkern u1="&#xc5;" u2="&#xab;" k="67" />
    <hkern u1="&#xc5;" u2="&#xaa;" k="191" />
    <hkern u1="&#xc5;" u2="&#xa9;" k="51" />
    <hkern u1="&#xc5;" u2="y" k="91" />
    <hkern u1="&#xc5;" u2="v" k="91" />
    <hkern u1="&#xc5;" u2="\" k="169" />
    <hkern u1="&#xc5;" u2="Y" k="182" />
    <hkern u1="&#xc5;" u2="W" k="102" />
    <hkern u1="&#xc5;" u2="V" k="169" />
    <hkern u1="&#xc5;" u2="U" k="52" />
    <hkern u1="&#xc5;" u2="T" k="147" />
    <hkern u1="&#xc5;" u2="Q" k="51" />
    <hkern u1="&#xc5;" u2="O" k="51" />
    <hkern u1="&#xc5;" u2="J" k="-56" />
    <hkern u1="&#xc5;" u2="G" k="51" />
    <hkern u1="&#xc5;" u2="C" k="51" />
    <hkern u1="&#xc5;" u2="&#x40;" k="51" />
    <hkern u1="&#xc5;" u2="&#x3f;" k="63" />
    <hkern u1="&#xc5;" u2="&#x2d;" k="67" />
    <hkern u1="&#xc5;" u2="&#x2a;" k="191" />
    <hkern u1="&#xc5;" u2="&#x27;" k="191" />
    <hkern u1="&#xc5;" u2="&#x22;" k="191" />
    <hkern u1="&#xc7;" u2="&#x203a;" k="144" />
    <hkern u1="&#xc7;" u2="&#x2039;" k="144" />
    <hkern u1="&#xc7;" u2="&#x2022;" k="144" />
    <hkern u1="&#xc7;" u2="&#x2014;" k="144" />
    <hkern u1="&#xc7;" u2="&#x2013;" k="144" />
    <hkern u1="&#xc7;" u2="&#xbb;" k="144" />
    <hkern u1="&#xc7;" u2="&#xb7;" k="144" />
    <hkern u1="&#xc7;" u2="&#xad;" k="144" />
    <hkern u1="&#xc7;" u2="&#xab;" k="144" />
    <hkern u1="&#xc7;" u2="&#x2d;" k="144" />
    <hkern u1="&#xd0;" u2="&#x2206;" k="51" />
    <hkern u1="&#xd0;" u2="&#x2122;" k="42" />
    <hkern u1="&#xd0;" u2="&#x201e;" k="52" />
    <hkern u1="&#xd0;" u2="&#x201d;" k="42" />
    <hkern u1="&#xd0;" u2="&#x201c;" k="42" />
    <hkern u1="&#xd0;" u2="&#x201a;" k="52" />
    <hkern u1="&#xd0;" u2="&#x2019;" k="42" />
    <hkern u1="&#xd0;" u2="&#x2018;" k="42" />
    <hkern u1="&#xd0;" u2="&#x17d;" k="64" />
    <hkern u1="&#xd0;" u2="&#x17b;" k="64" />
    <hkern u1="&#xd0;" u2="&#x179;" k="64" />
    <hkern u1="&#xd0;" u2="&#x178;" k="80" />
    <hkern u1="&#xd0;" u2="&#x104;" k="51" />
    <hkern u1="&#xd0;" u2="&#xdd;" k="80" />
    <hkern u1="&#xd0;" u2="&#xc6;" k="51" />
    <hkern u1="&#xd0;" u2="&#xc5;" k="51" />
    <hkern u1="&#xd0;" u2="&#xc4;" k="51" />
    <hkern u1="&#xd0;" u2="&#xc3;" k="51" />
    <hkern u1="&#xd0;" u2="&#xc2;" k="51" />
    <hkern u1="&#xd0;" u2="&#xc1;" k="51" />
    <hkern u1="&#xd0;" u2="&#xc0;" k="51" />
    <hkern u1="&#xd0;" u2="&#xba;" k="42" />
    <hkern u1="&#xd0;" u2="&#xb0;" k="42" />
    <hkern u1="&#xd0;" u2="&#xaa;" k="42" />
    <hkern u1="&#xd0;" u2="&#x7d;" k="40" />
    <hkern u1="&#xd0;" u2="]" k="40" />
    <hkern u1="&#xd0;" u2="\" k="56" />
    <hkern u1="&#xd0;" u2="Z" k="64" />
    <hkern u1="&#xd0;" u2="Y" k="80" />
    <hkern u1="&#xd0;" u2="X" k="30" />
    <hkern u1="&#xd0;" u2="V" k="56" />
    <hkern u1="&#xd0;" u2="T" k="78" />
    <hkern u1="&#xd0;" u2="A" k="51" />
    <hkern u1="&#xd0;" u2="&#x2f;" k="51" />
    <hkern u1="&#xd0;" u2="&#x2e;" k="52" />
    <hkern u1="&#xd0;" u2="&#x2c;" k="52" />
    <hkern u1="&#xd0;" u2="&#x2a;" k="42" />
    <hkern u1="&#xd0;" u2="&#x29;" k="40" />
    <hkern u1="&#xd0;" u2="&#x27;" k="42" />
    <hkern u1="&#xd0;" u2="&#x26;" k="51" />
    <hkern u1="&#xd0;" u2="&#x22;" k="42" />
    <hkern u1="&#xd2;" u2="&#x2206;" k="51" />
    <hkern u1="&#xd2;" u2="&#x2122;" k="42" />
    <hkern u1="&#xd2;" u2="&#x201e;" k="52" />
    <hkern u1="&#xd2;" u2="&#x201d;" k="42" />
    <hkern u1="&#xd2;" u2="&#x201c;" k="42" />
    <hkern u1="&#xd2;" u2="&#x201a;" k="52" />
    <hkern u1="&#xd2;" u2="&#x2019;" k="42" />
    <hkern u1="&#xd2;" u2="&#x2018;" k="42" />
    <hkern u1="&#xd2;" u2="&#x17d;" k="64" />
    <hkern u1="&#xd2;" u2="&#x17b;" k="64" />
    <hkern u1="&#xd2;" u2="&#x179;" k="64" />
    <hkern u1="&#xd2;" u2="&#x178;" k="80" />
    <hkern u1="&#xd2;" u2="&#x104;" k="51" />
    <hkern u1="&#xd2;" u2="&#xdd;" k="80" />
    <hkern u1="&#xd2;" u2="&#xc6;" k="51" />
    <hkern u1="&#xd2;" u2="&#xc5;" k="51" />
    <hkern u1="&#xd2;" u2="&#xc4;" k="51" />
    <hkern u1="&#xd2;" u2="&#xc3;" k="51" />
    <hkern u1="&#xd2;" u2="&#xc2;" k="51" />
    <hkern u1="&#xd2;" u2="&#xc1;" k="51" />
    <hkern u1="&#xd2;" u2="&#xc0;" k="51" />
    <hkern u1="&#xd2;" u2="&#xba;" k="42" />
    <hkern u1="&#xd2;" u2="&#xb0;" k="42" />
    <hkern u1="&#xd2;" u2="&#xaa;" k="42" />
    <hkern u1="&#xd2;" u2="&#x7d;" k="40" />
    <hkern u1="&#xd2;" u2="]" k="40" />
    <hkern u1="&#xd2;" u2="\" k="56" />
    <hkern u1="&#xd2;" u2="Z" k="64" />
    <hkern u1="&#xd2;" u2="Y" k="80" />
    <hkern u1="&#xd2;" u2="X" k="30" />
    <hkern u1="&#xd2;" u2="V" k="56" />
    <hkern u1="&#xd2;" u2="T" k="78" />
    <hkern u1="&#xd2;" u2="A" k="51" />
    <hkern u1="&#xd2;" u2="&#x2f;" k="51" />
    <hkern u1="&#xd2;" u2="&#x2e;" k="52" />
    <hkern u1="&#xd2;" u2="&#x2c;" k="52" />
    <hkern u1="&#xd2;" u2="&#x2a;" k="42" />
    <hkern u1="&#xd2;" u2="&#x29;" k="40" />
    <hkern u1="&#xd2;" u2="&#x27;" k="42" />
    <hkern u1="&#xd2;" u2="&#x26;" k="51" />
    <hkern u1="&#xd2;" u2="&#x22;" k="42" />
    <hkern u1="&#xd3;" u2="&#x2206;" k="51" />
    <hkern u1="&#xd3;" u2="&#x2122;" k="42" />
    <hkern u1="&#xd3;" u2="&#x201e;" k="52" />
    <hkern u1="&#xd3;" u2="&#x201d;" k="42" />
    <hkern u1="&#xd3;" u2="&#x201c;" k="42" />
    <hkern u1="&#xd3;" u2="&#x201a;" k="52" />
    <hkern u1="&#xd3;" u2="&#x2019;" k="42" />
    <hkern u1="&#xd3;" u2="&#x2018;" k="42" />
    <hkern u1="&#xd3;" u2="&#x17d;" k="64" />
    <hkern u1="&#xd3;" u2="&#x17b;" k="64" />
    <hkern u1="&#xd3;" u2="&#x179;" k="64" />
    <hkern u1="&#xd3;" u2="&#x178;" k="80" />
    <hkern u1="&#xd3;" u2="&#x104;" k="51" />
    <hkern u1="&#xd3;" u2="&#xdd;" k="80" />
    <hkern u1="&#xd3;" u2="&#xc6;" k="51" />
    <hkern u1="&#xd3;" u2="&#xc5;" k="51" />
    <hkern u1="&#xd3;" u2="&#xc4;" k="51" />
    <hkern u1="&#xd3;" u2="&#xc3;" k="51" />
    <hkern u1="&#xd3;" u2="&#xc2;" k="51" />
    <hkern u1="&#xd3;" u2="&#xc1;" k="51" />
    <hkern u1="&#xd3;" u2="&#xc0;" k="51" />
    <hkern u1="&#xd3;" u2="&#xba;" k="42" />
    <hkern u1="&#xd3;" u2="&#xb0;" k="42" />
    <hkern u1="&#xd3;" u2="&#xaa;" k="42" />
    <hkern u1="&#xd3;" u2="&#x7d;" k="40" />
    <hkern u1="&#xd3;" u2="]" k="40" />
    <hkern u1="&#xd3;" u2="\" k="56" />
    <hkern u1="&#xd3;" u2="Z" k="64" />
    <hkern u1="&#xd3;" u2="Y" k="80" />
    <hkern u1="&#xd3;" u2="X" k="30" />
    <hkern u1="&#xd3;" u2="V" k="56" />
    <hkern u1="&#xd3;" u2="T" k="78" />
    <hkern u1="&#xd3;" u2="A" k="51" />
    <hkern u1="&#xd3;" u2="&#x2f;" k="51" />
    <hkern u1="&#xd3;" u2="&#x2e;" k="52" />
    <hkern u1="&#xd3;" u2="&#x2c;" k="52" />
    <hkern u1="&#xd3;" u2="&#x2a;" k="42" />
    <hkern u1="&#xd3;" u2="&#x29;" k="40" />
    <hkern u1="&#xd3;" u2="&#x27;" k="42" />
    <hkern u1="&#xd3;" u2="&#x26;" k="51" />
    <hkern u1="&#xd3;" u2="&#x22;" k="42" />
    <hkern u1="&#xd4;" u2="&#x2206;" k="51" />
    <hkern u1="&#xd4;" u2="&#x2122;" k="42" />
    <hkern u1="&#xd4;" u2="&#x201e;" k="52" />
    <hkern u1="&#xd4;" u2="&#x201d;" k="42" />
    <hkern u1="&#xd4;" u2="&#x201c;" k="42" />
    <hkern u1="&#xd4;" u2="&#x201a;" k="52" />
    <hkern u1="&#xd4;" u2="&#x2019;" k="42" />
    <hkern u1="&#xd4;" u2="&#x2018;" k="42" />
    <hkern u1="&#xd4;" u2="&#x17d;" k="64" />
    <hkern u1="&#xd4;" u2="&#x17b;" k="64" />
    <hkern u1="&#xd4;" u2="&#x179;" k="64" />
    <hkern u1="&#xd4;" u2="&#x178;" k="80" />
    <hkern u1="&#xd4;" u2="&#x104;" k="51" />
    <hkern u1="&#xd4;" u2="&#xdd;" k="80" />
    <hkern u1="&#xd4;" u2="&#xc6;" k="51" />
    <hkern u1="&#xd4;" u2="&#xc5;" k="51" />
    <hkern u1="&#xd4;" u2="&#xc4;" k="51" />
    <hkern u1="&#xd4;" u2="&#xc3;" k="51" />
    <hkern u1="&#xd4;" u2="&#xc2;" k="51" />
    <hkern u1="&#xd4;" u2="&#xc1;" k="51" />
    <hkern u1="&#xd4;" u2="&#xc0;" k="51" />
    <hkern u1="&#xd4;" u2="&#xba;" k="42" />
    <hkern u1="&#xd4;" u2="&#xb0;" k="42" />
    <hkern u1="&#xd4;" u2="&#xaa;" k="42" />
    <hkern u1="&#xd4;" u2="&#x7d;" k="40" />
    <hkern u1="&#xd4;" u2="]" k="40" />
    <hkern u1="&#xd4;" u2="\" k="56" />
    <hkern u1="&#xd4;" u2="Z" k="64" />
    <hkern u1="&#xd4;" u2="Y" k="80" />
    <hkern u1="&#xd4;" u2="X" k="30" />
    <hkern u1="&#xd4;" u2="V" k="56" />
    <hkern u1="&#xd4;" u2="T" k="78" />
    <hkern u1="&#xd4;" u2="A" k="51" />
    <hkern u1="&#xd4;" u2="&#x2f;" k="51" />
    <hkern u1="&#xd4;" u2="&#x2e;" k="52" />
    <hkern u1="&#xd4;" u2="&#x2c;" k="52" />
    <hkern u1="&#xd4;" u2="&#x2a;" k="42" />
    <hkern u1="&#xd4;" u2="&#x29;" k="40" />
    <hkern u1="&#xd4;" u2="&#x27;" k="42" />
    <hkern u1="&#xd4;" u2="&#x26;" k="51" />
    <hkern u1="&#xd4;" u2="&#x22;" k="42" />
    <hkern u1="&#xd5;" u2="&#x2206;" k="51" />
    <hkern u1="&#xd5;" u2="&#x2122;" k="42" />
    <hkern u1="&#xd5;" u2="&#x201e;" k="52" />
    <hkern u1="&#xd5;" u2="&#x201d;" k="42" />
    <hkern u1="&#xd5;" u2="&#x201c;" k="42" />
    <hkern u1="&#xd5;" u2="&#x201a;" k="52" />
    <hkern u1="&#xd5;" u2="&#x2019;" k="42" />
    <hkern u1="&#xd5;" u2="&#x2018;" k="42" />
    <hkern u1="&#xd5;" u2="&#x17d;" k="64" />
    <hkern u1="&#xd5;" u2="&#x17b;" k="64" />
    <hkern u1="&#xd5;" u2="&#x179;" k="64" />
    <hkern u1="&#xd5;" u2="&#x178;" k="80" />
    <hkern u1="&#xd5;" u2="&#x104;" k="51" />
    <hkern u1="&#xd5;" u2="&#xdd;" k="80" />
    <hkern u1="&#xd5;" u2="&#xc6;" k="51" />
    <hkern u1="&#xd5;" u2="&#xc5;" k="51" />
    <hkern u1="&#xd5;" u2="&#xc4;" k="51" />
    <hkern u1="&#xd5;" u2="&#xc3;" k="51" />
    <hkern u1="&#xd5;" u2="&#xc2;" k="51" />
    <hkern u1="&#xd5;" u2="&#xc1;" k="51" />
    <hkern u1="&#xd5;" u2="&#xc0;" k="51" />
    <hkern u1="&#xd5;" u2="&#xba;" k="42" />
    <hkern u1="&#xd5;" u2="&#xb0;" k="42" />
    <hkern u1="&#xd5;" u2="&#xaa;" k="42" />
    <hkern u1="&#xd5;" u2="&#x7d;" k="40" />
    <hkern u1="&#xd5;" u2="]" k="40" />
    <hkern u1="&#xd5;" u2="\" k="56" />
    <hkern u1="&#xd5;" u2="Z" k="64" />
    <hkern u1="&#xd5;" u2="Y" k="80" />
    <hkern u1="&#xd5;" u2="X" k="30" />
    <hkern u1="&#xd5;" u2="V" k="56" />
    <hkern u1="&#xd5;" u2="T" k="78" />
    <hkern u1="&#xd5;" u2="A" k="51" />
    <hkern u1="&#xd5;" u2="&#x2f;" k="51" />
    <hkern u1="&#xd5;" u2="&#x2e;" k="52" />
    <hkern u1="&#xd5;" u2="&#x2c;" k="52" />
    <hkern u1="&#xd5;" u2="&#x2a;" k="42" />
    <hkern u1="&#xd5;" u2="&#x29;" k="40" />
    <hkern u1="&#xd5;" u2="&#x27;" k="42" />
    <hkern u1="&#xd5;" u2="&#x26;" k="51" />
    <hkern u1="&#xd5;" u2="&#x22;" k="42" />
    <hkern u1="&#xd6;" u2="&#x2206;" k="51" />
    <hkern u1="&#xd6;" u2="&#x2122;" k="42" />
    <hkern u1="&#xd6;" u2="&#x201e;" k="52" />
    <hkern u1="&#xd6;" u2="&#x201d;" k="42" />
    <hkern u1="&#xd6;" u2="&#x201c;" k="42" />
    <hkern u1="&#xd6;" u2="&#x201a;" k="52" />
    <hkern u1="&#xd6;" u2="&#x2019;" k="42" />
    <hkern u1="&#xd6;" u2="&#x2018;" k="42" />
    <hkern u1="&#xd6;" u2="&#x17d;" k="64" />
    <hkern u1="&#xd6;" u2="&#x17b;" k="64" />
    <hkern u1="&#xd6;" u2="&#x179;" k="64" />
    <hkern u1="&#xd6;" u2="&#x178;" k="80" />
    <hkern u1="&#xd6;" u2="&#x104;" k="51" />
    <hkern u1="&#xd6;" u2="&#xdd;" k="80" />
    <hkern u1="&#xd6;" u2="&#xc6;" k="51" />
    <hkern u1="&#xd6;" u2="&#xc5;" k="51" />
    <hkern u1="&#xd6;" u2="&#xc4;" k="51" />
    <hkern u1="&#xd6;" u2="&#xc3;" k="51" />
    <hkern u1="&#xd6;" u2="&#xc2;" k="51" />
    <hkern u1="&#xd6;" u2="&#xc1;" k="51" />
    <hkern u1="&#xd6;" u2="&#xc0;" k="51" />
    <hkern u1="&#xd6;" u2="&#xba;" k="42" />
    <hkern u1="&#xd6;" u2="&#xb0;" k="42" />
    <hkern u1="&#xd6;" u2="&#xaa;" k="42" />
    <hkern u1="&#xd6;" u2="&#x7d;" k="40" />
    <hkern u1="&#xd6;" u2="]" k="40" />
    <hkern u1="&#xd6;" u2="\" k="56" />
    <hkern u1="&#xd6;" u2="Z" k="64" />
    <hkern u1="&#xd6;" u2="Y" k="80" />
    <hkern u1="&#xd6;" u2="X" k="30" />
    <hkern u1="&#xd6;" u2="V" k="56" />
    <hkern u1="&#xd6;" u2="T" k="78" />
    <hkern u1="&#xd6;" u2="A" k="51" />
    <hkern u1="&#xd6;" u2="&#x2f;" k="51" />
    <hkern u1="&#xd6;" u2="&#x2e;" k="52" />
    <hkern u1="&#xd6;" u2="&#x2c;" k="52" />
    <hkern u1="&#xd6;" u2="&#x2a;" k="42" />
    <hkern u1="&#xd6;" u2="&#x29;" k="40" />
    <hkern u1="&#xd6;" u2="&#x27;" k="42" />
    <hkern u1="&#xd6;" u2="&#x26;" k="51" />
    <hkern u1="&#xd6;" u2="&#x22;" k="42" />
    <hkern u1="&#xd8;" u2="&#x2206;" k="51" />
    <hkern u1="&#xd8;" u2="&#x2122;" k="42" />
    <hkern u1="&#xd8;" u2="&#x201e;" k="52" />
    <hkern u1="&#xd8;" u2="&#x201d;" k="42" />
    <hkern u1="&#xd8;" u2="&#x201c;" k="42" />
    <hkern u1="&#xd8;" u2="&#x201a;" k="52" />
    <hkern u1="&#xd8;" u2="&#x2019;" k="42" />
    <hkern u1="&#xd8;" u2="&#x2018;" k="42" />
    <hkern u1="&#xd8;" u2="&#x17d;" k="64" />
    <hkern u1="&#xd8;" u2="&#x17b;" k="64" />
    <hkern u1="&#xd8;" u2="&#x179;" k="64" />
    <hkern u1="&#xd8;" u2="&#x178;" k="80" />
    <hkern u1="&#xd8;" u2="&#x104;" k="51" />
    <hkern u1="&#xd8;" u2="&#xdd;" k="80" />
    <hkern u1="&#xd8;" u2="&#xc6;" k="51" />
    <hkern u1="&#xd8;" u2="&#xc5;" k="51" />
    <hkern u1="&#xd8;" u2="&#xc4;" k="51" />
    <hkern u1="&#xd8;" u2="&#xc3;" k="51" />
    <hkern u1="&#xd8;" u2="&#xc2;" k="51" />
    <hkern u1="&#xd8;" u2="&#xc1;" k="51" />
    <hkern u1="&#xd8;" u2="&#xc0;" k="51" />
    <hkern u1="&#xd8;" u2="&#xba;" k="42" />
    <hkern u1="&#xd8;" u2="&#xb0;" k="42" />
    <hkern u1="&#xd8;" u2="&#xaa;" k="42" />
    <hkern u1="&#xd8;" u2="&#x7d;" k="40" />
    <hkern u1="&#xd8;" u2="]" k="40" />
    <hkern u1="&#xd8;" u2="\" k="56" />
    <hkern u1="&#xd8;" u2="Z" k="64" />
    <hkern u1="&#xd8;" u2="Y" k="80" />
    <hkern u1="&#xd8;" u2="X" k="30" />
    <hkern u1="&#xd8;" u2="V" k="56" />
    <hkern u1="&#xd8;" u2="T" k="78" />
    <hkern u1="&#xd8;" u2="A" k="51" />
    <hkern u1="&#xd8;" u2="&#x2f;" k="51" />
    <hkern u1="&#xd8;" u2="&#x2e;" k="52" />
    <hkern u1="&#xd8;" u2="&#x2c;" k="52" />
    <hkern u1="&#xd8;" u2="&#x2a;" k="42" />
    <hkern u1="&#xd8;" u2="&#x29;" k="40" />
    <hkern u1="&#xd8;" u2="&#x27;" k="42" />
    <hkern u1="&#xd8;" u2="&#x26;" k="51" />
    <hkern u1="&#xd8;" u2="&#x22;" k="42" />
    <hkern u1="&#xd9;" u2="&#x2206;" k="52" />
    <hkern u1="&#xd9;" u2="&#x201e;" k="50" />
    <hkern u1="&#xd9;" u2="&#x201a;" k="50" />
    <hkern u1="&#xd9;" u2="&#x104;" k="52" />
    <hkern u1="&#xd9;" u2="&#xc6;" k="52" />
    <hkern u1="&#xd9;" u2="&#xc5;" k="52" />
    <hkern u1="&#xd9;" u2="&#xc4;" k="52" />
    <hkern u1="&#xd9;" u2="&#xc3;" k="52" />
    <hkern u1="&#xd9;" u2="&#xc2;" k="52" />
    <hkern u1="&#xd9;" u2="&#xc1;" k="52" />
    <hkern u1="&#xd9;" u2="&#xc0;" k="52" />
    <hkern u1="&#xd9;" u2="A" k="52" />
    <hkern u1="&#xd9;" u2="&#x2f;" k="52" />
    <hkern u1="&#xd9;" u2="&#x2e;" k="50" />
    <hkern u1="&#xd9;" u2="&#x2c;" k="50" />
    <hkern u1="&#xd9;" u2="&#x26;" k="52" />
    <hkern u1="&#xda;" u2="&#x2206;" k="52" />
    <hkern u1="&#xda;" u2="&#x201e;" k="50" />
    <hkern u1="&#xda;" u2="&#x201a;" k="50" />
    <hkern u1="&#xda;" u2="&#x104;" k="52" />
    <hkern u1="&#xda;" u2="&#xc6;" k="52" />
    <hkern u1="&#xda;" u2="&#xc5;" k="52" />
    <hkern u1="&#xda;" u2="&#xc4;" k="52" />
    <hkern u1="&#xda;" u2="&#xc3;" k="52" />
    <hkern u1="&#xda;" u2="&#xc2;" k="52" />
    <hkern u1="&#xda;" u2="&#xc1;" k="52" />
    <hkern u1="&#xda;" u2="&#xc0;" k="52" />
    <hkern u1="&#xda;" u2="A" k="52" />
    <hkern u1="&#xda;" u2="&#x2f;" k="52" />
    <hkern u1="&#xda;" u2="&#x2e;" k="50" />
    <hkern u1="&#xda;" u2="&#x2c;" k="50" />
    <hkern u1="&#xda;" u2="&#x26;" k="52" />
    <hkern u1="&#xdb;" u2="&#x2206;" k="52" />
    <hkern u1="&#xdb;" u2="&#x201e;" k="50" />
    <hkern u1="&#xdb;" u2="&#x201a;" k="50" />
    <hkern u1="&#xdb;" u2="&#x104;" k="52" />
    <hkern u1="&#xdb;" u2="&#xc6;" k="52" />
    <hkern u1="&#xdb;" u2="&#xc5;" k="52" />
    <hkern u1="&#xdb;" u2="&#xc4;" k="52" />
    <hkern u1="&#xdb;" u2="&#xc3;" k="52" />
    <hkern u1="&#xdb;" u2="&#xc2;" k="52" />
    <hkern u1="&#xdb;" u2="&#xc1;" k="52" />
    <hkern u1="&#xdb;" u2="&#xc0;" k="52" />
    <hkern u1="&#xdb;" u2="A" k="52" />
    <hkern u1="&#xdb;" u2="&#x2f;" k="52" />
    <hkern u1="&#xdb;" u2="&#x2e;" k="50" />
    <hkern u1="&#xdb;" u2="&#x2c;" k="50" />
    <hkern u1="&#xdb;" u2="&#x26;" k="52" />
    <hkern u1="&#xdc;" u2="&#x2206;" k="52" />
    <hkern u1="&#xdc;" u2="&#x201e;" k="50" />
    <hkern u1="&#xdc;" u2="&#x201a;" k="50" />
    <hkern u1="&#xdc;" u2="&#x104;" k="52" />
    <hkern u1="&#xdc;" u2="&#xc6;" k="52" />
    <hkern u1="&#xdc;" u2="&#xc5;" k="52" />
    <hkern u1="&#xdc;" u2="&#xc4;" k="52" />
    <hkern u1="&#xdc;" u2="&#xc3;" k="52" />
    <hkern u1="&#xdc;" u2="&#xc2;" k="52" />
    <hkern u1="&#xdc;" u2="&#xc1;" k="52" />
    <hkern u1="&#xdc;" u2="&#xc0;" k="52" />
    <hkern u1="&#xdc;" u2="A" k="52" />
    <hkern u1="&#xdc;" u2="&#x2f;" k="52" />
    <hkern u1="&#xdc;" u2="&#x2e;" k="50" />
    <hkern u1="&#xdc;" u2="&#x2c;" k="50" />
    <hkern u1="&#xdc;" u2="&#x26;" k="52" />
    <hkern u1="&#xdd;" u2="&#x2206;" k="182" />
    <hkern u1="&#xdd;" u2="&#x2122;" k="-36" />
    <hkern u1="&#xdd;" u2="&#x203a;" k="160" />
    <hkern u1="&#xdd;" u2="&#x2039;" k="160" />
    <hkern u1="&#xdd;" u2="&#x2022;" k="160" />
    <hkern u1="&#xdd;" u2="&#x201e;" k="167" />
    <hkern u1="&#xdd;" u2="&#x201d;" k="-36" />
    <hkern u1="&#xdd;" u2="&#x201c;" k="-36" />
    <hkern u1="&#xdd;" u2="&#x201a;" k="167" />
    <hkern u1="&#xdd;" u2="&#x2019;" k="-36" />
    <hkern u1="&#xdd;" u2="&#x2018;" k="-36" />
    <hkern u1="&#xdd;" u2="&#x2014;" k="160" />
    <hkern u1="&#xdd;" u2="&#x2013;" k="160" />
    <hkern u1="&#xdd;" u2="&#x161;" k="139" />
    <hkern u1="&#xdd;" u2="&#x15b;" k="139" />
    <hkern u1="&#xdd;" u2="&#x153;" k="160" />
    <hkern u1="&#xdd;" u2="&#x152;" k="80" />
    <hkern u1="&#xdd;" u2="&#x144;" k="131" />
    <hkern u1="&#xdd;" u2="&#x131;" k="131" />
    <hkern u1="&#xdd;" u2="&#x119;" k="160" />
    <hkern u1="&#xdd;" u2="&#x107;" k="160" />
    <hkern u1="&#xdd;" u2="&#x106;" k="80" />
    <hkern u1="&#xdd;" u2="&#x105;" k="145" />
    <hkern u1="&#xdd;" u2="&#x104;" k="182" />
    <hkern u1="&#xdd;" u2="&#xff;" k="100" />
    <hkern u1="&#xdd;" u2="&#xfd;" k="100" />
    <hkern u1="&#xdd;" u2="&#xfc;" k="131" />
    <hkern u1="&#xdd;" u2="&#xfb;" k="131" />
    <hkern u1="&#xdd;" u2="&#xfa;" k="131" />
    <hkern u1="&#xdd;" u2="&#xf9;" k="131" />
    <hkern u1="&#xdd;" u2="&#xf8;" k="160" />
    <hkern u1="&#xdd;" u2="&#xf6;" k="160" />
    <hkern u1="&#xdd;" u2="&#xf5;" k="160" />
    <hkern u1="&#xdd;" u2="&#xf4;" k="160" />
    <hkern u1="&#xdd;" u2="&#xf3;" k="160" />
    <hkern u1="&#xdd;" u2="&#xf2;" k="160" />
    <hkern u1="&#xdd;" u2="&#xf1;" k="131" />
    <hkern u1="&#xdd;" u2="&#xf0;" k="160" />
    <hkern u1="&#xdd;" u2="&#xeb;" k="160" />
    <hkern u1="&#xdd;" u2="&#xea;" k="160" />
    <hkern u1="&#xdd;" u2="&#xe9;" k="160" />
    <hkern u1="&#xdd;" u2="&#xe8;" k="160" />
    <hkern u1="&#xdd;" u2="&#xe7;" k="160" />
    <hkern u1="&#xdd;" u2="&#xe6;" k="145" />
    <hkern u1="&#xdd;" u2="&#xe5;" k="145" />
    <hkern u1="&#xdd;" u2="&#xe4;" k="145" />
    <hkern u1="&#xdd;" u2="&#xe3;" k="145" />
    <hkern u1="&#xdd;" u2="&#xe2;" k="145" />
    <hkern u1="&#xdd;" u2="&#xe1;" k="145" />
    <hkern u1="&#xdd;" u2="&#xe0;" k="145" />
    <hkern u1="&#xdd;" u2="&#xd8;" k="80" />
    <hkern u1="&#xdd;" u2="&#xd6;" k="80" />
    <hkern u1="&#xdd;" u2="&#xd5;" k="80" />
    <hkern u1="&#xdd;" u2="&#xd4;" k="80" />
    <hkern u1="&#xdd;" u2="&#xd3;" k="80" />
    <hkern u1="&#xdd;" u2="&#xd2;" k="80" />
    <hkern u1="&#xdd;" u2="&#xc7;" k="80" />
    <hkern u1="&#xdd;" u2="&#xc6;" k="182" />
    <hkern u1="&#xdd;" u2="&#xc5;" k="182" />
    <hkern u1="&#xdd;" u2="&#xc4;" k="182" />
    <hkern u1="&#xdd;" u2="&#xc3;" k="182" />
    <hkern u1="&#xdd;" u2="&#xc2;" k="182" />
    <hkern u1="&#xdd;" u2="&#xc1;" k="182" />
    <hkern u1="&#xdd;" u2="&#xc0;" k="182" />
    <hkern u1="&#xdd;" u2="&#xbb;" k="160" />
    <hkern u1="&#xdd;" u2="&#xba;" k="-36" />
    <hkern u1="&#xdd;" u2="&#xb9;" k="-56" />
    <hkern u1="&#xdd;" u2="&#xb7;" k="160" />
    <hkern u1="&#xdd;" u2="&#xb5;" k="131" />
    <hkern u1="&#xdd;" u2="&#xb3;" k="-56" />
    <hkern u1="&#xdd;" u2="&#xb2;" k="-56" />
    <hkern u1="&#xdd;" u2="&#xb0;" k="-36" />
    <hkern u1="&#xdd;" u2="&#xae;" k="80" />
    <hkern u1="&#xdd;" u2="&#xad;" k="160" />
    <hkern u1="&#xdd;" u2="&#xab;" k="160" />
    <hkern u1="&#xdd;" u2="&#xaa;" k="-36" />
    <hkern u1="&#xdd;" u2="&#xa9;" k="80" />
    <hkern u1="&#xdd;" u2="y" k="100" />
    <hkern u1="&#xdd;" u2="x" k="136" />
    <hkern u1="&#xdd;" u2="w" k="96" />
    <hkern u1="&#xdd;" u2="v" k="100" />
    <hkern u1="&#xdd;" u2="u" k="131" />
    <hkern u1="&#xdd;" u2="s" k="139" />
    <hkern u1="&#xdd;" u2="r" k="131" />
    <hkern u1="&#xdd;" u2="q" k="160" />
    <hkern u1="&#xdd;" u2="p" k="131" />
    <hkern u1="&#xdd;" u2="o" k="160" />
    <hkern u1="&#xdd;" u2="n" k="131" />
    <hkern u1="&#xdd;" u2="m" k="131" />
    <hkern u1="&#xdd;" u2="g" k="176" />
    <hkern u1="&#xdd;" u2="e" k="160" />
    <hkern u1="&#xdd;" u2="d" k="160" />
    <hkern u1="&#xdd;" u2="c" k="160" />
    <hkern u1="&#xdd;" u2="a" k="145" />
    <hkern u1="&#xdd;" u2="Q" k="80" />
    <hkern u1="&#xdd;" u2="O" k="80" />
    <hkern u1="&#xdd;" u2="J" k="200" />
    <hkern u1="&#xdd;" u2="G" k="80" />
    <hkern u1="&#xdd;" u2="C" k="80" />
    <hkern u1="&#xdd;" u2="A" k="182" />
    <hkern u1="&#xdd;" u2="&#x40;" k="80" />
    <hkern u1="&#xdd;" u2="&#x3f;" k="-32" />
    <hkern u1="&#xdd;" u2="&#x3b;" k="131" />
    <hkern u1="&#xdd;" u2="&#x3a;" k="131" />
    <hkern u1="&#xdd;" u2="&#x2f;" k="182" />
    <hkern u1="&#xdd;" u2="&#x2e;" k="167" />
    <hkern u1="&#xdd;" u2="&#x2d;" k="160" />
    <hkern u1="&#xdd;" u2="&#x2c;" k="167" />
    <hkern u1="&#xdd;" u2="&#x2a;" k="-36" />
    <hkern u1="&#xdd;" u2="&#x27;" k="-36" />
    <hkern u1="&#xdd;" u2="&#x26;" k="182" />
    <hkern u1="&#xdd;" u2="&#x22;" k="-36" />
    <hkern u1="&#xde;" u2="&#x2206;" k="51" />
    <hkern u1="&#xde;" u2="&#x2122;" k="42" />
    <hkern u1="&#xde;" u2="&#x201e;" k="52" />
    <hkern u1="&#xde;" u2="&#x201d;" k="42" />
    <hkern u1="&#xde;" u2="&#x201c;" k="42" />
    <hkern u1="&#xde;" u2="&#x201a;" k="52" />
    <hkern u1="&#xde;" u2="&#x2019;" k="42" />
    <hkern u1="&#xde;" u2="&#x2018;" k="42" />
    <hkern u1="&#xde;" u2="&#x17d;" k="64" />
    <hkern u1="&#xde;" u2="&#x17b;" k="64" />
    <hkern u1="&#xde;" u2="&#x179;" k="64" />
    <hkern u1="&#xde;" u2="&#x178;" k="80" />
    <hkern u1="&#xde;" u2="&#x104;" k="51" />
    <hkern u1="&#xde;" u2="&#xdd;" k="80" />
    <hkern u1="&#xde;" u2="&#xc6;" k="51" />
    <hkern u1="&#xde;" u2="&#xc5;" k="51" />
    <hkern u1="&#xde;" u2="&#xc4;" k="51" />
    <hkern u1="&#xde;" u2="&#xc3;" k="51" />
    <hkern u1="&#xde;" u2="&#xc2;" k="51" />
    <hkern u1="&#xde;" u2="&#xc1;" k="51" />
    <hkern u1="&#xde;" u2="&#xc0;" k="51" />
    <hkern u1="&#xde;" u2="&#xba;" k="42" />
    <hkern u1="&#xde;" u2="&#xb0;" k="42" />
    <hkern u1="&#xde;" u2="&#xaa;" k="42" />
    <hkern u1="&#xde;" u2="&#x7d;" k="40" />
    <hkern u1="&#xde;" u2="]" k="40" />
    <hkern u1="&#xde;" u2="\" k="56" />
    <hkern u1="&#xde;" u2="Z" k="64" />
    <hkern u1="&#xde;" u2="Y" k="80" />
    <hkern u1="&#xde;" u2="X" k="30" />
    <hkern u1="&#xde;" u2="V" k="56" />
    <hkern u1="&#xde;" u2="T" k="78" />
    <hkern u1="&#xde;" u2="A" k="51" />
    <hkern u1="&#xde;" u2="&#x2f;" k="51" />
    <hkern u1="&#xde;" u2="&#x2e;" k="52" />
    <hkern u1="&#xde;" u2="&#x2c;" k="52" />
    <hkern u1="&#xde;" u2="&#x2a;" k="42" />
    <hkern u1="&#xde;" u2="&#x29;" k="40" />
    <hkern u1="&#xde;" u2="&#x27;" k="42" />
    <hkern u1="&#xde;" u2="&#x26;" k="51" />
    <hkern u1="&#xde;" u2="&#x22;" k="42" />
    <hkern u1="&#xe0;" u2="&#x2122;" k="76" />
    <hkern u1="&#xe0;" u2="&#x201d;" k="76" />
    <hkern u1="&#xe0;" u2="&#x201c;" k="76" />
    <hkern u1="&#xe0;" u2="&#x2019;" k="76" />
    <hkern u1="&#xe0;" u2="&#x2018;" k="76" />
    <hkern u1="&#xe0;" u2="&#xff;" k="36" />
    <hkern u1="&#xe0;" u2="&#xfd;" k="36" />
    <hkern u1="&#xe0;" u2="&#xba;" k="76" />
    <hkern u1="&#xe0;" u2="&#xb9;" k="76" />
    <hkern u1="&#xe0;" u2="&#xb3;" k="76" />
    <hkern u1="&#xe0;" u2="&#xb2;" k="76" />
    <hkern u1="&#xe0;" u2="&#xb0;" k="76" />
    <hkern u1="&#xe0;" u2="&#xaa;" k="76" />
    <hkern u1="&#xe0;" u2="y" k="36" />
    <hkern u1="&#xe0;" u2="w" k="18" />
    <hkern u1="&#xe0;" u2="v" k="36" />
    <hkern u1="&#xe0;" u2="&#x2a;" k="76" />
    <hkern u1="&#xe0;" u2="&#x27;" k="76" />
    <hkern u1="&#xe0;" u2="&#x22;" k="76" />
    <hkern u1="&#xe1;" u2="&#x2122;" k="76" />
    <hkern u1="&#xe1;" u2="&#x201d;" k="76" />
    <hkern u1="&#xe1;" u2="&#x201c;" k="76" />
    <hkern u1="&#xe1;" u2="&#x2019;" k="76" />
    <hkern u1="&#xe1;" u2="&#x2018;" k="76" />
    <hkern u1="&#xe1;" u2="&#xff;" k="36" />
    <hkern u1="&#xe1;" u2="&#xfd;" k="36" />
    <hkern u1="&#xe1;" u2="&#xba;" k="76" />
    <hkern u1="&#xe1;" u2="&#xb9;" k="76" />
    <hkern u1="&#xe1;" u2="&#xb3;" k="76" />
    <hkern u1="&#xe1;" u2="&#xb2;" k="76" />
    <hkern u1="&#xe1;" u2="&#xb0;" k="76" />
    <hkern u1="&#xe1;" u2="&#xaa;" k="76" />
    <hkern u1="&#xe1;" u2="y" k="36" />
    <hkern u1="&#xe1;" u2="w" k="18" />
    <hkern u1="&#xe1;" u2="v" k="36" />
    <hkern u1="&#xe1;" u2="&#x2a;" k="76" />
    <hkern u1="&#xe1;" u2="&#x27;" k="76" />
    <hkern u1="&#xe1;" u2="&#x22;" k="76" />
    <hkern u1="&#xe2;" u2="&#x2122;" k="76" />
    <hkern u1="&#xe2;" u2="&#x201d;" k="76" />
    <hkern u1="&#xe2;" u2="&#x201c;" k="76" />
    <hkern u1="&#xe2;" u2="&#x2019;" k="76" />
    <hkern u1="&#xe2;" u2="&#x2018;" k="76" />
    <hkern u1="&#xe2;" u2="&#xff;" k="36" />
    <hkern u1="&#xe2;" u2="&#xfd;" k="36" />
    <hkern u1="&#xe2;" u2="&#xba;" k="76" />
    <hkern u1="&#xe2;" u2="&#xb9;" k="76" />
    <hkern u1="&#xe2;" u2="&#xb3;" k="76" />
    <hkern u1="&#xe2;" u2="&#xb2;" k="76" />
    <hkern u1="&#xe2;" u2="&#xb0;" k="76" />
    <hkern u1="&#xe2;" u2="&#xaa;" k="76" />
    <hkern u1="&#xe2;" u2="y" k="36" />
    <hkern u1="&#xe2;" u2="w" k="18" />
    <hkern u1="&#xe2;" u2="v" k="36" />
    <hkern u1="&#xe2;" u2="&#x2a;" k="76" />
    <hkern u1="&#xe2;" u2="&#x27;" k="76" />
    <hkern u1="&#xe2;" u2="&#x22;" k="76" />
    <hkern u1="&#xe3;" u2="&#x2122;" k="76" />
    <hkern u1="&#xe3;" u2="&#x201d;" k="76" />
    <hkern u1="&#xe3;" u2="&#x201c;" k="76" />
    <hkern u1="&#xe3;" u2="&#x2019;" k="76" />
    <hkern u1="&#xe3;" u2="&#x2018;" k="76" />
    <hkern u1="&#xe3;" u2="&#xff;" k="36" />
    <hkern u1="&#xe3;" u2="&#xfd;" k="36" />
    <hkern u1="&#xe3;" u2="&#xba;" k="76" />
    <hkern u1="&#xe3;" u2="&#xb9;" k="76" />
    <hkern u1="&#xe3;" u2="&#xb3;" k="76" />
    <hkern u1="&#xe3;" u2="&#xb2;" k="76" />
    <hkern u1="&#xe3;" u2="&#xb0;" k="76" />
    <hkern u1="&#xe3;" u2="&#xaa;" k="76" />
    <hkern u1="&#xe3;" u2="y" k="36" />
    <hkern u1="&#xe3;" u2="w" k="18" />
    <hkern u1="&#xe3;" u2="v" k="36" />
    <hkern u1="&#xe3;" u2="&#x2a;" k="76" />
    <hkern u1="&#xe3;" u2="&#x27;" k="76" />
    <hkern u1="&#xe3;" u2="&#x22;" k="76" />
    <hkern u1="&#xe4;" u2="&#x2122;" k="76" />
    <hkern u1="&#xe4;" u2="&#x201d;" k="76" />
    <hkern u1="&#xe4;" u2="&#x201c;" k="76" />
    <hkern u1="&#xe4;" u2="&#x2019;" k="76" />
    <hkern u1="&#xe4;" u2="&#x2018;" k="76" />
    <hkern u1="&#xe4;" u2="&#xff;" k="36" />
    <hkern u1="&#xe4;" u2="&#xfd;" k="36" />
    <hkern u1="&#xe4;" u2="&#xba;" k="76" />
    <hkern u1="&#xe4;" u2="&#xb9;" k="76" />
    <hkern u1="&#xe4;" u2="&#xb3;" k="76" />
    <hkern u1="&#xe4;" u2="&#xb2;" k="76" />
    <hkern u1="&#xe4;" u2="&#xb0;" k="76" />
    <hkern u1="&#xe4;" u2="&#xaa;" k="76" />
    <hkern u1="&#xe4;" u2="y" k="36" />
    <hkern u1="&#xe4;" u2="w" k="18" />
    <hkern u1="&#xe4;" u2="v" k="36" />
    <hkern u1="&#xe4;" u2="&#x2a;" k="76" />
    <hkern u1="&#xe4;" u2="&#x27;" k="76" />
    <hkern u1="&#xe4;" u2="&#x22;" k="76" />
    <hkern u1="&#xe5;" u2="&#x2122;" k="76" />
    <hkern u1="&#xe5;" u2="&#x201d;" k="76" />
    <hkern u1="&#xe5;" u2="&#x201c;" k="76" />
    <hkern u1="&#xe5;" u2="&#x2019;" k="76" />
    <hkern u1="&#xe5;" u2="&#x2018;" k="76" />
    <hkern u1="&#xe5;" u2="&#xff;" k="36" />
    <hkern u1="&#xe5;" u2="&#xfd;" k="36" />
    <hkern u1="&#xe5;" u2="&#xba;" k="76" />
    <hkern u1="&#xe5;" u2="&#xb9;" k="76" />
    <hkern u1="&#xe5;" u2="&#xb3;" k="76" />
    <hkern u1="&#xe5;" u2="&#xb2;" k="76" />
    <hkern u1="&#xe5;" u2="&#xb0;" k="76" />
    <hkern u1="&#xe5;" u2="&#xaa;" k="76" />
    <hkern u1="&#xe5;" u2="y" k="36" />
    <hkern u1="&#xe5;" u2="w" k="18" />
    <hkern u1="&#xe5;" u2="v" k="36" />
    <hkern u1="&#xe5;" u2="&#x2a;" k="76" />
    <hkern u1="&#xe5;" u2="&#x27;" k="76" />
    <hkern u1="&#xe5;" u2="&#x22;" k="76" />
    <hkern u1="&#xe6;" u2="&#x2122;" k="96" />
    <hkern u1="&#xe6;" u2="&#x201d;" k="96" />
    <hkern u1="&#xe6;" u2="&#x201c;" k="96" />
    <hkern u1="&#xe6;" u2="&#x2019;" k="96" />
    <hkern u1="&#xe6;" u2="&#x2018;" k="96" />
    <hkern u1="&#xe6;" u2="&#xff;" k="33" />
    <hkern u1="&#xe6;" u2="&#xfd;" k="33" />
    <hkern u1="&#xe6;" u2="&#xba;" k="96" />
    <hkern u1="&#xe6;" u2="&#xb0;" k="96" />
    <hkern u1="&#xe6;" u2="&#xaa;" k="96" />
    <hkern u1="&#xe6;" u2="&#x7d;" k="36" />
    <hkern u1="&#xe6;" u2="y" k="33" />
    <hkern u1="&#xe6;" u2="x" k="60" />
    <hkern u1="&#xe6;" u2="v" k="33" />
    <hkern u1="&#xe6;" u2="]" k="36" />
    <hkern u1="&#xe6;" u2="\" k="123" />
    <hkern u1="&#xe6;" u2="W" k="41" />
    <hkern u1="&#xe6;" u2="V" k="123" />
    <hkern u1="&#xe6;" u2="&#x2a;" k="96" />
    <hkern u1="&#xe6;" u2="&#x29;" k="36" />
    <hkern u1="&#xe6;" u2="&#x27;" k="96" />
    <hkern u1="&#xe6;" u2="&#x22;" k="96" />
    <hkern u1="&#xe8;" u2="&#x2122;" k="96" />
    <hkern u1="&#xe8;" u2="&#x201d;" k="96" />
    <hkern u1="&#xe8;" u2="&#x201c;" k="96" />
    <hkern u1="&#xe8;" u2="&#x2019;" k="96" />
    <hkern u1="&#xe8;" u2="&#x2018;" k="96" />
    <hkern u1="&#xe8;" u2="&#xff;" k="33" />
    <hkern u1="&#xe8;" u2="&#xfd;" k="33" />
    <hkern u1="&#xe8;" u2="&#xba;" k="96" />
    <hkern u1="&#xe8;" u2="&#xb0;" k="96" />
    <hkern u1="&#xe8;" u2="&#xaa;" k="96" />
    <hkern u1="&#xe8;" u2="&#x7d;" k="36" />
    <hkern u1="&#xe8;" u2="y" k="33" />
    <hkern u1="&#xe8;" u2="x" k="60" />
    <hkern u1="&#xe8;" u2="v" k="33" />
    <hkern u1="&#xe8;" u2="]" k="36" />
    <hkern u1="&#xe8;" u2="\" k="123" />
    <hkern u1="&#xe8;" u2="W" k="41" />
    <hkern u1="&#xe8;" u2="V" k="123" />
    <hkern u1="&#xe8;" u2="&#x2a;" k="96" />
    <hkern u1="&#xe8;" u2="&#x29;" k="36" />
    <hkern u1="&#xe8;" u2="&#x27;" k="96" />
    <hkern u1="&#xe8;" u2="&#x22;" k="96" />
    <hkern u1="&#xe9;" u2="&#x2122;" k="96" />
    <hkern u1="&#xe9;" u2="&#x201d;" k="96" />
    <hkern u1="&#xe9;" u2="&#x201c;" k="96" />
    <hkern u1="&#xe9;" u2="&#x2019;" k="96" />
    <hkern u1="&#xe9;" u2="&#x2018;" k="96" />
    <hkern u1="&#xe9;" u2="&#xff;" k="33" />
    <hkern u1="&#xe9;" u2="&#xfd;" k="33" />
    <hkern u1="&#xe9;" u2="&#xba;" k="96" />
    <hkern u1="&#xe9;" u2="&#xb0;" k="96" />
    <hkern u1="&#xe9;" u2="&#xaa;" k="96" />
    <hkern u1="&#xe9;" u2="&#x7d;" k="36" />
    <hkern u1="&#xe9;" u2="y" k="33" />
    <hkern u1="&#xe9;" u2="x" k="60" />
    <hkern u1="&#xe9;" u2="v" k="33" />
    <hkern u1="&#xe9;" u2="]" k="36" />
    <hkern u1="&#xe9;" u2="\" k="123" />
    <hkern u1="&#xe9;" u2="W" k="41" />
    <hkern u1="&#xe9;" u2="V" k="123" />
    <hkern u1="&#xe9;" u2="&#x2a;" k="96" />
    <hkern u1="&#xe9;" u2="&#x29;" k="36" />
    <hkern u1="&#xe9;" u2="&#x27;" k="96" />
    <hkern u1="&#xe9;" u2="&#x22;" k="96" />
    <hkern u1="&#xea;" u2="&#x2122;" k="96" />
    <hkern u1="&#xea;" u2="&#x201d;" k="96" />
    <hkern u1="&#xea;" u2="&#x201c;" k="96" />
    <hkern u1="&#xea;" u2="&#x2019;" k="96" />
    <hkern u1="&#xea;" u2="&#x2018;" k="96" />
    <hkern u1="&#xea;" u2="&#xff;" k="33" />
    <hkern u1="&#xea;" u2="&#xfd;" k="33" />
    <hkern u1="&#xea;" u2="&#xba;" k="96" />
    <hkern u1="&#xea;" u2="&#xb0;" k="96" />
    <hkern u1="&#xea;" u2="&#xaa;" k="96" />
    <hkern u1="&#xea;" u2="&#x7d;" k="36" />
    <hkern u1="&#xea;" u2="y" k="33" />
    <hkern u1="&#xea;" u2="x" k="60" />
    <hkern u1="&#xea;" u2="v" k="33" />
    <hkern u1="&#xea;" u2="]" k="36" />
    <hkern u1="&#xea;" u2="\" k="123" />
    <hkern u1="&#xea;" u2="W" k="41" />
    <hkern u1="&#xea;" u2="V" k="123" />
    <hkern u1="&#xea;" u2="&#x2a;" k="96" />
    <hkern u1="&#xea;" u2="&#x29;" k="36" />
    <hkern u1="&#xea;" u2="&#x27;" k="96" />
    <hkern u1="&#xea;" u2="&#x22;" k="96" />
    <hkern u1="&#xeb;" u2="&#x2122;" k="96" />
    <hkern u1="&#xeb;" u2="&#x201d;" k="96" />
    <hkern u1="&#xeb;" u2="&#x201c;" k="96" />
    <hkern u1="&#xeb;" u2="&#x2019;" k="96" />
    <hkern u1="&#xeb;" u2="&#x2018;" k="96" />
    <hkern u1="&#xeb;" u2="&#xff;" k="33" />
    <hkern u1="&#xeb;" u2="&#xfd;" k="33" />
    <hkern u1="&#xeb;" u2="&#xba;" k="96" />
    <hkern u1="&#xeb;" u2="&#xb0;" k="96" />
    <hkern u1="&#xeb;" u2="&#xaa;" k="96" />
    <hkern u1="&#xeb;" u2="&#x7d;" k="36" />
    <hkern u1="&#xeb;" u2="y" k="33" />
    <hkern u1="&#xeb;" u2="x" k="60" />
    <hkern u1="&#xeb;" u2="v" k="33" />
    <hkern u1="&#xeb;" u2="]" k="36" />
    <hkern u1="&#xeb;" u2="\" k="123" />
    <hkern u1="&#xeb;" u2="W" k="41" />
    <hkern u1="&#xeb;" u2="V" k="123" />
    <hkern u1="&#xeb;" u2="&#x2a;" k="96" />
    <hkern u1="&#xeb;" u2="&#x29;" k="36" />
    <hkern u1="&#xeb;" u2="&#x27;" k="96" />
    <hkern u1="&#xeb;" u2="&#x22;" k="96" />
    <hkern u1="&#xf1;" u2="&#x2122;" k="76" />
    <hkern u1="&#xf1;" u2="&#x201d;" k="76" />
    <hkern u1="&#xf1;" u2="&#x201c;" k="76" />
    <hkern u1="&#xf1;" u2="&#x2019;" k="76" />
    <hkern u1="&#xf1;" u2="&#x2018;" k="76" />
    <hkern u1="&#xf1;" u2="&#xff;" k="36" />
    <hkern u1="&#xf1;" u2="&#xfd;" k="36" />
    <hkern u1="&#xf1;" u2="&#xba;" k="76" />
    <hkern u1="&#xf1;" u2="&#xb9;" k="76" />
    <hkern u1="&#xf1;" u2="&#xb3;" k="76" />
    <hkern u1="&#xf1;" u2="&#xb2;" k="76" />
    <hkern u1="&#xf1;" u2="&#xb0;" k="76" />
    <hkern u1="&#xf1;" u2="&#xaa;" k="76" />
    <hkern u1="&#xf1;" u2="y" k="36" />
    <hkern u1="&#xf1;" u2="w" k="18" />
    <hkern u1="&#xf1;" u2="v" k="36" />
    <hkern u1="&#xf1;" u2="&#x2a;" k="76" />
    <hkern u1="&#xf1;" u2="&#x27;" k="76" />
    <hkern u1="&#xf1;" u2="&#x22;" k="76" />
    <hkern u1="&#xf2;" u2="&#x2122;" k="96" />
    <hkern u1="&#xf2;" u2="&#x201d;" k="96" />
    <hkern u1="&#xf2;" u2="&#x201c;" k="96" />
    <hkern u1="&#xf2;" u2="&#x2019;" k="96" />
    <hkern u1="&#xf2;" u2="&#x2018;" k="96" />
    <hkern u1="&#xf2;" u2="&#xff;" k="33" />
    <hkern u1="&#xf2;" u2="&#xfd;" k="33" />
    <hkern u1="&#xf2;" u2="&#xba;" k="96" />
    <hkern u1="&#xf2;" u2="&#xb0;" k="96" />
    <hkern u1="&#xf2;" u2="&#xaa;" k="96" />
    <hkern u1="&#xf2;" u2="&#x7d;" k="36" />
    <hkern u1="&#xf2;" u2="y" k="33" />
    <hkern u1="&#xf2;" u2="x" k="60" />
    <hkern u1="&#xf2;" u2="v" k="33" />
    <hkern u1="&#xf2;" u2="]" k="36" />
    <hkern u1="&#xf2;" u2="\" k="123" />
    <hkern u1="&#xf2;" u2="W" k="41" />
    <hkern u1="&#xf2;" u2="V" k="123" />
    <hkern u1="&#xf2;" u2="&#x2a;" k="96" />
    <hkern u1="&#xf2;" u2="&#x29;" k="36" />
    <hkern u1="&#xf2;" u2="&#x27;" k="96" />
    <hkern u1="&#xf2;" u2="&#x22;" k="96" />
    <hkern u1="&#xf3;" u2="&#x2122;" k="96" />
    <hkern u1="&#xf3;" u2="&#x201d;" k="96" />
    <hkern u1="&#xf3;" u2="&#x201c;" k="96" />
    <hkern u1="&#xf3;" u2="&#x2019;" k="96" />
    <hkern u1="&#xf3;" u2="&#x2018;" k="96" />
    <hkern u1="&#xf3;" u2="&#xff;" k="33" />
    <hkern u1="&#xf3;" u2="&#xfd;" k="33" />
    <hkern u1="&#xf3;" u2="&#xba;" k="96" />
    <hkern u1="&#xf3;" u2="&#xb0;" k="96" />
    <hkern u1="&#xf3;" u2="&#xaa;" k="96" />
    <hkern u1="&#xf3;" u2="&#x7d;" k="36" />
    <hkern u1="&#xf3;" u2="y" k="33" />
    <hkern u1="&#xf3;" u2="x" k="60" />
    <hkern u1="&#xf3;" u2="v" k="33" />
    <hkern u1="&#xf3;" u2="]" k="36" />
    <hkern u1="&#xf3;" u2="\" k="123" />
    <hkern u1="&#xf3;" u2="W" k="41" />
    <hkern u1="&#xf3;" u2="V" k="123" />
    <hkern u1="&#xf3;" u2="&#x2a;" k="96" />
    <hkern u1="&#xf3;" u2="&#x29;" k="36" />
    <hkern u1="&#xf3;" u2="&#x27;" k="96" />
    <hkern u1="&#xf3;" u2="&#x22;" k="96" />
    <hkern u1="&#xf4;" u2="&#x2122;" k="96" />
    <hkern u1="&#xf4;" u2="&#x201d;" k="96" />
    <hkern u1="&#xf4;" u2="&#x201c;" k="96" />
    <hkern u1="&#xf4;" u2="&#x2019;" k="96" />
    <hkern u1="&#xf4;" u2="&#x2018;" k="96" />
    <hkern u1="&#xf4;" u2="&#xff;" k="33" />
    <hkern u1="&#xf4;" u2="&#xfd;" k="33" />
    <hkern u1="&#xf4;" u2="&#xba;" k="96" />
    <hkern u1="&#xf4;" u2="&#xb0;" k="96" />
    <hkern u1="&#xf4;" u2="&#xaa;" k="96" />
    <hkern u1="&#xf4;" u2="&#x7d;" k="36" />
    <hkern u1="&#xf4;" u2="y" k="33" />
    <hkern u1="&#xf4;" u2="x" k="60" />
    <hkern u1="&#xf4;" u2="v" k="33" />
    <hkern u1="&#xf4;" u2="]" k="36" />
    <hkern u1="&#xf4;" u2="\" k="123" />
    <hkern u1="&#xf4;" u2="W" k="41" />
    <hkern u1="&#xf4;" u2="V" k="123" />
    <hkern u1="&#xf4;" u2="&#x2a;" k="96" />
    <hkern u1="&#xf4;" u2="&#x29;" k="36" />
    <hkern u1="&#xf4;" u2="&#x27;" k="96" />
    <hkern u1="&#xf4;" u2="&#x22;" k="96" />
    <hkern u1="&#xf5;" u2="&#x2122;" k="96" />
    <hkern u1="&#xf5;" u2="&#x201d;" k="96" />
    <hkern u1="&#xf5;" u2="&#x201c;" k="96" />
    <hkern u1="&#xf5;" u2="&#x2019;" k="96" />
    <hkern u1="&#xf5;" u2="&#x2018;" k="96" />
    <hkern u1="&#xf5;" u2="&#xff;" k="33" />
    <hkern u1="&#xf5;" u2="&#xfd;" k="33" />
    <hkern u1="&#xf5;" u2="&#xba;" k="96" />
    <hkern u1="&#xf5;" u2="&#xb0;" k="96" />
    <hkern u1="&#xf5;" u2="&#xaa;" k="96" />
    <hkern u1="&#xf5;" u2="&#x7d;" k="36" />
    <hkern u1="&#xf5;" u2="y" k="33" />
    <hkern u1="&#xf5;" u2="x" k="60" />
    <hkern u1="&#xf5;" u2="v" k="33" />
    <hkern u1="&#xf5;" u2="]" k="36" />
    <hkern u1="&#xf5;" u2="\" k="123" />
    <hkern u1="&#xf5;" u2="W" k="41" />
    <hkern u1="&#xf5;" u2="V" k="123" />
    <hkern u1="&#xf5;" u2="&#x2a;" k="96" />
    <hkern u1="&#xf5;" u2="&#x29;" k="36" />
    <hkern u1="&#xf5;" u2="&#x27;" k="96" />
    <hkern u1="&#xf5;" u2="&#x22;" k="96" />
    <hkern u1="&#xf6;" u2="&#x2122;" k="96" />
    <hkern u1="&#xf6;" u2="&#x201d;" k="96" />
    <hkern u1="&#xf6;" u2="&#x201c;" k="96" />
    <hkern u1="&#xf6;" u2="&#x2019;" k="96" />
    <hkern u1="&#xf6;" u2="&#x2018;" k="96" />
    <hkern u1="&#xf6;" u2="&#xff;" k="33" />
    <hkern u1="&#xf6;" u2="&#xfd;" k="33" />
    <hkern u1="&#xf6;" u2="&#xba;" k="96" />
    <hkern u1="&#xf6;" u2="&#xb0;" k="96" />
    <hkern u1="&#xf6;" u2="&#xaa;" k="96" />
    <hkern u1="&#xf6;" u2="&#x7d;" k="36" />
    <hkern u1="&#xf6;" u2="y" k="33" />
    <hkern u1="&#xf6;" u2="x" k="60" />
    <hkern u1="&#xf6;" u2="v" k="33" />
    <hkern u1="&#xf6;" u2="]" k="36" />
    <hkern u1="&#xf6;" u2="\" k="123" />
    <hkern u1="&#xf6;" u2="W" k="41" />
    <hkern u1="&#xf6;" u2="V" k="123" />
    <hkern u1="&#xf6;" u2="&#x2a;" k="96" />
    <hkern u1="&#xf6;" u2="&#x29;" k="36" />
    <hkern u1="&#xf6;" u2="&#x27;" k="96" />
    <hkern u1="&#xf6;" u2="&#x22;" k="96" />
    <hkern u1="&#xf8;" u2="&#x2122;" k="96" />
    <hkern u1="&#xf8;" u2="&#x201d;" k="96" />
    <hkern u1="&#xf8;" u2="&#x201c;" k="96" />
    <hkern u1="&#xf8;" u2="&#x2019;" k="96" />
    <hkern u1="&#xf8;" u2="&#x2018;" k="96" />
    <hkern u1="&#xf8;" u2="&#xff;" k="33" />
    <hkern u1="&#xf8;" u2="&#xfd;" k="33" />
    <hkern u1="&#xf8;" u2="&#xba;" k="96" />
    <hkern u1="&#xf8;" u2="&#xb0;" k="96" />
    <hkern u1="&#xf8;" u2="&#xaa;" k="96" />
    <hkern u1="&#xf8;" u2="&#x7d;" k="36" />
    <hkern u1="&#xf8;" u2="y" k="33" />
    <hkern u1="&#xf8;" u2="x" k="60" />
    <hkern u1="&#xf8;" u2="v" k="33" />
    <hkern u1="&#xf8;" u2="]" k="36" />
    <hkern u1="&#xf8;" u2="\" k="123" />
    <hkern u1="&#xf8;" u2="W" k="41" />
    <hkern u1="&#xf8;" u2="V" k="123" />
    <hkern u1="&#xf8;" u2="&#x2a;" k="96" />
    <hkern u1="&#xf8;" u2="&#x29;" k="36" />
    <hkern u1="&#xf8;" u2="&#x27;" k="96" />
    <hkern u1="&#xf8;" u2="&#x22;" k="96" />
    <hkern u1="&#xfd;" u2="&#x2206;" k="91" />
    <hkern u1="&#xfd;" u2="&#x201e;" k="136" />
    <hkern u1="&#xfd;" u2="&#x201a;" k="136" />
    <hkern u1="&#xfd;" u2="&#x153;" k="33" />
    <hkern u1="&#xfd;" u2="&#x119;" k="33" />
    <hkern u1="&#xfd;" u2="&#x107;" k="33" />
    <hkern u1="&#xfd;" u2="&#x104;" k="91" />
    <hkern u1="&#xfd;" u2="&#xf8;" k="33" />
    <hkern u1="&#xfd;" u2="&#xf6;" k="33" />
    <hkern u1="&#xfd;" u2="&#xf5;" k="33" />
    <hkern u1="&#xfd;" u2="&#xf4;" k="33" />
    <hkern u1="&#xfd;" u2="&#xf3;" k="33" />
    <hkern u1="&#xfd;" u2="&#xf2;" k="33" />
    <hkern u1="&#xfd;" u2="&#xf0;" k="33" />
    <hkern u1="&#xfd;" u2="&#xeb;" k="33" />
    <hkern u1="&#xfd;" u2="&#xea;" k="33" />
    <hkern u1="&#xfd;" u2="&#xe9;" k="33" />
    <hkern u1="&#xfd;" u2="&#xe8;" k="33" />
    <hkern u1="&#xfd;" u2="&#xe7;" k="33" />
    <hkern u1="&#xfd;" u2="&#xc6;" k="91" />
    <hkern u1="&#xfd;" u2="&#xc5;" k="91" />
    <hkern u1="&#xfd;" u2="&#xc4;" k="91" />
    <hkern u1="&#xfd;" u2="&#xc3;" k="91" />
    <hkern u1="&#xfd;" u2="&#xc2;" k="91" />
    <hkern u1="&#xfd;" u2="&#xc1;" k="91" />
    <hkern u1="&#xfd;" u2="&#xc0;" k="91" />
    <hkern u1="&#xfd;" u2="q" k="33" />
    <hkern u1="&#xfd;" u2="o" k="33" />
    <hkern u1="&#xfd;" u2="e" k="33" />
    <hkern u1="&#xfd;" u2="d" k="33" />
    <hkern u1="&#xfd;" u2="c" k="33" />
    <hkern u1="&#xfd;" u2="A" k="91" />
    <hkern u1="&#xfd;" u2="&#x2f;" k="91" />
    <hkern u1="&#xfd;" u2="&#x2e;" k="136" />
    <hkern u1="&#xfd;" u2="&#x2c;" k="136" />
    <hkern u1="&#xfd;" u2="&#x26;" k="91" />
    <hkern u1="&#xfe;" u2="&#x2122;" k="96" />
    <hkern u1="&#xfe;" u2="&#x201d;" k="96" />
    <hkern u1="&#xfe;" u2="&#x201c;" k="96" />
    <hkern u1="&#xfe;" u2="&#x2019;" k="96" />
    <hkern u1="&#xfe;" u2="&#x2018;" k="96" />
    <hkern u1="&#xfe;" u2="&#xff;" k="33" />
    <hkern u1="&#xfe;" u2="&#xfd;" k="33" />
    <hkern u1="&#xfe;" u2="&#xba;" k="96" />
    <hkern u1="&#xfe;" u2="&#xb0;" k="96" />
    <hkern u1="&#xfe;" u2="&#xaa;" k="96" />
    <hkern u1="&#xfe;" u2="&#x7d;" k="36" />
    <hkern u1="&#xfe;" u2="y" k="33" />
    <hkern u1="&#xfe;" u2="x" k="60" />
    <hkern u1="&#xfe;" u2="v" k="33" />
    <hkern u1="&#xfe;" u2="]" k="36" />
    <hkern u1="&#xfe;" u2="\" k="123" />
    <hkern u1="&#xfe;" u2="W" k="41" />
    <hkern u1="&#xfe;" u2="V" k="123" />
    <hkern u1="&#xfe;" u2="&#x2a;" k="96" />
    <hkern u1="&#xfe;" u2="&#x29;" k="36" />
    <hkern u1="&#xfe;" u2="&#x27;" k="96" />
    <hkern u1="&#xfe;" u2="&#x22;" k="96" />
    <hkern u1="&#xff;" u2="&#x2206;" k="91" />
    <hkern u1="&#xff;" u2="&#x201e;" k="136" />
    <hkern u1="&#xff;" u2="&#x201a;" k="136" />
    <hkern u1="&#xff;" u2="&#x153;" k="33" />
    <hkern u1="&#xff;" u2="&#x119;" k="33" />
    <hkern u1="&#xff;" u2="&#x107;" k="33" />
    <hkern u1="&#xff;" u2="&#x104;" k="91" />
    <hkern u1="&#xff;" u2="&#xf8;" k="33" />
    <hkern u1="&#xff;" u2="&#xf6;" k="33" />
    <hkern u1="&#xff;" u2="&#xf5;" k="33" />
    <hkern u1="&#xff;" u2="&#xf4;" k="33" />
    <hkern u1="&#xff;" u2="&#xf3;" k="33" />
    <hkern u1="&#xff;" u2="&#xf2;" k="33" />
    <hkern u1="&#xff;" u2="&#xf0;" k="33" />
    <hkern u1="&#xff;" u2="&#xeb;" k="33" />
    <hkern u1="&#xff;" u2="&#xea;" k="33" />
    <hkern u1="&#xff;" u2="&#xe9;" k="33" />
    <hkern u1="&#xff;" u2="&#xe8;" k="33" />
    <hkern u1="&#xff;" u2="&#xe7;" k="33" />
    <hkern u1="&#xff;" u2="&#xc6;" k="91" />
    <hkern u1="&#xff;" u2="&#xc5;" k="91" />
    <hkern u1="&#xff;" u2="&#xc4;" k="91" />
    <hkern u1="&#xff;" u2="&#xc3;" k="91" />
    <hkern u1="&#xff;" u2="&#xc2;" k="91" />
    <hkern u1="&#xff;" u2="&#xc1;" k="91" />
    <hkern u1="&#xff;" u2="&#xc0;" k="91" />
    <hkern u1="&#xff;" u2="q" k="33" />
    <hkern u1="&#xff;" u2="o" k="33" />
    <hkern u1="&#xff;" u2="e" k="33" />
    <hkern u1="&#xff;" u2="d" k="33" />
    <hkern u1="&#xff;" u2="c" k="33" />
    <hkern u1="&#xff;" u2="A" k="91" />
    <hkern u1="&#xff;" u2="&#x2f;" k="91" />
    <hkern u1="&#xff;" u2="&#x2e;" k="136" />
    <hkern u1="&#xff;" u2="&#x2c;" k="136" />
    <hkern u1="&#xff;" u2="&#x26;" k="91" />
    <hkern u1="&#x104;" u2="&#x2122;" k="191" />
    <hkern u1="&#x104;" u2="&#x203a;" k="67" />
    <hkern u1="&#x104;" u2="&#x2039;" k="67" />
    <hkern u1="&#x104;" u2="&#x2022;" k="67" />
    <hkern u1="&#x104;" u2="&#x201d;" k="191" />
    <hkern u1="&#x104;" u2="&#x201c;" k="191" />
    <hkern u1="&#x104;" u2="&#x2019;" k="191" />
    <hkern u1="&#x104;" u2="&#x2018;" k="191" />
    <hkern u1="&#x104;" u2="&#x2014;" k="67" />
    <hkern u1="&#x104;" u2="&#x2013;" k="67" />
    <hkern u1="&#x104;" u2="&#x178;" k="182" />
    <hkern u1="&#x104;" u2="&#x152;" k="51" />
    <hkern u1="&#x104;" u2="&#x106;" k="51" />
    <hkern u1="&#x104;" u2="&#xff;" k="91" />
    <hkern u1="&#x104;" u2="&#xfd;" k="91" />
    <hkern u1="&#x104;" u2="&#xdd;" k="182" />
    <hkern u1="&#x104;" u2="&#xdc;" k="52" />
    <hkern u1="&#x104;" u2="&#xdb;" k="52" />
    <hkern u1="&#x104;" u2="&#xda;" k="52" />
    <hkern u1="&#x104;" u2="&#xd9;" k="52" />
    <hkern u1="&#x104;" u2="&#xd8;" k="51" />
    <hkern u1="&#x104;" u2="&#xd6;" k="51" />
    <hkern u1="&#x104;" u2="&#xd5;" k="51" />
    <hkern u1="&#x104;" u2="&#xd4;" k="51" />
    <hkern u1="&#x104;" u2="&#xd3;" k="51" />
    <hkern u1="&#x104;" u2="&#xd2;" k="51" />
    <hkern u1="&#x104;" u2="&#xc7;" k="51" />
    <hkern u1="&#x104;" u2="&#xbb;" k="67" />
    <hkern u1="&#x104;" u2="&#xba;" k="191" />
    <hkern u1="&#x104;" u2="&#xb9;" k="202" />
    <hkern u1="&#x104;" u2="&#xb7;" k="67" />
    <hkern u1="&#x104;" u2="&#xb3;" k="202" />
    <hkern u1="&#x104;" u2="&#xb2;" k="202" />
    <hkern u1="&#x104;" u2="&#xb0;" k="191" />
    <hkern u1="&#x104;" u2="&#xae;" k="51" />
    <hkern u1="&#x104;" u2="&#xad;" k="67" />
    <hkern u1="&#x104;" u2="&#xab;" k="67" />
    <hkern u1="&#x104;" u2="&#xaa;" k="191" />
    <hkern u1="&#x104;" u2="&#xa9;" k="51" />
    <hkern u1="&#x104;" u2="y" k="91" />
    <hkern u1="&#x104;" u2="v" k="91" />
    <hkern u1="&#x104;" u2="\" k="169" />
    <hkern u1="&#x104;" u2="Y" k="182" />
    <hkern u1="&#x104;" u2="W" k="102" />
    <hkern u1="&#x104;" u2="V" k="169" />
    <hkern u1="&#x104;" u2="U" k="52" />
    <hkern u1="&#x104;" u2="T" k="147" />
    <hkern u1="&#x104;" u2="Q" k="51" />
    <hkern u1="&#x104;" u2="O" k="51" />
    <hkern u1="&#x104;" u2="J" k="-56" />
    <hkern u1="&#x104;" u2="G" k="51" />
    <hkern u1="&#x104;" u2="C" k="51" />
    <hkern u1="&#x104;" u2="&#x40;" k="51" />
    <hkern u1="&#x104;" u2="&#x3f;" k="63" />
    <hkern u1="&#x104;" u2="&#x2d;" k="67" />
    <hkern u1="&#x104;" u2="&#x2a;" k="191" />
    <hkern u1="&#x104;" u2="&#x27;" k="191" />
    <hkern u1="&#x104;" u2="&#x22;" k="191" />
    <hkern u1="&#x105;" u2="&#x2122;" k="76" />
    <hkern u1="&#x105;" u2="&#x201d;" k="76" />
    <hkern u1="&#x105;" u2="&#x201c;" k="76" />
    <hkern u1="&#x105;" u2="&#x2019;" k="76" />
    <hkern u1="&#x105;" u2="&#x2018;" k="76" />
    <hkern u1="&#x105;" u2="&#xff;" k="36" />
    <hkern u1="&#x105;" u2="&#xfd;" k="36" />
    <hkern u1="&#x105;" u2="&#xba;" k="76" />
    <hkern u1="&#x105;" u2="&#xb9;" k="76" />
    <hkern u1="&#x105;" u2="&#xb3;" k="76" />
    <hkern u1="&#x105;" u2="&#xb2;" k="76" />
    <hkern u1="&#x105;" u2="&#xb0;" k="76" />
    <hkern u1="&#x105;" u2="&#xaa;" k="76" />
    <hkern u1="&#x105;" u2="y" k="36" />
    <hkern u1="&#x105;" u2="w" k="18" />
    <hkern u1="&#x105;" u2="v" k="36" />
    <hkern u1="&#x105;" u2="&#x2a;" k="76" />
    <hkern u1="&#x105;" u2="&#x27;" k="76" />
    <hkern u1="&#x105;" u2="&#x22;" k="76" />
    <hkern u1="&#x106;" u2="&#x203a;" k="144" />
    <hkern u1="&#x106;" u2="&#x2039;" k="144" />
    <hkern u1="&#x106;" u2="&#x2022;" k="144" />
    <hkern u1="&#x106;" u2="&#x2014;" k="144" />
    <hkern u1="&#x106;" u2="&#x2013;" k="144" />
    <hkern u1="&#x106;" u2="&#xbb;" k="144" />
    <hkern u1="&#x106;" u2="&#xb7;" k="144" />
    <hkern u1="&#x106;" u2="&#xad;" k="144" />
    <hkern u1="&#x106;" u2="&#xab;" k="144" />
    <hkern u1="&#x106;" u2="&#x2d;" k="144" />
    <hkern u1="&#x119;" u2="&#x2122;" k="96" />
    <hkern u1="&#x119;" u2="&#x201d;" k="96" />
    <hkern u1="&#x119;" u2="&#x201c;" k="96" />
    <hkern u1="&#x119;" u2="&#x2019;" k="96" />
    <hkern u1="&#x119;" u2="&#x2018;" k="96" />
    <hkern u1="&#x119;" u2="&#xff;" k="33" />
    <hkern u1="&#x119;" u2="&#xfd;" k="33" />
    <hkern u1="&#x119;" u2="&#xba;" k="96" />
    <hkern u1="&#x119;" u2="&#xb0;" k="96" />
    <hkern u1="&#x119;" u2="&#xaa;" k="96" />
    <hkern u1="&#x119;" u2="&#x7d;" k="36" />
    <hkern u1="&#x119;" u2="y" k="33" />
    <hkern u1="&#x119;" u2="x" k="60" />
    <hkern u1="&#x119;" u2="v" k="33" />
    <hkern u1="&#x119;" u2="]" k="36" />
    <hkern u1="&#x119;" u2="\" k="123" />
    <hkern u1="&#x119;" u2="W" k="41" />
    <hkern u1="&#x119;" u2="V" k="123" />
    <hkern u1="&#x119;" u2="&#x2a;" k="96" />
    <hkern u1="&#x119;" u2="&#x29;" k="36" />
    <hkern u1="&#x119;" u2="&#x27;" k="96" />
    <hkern u1="&#x119;" u2="&#x22;" k="96" />
    <hkern u1="&#x141;" u2="&#x2122;" k="140" />
    <hkern u1="&#x141;" u2="&#x203a;" k="113" />
    <hkern u1="&#x141;" u2="&#x2039;" k="113" />
    <hkern u1="&#x141;" u2="&#x2022;" k="113" />
    <hkern u1="&#x141;" u2="&#x201d;" k="140" />
    <hkern u1="&#x141;" u2="&#x201c;" k="140" />
    <hkern u1="&#x141;" u2="&#x2019;" k="140" />
    <hkern u1="&#x141;" u2="&#x2018;" k="140" />
    <hkern u1="&#x141;" u2="&#x2014;" k="113" />
    <hkern u1="&#x141;" u2="&#x2013;" k="113" />
    <hkern u1="&#x141;" u2="&#x178;" k="167" />
    <hkern u1="&#x141;" u2="&#xff;" k="58" />
    <hkern u1="&#x141;" u2="&#xfd;" k="58" />
    <hkern u1="&#x141;" u2="&#xdd;" k="167" />
    <hkern u1="&#x141;" u2="&#xbb;" k="113" />
    <hkern u1="&#x141;" u2="&#xba;" k="140" />
    <hkern u1="&#x141;" u2="&#xb9;" k="136" />
    <hkern u1="&#x141;" u2="&#xb7;" k="113" />
    <hkern u1="&#x141;" u2="&#xb3;" k="136" />
    <hkern u1="&#x141;" u2="&#xb2;" k="136" />
    <hkern u1="&#x141;" u2="&#xb0;" k="140" />
    <hkern u1="&#x141;" u2="&#xad;" k="113" />
    <hkern u1="&#x141;" u2="&#xab;" k="113" />
    <hkern u1="&#x141;" u2="&#xaa;" k="140" />
    <hkern u1="&#x141;" u2="y" k="58" />
    <hkern u1="&#x141;" u2="v" k="58" />
    <hkern u1="&#x141;" u2="\" k="171" />
    <hkern u1="&#x141;" u2="Y" k="167" />
    <hkern u1="&#x141;" u2="W" k="131" />
    <hkern u1="&#x141;" u2="V" k="171" />
    <hkern u1="&#x141;" u2="&#x2d;" k="113" />
    <hkern u1="&#x141;" u2="&#x2a;" k="140" />
    <hkern u1="&#x141;" u2="&#x27;" k="140" />
    <hkern u1="&#x141;" u2="&#x22;" k="140" />
    <hkern u1="&#x144;" u2="&#x2122;" k="76" />
    <hkern u1="&#x144;" u2="&#x201d;" k="76" />
    <hkern u1="&#x144;" u2="&#x201c;" k="76" />
    <hkern u1="&#x144;" u2="&#x2019;" k="76" />
    <hkern u1="&#x144;" u2="&#x2018;" k="76" />
    <hkern u1="&#x144;" u2="&#xff;" k="36" />
    <hkern u1="&#x144;" u2="&#xfd;" k="36" />
    <hkern u1="&#x144;" u2="&#xba;" k="76" />
    <hkern u1="&#x144;" u2="&#xb9;" k="76" />
    <hkern u1="&#x144;" u2="&#xb3;" k="76" />
    <hkern u1="&#x144;" u2="&#xb2;" k="76" />
    <hkern u1="&#x144;" u2="&#xb0;" k="76" />
    <hkern u1="&#x144;" u2="&#xaa;" k="76" />
    <hkern u1="&#x144;" u2="y" k="36" />
    <hkern u1="&#x144;" u2="w" k="18" />
    <hkern u1="&#x144;" u2="v" k="36" />
    <hkern u1="&#x144;" u2="&#x2a;" k="76" />
    <hkern u1="&#x144;" u2="&#x27;" k="76" />
    <hkern u1="&#x144;" u2="&#x22;" k="76" />
    <hkern u1="&#x153;" u2="&#x2122;" k="96" />
    <hkern u1="&#x153;" u2="&#x201d;" k="96" />
    <hkern u1="&#x153;" u2="&#x201c;" k="96" />
    <hkern u1="&#x153;" u2="&#x2019;" k="96" />
    <hkern u1="&#x153;" u2="&#x2018;" k="96" />
    <hkern u1="&#x153;" u2="&#xff;" k="33" />
    <hkern u1="&#x153;" u2="&#xfd;" k="33" />
    <hkern u1="&#x153;" u2="&#xba;" k="96" />
    <hkern u1="&#x153;" u2="&#xb0;" k="96" />
    <hkern u1="&#x153;" u2="&#xaa;" k="96" />
    <hkern u1="&#x153;" u2="&#x7d;" k="36" />
    <hkern u1="&#x153;" u2="y" k="33" />
    <hkern u1="&#x153;" u2="x" k="60" />
    <hkern u1="&#x153;" u2="v" k="33" />
    <hkern u1="&#x153;" u2="]" k="36" />
    <hkern u1="&#x153;" u2="\" k="123" />
    <hkern u1="&#x153;" u2="W" k="41" />
    <hkern u1="&#x153;" u2="V" k="123" />
    <hkern u1="&#x153;" u2="&#x2a;" k="96" />
    <hkern u1="&#x153;" u2="&#x29;" k="36" />
    <hkern u1="&#x153;" u2="&#x27;" k="96" />
    <hkern u1="&#x153;" u2="&#x22;" k="96" />
    <hkern u1="&#x178;" u2="&#x2206;" k="182" />
    <hkern u1="&#x178;" u2="&#x2122;" k="-36" />
    <hkern u1="&#x178;" u2="&#x203a;" k="160" />
    <hkern u1="&#x178;" u2="&#x2039;" k="160" />
    <hkern u1="&#x178;" u2="&#x2022;" k="160" />
    <hkern u1="&#x178;" u2="&#x201e;" k="167" />
    <hkern u1="&#x178;" u2="&#x201d;" k="-36" />
    <hkern u1="&#x178;" u2="&#x201c;" k="-36" />
    <hkern u1="&#x178;" u2="&#x201a;" k="167" />
    <hkern u1="&#x178;" u2="&#x2019;" k="-36" />
    <hkern u1="&#x178;" u2="&#x2018;" k="-36" />
    <hkern u1="&#x178;" u2="&#x2014;" k="160" />
    <hkern u1="&#x178;" u2="&#x2013;" k="160" />
    <hkern u1="&#x178;" u2="&#x161;" k="139" />
    <hkern u1="&#x178;" u2="&#x15b;" k="139" />
    <hkern u1="&#x178;" u2="&#x153;" k="160" />
    <hkern u1="&#x178;" u2="&#x152;" k="80" />
    <hkern u1="&#x178;" u2="&#x144;" k="131" />
    <hkern u1="&#x178;" u2="&#x131;" k="131" />
    <hkern u1="&#x178;" u2="&#x119;" k="160" />
    <hkern u1="&#x178;" u2="&#x107;" k="160" />
    <hkern u1="&#x178;" u2="&#x106;" k="80" />
    <hkern u1="&#x178;" u2="&#x105;" k="145" />
    <hkern u1="&#x178;" u2="&#x104;" k="182" />
    <hkern u1="&#x178;" u2="&#xff;" k="100" />
    <hkern u1="&#x178;" u2="&#xfd;" k="100" />
    <hkern u1="&#x178;" u2="&#xfc;" k="131" />
    <hkern u1="&#x178;" u2="&#xfb;" k="131" />
    <hkern u1="&#x178;" u2="&#xfa;" k="131" />
    <hkern u1="&#x178;" u2="&#xf9;" k="131" />
    <hkern u1="&#x178;" u2="&#xf8;" k="160" />
    <hkern u1="&#x178;" u2="&#xf6;" k="160" />
    <hkern u1="&#x178;" u2="&#xf5;" k="160" />
    <hkern u1="&#x178;" u2="&#xf4;" k="160" />
    <hkern u1="&#x178;" u2="&#xf3;" k="160" />
    <hkern u1="&#x178;" u2="&#xf2;" k="160" />
    <hkern u1="&#x178;" u2="&#xf1;" k="131" />
    <hkern u1="&#x178;" u2="&#xf0;" k="160" />
    <hkern u1="&#x178;" u2="&#xeb;" k="160" />
    <hkern u1="&#x178;" u2="&#xea;" k="160" />
    <hkern u1="&#x178;" u2="&#xe9;" k="160" />
    <hkern u1="&#x178;" u2="&#xe8;" k="160" />
    <hkern u1="&#x178;" u2="&#xe7;" k="160" />
    <hkern u1="&#x178;" u2="&#xe6;" k="145" />
    <hkern u1="&#x178;" u2="&#xe5;" k="145" />
    <hkern u1="&#x178;" u2="&#xe4;" k="145" />
    <hkern u1="&#x178;" u2="&#xe3;" k="145" />
    <hkern u1="&#x178;" u2="&#xe2;" k="145" />
    <hkern u1="&#x178;" u2="&#xe1;" k="145" />
    <hkern u1="&#x178;" u2="&#xe0;" k="145" />
    <hkern u1="&#x178;" u2="&#xd8;" k="80" />
    <hkern u1="&#x178;" u2="&#xd6;" k="80" />
    <hkern u1="&#x178;" u2="&#xd5;" k="80" />
    <hkern u1="&#x178;" u2="&#xd4;" k="80" />
    <hkern u1="&#x178;" u2="&#xd3;" k="80" />
    <hkern u1="&#x178;" u2="&#xd2;" k="80" />
    <hkern u1="&#x178;" u2="&#xc7;" k="80" />
    <hkern u1="&#x178;" u2="&#xc6;" k="182" />
    <hkern u1="&#x178;" u2="&#xc5;" k="182" />
    <hkern u1="&#x178;" u2="&#xc4;" k="182" />
    <hkern u1="&#x178;" u2="&#xc3;" k="182" />
    <hkern u1="&#x178;" u2="&#xc2;" k="182" />
    <hkern u1="&#x178;" u2="&#xc1;" k="182" />
    <hkern u1="&#x178;" u2="&#xc0;" k="182" />
    <hkern u1="&#x178;" u2="&#xbb;" k="160" />
    <hkern u1="&#x178;" u2="&#xba;" k="-36" />
    <hkern u1="&#x178;" u2="&#xb9;" k="-56" />
    <hkern u1="&#x178;" u2="&#xb7;" k="160" />
    <hkern u1="&#x178;" u2="&#xb5;" k="131" />
    <hkern u1="&#x178;" u2="&#xb3;" k="-56" />
    <hkern u1="&#x178;" u2="&#xb2;" k="-56" />
    <hkern u1="&#x178;" u2="&#xb0;" k="-36" />
    <hkern u1="&#x178;" u2="&#xae;" k="80" />
    <hkern u1="&#x178;" u2="&#xad;" k="160" />
    <hkern u1="&#x178;" u2="&#xab;" k="160" />
    <hkern u1="&#x178;" u2="&#xaa;" k="-36" />
    <hkern u1="&#x178;" u2="&#xa9;" k="80" />
    <hkern u1="&#x178;" u2="y" k="100" />
    <hkern u1="&#x178;" u2="x" k="136" />
    <hkern u1="&#x178;" u2="w" k="96" />
    <hkern u1="&#x178;" u2="v" k="100" />
    <hkern u1="&#x178;" u2="u" k="131" />
    <hkern u1="&#x178;" u2="s" k="139" />
    <hkern u1="&#x178;" u2="r" k="131" />
    <hkern u1="&#x178;" u2="q" k="160" />
    <hkern u1="&#x178;" u2="p" k="131" />
    <hkern u1="&#x178;" u2="o" k="160" />
    <hkern u1="&#x178;" u2="n" k="131" />
    <hkern u1="&#x178;" u2="m" k="131" />
    <hkern u1="&#x178;" u2="g" k="176" />
    <hkern u1="&#x178;" u2="e" k="160" />
    <hkern u1="&#x178;" u2="d" k="160" />
    <hkern u1="&#x178;" u2="c" k="160" />
    <hkern u1="&#x178;" u2="a" k="145" />
    <hkern u1="&#x178;" u2="Q" k="80" />
    <hkern u1="&#x178;" u2="O" k="80" />
    <hkern u1="&#x178;" u2="J" k="200" />
    <hkern u1="&#x178;" u2="G" k="80" />
    <hkern u1="&#x178;" u2="C" k="80" />
    <hkern u1="&#x178;" u2="A" k="182" />
    <hkern u1="&#x178;" u2="&#x40;" k="80" />
    <hkern u1="&#x178;" u2="&#x3f;" k="-32" />
    <hkern u1="&#x178;" u2="&#x3b;" k="131" />
    <hkern u1="&#x178;" u2="&#x3a;" k="131" />
    <hkern u1="&#x178;" u2="&#x2f;" k="182" />
    <hkern u1="&#x178;" u2="&#x2e;" k="167" />
    <hkern u1="&#x178;" u2="&#x2d;" k="160" />
    <hkern u1="&#x178;" u2="&#x2c;" k="167" />
    <hkern u1="&#x178;" u2="&#x2a;" k="-36" />
    <hkern u1="&#x178;" u2="&#x27;" k="-36" />
    <hkern u1="&#x178;" u2="&#x26;" k="182" />
    <hkern u1="&#x178;" u2="&#x22;" k="-36" />
    <hkern u1="&#x179;" u2="&#x203a;" k="64" />
    <hkern u1="&#x179;" u2="&#x2039;" k="64" />
    <hkern u1="&#x179;" u2="&#x2022;" k="64" />
    <hkern u1="&#x179;" u2="&#x2014;" k="64" />
    <hkern u1="&#x179;" u2="&#x2013;" k="64" />
    <hkern u1="&#x179;" u2="&#x161;" k="19" />
    <hkern u1="&#x179;" u2="&#x15b;" k="19" />
    <hkern u1="&#x179;" u2="&#x153;" k="29" />
    <hkern u1="&#x179;" u2="&#x152;" k="49" />
    <hkern u1="&#x179;" u2="&#x119;" k="29" />
    <hkern u1="&#x179;" u2="&#x107;" k="29" />
    <hkern u1="&#x179;" u2="&#x106;" k="49" />
    <hkern u1="&#x179;" u2="&#xff;" k="34" />
    <hkern u1="&#x179;" u2="&#xfd;" k="34" />
    <hkern u1="&#x179;" u2="&#xf8;" k="29" />
    <hkern u1="&#x179;" u2="&#xf6;" k="29" />
    <hkern u1="&#x179;" u2="&#xf5;" k="29" />
    <hkern u1="&#x179;" u2="&#xf4;" k="29" />
    <hkern u1="&#x179;" u2="&#xf3;" k="29" />
    <hkern u1="&#x179;" u2="&#xf2;" k="29" />
    <hkern u1="&#x179;" u2="&#xf0;" k="29" />
    <hkern u1="&#x179;" u2="&#xeb;" k="29" />
    <hkern u1="&#x179;" u2="&#xea;" k="29" />
    <hkern u1="&#x179;" u2="&#xe9;" k="29" />
    <hkern u1="&#x179;" u2="&#xe8;" k="29" />
    <hkern u1="&#x179;" u2="&#xe7;" k="29" />
    <hkern u1="&#x179;" u2="&#xd8;" k="49" />
    <hkern u1="&#x179;" u2="&#xd6;" k="49" />
    <hkern u1="&#x179;" u2="&#xd5;" k="49" />
    <hkern u1="&#x179;" u2="&#xd4;" k="49" />
    <hkern u1="&#x179;" u2="&#xd3;" k="49" />
    <hkern u1="&#x179;" u2="&#xd2;" k="49" />
    <hkern u1="&#x179;" u2="&#xc7;" k="49" />
    <hkern u1="&#x179;" u2="&#xbb;" k="64" />
    <hkern u1="&#x179;" u2="&#xb7;" k="64" />
    <hkern u1="&#x179;" u2="&#xae;" k="49" />
    <hkern u1="&#x179;" u2="&#xad;" k="64" />
    <hkern u1="&#x179;" u2="&#xab;" k="64" />
    <hkern u1="&#x179;" u2="&#xa9;" k="49" />
    <hkern u1="&#x179;" u2="y" k="34" />
    <hkern u1="&#x179;" u2="v" k="34" />
    <hkern u1="&#x179;" u2="s" k="19" />
    <hkern u1="&#x179;" u2="q" k="29" />
    <hkern u1="&#x179;" u2="o" k="29" />
    <hkern u1="&#x179;" u2="e" k="29" />
    <hkern u1="&#x179;" u2="d" k="29" />
    <hkern u1="&#x179;" u2="c" k="29" />
    <hkern u1="&#x179;" u2="Q" k="49" />
    <hkern u1="&#x179;" u2="O" k="49" />
    <hkern u1="&#x179;" u2="G" k="49" />
    <hkern u1="&#x179;" u2="C" k="49" />
    <hkern u1="&#x179;" u2="&#x40;" k="49" />
    <hkern u1="&#x179;" u2="&#x3f;" k="-32" />
    <hkern u1="&#x179;" u2="&#x2d;" k="64" />
    <hkern u1="&#x17b;" u2="&#x203a;" k="64" />
    <hkern u1="&#x17b;" u2="&#x2039;" k="64" />
    <hkern u1="&#x17b;" u2="&#x2022;" k="64" />
    <hkern u1="&#x17b;" u2="&#x2014;" k="64" />
    <hkern u1="&#x17b;" u2="&#x2013;" k="64" />
    <hkern u1="&#x17b;" u2="&#x161;" k="19" />
    <hkern u1="&#x17b;" u2="&#x15b;" k="19" />
    <hkern u1="&#x17b;" u2="&#x153;" k="29" />
    <hkern u1="&#x17b;" u2="&#x152;" k="49" />
    <hkern u1="&#x17b;" u2="&#x119;" k="29" />
    <hkern u1="&#x17b;" u2="&#x107;" k="29" />
    <hkern u1="&#x17b;" u2="&#x106;" k="49" />
    <hkern u1="&#x17b;" u2="&#xff;" k="34" />
    <hkern u1="&#x17b;" u2="&#xfd;" k="34" />
    <hkern u1="&#x17b;" u2="&#xf8;" k="29" />
    <hkern u1="&#x17b;" u2="&#xf6;" k="29" />
    <hkern u1="&#x17b;" u2="&#xf5;" k="29" />
    <hkern u1="&#x17b;" u2="&#xf4;" k="29" />
    <hkern u1="&#x17b;" u2="&#xf3;" k="29" />
    <hkern u1="&#x17b;" u2="&#xf2;" k="29" />
    <hkern u1="&#x17b;" u2="&#xf0;" k="29" />
    <hkern u1="&#x17b;" u2="&#xeb;" k="29" />
    <hkern u1="&#x17b;" u2="&#xea;" k="29" />
    <hkern u1="&#x17b;" u2="&#xe9;" k="29" />
    <hkern u1="&#x17b;" u2="&#xe8;" k="29" />
    <hkern u1="&#x17b;" u2="&#xe7;" k="29" />
    <hkern u1="&#x17b;" u2="&#xd8;" k="49" />
    <hkern u1="&#x17b;" u2="&#xd6;" k="49" />
    <hkern u1="&#x17b;" u2="&#xd5;" k="49" />
    <hkern u1="&#x17b;" u2="&#xd4;" k="49" />
    <hkern u1="&#x17b;" u2="&#xd3;" k="49" />
    <hkern u1="&#x17b;" u2="&#xd2;" k="49" />
    <hkern u1="&#x17b;" u2="&#xc7;" k="49" />
    <hkern u1="&#x17b;" u2="&#xbb;" k="64" />
    <hkern u1="&#x17b;" u2="&#xb7;" k="64" />
    <hkern u1="&#x17b;" u2="&#xae;" k="49" />
    <hkern u1="&#x17b;" u2="&#xad;" k="64" />
    <hkern u1="&#x17b;" u2="&#xab;" k="64" />
    <hkern u1="&#x17b;" u2="&#xa9;" k="49" />
    <hkern u1="&#x17b;" u2="y" k="34" />
    <hkern u1="&#x17b;" u2="v" k="34" />
    <hkern u1="&#x17b;" u2="s" k="19" />
    <hkern u1="&#x17b;" u2="q" k="29" />
    <hkern u1="&#x17b;" u2="o" k="29" />
    <hkern u1="&#x17b;" u2="e" k="29" />
    <hkern u1="&#x17b;" u2="d" k="29" />
    <hkern u1="&#x17b;" u2="c" k="29" />
    <hkern u1="&#x17b;" u2="Q" k="49" />
    <hkern u1="&#x17b;" u2="O" k="49" />
    <hkern u1="&#x17b;" u2="G" k="49" />
    <hkern u1="&#x17b;" u2="C" k="49" />
    <hkern u1="&#x17b;" u2="&#x40;" k="49" />
    <hkern u1="&#x17b;" u2="&#x3f;" k="-32" />
    <hkern u1="&#x17b;" u2="&#x2d;" k="64" />
    <hkern u1="&#x17d;" u2="&#x203a;" k="64" />
    <hkern u1="&#x17d;" u2="&#x2039;" k="64" />
    <hkern u1="&#x17d;" u2="&#x2022;" k="64" />
    <hkern u1="&#x17d;" u2="&#x2014;" k="64" />
    <hkern u1="&#x17d;" u2="&#x2013;" k="64" />
    <hkern u1="&#x17d;" u2="&#x161;" k="19" />
    <hkern u1="&#x17d;" u2="&#x15b;" k="19" />
    <hkern u1="&#x17d;" u2="&#x153;" k="29" />
    <hkern u1="&#x17d;" u2="&#x152;" k="49" />
    <hkern u1="&#x17d;" u2="&#x119;" k="29" />
    <hkern u1="&#x17d;" u2="&#x107;" k="29" />
    <hkern u1="&#x17d;" u2="&#x106;" k="49" />
    <hkern u1="&#x17d;" u2="&#xff;" k="34" />
    <hkern u1="&#x17d;" u2="&#xfd;" k="34" />
    <hkern u1="&#x17d;" u2="&#xf8;" k="29" />
    <hkern u1="&#x17d;" u2="&#xf6;" k="29" />
    <hkern u1="&#x17d;" u2="&#xf5;" k="29" />
    <hkern u1="&#x17d;" u2="&#xf4;" k="29" />
    <hkern u1="&#x17d;" u2="&#xf3;" k="29" />
    <hkern u1="&#x17d;" u2="&#xf2;" k="29" />
    <hkern u1="&#x17d;" u2="&#xf0;" k="29" />
    <hkern u1="&#x17d;" u2="&#xeb;" k="29" />
    <hkern u1="&#x17d;" u2="&#xea;" k="29" />
    <hkern u1="&#x17d;" u2="&#xe9;" k="29" />
    <hkern u1="&#x17d;" u2="&#xe8;" k="29" />
    <hkern u1="&#x17d;" u2="&#xe7;" k="29" />
    <hkern u1="&#x17d;" u2="&#xd8;" k="49" />
    <hkern u1="&#x17d;" u2="&#xd6;" k="49" />
    <hkern u1="&#x17d;" u2="&#xd5;" k="49" />
    <hkern u1="&#x17d;" u2="&#xd4;" k="49" />
    <hkern u1="&#x17d;" u2="&#xd3;" k="49" />
    <hkern u1="&#x17d;" u2="&#xd2;" k="49" />
    <hkern u1="&#x17d;" u2="&#xc7;" k="49" />
    <hkern u1="&#x17d;" u2="&#xbb;" k="64" />
    <hkern u1="&#x17d;" u2="&#xb7;" k="64" />
    <hkern u1="&#x17d;" u2="&#xae;" k="49" />
    <hkern u1="&#x17d;" u2="&#xad;" k="64" />
    <hkern u1="&#x17d;" u2="&#xab;" k="64" />
    <hkern u1="&#x17d;" u2="&#xa9;" k="49" />
    <hkern u1="&#x17d;" u2="y" k="34" />
    <hkern u1="&#x17d;" u2="v" k="34" />
    <hkern u1="&#x17d;" u2="s" k="19" />
    <hkern u1="&#x17d;" u2="q" k="29" />
    <hkern u1="&#x17d;" u2="o" k="29" />
    <hkern u1="&#x17d;" u2="e" k="29" />
    <hkern u1="&#x17d;" u2="d" k="29" />
    <hkern u1="&#x17d;" u2="c" k="29" />
    <hkern u1="&#x17d;" u2="Q" k="49" />
    <hkern u1="&#x17d;" u2="O" k="49" />
    <hkern u1="&#x17d;" u2="G" k="49" />
    <hkern u1="&#x17d;" u2="C" k="49" />
    <hkern u1="&#x17d;" u2="&#x40;" k="49" />
    <hkern u1="&#x17d;" u2="&#x3f;" k="-32" />
    <hkern u1="&#x17d;" u2="&#x2d;" k="64" />
    <hkern u1="&#x2013;" u2="&#x2206;" k="67" />
    <hkern u1="&#x2013;" u2="&#x2122;" k="169" />
    <hkern u1="&#x2013;" u2="&#x201e;" k="132" />
    <hkern u1="&#x2013;" u2="&#x201d;" k="169" />
    <hkern u1="&#x2013;" u2="&#x201c;" k="169" />
    <hkern u1="&#x2013;" u2="&#x201a;" k="132" />
    <hkern u1="&#x2013;" u2="&#x2019;" k="169" />
    <hkern u1="&#x2013;" u2="&#x2018;" k="169" />
    <hkern u1="&#x2013;" u2="&#x17d;" k="48" />
    <hkern u1="&#x2013;" u2="&#x17b;" k="48" />
    <hkern u1="&#x2013;" u2="&#x179;" k="48" />
    <hkern u1="&#x2013;" u2="&#x178;" k="160" />
    <hkern u1="&#x2013;" u2="&#x104;" k="67" />
    <hkern u1="&#x2013;" u2="&#xdd;" k="160" />
    <hkern u1="&#x2013;" u2="&#xc6;" k="67" />
    <hkern u1="&#x2013;" u2="&#xc5;" k="67" />
    <hkern u1="&#x2013;" u2="&#xc4;" k="67" />
    <hkern u1="&#x2013;" u2="&#xc3;" k="67" />
    <hkern u1="&#x2013;" u2="&#xc2;" k="67" />
    <hkern u1="&#x2013;" u2="&#xc1;" k="67" />
    <hkern u1="&#x2013;" u2="&#xc0;" k="67" />
    <hkern u1="&#x2013;" u2="&#xba;" k="169" />
    <hkern u1="&#x2013;" u2="&#xb0;" k="169" />
    <hkern u1="&#x2013;" u2="&#xaa;" k="169" />
    <hkern u1="&#x2013;" u2="\" k="116" />
    <hkern u1="&#x2013;" u2="Z" k="48" />
    <hkern u1="&#x2013;" u2="Y" k="160" />
    <hkern u1="&#x2013;" u2="X" k="66" />
    <hkern u1="&#x2013;" u2="W" k="36" />
    <hkern u1="&#x2013;" u2="V" k="116" />
    <hkern u1="&#x2013;" u2="T" k="180" />
    <hkern u1="&#x2013;" u2="A" k="67" />
    <hkern u1="&#x2013;" u2="&#x2f;" k="67" />
    <hkern u1="&#x2013;" u2="&#x2e;" k="132" />
    <hkern u1="&#x2013;" u2="&#x2c;" k="132" />
    <hkern u1="&#x2013;" u2="&#x2a;" k="169" />
    <hkern u1="&#x2013;" u2="&#x27;" k="169" />
    <hkern u1="&#x2013;" u2="&#x26;" k="67" />
    <hkern u1="&#x2013;" u2="&#x22;" k="169" />
    <hkern u1="&#x2014;" u2="&#x2206;" k="67" />
    <hkern u1="&#x2014;" u2="&#x2122;" k="169" />
    <hkern u1="&#x2014;" u2="&#x201e;" k="132" />
    <hkern u1="&#x2014;" u2="&#x201d;" k="169" />
    <hkern u1="&#x2014;" u2="&#x201c;" k="169" />
    <hkern u1="&#x2014;" u2="&#x201a;" k="132" />
    <hkern u1="&#x2014;" u2="&#x2019;" k="169" />
    <hkern u1="&#x2014;" u2="&#x2018;" k="169" />
    <hkern u1="&#x2014;" u2="&#x17d;" k="48" />
    <hkern u1="&#x2014;" u2="&#x17b;" k="48" />
    <hkern u1="&#x2014;" u2="&#x179;" k="48" />
    <hkern u1="&#x2014;" u2="&#x178;" k="160" />
    <hkern u1="&#x2014;" u2="&#x104;" k="67" />
    <hkern u1="&#x2014;" u2="&#xdd;" k="160" />
    <hkern u1="&#x2014;" u2="&#xc6;" k="67" />
    <hkern u1="&#x2014;" u2="&#xc5;" k="67" />
    <hkern u1="&#x2014;" u2="&#xc4;" k="67" />
    <hkern u1="&#x2014;" u2="&#xc3;" k="67" />
    <hkern u1="&#x2014;" u2="&#xc2;" k="67" />
    <hkern u1="&#x2014;" u2="&#xc1;" k="67" />
    <hkern u1="&#x2014;" u2="&#xc0;" k="67" />
    <hkern u1="&#x2014;" u2="&#xba;" k="169" />
    <hkern u1="&#x2014;" u2="&#xb0;" k="169" />
    <hkern u1="&#x2014;" u2="&#xaa;" k="169" />
    <hkern u1="&#x2014;" u2="\" k="116" />
    <hkern u1="&#x2014;" u2="Z" k="48" />
    <hkern u1="&#x2014;" u2="Y" k="160" />
    <hkern u1="&#x2014;" u2="X" k="66" />
    <hkern u1="&#x2014;" u2="W" k="36" />
    <hkern u1="&#x2014;" u2="V" k="116" />
    <hkern u1="&#x2014;" u2="T" k="180" />
    <hkern u1="&#x2014;" u2="A" k="67" />
    <hkern u1="&#x2014;" u2="&#x2f;" k="67" />
    <hkern u1="&#x2014;" u2="&#x2e;" k="132" />
    <hkern u1="&#x2014;" u2="&#x2c;" k="132" />
    <hkern u1="&#x2014;" u2="&#x2a;" k="169" />
    <hkern u1="&#x2014;" u2="&#x27;" k="169" />
    <hkern u1="&#x2014;" u2="&#x26;" k="67" />
    <hkern u1="&#x2014;" u2="&#x22;" k="169" />
    <hkern u1="&#x2018;" u2="&#x2206;" k="191" />
    <hkern u1="&#x2018;" u2="&#x203a;" k="169" />
    <hkern u1="&#x2018;" u2="&#x2039;" k="169" />
    <hkern u1="&#x2018;" u2="&#x2022;" k="169" />
    <hkern u1="&#x2018;" u2="&#x201e;" k="213" />
    <hkern u1="&#x2018;" u2="&#x201a;" k="213" />
    <hkern u1="&#x2018;" u2="&#x2014;" k="169" />
    <hkern u1="&#x2018;" u2="&#x2013;" k="169" />
    <hkern u1="&#x2018;" u2="&#x178;" k="-36" />
    <hkern u1="&#x2018;" u2="&#x153;" k="96" />
    <hkern u1="&#x2018;" u2="&#x152;" k="42" />
    <hkern u1="&#x2018;" u2="&#x119;" k="96" />
    <hkern u1="&#x2018;" u2="&#x107;" k="96" />
    <hkern u1="&#x2018;" u2="&#x106;" k="42" />
    <hkern u1="&#x2018;" u2="&#x105;" k="66" />
    <hkern u1="&#x2018;" u2="&#x104;" k="191" />
    <hkern u1="&#x2018;" u2="&#xf8;" k="96" />
    <hkern u1="&#x2018;" u2="&#xf6;" k="96" />
    <hkern u1="&#x2018;" u2="&#xf5;" k="96" />
    <hkern u1="&#x2018;" u2="&#xf4;" k="96" />
    <hkern u1="&#x2018;" u2="&#xf3;" k="96" />
    <hkern u1="&#x2018;" u2="&#xf2;" k="96" />
    <hkern u1="&#x2018;" u2="&#xf0;" k="96" />
    <hkern u1="&#x2018;" u2="&#xeb;" k="96" />
    <hkern u1="&#x2018;" u2="&#xea;" k="96" />
    <hkern u1="&#x2018;" u2="&#xe9;" k="96" />
    <hkern u1="&#x2018;" u2="&#xe8;" k="96" />
    <hkern u1="&#x2018;" u2="&#xe7;" k="96" />
    <hkern u1="&#x2018;" u2="&#xe6;" k="66" />
    <hkern u1="&#x2018;" u2="&#xe5;" k="66" />
    <hkern u1="&#x2018;" u2="&#xe4;" k="66" />
    <hkern u1="&#x2018;" u2="&#xe3;" k="66" />
    <hkern u1="&#x2018;" u2="&#xe2;" k="66" />
    <hkern u1="&#x2018;" u2="&#xe1;" k="66" />
    <hkern u1="&#x2018;" u2="&#xe0;" k="66" />
    <hkern u1="&#x2018;" u2="&#xdd;" k="-36" />
    <hkern u1="&#x2018;" u2="&#xd8;" k="42" />
    <hkern u1="&#x2018;" u2="&#xd6;" k="42" />
    <hkern u1="&#x2018;" u2="&#xd5;" k="42" />
    <hkern u1="&#x2018;" u2="&#xd4;" k="42" />
    <hkern u1="&#x2018;" u2="&#xd3;" k="42" />
    <hkern u1="&#x2018;" u2="&#xd2;" k="42" />
    <hkern u1="&#x2018;" u2="&#xc7;" k="42" />
    <hkern u1="&#x2018;" u2="&#xc6;" k="191" />
    <hkern u1="&#x2018;" u2="&#xc5;" k="191" />
    <hkern u1="&#x2018;" u2="&#xc4;" k="191" />
    <hkern u1="&#x2018;" u2="&#xc3;" k="191" />
    <hkern u1="&#x2018;" u2="&#xc2;" k="191" />
    <hkern u1="&#x2018;" u2="&#xc1;" k="191" />
    <hkern u1="&#x2018;" u2="&#xc0;" k="191" />
    <hkern u1="&#x2018;" u2="&#xbb;" k="169" />
    <hkern u1="&#x2018;" u2="&#xb7;" k="169" />
    <hkern u1="&#x2018;" u2="&#xae;" k="42" />
    <hkern u1="&#x2018;" u2="&#xad;" k="169" />
    <hkern u1="&#x2018;" u2="&#xab;" k="169" />
    <hkern u1="&#x2018;" u2="&#xa9;" k="42" />
    <hkern u1="&#x2018;" u2="q" k="96" />
    <hkern u1="&#x2018;" u2="o" k="96" />
    <hkern u1="&#x2018;" u2="e" k="96" />
    <hkern u1="&#x2018;" u2="d" k="96" />
    <hkern u1="&#x2018;" u2="c" k="96" />
    <hkern u1="&#x2018;" u2="a" k="66" />
    <hkern u1="&#x2018;" u2="\" k="-44" />
    <hkern u1="&#x2018;" u2="Y" k="-36" />
    <hkern u1="&#x2018;" u2="W" k="-44" />
    <hkern u1="&#x2018;" u2="V" k="-44" />
    <hkern u1="&#x2018;" u2="Q" k="42" />
    <hkern u1="&#x2018;" u2="O" k="42" />
    <hkern u1="&#x2018;" u2="G" k="42" />
    <hkern u1="&#x2018;" u2="C" k="42" />
    <hkern u1="&#x2018;" u2="A" k="191" />
    <hkern u1="&#x2018;" u2="&#x40;" k="42" />
    <hkern u1="&#x2018;" u2="&#x2f;" k="191" />
    <hkern u1="&#x2018;" u2="&#x2e;" k="213" />
    <hkern u1="&#x2018;" u2="&#x2d;" k="169" />
    <hkern u1="&#x2018;" u2="&#x2c;" k="213" />
    <hkern u1="&#x2018;" u2="&#x26;" k="191" />
    <hkern u1="&#x2019;" u2="&#x2206;" k="191" />
    <hkern u1="&#x2019;" u2="&#x203a;" k="169" />
    <hkern u1="&#x2019;" u2="&#x2039;" k="169" />
    <hkern u1="&#x2019;" u2="&#x2022;" k="169" />
    <hkern u1="&#x2019;" u2="&#x201e;" k="213" />
    <hkern u1="&#x2019;" u2="&#x201a;" k="213" />
    <hkern u1="&#x2019;" u2="&#x2014;" k="169" />
    <hkern u1="&#x2019;" u2="&#x2013;" k="169" />
    <hkern u1="&#x2019;" u2="&#x178;" k="-36" />
    <hkern u1="&#x2019;" u2="&#x153;" k="96" />
    <hkern u1="&#x2019;" u2="&#x152;" k="42" />
    <hkern u1="&#x2019;" u2="&#x119;" k="96" />
    <hkern u1="&#x2019;" u2="&#x107;" k="96" />
    <hkern u1="&#x2019;" u2="&#x106;" k="42" />
    <hkern u1="&#x2019;" u2="&#x105;" k="66" />
    <hkern u1="&#x2019;" u2="&#x104;" k="191" />
    <hkern u1="&#x2019;" u2="&#xf8;" k="96" />
    <hkern u1="&#x2019;" u2="&#xf6;" k="96" />
    <hkern u1="&#x2019;" u2="&#xf5;" k="96" />
    <hkern u1="&#x2019;" u2="&#xf4;" k="96" />
    <hkern u1="&#x2019;" u2="&#xf3;" k="96" />
    <hkern u1="&#x2019;" u2="&#xf2;" k="96" />
    <hkern u1="&#x2019;" u2="&#xf0;" k="96" />
    <hkern u1="&#x2019;" u2="&#xeb;" k="96" />
    <hkern u1="&#x2019;" u2="&#xea;" k="96" />
    <hkern u1="&#x2019;" u2="&#xe9;" k="96" />
    <hkern u1="&#x2019;" u2="&#xe8;" k="96" />
    <hkern u1="&#x2019;" u2="&#xe7;" k="96" />
    <hkern u1="&#x2019;" u2="&#xe6;" k="66" />
    <hkern u1="&#x2019;" u2="&#xe5;" k="66" />
    <hkern u1="&#x2019;" u2="&#xe4;" k="66" />
    <hkern u1="&#x2019;" u2="&#xe3;" k="66" />
    <hkern u1="&#x2019;" u2="&#xe2;" k="66" />
    <hkern u1="&#x2019;" u2="&#xe1;" k="66" />
    <hkern u1="&#x2019;" u2="&#xe0;" k="66" />
    <hkern u1="&#x2019;" u2="&#xdd;" k="-36" />
    <hkern u1="&#x2019;" u2="&#xd8;" k="42" />
    <hkern u1="&#x2019;" u2="&#xd6;" k="42" />
    <hkern u1="&#x2019;" u2="&#xd5;" k="42" />
    <hkern u1="&#x2019;" u2="&#xd4;" k="42" />
    <hkern u1="&#x2019;" u2="&#xd3;" k="42" />
    <hkern u1="&#x2019;" u2="&#xd2;" k="42" />
    <hkern u1="&#x2019;" u2="&#xc7;" k="42" />
    <hkern u1="&#x2019;" u2="&#xc6;" k="191" />
    <hkern u1="&#x2019;" u2="&#xc5;" k="191" />
    <hkern u1="&#x2019;" u2="&#xc4;" k="191" />
    <hkern u1="&#x2019;" u2="&#xc3;" k="191" />
    <hkern u1="&#x2019;" u2="&#xc2;" k="191" />
    <hkern u1="&#x2019;" u2="&#xc1;" k="191" />
    <hkern u1="&#x2019;" u2="&#xc0;" k="191" />
    <hkern u1="&#x2019;" u2="&#xbb;" k="169" />
    <hkern u1="&#x2019;" u2="&#xb7;" k="169" />
    <hkern u1="&#x2019;" u2="&#xae;" k="42" />
    <hkern u1="&#x2019;" u2="&#xad;" k="169" />
    <hkern u1="&#x2019;" u2="&#xab;" k="169" />
    <hkern u1="&#x2019;" u2="&#xa9;" k="42" />
    <hkern u1="&#x2019;" u2="q" k="96" />
    <hkern u1="&#x2019;" u2="o" k="96" />
    <hkern u1="&#x2019;" u2="e" k="96" />
    <hkern u1="&#x2019;" u2="d" k="96" />
    <hkern u1="&#x2019;" u2="c" k="96" />
    <hkern u1="&#x2019;" u2="a" k="66" />
    <hkern u1="&#x2019;" u2="\" k="-44" />
    <hkern u1="&#x2019;" u2="Y" k="-36" />
    <hkern u1="&#x2019;" u2="W" k="-44" />
    <hkern u1="&#x2019;" u2="V" k="-44" />
    <hkern u1="&#x2019;" u2="Q" k="42" />
    <hkern u1="&#x2019;" u2="O" k="42" />
    <hkern u1="&#x2019;" u2="G" k="42" />
    <hkern u1="&#x2019;" u2="C" k="42" />
    <hkern u1="&#x2019;" u2="A" k="191" />
    <hkern u1="&#x2019;" u2="&#x40;" k="42" />
    <hkern u1="&#x2019;" u2="&#x2f;" k="191" />
    <hkern u1="&#x2019;" u2="&#x2e;" k="213" />
    <hkern u1="&#x2019;" u2="&#x2d;" k="169" />
    <hkern u1="&#x2019;" u2="&#x2c;" k="213" />
    <hkern u1="&#x2019;" u2="&#x26;" k="191" />
    <hkern u1="&#x201a;" u2="&#x2122;" k="213" />
    <hkern u1="&#x201a;" u2="&#x203a;" k="132" />
    <hkern u1="&#x201a;" u2="&#x2039;" k="132" />
    <hkern u1="&#x201a;" u2="&#x2022;" k="132" />
    <hkern u1="&#x201a;" u2="&#x201d;" k="213" />
    <hkern u1="&#x201a;" u2="&#x201c;" k="213" />
    <hkern u1="&#x201a;" u2="&#x2019;" k="213" />
    <hkern u1="&#x201a;" u2="&#x2018;" k="213" />
    <hkern u1="&#x201a;" u2="&#x2014;" k="132" />
    <hkern u1="&#x201a;" u2="&#x2013;" k="132" />
    <hkern u1="&#x201a;" u2="&#x178;" k="167" />
    <hkern u1="&#x201a;" u2="&#x152;" k="52" />
    <hkern u1="&#x201a;" u2="&#x106;" k="52" />
    <hkern u1="&#x201a;" u2="&#xff;" k="136" />
    <hkern u1="&#x201a;" u2="&#xfd;" k="136" />
    <hkern u1="&#x201a;" u2="&#xdd;" k="167" />
    <hkern u1="&#x201a;" u2="&#xd8;" k="52" />
    <hkern u1="&#x201a;" u2="&#xd6;" k="52" />
    <hkern u1="&#x201a;" u2="&#xd5;" k="52" />
    <hkern u1="&#x201a;" u2="&#xd4;" k="52" />
    <hkern u1="&#x201a;" u2="&#xd3;" k="52" />
    <hkern u1="&#x201a;" u2="&#xd2;" k="52" />
    <hkern u1="&#x201a;" u2="&#xc7;" k="52" />
    <hkern u1="&#x201a;" u2="&#xbb;" k="132" />
    <hkern u1="&#x201a;" u2="&#xba;" k="213" />
    <hkern u1="&#x201a;" u2="&#xb7;" k="132" />
    <hkern u1="&#x201a;" u2="&#xb0;" k="213" />
    <hkern u1="&#x201a;" u2="&#xae;" k="52" />
    <hkern u1="&#x201a;" u2="&#xad;" k="132" />
    <hkern u1="&#x201a;" u2="&#xab;" k="132" />
    <hkern u1="&#x201a;" u2="&#xaa;" k="213" />
    <hkern u1="&#x201a;" u2="&#xa9;" k="52" />
    <hkern u1="&#x201a;" u2="y" k="136" />
    <hkern u1="&#x201a;" u2="w" k="71" />
    <hkern u1="&#x201a;" u2="v" k="136" />
    <hkern u1="&#x201a;" u2="\" k="180" />
    <hkern u1="&#x201a;" u2="Y" k="167" />
    <hkern u1="&#x201a;" u2="W" k="131" />
    <hkern u1="&#x201a;" u2="V" k="180" />
    <hkern u1="&#x201a;" u2="T" k="180" />
    <hkern u1="&#x201a;" u2="Q" k="52" />
    <hkern u1="&#x201a;" u2="O" k="52" />
    <hkern u1="&#x201a;" u2="G" k="52" />
    <hkern u1="&#x201a;" u2="C" k="52" />
    <hkern u1="&#x201a;" u2="&#x40;" k="52" />
    <hkern u1="&#x201a;" u2="&#x2d;" k="132" />
    <hkern u1="&#x201a;" u2="&#x2a;" k="213" />
    <hkern u1="&#x201a;" u2="&#x27;" k="213" />
    <hkern u1="&#x201a;" u2="&#x22;" k="213" />
    <hkern u1="&#x201c;" u2="&#x2206;" k="191" />
    <hkern u1="&#x201c;" u2="&#x203a;" k="169" />
    <hkern u1="&#x201c;" u2="&#x2039;" k="169" />
    <hkern u1="&#x201c;" u2="&#x2022;" k="169" />
    <hkern u1="&#x201c;" u2="&#x201e;" k="213" />
    <hkern u1="&#x201c;" u2="&#x201a;" k="213" />
    <hkern u1="&#x201c;" u2="&#x2014;" k="169" />
    <hkern u1="&#x201c;" u2="&#x2013;" k="169" />
    <hkern u1="&#x201c;" u2="&#x178;" k="-36" />
    <hkern u1="&#x201c;" u2="&#x153;" k="96" />
    <hkern u1="&#x201c;" u2="&#x152;" k="42" />
    <hkern u1="&#x201c;" u2="&#x119;" k="96" />
    <hkern u1="&#x201c;" u2="&#x107;" k="96" />
    <hkern u1="&#x201c;" u2="&#x106;" k="42" />
    <hkern u1="&#x201c;" u2="&#x105;" k="66" />
    <hkern u1="&#x201c;" u2="&#x104;" k="191" />
    <hkern u1="&#x201c;" u2="&#xf8;" k="96" />
    <hkern u1="&#x201c;" u2="&#xf6;" k="96" />
    <hkern u1="&#x201c;" u2="&#xf5;" k="96" />
    <hkern u1="&#x201c;" u2="&#xf4;" k="96" />
    <hkern u1="&#x201c;" u2="&#xf3;" k="96" />
    <hkern u1="&#x201c;" u2="&#xf2;" k="96" />
    <hkern u1="&#x201c;" u2="&#xf0;" k="96" />
    <hkern u1="&#x201c;" u2="&#xeb;" k="96" />
    <hkern u1="&#x201c;" u2="&#xea;" k="96" />
    <hkern u1="&#x201c;" u2="&#xe9;" k="96" />
    <hkern u1="&#x201c;" u2="&#xe8;" k="96" />
    <hkern u1="&#x201c;" u2="&#xe7;" k="96" />
    <hkern u1="&#x201c;" u2="&#xe6;" k="66" />
    <hkern u1="&#x201c;" u2="&#xe5;" k="66" />
    <hkern u1="&#x201c;" u2="&#xe4;" k="66" />
    <hkern u1="&#x201c;" u2="&#xe3;" k="66" />
    <hkern u1="&#x201c;" u2="&#xe2;" k="66" />
    <hkern u1="&#x201c;" u2="&#xe1;" k="66" />
    <hkern u1="&#x201c;" u2="&#xe0;" k="66" />
    <hkern u1="&#x201c;" u2="&#xdd;" k="-36" />
    <hkern u1="&#x201c;" u2="&#xd8;" k="42" />
    <hkern u1="&#x201c;" u2="&#xd6;" k="42" />
    <hkern u1="&#x201c;" u2="&#xd5;" k="42" />
    <hkern u1="&#x201c;" u2="&#xd4;" k="42" />
    <hkern u1="&#x201c;" u2="&#xd3;" k="42" />
    <hkern u1="&#x201c;" u2="&#xd2;" k="42" />
    <hkern u1="&#x201c;" u2="&#xc7;" k="42" />
    <hkern u1="&#x201c;" u2="&#xc6;" k="191" />
    <hkern u1="&#x201c;" u2="&#xc5;" k="191" />
    <hkern u1="&#x201c;" u2="&#xc4;" k="191" />
    <hkern u1="&#x201c;" u2="&#xc3;" k="191" />
    <hkern u1="&#x201c;" u2="&#xc2;" k="191" />
    <hkern u1="&#x201c;" u2="&#xc1;" k="191" />
    <hkern u1="&#x201c;" u2="&#xc0;" k="191" />
    <hkern u1="&#x201c;" u2="&#xbb;" k="169" />
    <hkern u1="&#x201c;" u2="&#xb7;" k="169" />
    <hkern u1="&#x201c;" u2="&#xae;" k="42" />
    <hkern u1="&#x201c;" u2="&#xad;" k="169" />
    <hkern u1="&#x201c;" u2="&#xab;" k="169" />
    <hkern u1="&#x201c;" u2="&#xa9;" k="42" />
    <hkern u1="&#x201c;" u2="q" k="96" />
    <hkern u1="&#x201c;" u2="o" k="96" />
    <hkern u1="&#x201c;" u2="e" k="96" />
    <hkern u1="&#x201c;" u2="d" k="96" />
    <hkern u1="&#x201c;" u2="c" k="96" />
    <hkern u1="&#x201c;" u2="a" k="66" />
    <hkern u1="&#x201c;" u2="\" k="-44" />
    <hkern u1="&#x201c;" u2="Y" k="-36" />
    <hkern u1="&#x201c;" u2="W" k="-44" />
    <hkern u1="&#x201c;" u2="V" k="-44" />
    <hkern u1="&#x201c;" u2="Q" k="42" />
    <hkern u1="&#x201c;" u2="O" k="42" />
    <hkern u1="&#x201c;" u2="G" k="42" />
    <hkern u1="&#x201c;" u2="C" k="42" />
    <hkern u1="&#x201c;" u2="A" k="191" />
    <hkern u1="&#x201c;" u2="&#x40;" k="42" />
    <hkern u1="&#x201c;" u2="&#x2f;" k="191" />
    <hkern u1="&#x201c;" u2="&#x2e;" k="213" />
    <hkern u1="&#x201c;" u2="&#x2d;" k="169" />
    <hkern u1="&#x201c;" u2="&#x2c;" k="213" />
    <hkern u1="&#x201c;" u2="&#x26;" k="191" />
    <hkern u1="&#x201d;" u2="&#x2206;" k="191" />
    <hkern u1="&#x201d;" u2="&#x203a;" k="169" />
    <hkern u1="&#x201d;" u2="&#x2039;" k="169" />
    <hkern u1="&#x201d;" u2="&#x2022;" k="169" />
    <hkern u1="&#x201d;" u2="&#x201e;" k="213" />
    <hkern u1="&#x201d;" u2="&#x201a;" k="213" />
    <hkern u1="&#x201d;" u2="&#x2014;" k="169" />
    <hkern u1="&#x201d;" u2="&#x2013;" k="169" />
    <hkern u1="&#x201d;" u2="&#x178;" k="-36" />
    <hkern u1="&#x201d;" u2="&#x153;" k="96" />
    <hkern u1="&#x201d;" u2="&#x152;" k="42" />
    <hkern u1="&#x201d;" u2="&#x119;" k="96" />
    <hkern u1="&#x201d;" u2="&#x107;" k="96" />
    <hkern u1="&#x201d;" u2="&#x106;" k="42" />
    <hkern u1="&#x201d;" u2="&#x105;" k="66" />
    <hkern u1="&#x201d;" u2="&#x104;" k="191" />
    <hkern u1="&#x201d;" u2="&#xf8;" k="96" />
    <hkern u1="&#x201d;" u2="&#xf6;" k="96" />
    <hkern u1="&#x201d;" u2="&#xf5;" k="96" />
    <hkern u1="&#x201d;" u2="&#xf4;" k="96" />
    <hkern u1="&#x201d;" u2="&#xf3;" k="96" />
    <hkern u1="&#x201d;" u2="&#xf2;" k="96" />
    <hkern u1="&#x201d;" u2="&#xf0;" k="96" />
    <hkern u1="&#x201d;" u2="&#xeb;" k="96" />
    <hkern u1="&#x201d;" u2="&#xea;" k="96" />
    <hkern u1="&#x201d;" u2="&#xe9;" k="96" />
    <hkern u1="&#x201d;" u2="&#xe8;" k="96" />
    <hkern u1="&#x201d;" u2="&#xe7;" k="96" />
    <hkern u1="&#x201d;" u2="&#xe6;" k="66" />
    <hkern u1="&#x201d;" u2="&#xe5;" k="66" />
    <hkern u1="&#x201d;" u2="&#xe4;" k="66" />
    <hkern u1="&#x201d;" u2="&#xe3;" k="66" />
    <hkern u1="&#x201d;" u2="&#xe2;" k="66" />
    <hkern u1="&#x201d;" u2="&#xe1;" k="66" />
    <hkern u1="&#x201d;" u2="&#xe0;" k="66" />
    <hkern u1="&#x201d;" u2="&#xdd;" k="-36" />
    <hkern u1="&#x201d;" u2="&#xd8;" k="42" />
    <hkern u1="&#x201d;" u2="&#xd6;" k="42" />
    <hkern u1="&#x201d;" u2="&#xd5;" k="42" />
    <hkern u1="&#x201d;" u2="&#xd4;" k="42" />
    <hkern u1="&#x201d;" u2="&#xd3;" k="42" />
    <hkern u1="&#x201d;" u2="&#xd2;" k="42" />
    <hkern u1="&#x201d;" u2="&#xc7;" k="42" />
    <hkern u1="&#x201d;" u2="&#xc6;" k="191" />
    <hkern u1="&#x201d;" u2="&#xc5;" k="191" />
    <hkern u1="&#x201d;" u2="&#xc4;" k="191" />
    <hkern u1="&#x201d;" u2="&#xc3;" k="191" />
    <hkern u1="&#x201d;" u2="&#xc2;" k="191" />
    <hkern u1="&#x201d;" u2="&#xc1;" k="191" />
    <hkern u1="&#x201d;" u2="&#xc0;" k="191" />
    <hkern u1="&#x201d;" u2="&#xbb;" k="169" />
    <hkern u1="&#x201d;" u2="&#xb7;" k="169" />
    <hkern u1="&#x201d;" u2="&#xae;" k="42" />
    <hkern u1="&#x201d;" u2="&#xad;" k="169" />
    <hkern u1="&#x201d;" u2="&#xab;" k="169" />
    <hkern u1="&#x201d;" u2="&#xa9;" k="42" />
    <hkern u1="&#x201d;" u2="q" k="96" />
    <hkern u1="&#x201d;" u2="o" k="96" />
    <hkern u1="&#x201d;" u2="e" k="96" />
    <hkern u1="&#x201d;" u2="d" k="96" />
    <hkern u1="&#x201d;" u2="c" k="96" />
    <hkern u1="&#x201d;" u2="a" k="66" />
    <hkern u1="&#x201d;" u2="\" k="-44" />
    <hkern u1="&#x201d;" u2="Y" k="-36" />
    <hkern u1="&#x201d;" u2="W" k="-44" />
    <hkern u1="&#x201d;" u2="V" k="-44" />
    <hkern u1="&#x201d;" u2="Q" k="42" />
    <hkern u1="&#x201d;" u2="O" k="42" />
    <hkern u1="&#x201d;" u2="G" k="42" />
    <hkern u1="&#x201d;" u2="C" k="42" />
    <hkern u1="&#x201d;" u2="A" k="191" />
    <hkern u1="&#x201d;" u2="&#x40;" k="42" />
    <hkern u1="&#x201d;" u2="&#x2f;" k="191" />
    <hkern u1="&#x201d;" u2="&#x2e;" k="213" />
    <hkern u1="&#x201d;" u2="&#x2d;" k="169" />
    <hkern u1="&#x201d;" u2="&#x2c;" k="213" />
    <hkern u1="&#x201d;" u2="&#x26;" k="191" />
    <hkern u1="&#x201e;" u2="&#x2122;" k="213" />
    <hkern u1="&#x201e;" u2="&#x203a;" k="132" />
    <hkern u1="&#x201e;" u2="&#x2039;" k="132" />
    <hkern u1="&#x201e;" u2="&#x2022;" k="132" />
    <hkern u1="&#x201e;" u2="&#x201d;" k="213" />
    <hkern u1="&#x201e;" u2="&#x201c;" k="213" />
    <hkern u1="&#x201e;" u2="&#x2019;" k="213" />
    <hkern u1="&#x201e;" u2="&#x2018;" k="213" />
    <hkern u1="&#x201e;" u2="&#x2014;" k="132" />
    <hkern u1="&#x201e;" u2="&#x2013;" k="132" />
    <hkern u1="&#x201e;" u2="&#x178;" k="167" />
    <hkern u1="&#x201e;" u2="&#x152;" k="52" />
    <hkern u1="&#x201e;" u2="&#x106;" k="52" />
    <hkern u1="&#x201e;" u2="&#xff;" k="136" />
    <hkern u1="&#x201e;" u2="&#xfd;" k="136" />
    <hkern u1="&#x201e;" u2="&#xdd;" k="167" />
    <hkern u1="&#x201e;" u2="&#xd8;" k="52" />
    <hkern u1="&#x201e;" u2="&#xd6;" k="52" />
    <hkern u1="&#x201e;" u2="&#xd5;" k="52" />
    <hkern u1="&#x201e;" u2="&#xd4;" k="52" />
    <hkern u1="&#x201e;" u2="&#xd3;" k="52" />
    <hkern u1="&#x201e;" u2="&#xd2;" k="52" />
    <hkern u1="&#x201e;" u2="&#xc7;" k="52" />
    <hkern u1="&#x201e;" u2="&#xbb;" k="132" />
    <hkern u1="&#x201e;" u2="&#xba;" k="213" />
    <hkern u1="&#x201e;" u2="&#xb7;" k="132" />
    <hkern u1="&#x201e;" u2="&#xb0;" k="213" />
    <hkern u1="&#x201e;" u2="&#xae;" k="52" />
    <hkern u1="&#x201e;" u2="&#xad;" k="132" />
    <hkern u1="&#x201e;" u2="&#xab;" k="132" />
    <hkern u1="&#x201e;" u2="&#xaa;" k="213" />
    <hkern u1="&#x201e;" u2="&#xa9;" k="52" />
    <hkern u1="&#x201e;" u2="y" k="136" />
    <hkern u1="&#x201e;" u2="w" k="71" />
    <hkern u1="&#x201e;" u2="v" k="136" />
    <hkern u1="&#x201e;" u2="\" k="180" />
    <hkern u1="&#x201e;" u2="Y" k="167" />
    <hkern u1="&#x201e;" u2="W" k="131" />
    <hkern u1="&#x201e;" u2="V" k="180" />
    <hkern u1="&#x201e;" u2="T" k="180" />
    <hkern u1="&#x201e;" u2="Q" k="52" />
    <hkern u1="&#x201e;" u2="O" k="52" />
    <hkern u1="&#x201e;" u2="G" k="52" />
    <hkern u1="&#x201e;" u2="C" k="52" />
    <hkern u1="&#x201e;" u2="&#x40;" k="52" />
    <hkern u1="&#x201e;" u2="&#x2d;" k="132" />
    <hkern u1="&#x201e;" u2="&#x2a;" k="213" />
    <hkern u1="&#x201e;" u2="&#x27;" k="213" />
    <hkern u1="&#x201e;" u2="&#x22;" k="213" />
    <hkern u1="&#x2022;" u2="&#x2206;" k="67" />
    <hkern u1="&#x2022;" u2="&#x2122;" k="169" />
    <hkern u1="&#x2022;" u2="&#x201e;" k="132" />
    <hkern u1="&#x2022;" u2="&#x201d;" k="169" />
    <hkern u1="&#x2022;" u2="&#x201c;" k="169" />
    <hkern u1="&#x2022;" u2="&#x201a;" k="132" />
    <hkern u1="&#x2022;" u2="&#x2019;" k="169" />
    <hkern u1="&#x2022;" u2="&#x2018;" k="169" />
    <hkern u1="&#x2022;" u2="&#x17d;" k="48" />
    <hkern u1="&#x2022;" u2="&#x17b;" k="48" />
    <hkern u1="&#x2022;" u2="&#x179;" k="48" />
    <hkern u1="&#x2022;" u2="&#x178;" k="160" />
    <hkern u1="&#x2022;" u2="&#x104;" k="67" />
    <hkern u1="&#x2022;" u2="&#xdd;" k="160" />
    <hkern u1="&#x2022;" u2="&#xc6;" k="67" />
    <hkern u1="&#x2022;" u2="&#xc5;" k="67" />
    <hkern u1="&#x2022;" u2="&#xc4;" k="67" />
    <hkern u1="&#x2022;" u2="&#xc3;" k="67" />
    <hkern u1="&#x2022;" u2="&#xc2;" k="67" />
    <hkern u1="&#x2022;" u2="&#xc1;" k="67" />
    <hkern u1="&#x2022;" u2="&#xc0;" k="67" />
    <hkern u1="&#x2022;" u2="&#xba;" k="169" />
    <hkern u1="&#x2022;" u2="&#xb0;" k="169" />
    <hkern u1="&#x2022;" u2="&#xaa;" k="169" />
    <hkern u1="&#x2022;" u2="\" k="116" />
    <hkern u1="&#x2022;" u2="Z" k="48" />
    <hkern u1="&#x2022;" u2="Y" k="160" />
    <hkern u1="&#x2022;" u2="X" k="66" />
    <hkern u1="&#x2022;" u2="W" k="36" />
    <hkern u1="&#x2022;" u2="V" k="116" />
    <hkern u1="&#x2022;" u2="T" k="180" />
    <hkern u1="&#x2022;" u2="A" k="67" />
    <hkern u1="&#x2022;" u2="&#x2f;" k="67" />
    <hkern u1="&#x2022;" u2="&#x2e;" k="132" />
    <hkern u1="&#x2022;" u2="&#x2c;" k="132" />
    <hkern u1="&#x2022;" u2="&#x2a;" k="169" />
    <hkern u1="&#x2022;" u2="&#x27;" k="169" />
    <hkern u1="&#x2022;" u2="&#x26;" k="67" />
    <hkern u1="&#x2022;" u2="&#x22;" k="169" />
    <hkern u1="&#x2039;" u2="&#x2206;" k="67" />
    <hkern u1="&#x2039;" u2="&#x2122;" k="169" />
    <hkern u1="&#x2039;" u2="&#x201e;" k="132" />
    <hkern u1="&#x2039;" u2="&#x201d;" k="169" />
    <hkern u1="&#x2039;" u2="&#x201c;" k="169" />
    <hkern u1="&#x2039;" u2="&#x201a;" k="132" />
    <hkern u1="&#x2039;" u2="&#x2019;" k="169" />
    <hkern u1="&#x2039;" u2="&#x2018;" k="169" />
    <hkern u1="&#x2039;" u2="&#x17d;" k="48" />
    <hkern u1="&#x2039;" u2="&#x17b;" k="48" />
    <hkern u1="&#x2039;" u2="&#x179;" k="48" />
    <hkern u1="&#x2039;" u2="&#x178;" k="160" />
    <hkern u1="&#x2039;" u2="&#x104;" k="67" />
    <hkern u1="&#x2039;" u2="&#xdd;" k="160" />
    <hkern u1="&#x2039;" u2="&#xc6;" k="67" />
    <hkern u1="&#x2039;" u2="&#xc5;" k="67" />
    <hkern u1="&#x2039;" u2="&#xc4;" k="67" />
    <hkern u1="&#x2039;" u2="&#xc3;" k="67" />
    <hkern u1="&#x2039;" u2="&#xc2;" k="67" />
    <hkern u1="&#x2039;" u2="&#xc1;" k="67" />
    <hkern u1="&#x2039;" u2="&#xc0;" k="67" />
    <hkern u1="&#x2039;" u2="&#xba;" k="169" />
    <hkern u1="&#x2039;" u2="&#xb0;" k="169" />
    <hkern u1="&#x2039;" u2="&#xaa;" k="169" />
    <hkern u1="&#x2039;" u2="\" k="116" />
    <hkern u1="&#x2039;" u2="Z" k="48" />
    <hkern u1="&#x2039;" u2="Y" k="160" />
    <hkern u1="&#x2039;" u2="X" k="66" />
    <hkern u1="&#x2039;" u2="W" k="36" />
    <hkern u1="&#x2039;" u2="V" k="116" />
    <hkern u1="&#x2039;" u2="T" k="180" />
    <hkern u1="&#x2039;" u2="A" k="67" />
    <hkern u1="&#x2039;" u2="&#x2f;" k="67" />
    <hkern u1="&#x2039;" u2="&#x2e;" k="132" />
    <hkern u1="&#x2039;" u2="&#x2c;" k="132" />
    <hkern u1="&#x2039;" u2="&#x2a;" k="169" />
    <hkern u1="&#x2039;" u2="&#x27;" k="169" />
    <hkern u1="&#x2039;" u2="&#x26;" k="67" />
    <hkern u1="&#x2039;" u2="&#x22;" k="169" />
    <hkern u1="&#x203a;" u2="&#x2206;" k="67" />
    <hkern u1="&#x203a;" u2="&#x2122;" k="169" />
    <hkern u1="&#x203a;" u2="&#x201e;" k="132" />
    <hkern u1="&#x203a;" u2="&#x201d;" k="169" />
    <hkern u1="&#x203a;" u2="&#x201c;" k="169" />
    <hkern u1="&#x203a;" u2="&#x201a;" k="132" />
    <hkern u1="&#x203a;" u2="&#x2019;" k="169" />
    <hkern u1="&#x203a;" u2="&#x2018;" k="169" />
    <hkern u1="&#x203a;" u2="&#x17d;" k="48" />
    <hkern u1="&#x203a;" u2="&#x17b;" k="48" />
    <hkern u1="&#x203a;" u2="&#x179;" k="48" />
    <hkern u1="&#x203a;" u2="&#x178;" k="160" />
    <hkern u1="&#x203a;" u2="&#x104;" k="67" />
    <hkern u1="&#x203a;" u2="&#xdd;" k="160" />
    <hkern u1="&#x203a;" u2="&#xc6;" k="67" />
    <hkern u1="&#x203a;" u2="&#xc5;" k="67" />
    <hkern u1="&#x203a;" u2="&#xc4;" k="67" />
    <hkern u1="&#x203a;" u2="&#xc3;" k="67" />
    <hkern u1="&#x203a;" u2="&#xc2;" k="67" />
    <hkern u1="&#x203a;" u2="&#xc1;" k="67" />
    <hkern u1="&#x203a;" u2="&#xc0;" k="67" />
    <hkern u1="&#x203a;" u2="&#xba;" k="169" />
    <hkern u1="&#x203a;" u2="&#xb0;" k="169" />
    <hkern u1="&#x203a;" u2="&#xaa;" k="169" />
    <hkern u1="&#x203a;" u2="\" k="116" />
    <hkern u1="&#x203a;" u2="Z" k="48" />
    <hkern u1="&#x203a;" u2="Y" k="160" />
    <hkern u1="&#x203a;" u2="X" k="66" />
    <hkern u1="&#x203a;" u2="W" k="36" />
    <hkern u1="&#x203a;" u2="V" k="116" />
    <hkern u1="&#x203a;" u2="T" k="180" />
    <hkern u1="&#x203a;" u2="A" k="67" />
    <hkern u1="&#x203a;" u2="&#x2f;" k="67" />
    <hkern u1="&#x203a;" u2="&#x2e;" k="132" />
    <hkern u1="&#x203a;" u2="&#x2c;" k="132" />
    <hkern u1="&#x203a;" u2="&#x2a;" k="169" />
    <hkern u1="&#x203a;" u2="&#x27;" k="169" />
    <hkern u1="&#x203a;" u2="&#x26;" k="67" />
    <hkern u1="&#x203a;" u2="&#x22;" k="169" />
    <hkern u1="&#x2122;" u2="&#x2206;" k="191" />
    <hkern u1="&#x2122;" u2="&#x203a;" k="169" />
    <hkern u1="&#x2122;" u2="&#x2039;" k="169" />
    <hkern u1="&#x2122;" u2="&#x2022;" k="169" />
    <hkern u1="&#x2122;" u2="&#x201e;" k="213" />
    <hkern u1="&#x2122;" u2="&#x201a;" k="213" />
    <hkern u1="&#x2122;" u2="&#x2014;" k="169" />
    <hkern u1="&#x2122;" u2="&#x2013;" k="169" />
    <hkern u1="&#x2122;" u2="&#x178;" k="-36" />
    <hkern u1="&#x2122;" u2="&#x153;" k="96" />
    <hkern u1="&#x2122;" u2="&#x152;" k="42" />
    <hkern u1="&#x2122;" u2="&#x119;" k="96" />
    <hkern u1="&#x2122;" u2="&#x107;" k="96" />
    <hkern u1="&#x2122;" u2="&#x106;" k="42" />
    <hkern u1="&#x2122;" u2="&#x105;" k="66" />
    <hkern u1="&#x2122;" u2="&#x104;" k="191" />
    <hkern u1="&#x2122;" u2="&#xf8;" k="96" />
    <hkern u1="&#x2122;" u2="&#xf6;" k="96" />
    <hkern u1="&#x2122;" u2="&#xf5;" k="96" />
    <hkern u1="&#x2122;" u2="&#xf4;" k="96" />
    <hkern u1="&#x2122;" u2="&#xf3;" k="96" />
    <hkern u1="&#x2122;" u2="&#xf2;" k="96" />
    <hkern u1="&#x2122;" u2="&#xf0;" k="96" />
    <hkern u1="&#x2122;" u2="&#xeb;" k="96" />
    <hkern u1="&#x2122;" u2="&#xea;" k="96" />
    <hkern u1="&#x2122;" u2="&#xe9;" k="96" />
    <hkern u1="&#x2122;" u2="&#xe8;" k="96" />
    <hkern u1="&#x2122;" u2="&#xe7;" k="96" />
    <hkern u1="&#x2122;" u2="&#xe6;" k="66" />
    <hkern u1="&#x2122;" u2="&#xe5;" k="66" />
    <hkern u1="&#x2122;" u2="&#xe4;" k="66" />
    <hkern u1="&#x2122;" u2="&#xe3;" k="66" />
    <hkern u1="&#x2122;" u2="&#xe2;" k="66" />
    <hkern u1="&#x2122;" u2="&#xe1;" k="66" />
    <hkern u1="&#x2122;" u2="&#xe0;" k="66" />
    <hkern u1="&#x2122;" u2="&#xdd;" k="-36" />
    <hkern u1="&#x2122;" u2="&#xd8;" k="42" />
    <hkern u1="&#x2122;" u2="&#xd6;" k="42" />
    <hkern u1="&#x2122;" u2="&#xd5;" k="42" />
    <hkern u1="&#x2122;" u2="&#xd4;" k="42" />
    <hkern u1="&#x2122;" u2="&#xd3;" k="42" />
    <hkern u1="&#x2122;" u2="&#xd2;" k="42" />
    <hkern u1="&#x2122;" u2="&#xc7;" k="42" />
    <hkern u1="&#x2122;" u2="&#xc6;" k="191" />
    <hkern u1="&#x2122;" u2="&#xc5;" k="191" />
    <hkern u1="&#x2122;" u2="&#xc4;" k="191" />
    <hkern u1="&#x2122;" u2="&#xc3;" k="191" />
    <hkern u1="&#x2122;" u2="&#xc2;" k="191" />
    <hkern u1="&#x2122;" u2="&#xc1;" k="191" />
    <hkern u1="&#x2122;" u2="&#xc0;" k="191" />
    <hkern u1="&#x2122;" u2="&#xbb;" k="169" />
    <hkern u1="&#x2122;" u2="&#xb7;" k="169" />
    <hkern u1="&#x2122;" u2="&#xae;" k="42" />
    <hkern u1="&#x2122;" u2="&#xad;" k="169" />
    <hkern u1="&#x2122;" u2="&#xab;" k="169" />
    <hkern u1="&#x2122;" u2="&#xa9;" k="42" />
    <hkern u1="&#x2122;" u2="q" k="96" />
    <hkern u1="&#x2122;" u2="o" k="96" />
    <hkern u1="&#x2122;" u2="e" k="96" />
    <hkern u1="&#x2122;" u2="d" k="96" />
    <hkern u1="&#x2122;" u2="c" k="96" />
    <hkern u1="&#x2122;" u2="a" k="66" />
    <hkern u1="&#x2122;" u2="\" k="-44" />
    <hkern u1="&#x2122;" u2="Y" k="-36" />
    <hkern u1="&#x2122;" u2="W" k="-44" />
    <hkern u1="&#x2122;" u2="V" k="-44" />
    <hkern u1="&#x2122;" u2="Q" k="42" />
    <hkern u1="&#x2122;" u2="O" k="42" />
    <hkern u1="&#x2122;" u2="G" k="42" />
    <hkern u1="&#x2122;" u2="C" k="42" />
    <hkern u1="&#x2122;" u2="A" k="191" />
    <hkern u1="&#x2122;" u2="&#x40;" k="42" />
    <hkern u1="&#x2122;" u2="&#x2f;" k="191" />
    <hkern u1="&#x2122;" u2="&#x2e;" k="213" />
    <hkern u1="&#x2122;" u2="&#x2d;" k="169" />
    <hkern u1="&#x2122;" u2="&#x2c;" k="213" />
    <hkern u1="&#x2122;" u2="&#x26;" k="191" />
    <hkern u1="&#x2206;" u2="&#x2122;" k="191" />
    <hkern u1="&#x2206;" u2="&#x203a;" k="67" />
    <hkern u1="&#x2206;" u2="&#x2039;" k="67" />
    <hkern u1="&#x2206;" u2="&#x2022;" k="67" />
    <hkern u1="&#x2206;" u2="&#x201d;" k="191" />
    <hkern u1="&#x2206;" u2="&#x201c;" k="191" />
    <hkern u1="&#x2206;" u2="&#x2019;" k="191" />
    <hkern u1="&#x2206;" u2="&#x2018;" k="191" />
    <hkern u1="&#x2206;" u2="&#x2014;" k="67" />
    <hkern u1="&#x2206;" u2="&#x2013;" k="67" />
    <hkern u1="&#x2206;" u2="&#x178;" k="182" />
    <hkern u1="&#x2206;" u2="&#x152;" k="51" />
    <hkern u1="&#x2206;" u2="&#x106;" k="51" />
    <hkern u1="&#x2206;" u2="&#xff;" k="91" />
    <hkern u1="&#x2206;" u2="&#xfd;" k="91" />
    <hkern u1="&#x2206;" u2="&#xdd;" k="182" />
    <hkern u1="&#x2206;" u2="&#xdc;" k="52" />
    <hkern u1="&#x2206;" u2="&#xdb;" k="52" />
    <hkern u1="&#x2206;" u2="&#xda;" k="52" />
    <hkern u1="&#x2206;" u2="&#xd9;" k="52" />
    <hkern u1="&#x2206;" u2="&#xd8;" k="51" />
    <hkern u1="&#x2206;" u2="&#xd6;" k="51" />
    <hkern u1="&#x2206;" u2="&#xd5;" k="51" />
    <hkern u1="&#x2206;" u2="&#xd4;" k="51" />
    <hkern u1="&#x2206;" u2="&#xd3;" k="51" />
    <hkern u1="&#x2206;" u2="&#xd2;" k="51" />
    <hkern u1="&#x2206;" u2="&#xc7;" k="51" />
    <hkern u1="&#x2206;" u2="&#xbb;" k="67" />
    <hkern u1="&#x2206;" u2="&#xba;" k="191" />
    <hkern u1="&#x2206;" u2="&#xb9;" k="202" />
    <hkern u1="&#x2206;" u2="&#xb7;" k="67" />
    <hkern u1="&#x2206;" u2="&#xb3;" k="202" />
    <hkern u1="&#x2206;" u2="&#xb2;" k="202" />
    <hkern u1="&#x2206;" u2="&#xb0;" k="191" />
    <hkern u1="&#x2206;" u2="&#xae;" k="51" />
    <hkern u1="&#x2206;" u2="&#xad;" k="67" />
    <hkern u1="&#x2206;" u2="&#xab;" k="67" />
    <hkern u1="&#x2206;" u2="&#xaa;" k="191" />
    <hkern u1="&#x2206;" u2="&#xa9;" k="51" />
    <hkern u1="&#x2206;" u2="y" k="91" />
    <hkern u1="&#x2206;" u2="v" k="91" />
    <hkern u1="&#x2206;" u2="\" k="169" />
    <hkern u1="&#x2206;" u2="Y" k="182" />
    <hkern u1="&#x2206;" u2="W" k="102" />
    <hkern u1="&#x2206;" u2="V" k="169" />
    <hkern u1="&#x2206;" u2="U" k="52" />
    <hkern u1="&#x2206;" u2="T" k="147" />
    <hkern u1="&#x2206;" u2="Q" k="51" />
    <hkern u1="&#x2206;" u2="O" k="51" />
    <hkern u1="&#x2206;" u2="J" k="-56" />
    <hkern u1="&#x2206;" u2="G" k="51" />
    <hkern u1="&#x2206;" u2="C" k="51" />
    <hkern u1="&#x2206;" u2="&#x40;" k="51" />
    <hkern u1="&#x2206;" u2="&#x3f;" k="63" />
    <hkern u1="&#x2206;" u2="&#x2d;" k="67" />
    <hkern u1="&#x2206;" u2="&#x2a;" k="191" />
    <hkern u1="&#x2206;" u2="&#x27;" k="191" />
    <hkern u1="&#x2206;" u2="&#x22;" k="191" />
  </font>
</defs></svg>
