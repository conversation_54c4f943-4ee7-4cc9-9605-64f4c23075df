<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg>
<metadata>
Created by FontForge 20090914 at Wed Aug 24 12:09:01 2016
 By www-data
Copyright (c) 2010-2011 by tyPoland Lukasz Dziedzic with Reserved Font Name "Lato". Licensed under the SIL Open Font License, Version 1.1.
</metadata>
<defs>
<font id="Lato-Black" horiz-adv-x="1160" >
  <font-face 
    font-family="Lato Black"
    font-weight="900"
    font-stretch="normal"
    units-per-em="2000"
    panose-1="2 15 10 2 2 2 4 3 2 3"
    ascent="1610"
    descent="-390"
    x-height="1037"
    cap-height="1457"
    bbox="-166 -357 2334 1904"
    underline-thickness="194"
    underline-position="-6"
    unicode-range="U+000D-U+FB02"
  />
<missing-glyph horiz-adv-x="1123" 
d="M230 1152c41.333 32 88.001 58.498 140.001 79.498s113 31.5 183 31.5c52 0 97.833 -6.66699 137.5 -20s73.167 -32 100.5 -56s48 -53 62 -87s21 -71.667 21 -113c0 -36.667 -4.33301 -68.334 -13 -95.001s-19.5 -49.667 -32.5 -69s-27.333 -36 -43 -50l-45.5 -39
c-14.667 -12 -27.667 -23.667 -39 -35s-19 -24 -23 -38l-27 -90h-212l-22 112c-5.33301 26 -4.83301 48.167 1.5 66.5s15.833 34.666 28.5 48.999s27.167 27.333 43.5 39s31.666 23.834 45.999 36.501s26.333 26.334 36 41.001s14.5 32.334 14.5 53.001
c0 15.333 -4.33301 28.166 -13 38.499s-25 15.5 -49 15.5c-21.333 0 -39 -1.66699 -53 -5s-26.167 -7.16602 -36.5 -11.499s-20 -8.16602 -29 -11.499s-19.5 -5 -31.5 -5c-28.667 0 -49.667 12 -63 36zM374.001 330.998c0 22 4 42.5 12 61.5s19.333 35.5 34 49.5
s31.667 25 51 33s40.666 12 63.999 12c22 0 42.667 -4 62 -12s36.166 -19 50.499 -33s25.666 -30.5 33.999 -49.5s12.5 -39.5 12.5 -61.5s-4.16699 -42.5 -12.5 -61.5s-19.666 -35.5 -33.999 -49.5s-31.166 -25 -50.499 -33s-40 -12 -62 -12c-23.333 0 -44.666 4 -63.999 12
s-36.333 19 -51 33s-26 30.5 -34 49.5s-12 39.5 -12 61.5zM40.001 1457h1043v-1457h-1043v1457zM117.001 79.998h879v1297h-879v-1297z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="1258" 
d="M166 0l-0.00390625 824l-68 14c-23.333 5.33301 -42.166 13.666 -56.499 24.999s-21.5 28 -21.5 50v122h146v15c0 62.667 10.833 121.834 32.5 177.501s55.167 104 100.5 145s103.166 73.5 173.499 97.5s154.166 36 251.499 36c30 0 61 -1.5 93 -4.5
s59.667 -7.5 83 -13.5l-10 -157c-1.33301 -16.667 -9.66602 -27.667 -24.999 -33s-31.666 -8 -48.999 -8c-69.333 0 -126.666 -5.33301 -171.999 -16s-81.166 -26.5 -107.499 -47.5s-44.666 -47.167 -54.999 -78.5s-15.5 -67.333 -15.5 -108v-5h658v-1035h-310v823h-338
v-823h-310z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="1276" 
d="M166 0l-0.000976562 824.001l-68 14c-23.333 5.33301 -42.166 13.666 -56.499 24.999s-21.5 28 -21.5 50v122h146v36c0 52.667 8.66699 104.167 26 154.5s44 95 80 134s82 70.333 138 94s122.667 35.5 200 35.5c65.333 0 126.833 -2 184.5 -6s116.167 -6 175.5 -6h174
v-1477h-308v1269c-34 1.33301 -66.833 2.83301 -98.5 4.5s-57.5 2.5 -77.5 2.5c-63.333 0 -111.5 -17.5 -144.5 -52.5s-49.5 -85.833 -49.5 -152.5v-36h224v-212h-214v-823h-310z" />
    <glyph glyph-name=".notdef" horiz-adv-x="1123" 
d="M230 1152c41.333 32 88.001 58.498 140.001 79.498s113 31.5 183 31.5c52 0 97.833 -6.66699 137.5 -20s73.167 -32 100.5 -56s48 -53 62 -87s21 -71.667 21 -113c0 -36.667 -4.33301 -68.334 -13 -95.001s-19.5 -49.667 -32.5 -69s-27.333 -36 -43 -50l-45.5 -39
c-14.667 -12 -27.667 -23.667 -39 -35s-19 -24 -23 -38l-27 -90h-212l-22 112c-5.33301 26 -4.83301 48.167 1.5 66.5s15.833 34.666 28.5 48.999s27.167 27.333 43.5 39s31.666 23.834 45.999 36.501s26.333 26.334 36 41.001s14.5 32.334 14.5 53.001
c0 15.333 -4.33301 28.166 -13 38.499s-25 15.5 -49 15.5c-21.333 0 -39 -1.66699 -53 -5s-26.167 -7.16602 -36.5 -11.499s-20 -8.16602 -29 -11.499s-19.5 -5 -31.5 -5c-28.667 0 -49.667 12 -63 36zM374.001 330.998c0 22 4 42.5 12 61.5s19.333 35.5 34 49.5
s31.667 25 51 33s40.666 12 63.999 12c22 0 42.667 -4 62 -12s36.166 -19 50.499 -33s25.666 -30.5 33.999 -49.5s12.5 -39.5 12.5 -61.5s-4.16699 -42.5 -12.5 -61.5s-19.666 -35.5 -33.999 -49.5s-31.166 -25 -50.499 -33s-40 -12 -62 -12c-23.333 0 -44.666 4 -63.999 12
s-36.333 19 -51 33s-26 30.5 -34 49.5s-12 39.5 -12 61.5zM40.001 1457h1043v-1457h-1043v1457zM117.001 79.998h879v1297h-879v-1297z" />
    <glyph glyph-name="NULL" horiz-adv-x="0" 
 />
    <glyph glyph-name="NULL" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" unicode="&#xd;" horiz-adv-x="0" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="386" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="752" 
d="M524 1457v-572c0 -61.333 -3.66699 -122.166 -11 -182.499s-17 -121.833 -29 -184.5h-210c-12 62.667 -21.667 124.167 -29 184.5s-11 121.166 -11 182.499v572h290zM200 157c0 24 4.5 46.333 13.5 67s21.333 38.667 37 54s34.167 27.5 55.5 36.5
s44.666 13.5 69.999 13.5c24.667 0 47.667 -4.5 69 -13.5s40 -21.167 56 -36.5s28.5 -33.333 37.5 -54s13.5 -43 13.5 -67s-4.5 -46.5 -13.5 -67.5s-21.5 -39.167 -37.5 -54.5s-34.667 -27.333 -56 -36s-44.333 -13 -69 -13c-25.333 0 -48.666 4.33301 -69.999 13
s-39.833 20.667 -55.5 36s-28 33.5 -37 54.5s-13.5 43.5 -13.5 67.5z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="890" 
d="M380 1457l-0.000976562 -286l-28 -158c-6 -36 -16.667 -63.5 -32 -82.5s-40.666 -28.5 -75.999 -28.5c-30 0 -53.5 9.5 -70.5 28.5s-28.833 46.5 -35.5 82.5l-28 158v286h270zM779.999 1457l-0.000976562 -286l-28 -158c-6 -36 -16.667 -63.5 -32 -82.5
s-40.666 -28.5 -75.999 -28.5c-30 0 -53.5 9.5 -70.5 28.5s-28.833 46.5 -35.5 82.5l-28 158v286h270z" />
    <glyph glyph-name="numbersign" unicode="#" 
d="M862 395l-69.998 -395.002h-144c-12.667 0 -24.5 2.66699 -35.5 8s-20.5 12.5 -28.5 21.5s-14.333 19.667 -19 32s-7 25.5 -7 39.5c0 3.33301 0.166992 7 0.5 11s0.833008 7.66699 1.5 11l48 272h-165l-49 -287c-3.33301 -19.333 -9.33301 -35.833 -18 -49.5
s-19.334 -24.834 -32.001 -33.501s-26.5 -15 -41.5 -19s-30.5 -6 -46.5 -6h-138l70 395h-88c-25.333 0 -45 6.16699 -59 18.5s-21 33.166 -21 62.499c0 6 0.333008 12.333 1 19s1.66699 13.667 3 21l16 96h177l42 236h-199l24 126c6 30.667 19.167 53.334 39.5 68.001
s52.5 22 96.5 22h68l52 293c5.33301 31.333 20.333 55.833 45 73.5s53 26.5 85 26.5h142l-70 -393h166l70 393h138c29.333 0 53.166 -8 71.499 -24s27.5 -36.667 27.5 -62v-7.5c0 -2.33301 -0.333008 -5.16602 -1 -8.49902l-52 -291h194l-24 -126
c-6 -31.333 -19.167 -54.166 -39.5 -68.499s-52.5 -21.5 -96.5 -21.5h-63l-42 -236h109c25.333 0 45 -6.16699 59 -18.5s21 -33.166 21 -62.499c0 -6 -0.333008 -12.333 -1 -19s-1.66699 -13.667 -3 -21l-16 -96h-198zM471.002 611.998h166l42 236h-166z" />
    <glyph glyph-name="dollar" unicode="$" 
d="M430 -5c-76.667 12.667 -149.836 34.835 -219.503 66.502s-128.5 69.834 -176.5 114.501l96 139c8 11.333 18.833 20.833 32.5 28.5s28.167 11.5 43.5 11.5c16 0 32.333 -4.16699 49 -12.5s34.834 -18.5 54.501 -30.5s41.167 -24.167 64.5 -36.5s50 -22.5 80 -30.5
l37 378c-48 13.333 -95.667 29 -143 47s-90 42.333 -128 73s-68.833 69.667 -92.5 117s-35.5 106.666 -35.5 177.999c0 52.667 10.667 104 32 154s52.5 95 93.5 135s91.5 72.833 151.5 98.5s128.667 40.5 206 44.5l11 111c2 18.667 10.333 35.5 25 50.5s33.667 22.5 57 22.5
h114l-20 -197c72 -13.333 134 -34.833 186 -64.5s96 -61.5 132 -95.5l-76 -111c-11.333 -16.667 -22.833 -28.834 -34.5 -36.501s-25.5 -11.5 -41.5 -11.5c-10.667 0 -22.667 2.33301 -36 7s-28 10.5 -44 17.5l-51.5 22.5c-18.333 8 -37.833 15 -58.5 21l-35 -355
c32 -9.33301 64 -19.5 96 -30.5s62.833 -23.667 92.5 -38s57.334 -30.833 83.001 -49.5s48 -40.667 67 -66s33.833 -54.5 44.5 -87.5s16 -70.5 16 -112.5c0 -63.333 -10.667 -122.833 -32 -178.5s-52.666 -104.834 -93.999 -147.501s-92 -77.5 -152 -104.5
s-128.667 -43.5 -206 -49.5l-12 -125c-2 -18.667 -10.333 -35.5 -25 -50.5s-33.667 -22.5 -57 -22.5h-114zM807.998 421.001c0 38 -12.167 68.333 -36.5 91s-56.5 41.667 -96.5 57l-32 -330c110 19.333 165 80 165 182zM383.998 1068c0 -39.333 12.5 -71.166 37.5 -95.499
s57.833 -44.833 98.5 -61.5l30 302c-30.667 -4.66699 -56.5 -11.5 -77.5 -20.5s-38 -19.833 -51 -32.5s-22.5 -26.834 -28.5 -42.501s-9 -32.167 -9 -49.5z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="1636" 
d="M748 1104c0 -53.333 -9.66699 -102 -29 -146s-45 -81.833 -77 -113.5s-69.167 -56.167 -111.5 -73.5s-86.5 -26 -132.5 -26c-50.667 0 -97.167 8.66699 -139.5 26s-79 41.833 -110 73.5s-55.167 69.5 -72.5 113.5s-26 92.667 -26 146c0 55.333 8.66699 105.5 26 150.5
s41.5 83.5 72.5 115.5s67.667 56.833 110 74.5s88.833 26.5 139.5 26.5s97.5 -8.83301 140.5 -26.5s80 -42.5 111 -74.5s55.167 -70.5 72.5 -115.5s26 -95.167 26 -150.5zM510 1104c0 34 -2.83301 62.167 -8.5 84.5s-13.5 40.166 -23.5 53.499s-21.833 22.666 -35.5 27.999
s-28.5 8 -44.5 8s-30.667 -2.66699 -44 -8s-24.666 -14.666 -33.999 -27.999s-16.666 -31.166 -21.999 -53.499s-8 -50.5 -8 -84.5c0 -32 2.66699 -58.667 8 -80s12.666 -38.333 21.999 -51s20.666 -21.667 33.999 -27s28 -8 44 -8s30.833 2.66699 44.5 8
s25.5 14.333 35.5 27s17.833 29.667 23.5 51s8.5 48 8.5 80zM1206 1411c10 11.333 22.333 21.833 37 31.5s35 14.5 61 14.5h226l-1098 -1413c-10 -12.667 -22.5 -23.167 -37.5 -31.5s-33.167 -12.5 -54.5 -12.5h-232zM1586 342c0 -53.333 -9.66699 -102.166 -29 -146.499
s-45 -82.333 -77 -114s-69.167 -56.167 -111.5 -73.5s-86.5 -26 -132.5 -26c-50.667 0 -97.167 8.66699 -139.5 26s-79 41.833 -110 73.5s-55.167 69.667 -72.5 114s-26 93.166 -26 146.499c0 55.333 8.66699 105.5 26 150.5s41.5 83.5 72.5 115.5s67.667 56.833 110 74.5
s88.833 26.5 139.5 26.5s97.5 -8.83301 140.5 -26.5s80 -42.5 111 -74.5s55.167 -70.5 72.5 -115.5s26 -95.167 26 -150.5zM1348 342c0 33.333 -2.83301 61.166 -8.5 83.499s-13.5 40.166 -23.5 53.499s-21.833 22.666 -35.5 27.999s-28.5 8 -44.5 8
s-30.667 -2.66699 -44 -8s-24.666 -14.666 -33.999 -27.999s-16.666 -31.166 -21.999 -53.499s-8 -50.166 -8 -83.499c0 -32 2.66699 -58.667 8 -80s12.666 -38.333 21.999 -51s20.666 -21.667 33.999 -27s28 -8 44 -8s30.833 2.66699 44.5 8s25.5 14.333 35.5 27
s17.833 29.667 23.5 51s8.5 48 8.5 80z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="1446" 
d="M664 1473c64.667 0 121.661 -9.50391 170.994 -28.5039s91 -44 125 -75s60.667 -66.833 80 -107.5s31.333 -82.334 36 -125.001l-200 -40c-3.33301 -0.666992 -6.16602 -1.16699 -8.49902 -1.5s-4.83301 -0.5 -7.5 -0.5c-28 0 -48.667 14.667 -62 44
c-15.333 34.667 -33.166 61 -53.499 79s-47.166 27 -80.499 27c-23.333 0 -43.666 -4.33301 -60.999 -13s-31.833 -20 -43.5 -34s-20.5 -30.333 -26.5 -49s-9 -38 -9 -58c0 -17.333 1.5 -34 4.5 -50s8 -32.167 15 -48.5s16.667 -33.166 29 -50.499s28.166 -36 47.499 -56
l360 -380c18 40 32.333 81.667 43 125s18.334 88.333 23.001 135c1.33301 18.667 7.5 33.334 18.5 44.001s25.5 16 43.5 16h196c0 -97.333 -13 -188.166 -39 -272.499s-63 -161.5 -111 -231.5l306 -322h-306c-15.333 0 -29 0.833008 -41 2.5s-23.167 4.5 -33.5 8.5
s-20.166 9.66699 -29.499 17s-19.333 16.666 -30 27.999l-63 66c-63.333 -44 -132.5 -78 -207.5 -102s-154.833 -36 -239.5 -36c-58 0 -114.833 10 -170.5 30s-105.167 48.5 -148.5 85.5s-78.333 81.833 -105 134.5s-40 111.667 -40 177c0 44 6.83301 85.667 20.5 125
s32.834 75.833 57.501 109.5s54.167 64.334 88.5 92.001s71.833 51.834 112.5 72.501c-31.333 48.667 -54 96.334 -68 143.001s-21 92 -21 136c0 52 9.5 101.167 28.5 147.5s46.667 87 83 122s81 62.833 134 83.5s113.833 31 182.5 31zM361.994 435.996
c0 -32 4.83398 -61 14.501 -87s23.5 -48.333 41.5 -67s39.5 -33.167 64.5 -43.5s52.833 -15.5 83.5 -15.5c44.667 0 86.167 5.83301 124.5 17.5s74.166 28.167 107.499 49.5l-335 355c-36.667 -30 -62.667 -62.667 -78 -98s-23 -72.333 -23 -111z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="490" 
d="M380 1457l-0.000976562 -286l-28 -158c-6 -36 -16.667 -63.5 -32 -82.5s-40.666 -28.5 -75.999 -28.5c-30 0 -53.5 9.5 -70.5 28.5s-28.833 46.5 -35.5 82.5l-28 158v286h270z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="600" 
d="M348 626c0 -66 3.83203 -132.332 11.499 -198.999s19 -132.334 34 -197.001s33.333 -127.667 55 -189s46.167 -120 73.5 -176c9.33301 -19.333 14 -37 14 -53s-4.33301 -29 -13 -39s-19 -18.667 -31 -26l-138 -80c-48.667 74.667 -90.334 150.667 -125.001 228
s-63 155.833 -85 235.5s-38.167 160.834 -48.5 243.501s-15.5 166.667 -15.5 252s5.16699 169.333 15.5 252s26.5 163.834 48.5 243.501s50.333 158.167 85 235.5s76.334 153.666 125.001 228.999l138 -80c11.333 -7.33301 21.333 -16 30 -26s13 -22.667 13 -38
c0 -7.33301 -1.16699 -15.333 -3.5 -24s-6.16602 -18.334 -11.499 -29.001c-28 -55.333 -52.667 -113.833 -74 -175.5s-39.333 -125 -54 -190s-25.667 -131 -33 -198s-11 -133.5 -11 -199.5z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="600" 
d="M252 626c0 66 -3.66797 132.498 -11.001 199.498s-18.333 133 -33 198s-32.667 128.333 -54 190s-46 120.167 -74 175.5c-5.33301 10.667 -9.16602 20.334 -11.499 29.001s-3.5 16.667 -3.5 24c0 15.333 4.33301 28 13 38s18.667 18.667 30 26l138 80
c96.667 -150.667 166.5 -305.667 209.5 -465s64.5 -324.333 64.5 -495c0 -171.333 -21.5 -336.5 -64.5 -495.5s-112.833 -313.5 -209.5 -463.5l-138 80c-12 7.33301 -22.333 16 -31 26s-13 23 -13 39s4.66699 33.667 14 53c27.333 56 51.833 114.667 73.5 176
s40 124.333 55 189s26.333 130.334 34 197.001s11.5 133 11.5 199z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="800" 
d="M326 815l0.000976562 172.997c0 13.333 0.833008 26.833 2.5 40.5s4.5 26.167 8.5 37.5c-8.66699 -9.33301 -18.334 -17.666 -29.001 -24.999s-22 -14.666 -34 -21.999l-146 -85l-72 119l146 85c12.667 7.33301 25.334 13.666 38.001 18.999s25.667 9.33301 39 12
c-13.333 2.66699 -26.333 6.83398 -39 12.501s-25.334 12.167 -38.001 19.5l-146 87l72 120l146 -88c12 -7.33301 23.5 -14.833 34.5 -22.5s20.833 -16.167 29.5 -25.5c-8 22.667 -12 48.334 -12 77.001v175h146v-173c0 -14 -0.833008 -27.667 -2.5 -41s-4.5 -26 -8.5 -38
c8.66699 9.33301 18.334 17.833 29.001 25.5s22 15.167 34 22.5l146 86l72 -120l-146 -85c-13.333 -7.33301 -26.333 -13.666 -39 -18.999s-25.667 -9.33301 -39 -12c13.333 -2.66699 26.5 -6.83398 39.5 -12.501s25.833 -12.167 38.5 -19.5l146 -87l-72 -119l-146 87
c-12.667 7.33301 -24.334 14.833 -35.001 22.5s-20.667 16.167 -30 25.5c8.66699 -23.333 13 -49 13 -77v-175h-146z" />
    <glyph glyph-name="plus" unicode="+" 
d="M704 1179v-396h370v-222h-370v-398h-252v398h-368v222h368v396h252z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="472" 
d="M68 176c0 21.333 3.99902 41.168 11.999 59.501s19.5 34.333 34.5 48s32.667 24.5 53 32.5s43.166 12 68.499 12c30 0 56 -5.16699 78 -15.5s40.333 -24.333 55 -42s25.5 -38 32.5 -61s10.5 -47.167 10.5 -72.5c0 -33.333 -5.33301 -68.833 -16 -106.5
s-26.5 -75.5 -47.5 -113.5s-47.167 -75.333 -78.5 -112s-67.333 -70 -108 -100l-54 48c-7.33301 7.33301 -12.833 14.166 -16.5 20.499s-5.5 14.166 -5.5 23.499c0 6.66699 2.33301 13.834 7 21.501s10.334 14.5 17.001 20.5l28 29c10.667 11.333 21.334 24 32.001 38
s20.5 29.333 29.5 46s15.833 34.334 20.5 53.001c-23.333 2 -44.333 7.66699 -63 17s-34.5 21.666 -47.5 36.999s-23.167 32.833 -30.5 52.5s-11 41.167 -11 64.5z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="740" 
d="M100 733h540v-250h-540v250z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="472" 
d="M60 157c0 24 4.5 46.333 13.5 67s21.333 38.667 37 54s34.167 27.5 55.5 36.5s44.666 13.5 69.999 13.5c24.667 0 47.667 -4.5 69 -13.5s40 -21.167 56 -36.5s28.5 -33.333 37.5 -54s13.5 -43 13.5 -67s-4.5 -46.5 -13.5 -67.5s-21.5 -39.167 -37.5 -54.5
s-34.667 -27.333 -56 -36s-44.333 -13 -69 -13c-25.333 0 -48.666 4.33301 -69.999 13s-39.833 20.667 -55.5 36s-28 33.5 -37 54.5s-13.5 43.5 -13.5 67.5z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="800" 
d="M270 12c-7.33301 -18 -16.833 -34 -28.5 -48s-24.667 -25.667 -39 -35s-29.666 -16.333 -45.999 -21s-32.5 -7 -48.5 -7h-132l568 1491c13.333 34 33.666 60 60.999 78s59 27 95 27h132z" />
    <glyph glyph-name="zero" unicode="0" 
d="M1120 729c0 -127.333 -13.667 -237.666 -41 -330.999s-65.166 -170.666 -113.499 -231.999s-105.666 -107 -171.999 -137s-138.166 -45 -215.499 -45s-148.833 15 -214.5 45s-122.5 75.667 -170.5 137s-85.5 138.666 -112.5 231.999s-40.5 203.666 -40.5 330.999
s13.5 237.666 40.5 330.999s64.5 170.666 112.5 231.999s104.833 106.833 170.5 136.5s137.167 44.5 214.5 44.5s149.166 -14.833 215.499 -44.5s123.666 -75.167 171.999 -136.5s86.166 -138.666 113.499 -231.999s41 -203.666 41 -330.999zM800 729
c0 100.667 -6.33301 183.167 -19 247.5s-29.5 114.833 -50.5 151.5s-44.833 61.834 -71.5 75.501s-53.667 20.5 -81 20.5s-54.166 -6.83301 -80.499 -20.5s-49.666 -38.834 -69.999 -75.501s-36.666 -87.167 -48.999 -151.5s-18.5 -146.833 -18.5 -247.5
c0 -101.333 6.16699 -184 18.5 -248s28.666 -114.5 48.999 -151.5s43.666 -62.333 69.999 -76s53.166 -20.5 80.499 -20.5s54.333 6.83301 81 20.5s50.5 39 71.5 76s37.833 87.5 50.5 151.5s19 146.667 19 248z" />
    <glyph glyph-name="one" unicode="1" 
d="M252 224h280.002v735c0 36 1 73.667 3 113l-165 -137c-10.667 -8.66699 -21.334 -14.834 -32.001 -18.501s-21 -5.5 -31 -5.5c-17.333 0 -32.833 3.83301 -46.5 11.5s-23.834 15.834 -30.501 24.501l-98 130l454 381h256v-1234h240v-224h-830v224z" />
    <glyph glyph-name="two" unicode="2" 
d="M602 1473c72 0 136.831 -10.5039 194.498 -31.5039s106.834 -50.167 147.501 -87.5s71.834 -81.833 93.501 -133.5s32.5 -108.5 32.5 -170.5c0 -53.333 -7.5 -102.666 -22.5 -147.999s-35.5 -88.5 -61.5 -129.5s-56.167 -80.667 -90.5 -119l-109.5 -116.5l-285 -292
c36.667 10.667 72.834 19.167 108.501 25.5s69.167 9.5 100.5 9.5h272c33.333 0 59.666 -9.16699 78.999 -27.5s29 -42.5 29 -72.5v-180h-1024v100c0 19.333 4 40 12 62s22 42 42 60l420 423c36 36 67.333 70.333 94 103s48.667 64.834 66 96.501s30.333 63.334 39 95.001
s13 65.167 13 100.5c0 58 -13.833 102.5 -41.5 133.5s-69.834 46.5 -126.501 46.5c-23.333 0 -44.666 -3.33301 -63.999 -10s-36.666 -15.667 -51.999 -27s-28.5 -24.666 -39.5 -39.999s-19.833 -32 -26.5 -50c-10.667 -30 -24.167 -51.667 -40.5 -65s-38.5 -20 -66.5 -20
c-6.66699 0 -13.834 0.333008 -21.501 1s-15.5 1.66699 -23.5 3l-164 29c10.667 72.667 31.167 136 61.5 190s68 99 113 135s96.5 62.833 154.5 80.5s120.333 26.5 187 26.5z" />
    <glyph glyph-name="three" unicode="3" 
d="M630 1473c72 0 135.999 -10.1641 191.999 -30.4971s103.333 -47.833 142 -82.5s68 -74.834 88 -120.501s30 -93.834 30 -144.501c0 -46 -4.66699 -86.5 -14 -121.5s-23.166 -65.333 -41.499 -91s-41 -47.5 -68 -65.5s-57.833 -33 -92.5 -45
c161.333 -56 242 -165.667 242 -329c0 -77.333 -14 -144.666 -42 -201.999s-65.5 -105 -112.5 -143s-101.5 -66.5 -163.5 -85.5s-126.667 -28.5 -194 -28.5c-69.333 0 -131 7.5 -185 22.5s-102 38.333 -144 70s-78.833 72.167 -110.5 121.5s-59.167 108 -82.5 176l138 56
c12 5.33301 23.833 9 35.5 11s23.167 3 34.5 3c21.333 0 40.5 -4.16699 57.5 -12.5s29.833 -20.5 38.5 -36.5c30 -55.333 61.833 -95.5 95.5 -120.5s73.167 -37.5 118.5 -37.5c34.667 0 64.834 5.83301 90.501 17.5s47 26.667 64 45s29.833 39.166 38.5 62.499s13 47 13 71
c0 31.333 -2.16699 59.5 -6.5 84.5s-16.5 46.5 -36.5 64.5s-50.167 31.833 -90.5 41.5s-96.5 14.5 -168.5 14.5v214c60.667 0 109.667 4.66699 147 14s66.166 22.166 86.499 38.499s33.833 36.166 40.5 59.499s10 49 10 77c0 57.333 -13.833 101.5 -41.5 132.5
s-69.834 46.5 -126.501 46.5c-23.333 0 -44.666 -3.33301 -63.999 -10s-36.666 -15.667 -51.999 -27s-28.5 -24.666 -39.5 -39.999s-19.833 -32 -26.5 -50c-10.667 -30 -24.167 -51.667 -40.5 -65s-38.5 -20 -66.5 -20c-6.66699 0 -13.834 0.333008 -21.501 1
s-15.5 1.66699 -23.5 3l-164 29c10.667 72.667 31.167 136 61.5 190s68 99 113 135s96.5 62.833 154.5 80.5s120.333 26.5 187 26.5z" />
    <glyph glyph-name="four" unicode="4" 
d="M976 566l149.999 -0.000976562v-174c0 -15.333 -5.16699 -28.666 -15.5 -39.999s-25.166 -17 -44.499 -17h-90v-335h-270v335h-586c-19.333 0 -36.833 6 -52.5 18s-25.5 27 -29.5 45l-32 153l674 906h296v-891zM705.999 945.999c0 23.333 0.833008 48.5 2.5 75.5
s4.16699 54.833 7.5 83.5l-385 -539h375v380z" />
    <glyph glyph-name="five" unicode="5" 
d="M998 1329c0 -21.333 -3.33203 -40.6689 -9.99902 -58.002s-17.167 -32.5 -31.5 -45.5s-33.166 -23 -56.499 -30s-51.333 -10.5 -84 -10.5h-348l-41 -241c27.333 4.66699 53.666 8.16699 78.999 10.5s50 3.5 74 3.5c76.667 0 144.334 -11.667 203.001 -35
s108 -55.166 148 -95.499s70.167 -87.666 90.5 -141.999s30.5 -112.5 30.5 -174.5c0 -78 -13.833 -149.167 -41.5 -213.5s-66.167 -119.833 -115.5 -166.5s-108.166 -82.834 -176.499 -108.501s-143.166 -38.5 -224.499 -38.5c-47.333 0 -92.5 5 -135.5 15
s-83.333 23.5 -121 40.5s-72.5 36.667 -104.5 59s-61 46.166 -87 71.499l96 128c10 13.333 21.667 23.5 35 30.5s27.666 10.5 42.999 10.5c20 0 38.833 -5.5 56.5 -16.5l58 -36c21 -13 45.5 -24.833 73.5 -35.5s62.667 -16 104 -16s76.666 6.83301 105.999 20.5
s53.166 32.334 71.499 56.001s31.666 51.334 39.999 83.001s12.5 65.5 12.5 101.5c0 70.667 -20 124.5 -60 161.5s-96.667 55.5 -170 55.5c-65.333 0 -129.333 -12.333 -192 -37l-192 52l124 729h746v-128z" />
    <glyph glyph-name="six" unicode="6" 
d="M688 923c52.667 0 104.665 -8.66699 155.998 -26s97.333 -44 138 -80s73.5 -81.5 98.5 -136.5s37.5 -119.833 37.5 -194.5c0 -68 -12.667 -132.5 -38 -193.5s-61.166 -114.333 -107.499 -160s-102.166 -81.834 -167.499 -108.501s-137.666 -40 -216.999 -40
c-81.333 0 -154.333 12.833 -219 38.5s-119.5 61.5 -164.5 107.5s-79.333 101 -103 165s-35.5 134.333 -35.5 211c0 72.667 14 145 42 217s69.333 145.333 124 220l328 443c14.667 20 36.667 36.833 66 50.5s62.666 20.5 99.999 20.5h276l-432 -516l-19.5 -22.5l-18.5 -22.5
c23.333 8.66699 47.833 15.334 73.5 20.001s53.167 7 82.5 7zM363.998 464c0 -36 4.33398 -68.3291 13.001 -96.9961s22 -53.334 40 -74.001s40.667 -36.5 68 -47.5s59.666 -16.5 96.999 -16.5c32 0 61.833 6 89.5 18s51.5 28.5 71.5 49.5s35.833 45.667 47.5 74
s17.5 59.166 17.5 92.499c0 37.333 -5.5 70.666 -16.5 99.999s-26.5 54 -46.5 74s-44.167 35.333 -72.5 46s-59.833 16 -94.5 16c-31.333 0 -60 -5.5 -86 -16.5s-48.5 -26.833 -67.5 -47.5s-33.833 -45.334 -44.5 -74.001s-16 -61 -16 -97z" />
    <glyph glyph-name="seven" unicode="7" 
d="M1106 1457l-0.000976562 -129.999c0 -36.667 -4 -66.334 -12 -89.001s-15.667 -42 -23 -58l-509 -1078c-13.333 -28.667 -33 -52.834 -59 -72.501s-60.333 -29.5 -103 -29.5h-226l520 1043c14 28.667 29.167 54.334 45.5 77.001s34.166 44.334 53.499 65.001h-641
c-9.33301 0 -18.166 1.83301 -26.499 5.5s-15.666 8.66699 -21.999 15s-11.5 13.666 -15.5 21.999s-6 16.833 -6 25.5v204h1024z" />
    <glyph glyph-name="eight" unicode="8" 
d="M580 -16c-77.333 0 -148.002 10.333 -212.002 31s-118.833 49.834 -164.5 87.501s-81 83.167 -106 136.5s-37.5 112.666 -37.5 177.999c0 39.333 4.5 76.5 13.5 111.5s23.333 67 43 96s45 54.833 76 77.5s68.5 41.667 112.5 57c-66.667 30 -116.167 71.667 -148.5 125
s-48.5 117.333 -48.5 192c0 56.667 11.667 109.167 35 157.5s56 90.166 98 125.499s91.833 63.166 149.5 83.499s120.834 30.5 189.501 30.5s131.834 -10.167 189.501 -30.5s107.5 -48.166 149.5 -83.499s74.667 -77.166 98 -125.499s35 -100.833 35 -157.5
c0 -74.667 -16.333 -138.667 -49 -192s-82 -95 -148 -125c43.333 -15.333 80.5 -34.333 111.5 -57s56.333 -48.5 76 -77.5s34.167 -61 43.5 -96s14 -72.167 14 -111.5c0 -65.333 -12.5 -124.666 -37.5 -177.999s-60.333 -98.833 -106 -136.5s-100.5 -66.834 -164.5 -87.501
s-134.667 -31 -212 -31zM579.998 218c34.667 0 64.668 5.33301 90.001 16s46.166 25.167 62.499 43.5s28.333 40 36 65s11.5 51.833 11.5 80.5c0 31.333 -3.33301 60 -10 86s-17.667 48.167 -33 66.5s-35.833 32.666 -61.5 42.999s-57.5 15.5 -95.5 15.5
s-69.833 -5.16699 -95.5 -15.5s-46.167 -24.666 -61.5 -42.999s-26.333 -40.5 -33 -66.5s-10 -54.667 -10 -86c0 -28.667 3.83301 -55.5 11.5 -80.5s19.667 -46.667 36 -65s37 -32.833 62 -43.5s55.167 -16 90.5 -16zM579.999 870c34.667 0 62.999 5.66699 84.999 17
s39.333 26.333 52 45s21.334 40 26.001 64s7 48.667 7 74c0 22.667 -3 44.667 -9 66s-15.833 40.166 -29.5 56.499s-31.334 29.5 -53.001 39.5s-47.834 15 -78.501 15c-31.333 0 -57.666 -5 -78.999 -15s-38.833 -23.167 -52.5 -39.5s-23.5 -35.166 -29.5 -56.499
s-9 -43.333 -9 -66c0 -25.333 2.33301 -50 7 -74s13.334 -45.333 26.001 -64s30 -33.667 52 -45s50.333 -17 85 -17z" />
    <glyph glyph-name="nine" unicode="9" 
d="M512 568c-47.333 0 -94.667 8.49805 -142 25.498s-90.166 42.833 -128.499 77.5s-69.5 78.334 -93.5 131.001s-36 115 -36 187c0 66 12.167 128.333 36.5 187s59 110 104 154s98.833 78.833 161.5 104.5s132.667 38.5 210 38.5c78.667 0 149 -12 211 -36
s114.5 -57.667 157.5 -101s76 -94.833 99 -154.5s34.5 -125.5 34.5 -197.5c0 -48 -3.83301 -93 -11.5 -135s-18.334 -82.167 -32.001 -120.5s-30.167 -75 -49.5 -110s-41 -69.5 -65 -103.5l-314 -446c-13.333 -19.333 -34.333 -35.666 -63 -48.999s-61 -20 -97 -20h-286
l462 552l26.5 32.5l24.5 31.5c-30 -16 -62.667 -28 -98 -36s-72.333 -12 -111 -12zM829.999 1012c0 35.333 -5.16895 66.499 -15.502 93.499s-24.666 49.5 -42.999 67.5s-40.5 31.667 -66.5 41s-54.333 14 -85 14c-31.333 0 -59.166 -5.5 -83.499 -16.5
s-45 -26.167 -62 -45.5s-30 -42.166 -39 -68.499s-13.5 -54.833 -13.5 -85.5c0 -145.333 67.333 -218 202 -218c66 0 116.833 19.833 152.5 59.5s53.5 92.5 53.5 158.5z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="552" 
d="M100 157c0 24 4.5 46.333 13.5 67s21.333 38.667 37 54s34.167 27.5 55.5 36.5s44.666 13.5 69.999 13.5c24.667 0 47.667 -4.5 69 -13.5s40 -21.167 56 -36.5s28.5 -33.333 37.5 -54s13.5 -43 13.5 -67s-4.5 -46.5 -13.5 -67.5s-21.5 -39.167 -37.5 -54.5
s-34.667 -27.333 -56 -36s-44.333 -13 -69 -13c-25.333 0 -48.666 4.33301 -69.999 13s-39.833 20.667 -55.5 36s-28 33.5 -37 54.5s-13.5 43.5 -13.5 67.5zM100 840c0 24 4.5 46.333 13.5 67s21.333 38.667 37 54s34.167 27.5 55.5 36.5s44.666 13.5 69.999 13.5
c24.667 0 47.667 -4.5 69 -13.5s40 -21.167 56 -36.5s28.5 -33.333 37.5 -54s13.5 -43 13.5 -67s-4.5 -46.5 -13.5 -67.5s-21.5 -39.167 -37.5 -54.5s-34.667 -27.333 -56 -36s-44.333 -13 -69 -13c-25.333 0 -48.666 4.33301 -69.999 13s-39.833 20.667 -55.5 36
s-28 33.5 -37 54.5s-13.5 43.5 -13.5 67.5z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="552" 
d="M108 176c0 21.333 3.99902 41.168 11.999 59.501s19.5 34.333 34.5 48s32.667 24.5 53 32.5s43.166 12 68.499 12c30 0 56 -5.16699 78 -15.5s40.333 -24.333 55 -42s25.5 -38 32.5 -61s10.5 -47.167 10.5 -72.5c0 -33.333 -5.33301 -68.833 -16 -106.5
s-26.5 -75.5 -47.5 -113.5s-47.167 -75.333 -78.5 -112s-67.333 -70 -108 -100l-54 48c-7.33301 7.33301 -12.833 14.166 -16.5 20.499s-5.5 14.166 -5.5 23.499c0 6.66699 2.33301 13.834 7 21.501s10.334 14.5 17.001 20.5l28 29c10.667 11.333 21.334 24 32.001 38
s20.5 29.333 29.5 46s15.833 34.334 20.5 53.001c-23.333 2 -44.333 7.66699 -63 17s-34.5 21.666 -47.5 36.999s-23.167 32.833 -30.5 52.5s-11 41.167 -11 64.5zM99.999 840.002c0 24 4.5 46.333 13.5 67s21.333 38.667 37 54s34.167 27.5 55.5 36.5
s44.666 13.5 69.999 13.5c24.667 0 47.667 -4.5 69 -13.5s40 -21.167 56 -36.5s28.5 -33.333 37.5 -54s13.5 -43 13.5 -67s-4.5 -46.5 -13.5 -67.5s-21.5 -39.167 -37.5 -54.5s-34.667 -27.333 -56 -36s-44.333 -13 -69 -13c-25.333 0 -48.666 4.33301 -69.999 13
s-39.833 20.667 -55.5 36s-28 33.5 -37 54.5s-13.5 43.5 -13.5 67.5z" />
    <glyph glyph-name="less" unicode="&#x3c;" 
d="M130 735l810 423.998v-215c0 -15.333 -4 -29.5 -12 -42.5s-20.667 -24.167 -38 -33.5l-284 -153c-17.333 -9.33301 -35.666 -17.166 -54.999 -23.499s-39.666 -12.166 -60.999 -17.499c21.333 -5.33301 41.666 -10.833 60.999 -16.5s37.666 -13.167 54.999 -22.5
l284 -154c17.333 -9.33301 30 -20.333 38 -33s12 -26.667 12 -42v-215l-810 423v121z" />
    <glyph glyph-name="equal" unicode="=" 
d="M124 599h910v-222h-910v222zM124 966h910v-223h-910v223z" />
    <glyph glyph-name="greater" unicode="&#x3e;" 
d="M1030 614l-810 -422.998v215c0 15.333 4 29.333 12 42s20.667 23.667 38 33l284 154c16.667 9.33301 34.834 16.833 54.501 22.5s40.167 11.167 61.5 16.5c-21.333 5.33301 -41.833 11.166 -61.5 17.499s-37.834 14.166 -54.501 23.499l-284 153
c-17.333 9.33301 -30 20.5 -38 33.5s-12 27.167 -12 42.5v215l810 -424v-121z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="882" 
d="M38 1324c24.667 20.667 51.5039 40.001 80.5039 58.001s60.167 33.833 93.5 47.5s69.166 24.334 107.499 32.001s79.833 11.5 124.5 11.5c62.667 0 119.167 -8.33301 169.5 -25s93.166 -40.5 128.499 -71.5s62.5 -68.333 81.5 -112s28.5 -92.167 28.5 -145.5
c0 -51.333 -7 -95.333 -21 -132s-31.833 -68.667 -53.5 -96s-45.334 -50.833 -71.001 -70.5l-73 -55.5c-23 -17.333 -43 -34.333 -60 -51s-27.5 -35.334 -31.5 -56.001l-28 -140h-212l-22 161c-0.666992 4 -1.16699 8 -1.5 12s-0.5 8 -0.5 12c0 29.333 7 55 21 77
s31.667 42.167 53 60.5s44.333 36 69 53s47.667 35.333 69 55s39 41.667 53 66s21 53.166 21 86.499c0 40 -13.167 71.833 -39.5 95.5s-62.5 35.5 -108.5 35.5c-35.333 0 -64.833 -3.66699 -88.5 -11s-44 -15.5 -61 -24.5s-31.833 -17.167 -44.5 -24.5
s-25.334 -11 -38.001 -11c-28.667 0 -50 12.333 -64 37zM222.004 157.001c0 24 4.5 46.333 13.5 67s21.333 38.667 37 54s34.167 27.5 55.5 36.5s44.666 13.5 69.999 13.5c24.667 0 47.667 -4.5 69 -13.5s40 -21.167 56 -36.5s28.5 -33.333 37.5 -54s13.5 -43 13.5 -67
s-4.5 -46.5 -13.5 -67.5s-21.5 -39.167 -37.5 -54.5s-34.667 -27.333 -56 -36s-44.333 -13 -69 -13c-25.333 0 -48.666 4.33301 -69.999 13s-39.833 20.667 -55.5 36s-28 33.5 -37 54.5s-13.5 43.5 -13.5 67.5z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="1646" 
d="M1166 182c-21.333 0 -42.001 2.16895 -62.001 6.50195s-38.833 11.333 -56.5 21s-33.667 22.334 -48 38.001s-25.833 34.834 -34.5 57.501c-40 -44 -81.667 -75.333 -125 -94s-90 -28 -140 -28c-43.333 0 -81.333 7.5 -114 22.5s-60 35.833 -82 62.5
s-38.5 58.334 -49.5 95.001s-16.5 76.667 -16.5 120c0 38 4.83301 76.667 14.5 116s24.167 77.166 43.5 113.499s43.833 70.666 73.5 102.999s64.5 60.333 104.5 84s85.5 42.5 136.5 56.5s107.5 21 169.5 21c29.333 0 56 -1.16699 80 -3.5s46.333 -5.5 67 -9.5
s40.167 -9.16699 58.5 -15.5s37.166 -13.5 56.499 -21.5l-98 -379c-10 -39.333 -15 -70.666 -15 -93.999c0 -33.333 7.5 -56.333 22.5 -69s35.167 -19 60.5 -19c24 0 46.5 8.5 67.5 25.5s39.167 40.5 54.5 70.5s27.5 65.333 36.5 106s13.5 85 13.5 133
c0 82.667 -12.333 155.5 -37 218.5s-59.834 115.833 -105.501 158.5s-100.667 75 -165 97s-135.833 33 -214.5 33c-86 0 -165 -16 -237 -48s-134 -75.833 -186 -131.5s-92.5 -120.667 -121.5 -195s-43.5 -153.833 -43.5 -238.5c0 -106.667 16.167 -200.334 48.5 -281.001
s77 -148 134 -202s124.667 -94.5 203 -121.5s163.166 -40.5 254.499 -40.5c52.667 0 101.167 2.83301 145.5 8.5s84.5 13 120.5 22s68 19.167 96 30.5s52 22.666 72 33.999c14.667 8 28.334 12 41.001 12c23.333 0 39.666 -12.333 48.999 -37l42 -108
c-33.333 -22 -70.166 -42.333 -110.499 -61s-84 -35 -131 -49s-97.5 -24.833 -151.5 -32.5s-111.667 -11.5 -173 -11.5c-125.333 0 -240.666 19.5 -345.999 58.5s-196.5 94.833 -273.5 167.5s-137 160.667 -180 264s-64.5 219 -64.5 347c0 70 9.33301 138.667 28 206
s45.334 130.666 80.001 189.999s76.334 114 125.001 164s103 93.167 163 129.5s125 64.5 195 84.5s143.667 30 221 30c64.667 0 127.834 -7.33301 189.501 -22s120 -36 175 -64s105.667 -62.333 152 -103s86.166 -86.834 119.499 -138.501s59.333 -108.667 78 -171
s28 -128.833 28 -199.5c0 -71.333 -10.833 -138.5 -32.5 -201.5s-52 -118 -91 -165s-85.333 -84.167 -139 -111.5s-112.167 -41 -175.5 -41zM761.999 374.002c14.667 0 29.167 2.33105 43.5 6.99805s27.833 12.5 40.5 23.5s24.167 25.5 34.5 43.5s18.833 40.333 25.5 67
l70 273c-16 2 -32 3 -48 3c-33.333 0 -65.333 -8 -96 -24s-57.5 -37.667 -80.5 -65s-41.333 -59.166 -55 -95.499s-20.5 -74.833 -20.5 -115.5c0 -39.333 7.5 -68.666 22.5 -87.999s36.167 -29 63.5 -29z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="1474" 
d="M1476 0h-262c-29.333 -0 -53.5 6.83301 -72.5 20.5s-32.167 31.167 -39.5 52.5l-86 254h-554l-86 -254c-6.66699 -18.667 -19.667 -35.5 -39 -50.5s-43 -22.5 -71 -22.5h-264l564 1457h346zM540 557l398 0.000976562l-134 399l-30.5 89.5
c-11.667 34.333 -23.167 71.5 -34.5 111.5c-10.667 -40.667 -21.667 -78.334 -33 -113.001s-22 -64.667 -32 -90z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="1312" 
d="M120 0l-0.00195312 1457h536c100.667 0 186.334 -9.33301 257.001 -28s128.334 -45 173.001 -79s77.167 -75.333 97.5 -124s30.5 -103 30.5 -163c0 -32.667 -4.66699 -64.167 -14 -94.5s-24 -58.833 -44 -85.5s-45.667 -50.834 -77 -72.501s-69 -40.5 -113 -56.5
c96 -23.333 167 -61.166 213 -113.499s69 -119.5 69 -201.5c0 -62 -12 -119.667 -36 -173s-59.167 -99.833 -105.5 -139.5s-103.5 -70.667 -171.5 -93s-145.667 -33.5 -233 -33.5h-582zM457.998 618v-363h238c44.667 0 81.167 5.66699 109.5 17s50.5 26 66.5 44
s27 38.333 33 61s9 45.667 9 69c0 26.667 -3.5 50.667 -10.5 72s-19 39.333 -36 54s-39.5 26 -67.5 34s-63.333 12 -106 12h-236zM457.998 844l176 0.00292969c37.333 0 71 2.66699 101 8s55.5 14.666 76.5 27.999s37 31.666 48 54.999s16.5 52.666 16.5 87.999
c0 34.667 -4.33301 63.5 -13 86.5s-22 41.5 -40 55.5s-40.833 24 -68.5 30s-60.5 9 -98.5 9h-198v-360z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="1314" 
d="M1099 377c8 0 15.999 -1.5 23.999 -4.5s15.667 -8.16699 23 -15.5l134 -141c-58.667 -76.667 -132.167 -134.5 -220.5 -173.5s-192.833 -58.5 -313.5 -58.5c-110.667 0 -209.834 18.833 -297.501 56.5s-162 89.667 -223 156s-107.833 145 -140.5 236
s-49 189.833 -49 296.5c0 108.667 18.667 208.5 56 299.5s89.5 169.5 156.5 235.5s147.167 117.333 240.5 154s196.333 55 309 55c54.667 0 106.167 -4.83301 154.5 -14.5s93.5 -23 135.5 -40s80.667 -37.333 116 -61s66.666 -49.834 93.999 -78.501l-114 -153
c-7.33301 -9.33301 -16 -17.833 -26 -25.5s-24 -11.5 -42 -11.5c-12 0 -23.333 2.66699 -34 8s-22 11.833 -34 19.5l-39.5 25c-14.333 9 -31.333 17.333 -51 25s-42.667 14.167 -69 19.5s-57.166 8 -92.499 8c-61.333 0 -117.333 -10.833 -168 -32.5
s-94.167 -52.667 -130.5 -93s-64.666 -89.166 -84.999 -146.499s-30.5 -121.666 -30.5 -192.999c0 -74.667 10.167 -141 30.5 -199s48 -106.833 83 -146.5s75.833 -69.834 122.5 -90.501s96.667 -31 150 -31c30.667 0 58.667 1.5 84 4.5s48.833 8.16699 70.5 15.5
s42.334 16.833 62.001 28.5s39.5 26.167 59.5 43.5c8 6.66699 16.667 12.167 26 16.5s19 6.5 29 6.5z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="1484" 
d="M1442 729c0 -105.333 -18.167 -202.499 -54.5 -291.499s-87.5 -166 -153.5 -231s-145.5 -115.667 -238.5 -152s-196.167 -54.5 -309.5 -54.5h-566v1457h566c113.333 0 216.5 -18.333 309.5 -55s172.5 -87.334 238.5 -152.001s117.167 -141.5 153.5 -230.5
s54.5 -185.833 54.5 -290.5zM1096 729.001c0 72 -9.33301 137 -28 195s-45.5 107.167 -80.5 147.5s-77.833 71.333 -128.5 93s-108.334 32.5 -173.001 32.5h-226v-937h226c64.667 0 122.334 10.833 173.001 32.5s93.5 52.667 128.5 93s61.833 89.5 80.5 147.5
s28 123.333 28 196z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="1130" 
d="M1058 1457v-260h-598v-340h458v-250h-458v-347h598v-260h-938v1457h938z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="1114" 
d="M1058 1457v-260h-598v-369h498v-261h-498v-567h-340v1457h938z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="1424" 
d="M806 244c46.667 0 88.0039 4.00293 124.004 12.0029s70 18.667 102 32v221h-144c-21.333 0 -38 5.66699 -50 17s-18 25.666 -18 42.999v186h518v-615c-37.333 -27.333 -76.166 -50.833 -116.499 -70.5s-83.333 -35.834 -129 -48.501s-94.334 -22 -146.001 -28
s-107.167 -9 -166.5 -9c-106.667 0 -205.334 18.833 -296.001 56.5s-169.167 89.667 -235.5 156s-118.333 145 -156 236s-56.5 189.833 -56.5 296.5c0 108.667 18 208.5 54 299.5s87.5 169.5 154.5 235.5s148 117.333 243 154s201.167 55 318.5 55
c60.667 0 117.667 -5 171 -15s102.5 -23.667 147.5 -41s86.167 -38 123.5 -62s70.666 -50 99.999 -78l-98 -149c-9.33301 -14 -20.5 -25 -33.5 -33s-27.167 -12 -42.5 -12c-20 0 -40.667 6.66699 -62 20c-26.667 16 -51.834 29.833 -75.501 41.5
s-47.834 21.167 -72.501 28.5s-50.667 12.666 -78 15.999s-58 5 -92 5c-63.333 0 -120.333 -11.167 -171 -33.5s-93.834 -53.833 -129.501 -94.5s-63.167 -89.5 -82.5 -146.5s-29 -120.5 -29 -190.5c0 -77.333 10.667 -146 32 -206s50.833 -110.667 88.5 -152
s82.334 -72.833 134.001 -94.5s108.167 -32.5 169.5 -32.5z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="1512" 
d="M1392 0h-340v616h-592v-616h-340v1457h340v-609h592v609h340v-1457z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="640" 
d="M490 0h-340v1457h340v-1457z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="860" 
d="M740 528c0 -81.333 -10.166 -155.332 -30.499 -221.999s-51 -123.834 -92 -171.501s-92.5 -84.667 -154.5 -111s-135 -39.5 -219 -39.5c-38 0 -75.5 2 -112.5 6s-75.5 10.667 -115.5 20l18 197c1.33301 17.333 8.16602 31.333 20.499 42s29.5 16 51.5 16
c11.333 0 25.333 -1.83301 42 -5.5s36.667 -5.5 60 -5.5c32.667 0 61.167 4.5 85.5 13.5s44.5 24 60.5 45s28 48.5 36 82.5s12 76 12 126v936h338v-929z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="1428" 
d="M458 867l53.999 -0.00292969c50 0 86 15.667 108 47l328 476c18 26 38.5 43.667 61.5 53s51.167 14 84.5 14h294l-432 -589c-15.333 -21.333 -31.333 -39 -48 -53s-33.667 -25.667 -51 -35c25.333 -9.33301 48.5 -22.166 69.5 -38.499s40.167 -38.166 57.5 -65.499
l440 -676h-302c-19.333 0 -35.833 1.33301 -49.5 4s-25.5 6.66699 -35.5 12s-18.667 11.833 -26 19.5s-14.333 16.5 -21 26.5l-330 507c-12 18.667 -27.167 31.834 -45.5 39.501s-43.166 11.5 -74.499 11.5h-82v-620h-338v1457h338v-590z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="1038" 
d="M458 270h540v-270h-878v1457h338v-1187z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="1878" 
d="M865 666c13.333 -25.333 26.168 -51.667 38.501 -79s24.166 -55.333 35.499 -84c11.333 29.333 23.333 57.833 36 85.5s25.667 54.5 39 80.5l374 736c6.66699 12.667 13.5 22.5 20.5 29.5s14.833 12.167 23.5 15.5s18.5 5.33301 29.5 6s23.833 1 38.5 1h258v-1457h-298
v838c0 40.667 2 84.667 6 132l-386 -749c-12 -23.333 -28.167 -41 -48.5 -53s-43.5 -18 -69.5 -18h-46c-26 0 -49.167 6 -69.5 18s-36.5 29.667 -48.5 53l-388 750c2.66699 -23.333 4.66699 -46.5 6 -69.5s2 -44.167 2 -63.5v-838h-298v1457h258
c14.667 0 27.5 -0.333008 38.5 -1s20.833 -2.66699 29.5 -6s16.5 -8.5 23.5 -15.5s13.833 -16.833 20.5 -29.5l375 -739v0z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="1512" 
d="M298 1457c14.667 0 27.001 -0.666992 37.001 -2s19 -3.83301 27 -7.5s15.667 -8.83398 23 -15.501s15.666 -15.667 24.999 -27l692 -875c-2.66699 28 -4.66699 55.167 -6 81.5s-2 51.166 -2 74.499v771h298v-1457h-176c-26 0 -48 4 -66 12s-35.333 22.667 -52 44
l-687 868c2 -25.333 3.66699 -50.166 5 -74.499s2 -47.166 2 -68.499v-781h-298v1457h178v0z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="1600" 
d="M1558 729c0 -105.333 -18.167 -203.332 -54.5 -293.999s-87.5 -169.5 -153.5 -236.5s-145.5 -119.5 -238.5 -157.5s-196.167 -57 -309.5 -57s-216.666 19 -309.999 57s-173.166 90.5 -239.499 157.5s-117.666 145.833 -153.999 236.5s-54.5 188.667 -54.5 294
s18.167 203.333 54.5 294s87.666 169.334 153.999 236.001s146.166 119 239.499 157s196.666 57 309.999 57s216.5 -19.167 309.5 -57.5s172.5 -90.833 238.5 -157.5s117.167 -145.334 153.5 -236.001s54.5 -188.334 54.5 -293.001zM1212 729.001
c0 72 -9.33301 136.833 -28 194.5s-45.5 106.5 -80.5 146.5s-77.833 70.667 -128.5 92s-108.334 32 -173.001 32c-65.333 0 -123.5 -10.667 -174.5 -32s-94 -52 -129 -92s-61.833 -88.833 -80.5 -146.5s-28 -122.5 -28 -194.5c0 -72.667 9.33301 -137.834 28 -195.501
s45.5 -106.5 80.5 -146.5s78 -70.5 129 -91.5s109.167 -31.5 174.5 -31.5c64.667 0 122.334 10.5 173.001 31.5s93.5 51.5 128.5 91.5s61.833 88.833 80.5 146.5s28 122.834 28 195.501z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="1276" 
d="M458 487l-0.00195312 -487h-338v1457h514c102.667 0 190.834 -12.167 264.501 -36.5s134.334 -57.833 182.001 -100.5s82.834 -93 105.501 -151s34 -120.667 34 -188c0 -72.667 -11.667 -139.334 -35 -200.001s-59 -112.667 -107 -156s-108.833 -77.166 -182.5 -101.499
s-160.834 -36.5 -261.501 -36.5h-176zM457.998 742h176c88 0 151.333 21 190 63s58 100.667 58 176c0 33.333 -5 63.666 -15 90.999s-25.167 50.833 -45.5 70.5s-46 34.834 -77 45.501s-67.833 16 -110.5 16h-176v-462z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="1600" 
d="M1558 729c0 -62.667 -6.50098 -122.832 -19.501 -180.499s-31.667 -111.834 -56 -162.501s-54.333 -97.667 -90 -141s-76.167 -81.666 -121.5 -114.999l369 -403h-278c-40 0 -76.333 4.83301 -109 14.5s-61.667 29.167 -87 58.5l-181 203
c-29.333 -6 -59.166 -10.667 -89.499 -14s-61.5 -5 -93.5 -5c-113.333 0 -216.666 19 -309.999 57s-173.166 90.5 -239.499 157.5s-117.666 145.833 -153.999 236.5s-54.5 188.667 -54.5 294s18.167 203.333 54.5 294s87.666 169.334 153.999 236.001
s146.166 119 239.499 157s196.666 57 309.999 57s216.5 -19.167 309.5 -57.5s172.5 -90.833 238.5 -157.5s117.167 -145.334 153.5 -236.001s54.5 -188.334 54.5 -293.001zM1212 729.002c0 72 -9.33301 136.833 -28 194.5s-45.5 106.5 -80.5 146.5s-77.833 70.667 -128.5 92
s-108.334 32 -173.001 32c-65.333 0 -123.5 -10.667 -174.5 -32s-94 -52 -129 -92s-61.833 -88.833 -80.5 -146.5s-28 -122.5 -28 -194.5c0 -72.667 9.33301 -137.834 28 -195.501s45.5 -106.5 80.5 -146.5s78 -70.5 129 -91.5s109.167 -31.5 174.5 -31.5
c64.667 0 122.334 10.5 173.001 31.5s93.5 51.5 128.5 91.5s61.833 88.833 80.5 146.5s28 122.834 28 195.501z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="1330" 
d="M458 539l-0.000976562 -538.998h-338v1457h474c105.333 0 195.166 -10.833 269.499 -32.5s135 -51.834 182 -90.501s81.167 -84.334 102.5 -137.001s32 -110 32 -172c0 -47.333 -6.33301 -92 -19 -134s-31.5 -80.833 -56.5 -116.5s-55.667 -67.5 -92 -95.5
s-78.166 -51 -125.499 -69c22.667 -11.333 43.834 -25.333 63.501 -42s36.834 -37 51.501 -61l310 -507h-306c-56.667 -0 -97.334 21.333 -122.001 64l-242 423c-10.667 18.667 -23.167 32 -37.5 40s-34.5 12 -60.5 12h-86zM457.999 772.002l136 -0.00195312
c46 0 85.167 5.83301 117.5 17.5s58.833 27.834 79.5 48.501s35.667 44.834 45 72.501s14 57.5 14 89.5c0 64 -20.833 114 -62.5 150s-106.167 54 -193.5 54h-136v-432z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="1084" 
d="M932 1161c-10 -16 -20.4941 -28.001 -31.4941 -36.001s-25.167 -12 -42.5 -12c-15.333 0 -31.833 4.83301 -49.5 14.5l-60 32.5c-22.333 12 -47.833 22.833 -76.5 32.5s-61.334 14.5 -98.001 14.5c-63.333 0 -110.5 -13.5 -141.5 -40.5s-46.5 -63.5 -46.5 -109.5
c0 -29.333 9.33301 -53.666 28 -72.999s43.167 -36 73.5 -50s65 -26.833 104 -38.5s78.833 -24.667 119.5 -39s80.5 -31.166 119.5 -50.499s73.667 -44 104 -74s54.833 -66.5 73.5 -109.5s28 -94.833 28 -155.5c0 -67.333 -11.667 -130.333 -35 -189
s-57.166 -109.834 -101.499 -153.501s-99 -78 -164 -103s-138.833 -37.5 -221.5 -37.5c-45.333 0 -91.5 4.66699 -138.5 14s-92.5 22.5 -136.5 39.5s-85.333 37.167 -124 60.5s-72.334 49.333 -101.001 78l100 158c7.33301 12 17.666 21.667 30.999 29s27.666 11 42.999 11
c20 0 40.167 -6.33301 60.5 -19l69 -42c25.667 -15.333 55.167 -29.333 88.5 -42s72.666 -19 117.999 -19c61.333 0 109 13.5 143 40.5s51 69.833 51 128.5c0 34 -9.33301 61.667 -28 83s-43.167 39 -73.5 53s-64.833 26.333 -103.5 37s-78.334 22.5 -119.001 35.5
s-80.334 29 -119.001 48s-73.167 44 -103.5 75s-54.833 69.667 -73.5 116s-28 103.5 -28 171.5c0 54.667 11 108 33 160s54.333 98.333 97 139s95 73.167 157 97.5s133 36.5 213 36.5c44.667 0 88.167 -3.5 130.5 -10.5s82.5 -17.333 120.5 -31s73.5 -30 106.5 -49
s62.5 -40.5 88.5 -64.5z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="1198" 
d="M1178 1457v-268h-410v-1189h-338v1189h-410v268h1158z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="1452" 
d="M726 267c45.333 0 85.8301 7.49902 121.497 22.499s65.834 36.5 90.501 64.5s43.5 62.167 56.5 102.5s19.5 86.166 19.5 137.499v863h338v-863c0 -89.333 -14.5 -171.333 -43.5 -246s-70.5 -139 -124.5 -193s-119.667 -96 -197 -126s-164.333 -45 -261 -45
c-97.333 0 -184.666 15 -261.999 45s-143 72 -197 126s-95.333 118.333 -124 193s-43 156.667 -43 246v863h338v-862c0 -51.333 6.5 -97.166 19.5 -137.499s31.833 -74.666 56.5 -102.999s54.834 -50 90.501 -65s76.167 -22.5 121.5 -22.5z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="1474" 
d="M0 1457h272.001c29.333 0 53.333 -6.66699 72 -20s32 -31 40 -53l282 -780c12.667 -34 25.5 -71 38.5 -111s25.167 -82 36.5 -126c9.33301 44 20.166 86 32.499 126s24.5 77 36.5 111l280 780c6.66699 18.667 19.667 35.5 39 50.5s43 22.5 71 22.5h274l-584 -1457h-306z
" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="2142" 
d="M10 1457h283.999c29.333 0 53.833 -6.66699 73.5 -20s32.5 -31 38.5 -53l200 -760c6.66699 -24.667 13.334 -51 20.001 -79s11.667 -57.333 15 -88c6.66699 31.333 14 60.833 22 88.5l23 78.5l236 760c6 18.667 18.833 35.5 38.5 50.5s43.5 22.5 71.5 22.5h100
c29.333 0 53.5 -6.5 72.5 -19.5s32.167 -30.833 39.5 -53.5l234 -760l23 -74.5c8 -26.333 15 -54.5 21 -84.5c5.33301 29.333 10.833 57.333 16.5 84s11.5 51.667 17.5 75l200 760c5.33301 19.333 18 36.333 38 51s44 22 72 22h266l-450 -1457h-306l-274 903
c-4.66699 14 -9.5 29.333 -14.5 46s-9.83301 34.334 -14.5 53.001c-4.66699 -18.667 -9.5 -36.334 -14.5 -53.001s-9.83301 -32 -14.5 -46l-278 -903h-306z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="1424" 
d="M479 749l-450.999 708h338c22 0 38.167 -2.83301 48.5 -8.5s19.5 -15.167 27.5 -28.5l290 -491c3.33301 8.66699 7.16602 17 11.499 25s9.16602 16.333 14.499 25l254 436c16.667 28 39.334 42 68.001 42h324l-457 -693l467 -764h-338c-22 -0 -39.833 5.33301 -53.5 16
s-24.5 23.334 -32.5 38.001l-292 510c-5.33301 -14.667 -11.333 -27.334 -18 -38.001l-272 -472c-8 -14 -18.667 -26.5 -32 -37.5s-30 -16.5 -50 -16.5h-316z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="1354" 
d="M846 554l0.000976562 -554.002h-338v554l-528 903h298c29.333 0 52.666 -6.83301 69.999 -20.5s31.333 -31.167 42 -52.5l206 -424l46 -94c14 -28.667 26.333 -57 37 -85c10 28.667 22 57.334 36 86.001l45 93l204 424c4 8.66699 9.5 17.334 16.5 26.001s15 16.5 24 23.5
s19.333 12.667 31 17s24.5 6.5 38.5 6.5h300z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="1220" 
d="M1186 1457l0.000976562 -121.999c0 -17.333 -2.83301 -34.333 -8.5 -51s-13.5 -32.334 -23.5 -47.001l-684 -977h694v-260h-1124v130c0 15.333 2.66699 30.5 8 45.5s12.666 28.833 21.999 41.5l686 980h-660v260h1090z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="600" 
d="M90 -324v1881h448v-118c0 -20.667 -7.5 -38.334 -22.5 -53.001s-34.167 -22 -57.5 -22h-110v-1495h110c23.333 0 42.5 -7.33301 57.5 -22s22.5 -32 22.5 -52v-119h-448z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="808" 
d="M-36 1497h132c36 0 67.667 -9 95 -27s47.666 -44 60.999 -78l568 -1491h-132c-16 0 -32.167 2.33301 -48.5 7s-31.666 11.667 -45.999 21s-27.5 21 -39.5 35s-21.333 30 -28 48z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="600" 
d="M62 1557l448 -0.000976562v-1881h-448v119c0 20 7.5 37.333 22.5 52s34.167 22 57.5 22h110v1495h-110c-23.333 0 -42.5 7.33301 -57.5 22s-22.5 32.334 -22.5 53.001v118z" />
    <glyph glyph-name="asciicircum" unicode="^" 
d="M1062 768l-228 -0.00195312c-18 0 -33 4.5 -45 13.5s-21.667 20.5 -29 34.5l-124 237l-31.5 61c-9.66699 19.333 -18.167 38.666 -25.5 57.999c-6.66699 -19.333 -14.334 -38.666 -23.001 -57.999s-18.667 -39.666 -30 -60.999l-120 -237
c-7.33301 -13.333 -17.166 -24.666 -29.499 -33.999s-28.5 -14 -48.5 -14h-240l386 689h202z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="788" 
d="M788 -108v-194h-788v194h788z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="660" 
d="M252 1473c31.333 0 54.668 -5.16699 70.001 -15.5s28.666 -25.166 39.999 -44.499l136 -245h-176c-23.333 0 -42 3.16699 -56 9.5s-28.667 17.166 -44 32.499l-260 263h290z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="1078" 
d="M836 0c-29.333 0 -51.501 3.99902 -66.501 11.999s-27.5 24.667 -37.5 50l-22 57c-26 -22 -51 -41.333 -75 -58s-48.833 -30.834 -74.5 -42.501s-53 -20.334 -82 -26.001s-61.167 -8.5 -96.5 -8.5c-47.333 0 -90.333 6.16699 -129 18.5s-71.5 30.5 -98.5 54.5
s-47.833 53.833 -62.5 89.5s-22 76.834 -22 123.501c0 36.667 9.33301 74.334 28 113.001s51 73.834 97 105.501s108 58.167 186 79.5s176.333 33.333 295 36v44c0 60.667 -12.5 104.5 -37.5 131.5s-60.5 40.5 -106.5 40.5c-36.667 0 -66.667 -4 -90 -12
s-44.166 -17 -62.499 -27s-36.166 -19 -53.499 -27s-38 -12 -62 -12c-21.333 0 -39.5 5.16699 -54.5 15.5s-26.833 23.166 -35.5 38.499l-56 97c62.667 55.333 132.167 96.5 208.5 123.5s158.166 40.5 245.499 40.5c62.667 0 119.167 -10.167 169.5 -30.5
s93 -48.666 128 -84.999s61.833 -79.5 80.5 -129.5s28 -104.667 28 -164v-648h-142zM479.999 188.999c40 0 75.001 6.99902 105.001 20.999s60.333 36.667 91 68v144c-62 -2.66699 -113 -8 -153 -16s-71.667 -18 -95 -30s-39.5 -25.667 -48.5 -41s-13.5 -32 -13.5 -50
c0 -35.333 9.66699 -60.166 29 -74.499s47.666 -21.5 84.999 -21.5z" />
    <glyph glyph-name="b" unicode="b" 
d="M120 0l-0.000976562 1497h310v-570c38.667 38 82.5 68.5 131.5 91.5s106.5 34.5 172.5 34.5c54 0 104 -11.667 150 -35s85.667 -57.5 119 -102.5s59.5 -100.167 78.5 -165.5s28.5 -139.666 28.5 -222.999c0 -78 -10.833 -150 -32.5 -216s-52 -123.333 -91 -172
s-85.667 -86.667 -140 -114s-114.5 -41 -180.5 -41c-30.667 0 -58.167 3 -82.5 9s-46.666 14.5 -66.999 25.5s-39 24.167 -56 39.5s-33.5 32 -49.5 50l-11 -52c-4.66699 -21.333 -13.167 -36 -25.5 -44s-28.5 -12 -48.5 -12h-206zM625.999 819.999
c-22.667 0 -43.167 -2.16699 -61.5 -6.5s-35.333 -10.5 -51 -18.5s-30.167 -18.167 -43.5 -30.5s-26.666 -26.5 -39.999 -42.5v-440c23.333 -26 48.666 -44.167 75.999 -54.5s56 -15.5 86 -15.5c28.667 0 55 5.66699 79 17s44.833 29.5 62.5 54.5s31.5 57.5 41.5 97.5
s15 88.667 15 146c0 54.667 -4 100.667 -12 138s-19.167 67.5 -33.5 90.5s-31.5 39.5 -51.5 49.5s-42.333 15 -67 15z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="972" 
d="M868 791c-9.33301 -11.333 -18.3301 -20.335 -26.9971 -27.002s-21 -10 -37 -10c-15.333 0 -29.166 3.66699 -41.499 11l-41.5 24.5c-15.333 9 -33.333 17.167 -54 24.5s-46.334 11 -77.001 11c-38 0 -70.833 -7 -98.5 -21s-50.5 -34 -68.5 -60
s-31.333 -57.833 -40 -95.5s-13 -80.167 -13 -127.5c0 -99.333 19.167 -175.666 57.5 -228.999s91.166 -80 158.499 -80c36 0 64.5 4.5 85.5 13.5s38.833 19 53.5 30l40.5 30.5c12.333 9.33301 27.833 14 46.5 14c24.667 0 43.334 -9 56.001 -27l90 -111
c-32 -36.667 -65.667 -66.5 -101 -89.5s-71.5 -41 -108.5 -54s-74.167 -22 -111.5 -27s-73.666 -7.5 -108.999 -7.5c-63.333 0 -123.833 12 -181.5 36s-108.334 58.833 -152.001 104.5s-78.334 101.834 -104.001 168.501s-38.5 142.667 -38.5 228
c0 74.667 11.167 144.5 33.5 209.5s55.333 121.333 99 169s97.667 85.167 162 112.5s138.833 41 223.5 41c81.333 0 152.666 -13 213.999 -39s116.666 -64 165.999 -114z" />
    <glyph glyph-name="d" unicode="d" 
d="M848 0c-19.333 0 -35.501 4.33594 -48.501 13.0029s-22.167 21.667 -27.5 39l-24 79c-20.667 -22 -42.334 -42 -65.001 -60s-47.167 -33.5 -73.5 -46.5s-54.666 -23 -84.999 -30s-63.166 -10.5 -98.499 -10.5c-54 0 -104 12 -150 36s-85.667 58.667 -119 104
s-59.5 100.833 -78.5 166.5s-28.5 140.5 -28.5 224.5c0 77.333 10.667 149.166 32 215.499s51.5 123.666 90.5 171.999s85.833 86.166 140.5 113.499s115 41 181 41c53.333 0 98.5 -7.83301 135.5 -23.5s70.5 -36.167 100.5 -61.5v525h310v-1497h-192zM533.999 222.003
c22.667 0 43.168 2.16699 61.501 6.5s35.166 10.5 50.499 18.5s29.833 18 43.5 30s27.167 26 40.5 42v440c-24 26 -49.5 44.167 -76.5 54.5s-55.5 15.5 -85.5 15.5c-28.667 0 -55 -5.66699 -79 -17s-44.833 -29.5 -62.5 -54.5s-31.5 -57.5 -41.5 -97.5s-15 -88.333 -15 -145
c0 -54.667 4 -100.667 12 -138s19.167 -67.5 33.5 -90.5s31.5 -39.5 51.5 -49.5s42.333 -15 67 -15z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="1088" 
d="M564 1053c68.667 0 131.502 -10.668 188.502 -32.001s106 -52.333 147 -93s73 -90.5 96 -149.5s34.5 -125.833 34.5 -200.5c0 -23.333 -1 -42.333 -3 -57s-5.66699 -26.334 -11 -35.001s-12.5 -14.667 -21.5 -18s-20.5 -5 -34.5 -5h-598
c10 -86.667 36.333 -149.5 79 -188.5s97.667 -58.5 165 -58.5c36 0 67 4.33301 93 13s49.167 18.334 69.5 29.001l56 29c17 8.66699 34.833 13 53.5 13c24.667 0 43.334 -9 56.001 -27l90 -111c-32 -36.667 -66.5 -66.5 -103.5 -89.5s-75 -41 -114 -54s-78 -22 -117 -27
s-76.167 -7.5 -111.5 -7.5c-72.667 0 -140.834 11.833 -204.501 35.5s-119.334 58.834 -167.001 105.501s-85.334 104.667 -113.001 174s-41.5 150 -41.5 242c0 69.333 11.833 135 35.5 197s57.667 116.333 102 163s98 83.667 161 111s134.167 41 213.5 41zM570.002 838.999
c-59.333 0 -105.67 -16.8311 -139.003 -50.498s-55.333 -82.167 -66 -145.5h387c0 24.667 -3.16699 48.667 -9.5 72s-16.666 44.166 -30.999 62.499s-33.166 33.166 -56.499 44.499s-51.666 17 -84.999 17z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="724" 
d="M166 0l-0.00292969 824.001l-68 14c-23.333 5.33301 -42.166 13.666 -56.499 24.999s-21.5 28 -21.5 50v122h146v59c0 59.333 9.5 113 28.5 161s46.333 89 82 123s79.167 60.333 130.5 79s109.666 28 174.999 28c25.333 0 48.833 -1.5 70.5 -4.5
s44.167 -7.83301 67.5 -14.5l-6 -151c-0.666992 -10.667 -3.5 -19.5 -8.5 -26.5s-11.167 -12.667 -18.5 -17s-15.5 -7.5 -24.5 -9.5s-17.833 -3 -26.5 -3c-27.333 0 -51.666 -2.66699 -72.999 -8s-39.166 -14.833 -53.499 -28.5s-25.166 -31.667 -32.499 -54
s-11 -50.5 -11 -84.5v-49h244v-212h-234v-823h-310z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="1046" 
d="M486 1055c44 0 85.5039 -4.33203 124.504 -12.999s75.167 -21 108.5 -37h313v-113c0 -17.333 -4.83301 -31 -14.5 -41s-26.167 -17.667 -49.5 -23l-76 -17c9.33301 -30 14 -61 14 -93c0 -52 -10.833 -98.667 -32.5 -140s-51.334 -76.5 -89.001 -105.5
s-82.167 -51.333 -133.5 -67s-106.333 -23.5 -165 -23.5c-32.667 0 -63.334 2 -92.001 6c-22.667 -13.333 -34 -28.666 -34 -45.999s9.16699 -29.833 27.5 -37.5s42.5 -13 72.5 -16s64.333 -5 103 -6s78 -3.33301 118 -7s79.333 -9.83398 118 -18.501s73 -22.667 103 -42
s54.167 -45.166 72.5 -77.499s27.5 -73.5 27.5 -123.5c0 -46.667 -11.333 -92.334 -34 -137.001s-56 -84.334 -100 -119.001s-98 -62.5 -162 -83.5s-137.333 -31.5 -220 -31.5c-81.333 0 -151.5 7.66699 -210.5 23s-108 35.5 -147 60.5s-67.833 53.833 -86.5 86.5
s-28 66.667 -28 102c0 46 14 84.833 42 116.5s66.333 56.5 115 74.5c-23.333 15.333 -42 35 -56 59s-21 54.333 -21 91c0 15.333 2.66699 31.5 8 48.5s13.666 33.5 24.999 49.5s25.666 31.333 42.999 46s38 27.667 62 39c-54 28.667 -96.5 66.667 -127.5 114
s-46.5 102.333 -46.5 165c0 52 11 98.833 33 140.5s52.167 77 90.5 106s83.5 51.333 135.5 67s108.333 23.5 169 23.5zM716.004 -37.999c0 16.667 -5.16895 30 -15.502 40s-24.333 18 -42 24s-38.334 10.333 -62.001 13s-49.167 4.66699 -76.5 6s-55.666 2.5 -84.999 3.5
s-58.333 3.16699 -87 6.5c-19.333 -13.333 -35.166 -28.166 -47.499 -44.499s-18.5 -34.833 -18.5 -55.5c0 -14.667 3.16699 -28 9.5 -40s17.5 -22.333 33.5 -31s37.5 -15.5 64.5 -20.5s61.167 -7.5 102.5 -7.5c44.667 0 81.334 2.66699 110.001 8s51.5 12.666 68.5 21.999
s28.833 20.5 35.5 33.5s10 27.167 10 42.5zM486.002 573.001c51.333 0 88.501 12.832 111.501 38.499s34.5 58.834 34.5 99.501c0 42 -11.5 75 -34.5 99s-60.167 36 -111.5 36s-88.5 -12 -111.5 -36s-34.5 -57 -34.5 -99c0 -20 2.83301 -38.333 8.5 -55
s14.5 -31.167 26.5 -43.5s27.167 -22 45.5 -29s40.166 -10.5 65.499 -10.5z" />
    <glyph glyph-name="h" unicode="h" 
d="M120 0l-0.00292969 1497h310v-551c37.333 32 77.833 57.833 121.5 77.5s95.834 29.5 156.501 29.5c56.667 0 106.834 -9.83301 150.501 -29.5s80.5 -47 110.5 -82s52.667 -76.667 68 -125s23 -100.833 23 -157.5v-659h-310v659c0 50.667 -11.667 90.167 -35 118.5
s-57.666 42.5 -102.999 42.5c-34 0 -66 -7.33301 -96 -22s-58.667 -34.334 -86 -59.001v-739h-310z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="570" 
d="M440 1037v-1037h-310v1037h310zM476 1321c0 -24.667 -5 -47.834 -15 -69.501s-23.667 -40.667 -41 -57s-37.5 -29.333 -60.5 -39s-47.5 -14.5 -73.5 -14.5c-25.333 0 -49 4.83301 -71 14.5s-41.5 22.667 -58.5 39s-30.333 35.333 -40 57s-14.5 44.834 -14.5 69.501
c0 25.333 4.83301 49 14.5 71s23 41.333 40 58s36.5 29.667 58.5 39s45.667 14 71 14c26 0 50.5 -4.66699 73.5 -14s43.167 -22.333 60.5 -39s31 -36 41 -58s15 -45.667 15 -71z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="566" 
d="M440 1037l-0.00195312 -1039c0 -48.667 -6.16699 -94.5 -18.5 -137.5s-32.666 -80.667 -60.999 -113s-65.666 -57.833 -111.999 -76.5s-103.833 -28 -172.5 -28c-25.333 0 -48.833 1.5 -70.5 4.5s-44.167 7.5 -67.5 13.5l10 163c1.33301 14 6 24.333 14 31s24 10 48 10
s43.667 2 59 6s27.5 11 36.5 21s15.333 23.5 19 40.5s5.5 38.833 5.5 65.5v1039h310zM475.998 1321c0 -24.667 -5 -47.834 -15 -69.501s-23.667 -40.667 -41 -57s-37.5 -29.333 -60.5 -39s-47.5 -14.5 -73.5 -14.5c-25.333 0 -49 4.83301 -71 14.5s-41.5 22.667 -58.5 39
s-30.333 35.333 -40 57s-14.5 44.834 -14.5 69.501c0 25.333 4.83301 49 14.5 71s23 41.333 40 58s36.5 29.667 58.5 39s45.667 14 71 14c26 0 50.5 -4.66699 73.5 -14s43.167 -22.333 60.5 -39s31 -36 41 -58s15 -45.667 15 -71z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="1152" 
d="M430 1497l0.00195312 -839.001h46c19.333 0 34.166 2.66699 44.499 8s20.166 15 29.499 29l196 292c11.333 17.333 24.833 30 40.5 38s35.5 12 59.5 12h284l-268 -369c-12.667 -17.333 -26.5 -32.666 -41.5 -45.999s-31.167 -25.333 -48.5 -36
c30 -22.667 56 -52.667 78 -90l292 -496h-280c-23.333 -0 -43.5 3.83301 -60.5 11.5s-30.833 21.167 -41.5 40.5l-196 367c-9.33301 16.667 -19 27.5 -29 32.5s-25 7.5 -45 7.5h-60v-459h-310v1497h310z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="570" 
d="M440 1497v-1497h-310v1497h310z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="1722" 
d="M120 0l-0.00195312 1037h192c19.333 0 35.5 -4.33301 48.5 -13s22.167 -21.667 27.5 -39l17 -56c17.333 18 35.333 34.5 54 49.5s38.834 28 60.501 39s45 19.667 70 26s52.5 9.5 82.5 9.5c62.667 0 114.5 -16.167 155.5 -48.5s72.167 -75.5 93.5 -129.5
c17.333 32 38.5 59.333 63.5 82s52 41 81 55s59.667 24.333 92 31s64.5 10 96.5 10c59.333 0 112 -8.83301 158 -26.5s84.5 -43.334 115.5 -77.001s54.5 -75 70.5 -124s24 -104.5 24 -166.5v-659h-310v659c0 107.333 -46 161 -138 161c-42 0 -76.833 -13.833 -104.5 -41.5
s-41.5 -67.5 -41.5 -119.5v-659h-310v659c0 59.333 -11.667 101 -35 125s-57.666 36 -102.999 36c-28 0 -54.5 -6.33301 -79.5 -19s-48.5 -29.667 -70.5 -51v-750h-310z" />
    <glyph glyph-name="n" unicode="n" 
d="M120 0l0.000976562 1037h192c19.333 0 35.5 -4.33301 48.5 -13s22.167 -21.667 27.5 -39l18 -60c20 18.667 40.667 36 62 52s44.166 29.5 68.499 40.5s50.666 19.667 78.999 26s59.166 9.5 92.499 9.5c56.667 0 106.834 -9.83301 150.501 -29.5s80.5 -47 110.5 -82
s52.667 -76.667 68 -125s23 -100.833 23 -157.5v-659h-310v659c0 50.667 -11.667 90.167 -35 118.5s-57.666 42.5 -102.999 42.5c-34 0 -66 -7.33301 -96 -22s-58.667 -34.334 -86 -59.001v-739h-310z" />
    <glyph glyph-name="o" unicode="o" 
d="M582 1053c78.667 0 150.334 -12.333 215.001 -37s120.167 -60 166.5 -106s82.333 -101.833 108 -167.5s38.5 -139.5 38.5 -221.5c0 -82.667 -12.833 -157.167 -38.5 -223.5s-61.667 -122.666 -108 -168.999s-101.833 -82 -166.5 -107s-136.334 -37.5 -215.001 -37.5
c-79.333 0 -151.5 12.5 -216.5 37.5s-121 60.667 -168 107s-83.333 102.666 -109 168.999s-38.5 140.833 -38.5 223.5c0 82 12.833 155.833 38.5 221.5s62 121.5 109 167.5s103 81.333 168 106s137.167 37 216.5 37zM582 214c71.333 0 123.833 25.167 157.5 75.5
s50.5 126.833 50.5 229.5s-16.833 179 -50.5 229s-86.167 75 -157.5 75c-73.333 0 -127 -25 -161 -75s-51 -126.333 -51 -229s17 -179.167 51 -229.5s87.667 -75.5 161 -75.5z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="1156" 
d="M120 -329l0.000976562 1366h192c19.333 0 35.5 -4.33301 48.5 -13s22.167 -21.667 27.5 -39l23 -76c20 22 41.5 42 64.5 60s47.667 33.5 74 46.5s54.666 23.167 84.999 30.5s63.5 11 99.5 11c54 0 104 -12 150 -36s85.667 -58.667 119 -104s59.5 -100.833 78.5 -166.5
s28.5 -140.167 28.5 -223.5c0 -78 -10.833 -150 -32.5 -216s-52 -123.333 -91 -172s-85.667 -86.667 -140 -114s-114.5 -41 -180.5 -41c-54 0 -99.333 7.66699 -136 23s-70 36 -100 62v-398h-310zM626.001 820c-22.667 0 -43.167 -2.16699 -61.5 -6.5
s-35.333 -10.5 -51 -18.5s-30.167 -18.167 -43.5 -30.5s-26.666 -26.5 -39.999 -42.5v-440c23.333 -26 48.666 -44.167 75.999 -54.5s56 -15.5 86 -15.5c28.667 0 55 5.66699 79 17s44.833 29.5 62.5 54.5s31.5 57.5 41.5 97.5s15 88.667 15 146
c0 54.667 -4 100.667 -12 138s-19.167 67.5 -33.5 90.5s-31.5 39.5 -51.5 49.5s-42.333 15 -67 15z" />
    <glyph glyph-name="q" unicode="q" 
d="M1040 1037l0.000976562 -1366h-310v442c-39.333 -38.667 -83.333 -69.834 -132 -93.501s-106 -35.5 -172 -35.5c-54 0 -104 12 -150 36s-85.667 58.667 -119 104s-59.5 100.833 -78.5 166.5s-28.5 140.5 -28.5 224.5c0 77.333 10.667 149.166 32 215.499
s51.5 123.666 90.5 171.999s85.833 86.166 140.5 113.499s115 41 181 41c31.333 0 59.666 -2.66699 84.999 -8s48.5 -13 69.5 -23s40.5 -22 58.5 -36s35.333 -29.333 52 -46l13 41c12 34.667 37.333 52 76 52h192zM534.001 222.004c22.667 0 43.168 2.16699 61.501 6.5
s35.166 10.5 50.499 18.5s29.833 18 43.5 30s27.167 26 40.5 42v440c-24 25.333 -49.5 43.333 -76.5 54s-55.5 16 -85.5 16c-28.667 0 -55 -5.66699 -79 -17s-44.833 -29.5 -62.5 -54.5s-31.5 -57.5 -41.5 -97.5s-15 -88.333 -15 -145c0 -54.667 4 -100.667 12 -138
s19.167 -67.5 33.5 -90.5s31.5 -39.5 51.5 -49.5s42.333 -15 67 -15z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="826" 
d="M120 0l-0.00195312 1037h184c15.333 0 28.166 -1.33301 38.499 -4s19 -6.83398 26 -12.501s12.333 -13.167 16 -22.5s6.83398 -20.666 9.50098 -33.999l17 -97c38 58.667 80.667 105 128 139s99 51 155 51c47.333 0 85.333 -11.333 114 -34l-40 -228
c-2.66699 -14 -8 -23.833 -16 -29.5s-18.667 -8.5 -32 -8.5c-11.333 0 -24.666 1.5 -39.999 4.5s-34.666 4.5 -57.999 4.5c-81.333 0 -145.333 -43.333 -192 -130v-636h-310z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="880" 
d="M754 810c-8 -12.667 -16.5039 -21.667 -25.5039 -27s-20.5 -8 -34.5 -8c-14.667 0 -29.334 3.16699 -44.001 9.5l-47.5 20.5c-17 7.33301 -36.167 14.166 -57.5 20.499s-45.666 9.5 -72.999 9.5c-39.333 0 -69.5 -7.5 -90.5 -22.5s-31.5 -35.833 -31.5 -62.5
c0 -19.333 6.83301 -35.333 20.5 -48s31.834 -23.834 54.501 -33.501s48.334 -18.834 77.001 -27.501s58 -18.334 88 -29.001s59.333 -23.167 88 -37.5s54.334 -32 77.001 -53s40.834 -46.5 54.501 -76.5s20.5 -66 20.5 -108c0 -51.333 -9.33301 -98.666 -28 -141.999
s-46.5 -80.666 -83.5 -111.999s-82.667 -55.666 -137 -72.999s-116.833 -26 -187.5 -26c-35.333 0 -70.666 3.33301 -105.999 10s-69.5 15.667 -102.5 27s-63.5 24.833 -91.5 40.5s-52 32.5 -72 50.5l72 114c8.66699 13.333 19 23.833 31 31.5s27.667 11.5 47 11.5
c18 0 34.167 -4 48.5 -12l46.5 -26c16.667 -9.33301 36.167 -18 58.5 -26s50.5 -12 84.5 -12c24 0 44.333 2.5 61 7.5s30 11.833 40 20.5s17.333 18.334 22 29.001s7 21.667 7 33c0 20.667 -7 37.667 -21 51s-32.333 24.833 -55 34.5s-48.5 18.667 -77.5 27
s-58.5 17.833 -88.5 28.5s-59.5 23.5 -88.5 38.5s-54.833 34 -77.5 57s-41 51.167 -55 84.5s-21 73.666 -21 120.999c0 44 8.5 85.833 25.5 125.5s42.5 74.5 76.5 104.5s76.667 53.833 128 71.5s111.333 26.5 180 26.5c37.333 0 73.5 -3.33301 108.5 -10
s67.833 -16 98.5 -28s58.667 -26.167 84 -42.5s47.666 -34.166 66.999 -53.499z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="800" 
d="M484 -16c-49.333 0 -92.833 7.16797 -130.5 21.501s-69.334 34.666 -95.001 60.999s-45.167 58.166 -58.5 95.499s-20 79 -20 125v536h-88c-16 0 -29.667 5.16699 -41 15.5s-17 25.5 -17 45.5v121l165 32l61 253c8 32 30.667 48 68 48h162v-303h252v-212h-252v-515
c0 -24 5.83301 -43.833 17.5 -59.5s28.5 -23.5 50.5 -23.5c11.333 0 20.833 1.16699 28.5 3.5s14.334 5 20.001 8s11 5.66699 16 8s10.833 3.5 17.5 3.5c9.33301 0 16.833 -2.16699 22.5 -6.5s11.5 -11.166 17.5 -20.499l94 -147c-40 -30 -85 -52.5 -135 -67.5
s-101.667 -22.5 -155 -22.5z" />
    <glyph glyph-name="u" unicode="u" 
d="M410 1037l0.000976562 -658c0 -51.333 11.667 -91 35 -119s57.666 -42 102.999 -42c34 0 65.833 7 95.5 21s58.5 33.667 86.5 59v739h310v-1037h-192c-38.667 -0 -64 17.333 -76 52l-19 60c-20 -19.333 -40.667 -36.833 -62 -52.5s-44.166 -29 -68.499 -40
s-50.5 -19.667 -78.5 -26s-58.667 -9.5 -92 -9.5c-56.667 0 -106.834 9.83301 -150.501 29.5s-80.5 47.167 -110.5 82.5s-52.667 77 -68 125s-23 100.667 -23 158v658h310z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="1106" 
d="M694 0h-281.999l-402 1037h258c22 0 40.5 -5.16699 55.5 -15.5s25.167 -23.166 30.5 -38.499l144 -465l32.5 -111c10.333 -36 19.166 -72 26.499 -108c7.33301 36 16.333 72 27 108l34 111l150 465c5.33301 15.333 15.333 28.166 30 38.499s32 15.5 52 15.5h246z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="1626" 
d="M0 1037l247.998 -0.00195312c22 0 41 -5 57 -15s25.667 -23 29 -39l104 -465c8 -35.333 15.333 -69.666 22 -102.999s12.667 -66.333 18 -99l29.5 98.5c10.333 33 20.5 67.5 30.5 103.5l130 466c4 15.333 13.667 28.166 29 38.499s33 15.5 53 15.5h138
c22 0 40.833 -5.16699 56.5 -15.5s25.5 -23.166 29.5 -38.499l124 -466l28.5 -103c9.66699 -33.333 18.834 -67 27.501 -101c5.33301 33.333 11.833 66.5 19.5 99.5l24.5 104.5l110 465c3.33301 16 12.833 29 28.5 39s33.5 15 53.5 15h236l-322 -1037h-252
c-13.333 0 -24.666 4 -33.999 12s-16.666 21.333 -21.999 40l-148 517l-19 69c-6 22.667 -11.333 45.334 -16 68.001c-4.66699 -23.333 -9.83398 -46.5 -15.501 -69.5s-12.167 -46.167 -19.5 -69.5l-150 -515c-10 -34.667 -32 -52 -66 -52h-240z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="1146" 
d="M368 536l-334.001 501h298c22 0 38.167 -2.83301 48.5 -8.5s19.5 -15.167 27.5 -28.5l185 -307c4 12 9 23.667 15 35l20 36l132 231c8.66699 15.333 18.334 26.166 29.001 32.499s23.667 9.5 39 9.5h284l-334 -487l348 -550h-298c-22 0 -39.667 5.33301 -53 16
s-24.333 23.334 -33 38.001l-185 318c-3.33301 -11.333 -7.33301 -22 -12 -32s-9.66699 -19.333 -15 -28l-152 -258c-8 -14 -18.667 -26.5 -32 -37.5s-30 -16.5 -50 -16.5h-276z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="1106" 
d="M544 -269c-9.33301 -20.667 -20.667 -35.832 -34 -45.499s-34.666 -14.5 -63.999 -14.5h-232l200 420l-414 946h274c24 0 42.667 -5.33301 56 -16s22.666 -23.334 27.999 -38.001l170 -447c16 -41.333 29 -82.666 39 -123.999c6.66699 21.333 14 42.333 22 63
s15.667 41.667 23 63l154 445c5.33301 15.333 15.833 28.166 31.5 38.499s32.5 15.5 50.5 15.5h250z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="954" 
d="M894 913c0 -21.333 -4 -41.667 -12 -61s-17.333 -36 -28 -50l-433 -572h463v-230h-820v126c0 12.667 3.5 28.5 10.5 47.5s16.833 37.167 29.5 54.5l437 579h-449v230h802v-124z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="600" 
d="M120 397c0 40 -7.66699 72.1699 -23 96.5029s-43 36.5 -83 36.5v173c20 0 36.833 3.33301 50.5 10s24.5 15.834 32.5 27.501s13.833 25.667 17.5 42s5.5 34.166 5.5 53.499c0 29.333 -1.83301 59 -5.5 89s-7.83398 60 -12.501 90s-8.83398 60.333 -12.501 91
s-5.5 61.334 -5.5 92.001c0 54 7.83301 103.167 23.5 147.5s40 82.166 73 113.499s74.833 55.5 125.5 72.5s111.334 25.5 182.001 25.5h52v-134c0 -10.667 -2.33301 -19.667 -7 -27s-10.334 -13.333 -17.001 -18s-13.667 -8.16699 -21 -10.5s-13.666 -3.5 -18.999 -3.5
c-42 0 -72.833 -13 -92.5 -39s-29.5 -62 -29.5 -108c0 -36.667 1.5 -71.334 4.5 -104.001s6.16699 -64.167 9.5 -94.5l9.5 -89.5c3 -29.333 4.5 -59 4.5 -89c0 -25.333 -3.33301 -49.666 -10 -72.999s-16.834 -44.833 -30.501 -64.5s-30.5 -37 -50.5 -52
s-43 -26.5 -69 -34.5c26 -8 49 -19.333 69 -34s36.833 -31.834 50.5 -51.501s23.834 -41.334 30.501 -65.001s10 -47.834 10 -72.501c0 -30 -1.5 -59.667 -4.5 -89l-9.5 -89.5c-3.33301 -30.333 -6.5 -61.833 -9.5 -94.5s-4.5 -67.334 -4.5 -104.001
c0 -46 9.83301 -82 29.5 -108s50.5 -39 92.5 -39c5.33301 0 11.666 -1.16699 18.999 -3.5s14.333 -5.83301 21 -10.5s12.334 -10.667 17.001 -18s7 -16.333 7 -27v-134h-52c-70.667 0 -131.334 8.5 -182.001 25.5s-92.5 41.333 -125.5 73s-57.333 69.5 -73 113.5
s-23.5 93 -23.5 147c0 30.667 1.83301 61.334 5.5 92.001s7.83398 61 12.501 91s8.83398 60 12.501 90s5.5 59.667 5.5 89z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="600" 
d="M180 1557h240v-1886h-240v1886z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="600" 
d="M480 397c0 -29.333 1.83301 -59.0029 5.5 -89.0029s7.83398 -60 12.501 -90s8.83398 -60.333 12.501 -91s5.5 -61.334 5.5 -92.001c0 -54 -7.83301 -103 -23.5 -147s-40 -81.833 -73 -113.5s-74.833 -56 -125.5 -73s-111.334 -25.5 -182.001 -25.5h-52v134
c0 10.667 2.33301 19.667 7 27s10.334 13.333 17.001 18s13.667 8.16699 21 10.5s13.666 3.5 18.999 3.5c42 0 72.833 13 92.5 39s29.5 62 29.5 108c0 36.667 -1.5 71.334 -4.5 104.001s-6.16699 64.167 -9.5 94.5l-9.5 89.5c-3 29.333 -4.5 59 -4.5 89
c0 24.667 3.33301 48.834 10 72.501s16.834 45.334 30.501 65.001s30.334 36.834 50.001 51.501s42.5 26 68.5 34c-26 8 -48.833 19.5 -68.5 34.5s-36.334 32.333 -50.001 52s-23.834 41.167 -30.501 64.5s-10 47.666 -10 72.999c0 30 1.5 59.667 4.5 89l9.5 89.5
c3.33301 30.333 6.5 61.833 9.5 94.5s4.5 67.334 4.5 104.001c0 46 -9.83301 82 -29.5 108s-50.5 39 -92.5 39c-5.33301 0 -11.666 1.16699 -18.999 3.5s-14.333 5.83301 -21 10.5s-12.334 10.667 -17.001 18s-7 16.333 -7 27v134h52c70.667 0 131.334 -8.5 182.001 -25.5
s92.5 -41.167 125.5 -72.5s57.333 -69.166 73 -113.499s23.5 -93.5 23.5 -147.5c0 -30.667 -1.83301 -61.334 -5.5 -92.001s-7.83398 -61 -12.501 -91s-8.83398 -60 -12.501 -90s-5.5 -59.667 -5.5 -89c0 -19.333 1.83301 -37.166 5.5 -53.499s9.5 -30.333 17.5 -42
s18.833 -20.834 32.5 -27.501s30.5 -10 50.5 -10v-173c-40 0 -67.667 -12.167 -83 -36.5s-23 -56.5 -23 -96.5z" />
    <glyph glyph-name="asciitilde" unicode="~" 
d="M730 677c32 0 56.667 10.333 74 31s26 51 26 91h250c0 -56.667 -7.5 -107.667 -22.5 -153s-36.5 -83.833 -64.5 -115.5s-62.5 -56 -103.5 -73s-87.5 -25.5 -139.5 -25.5c-35.333 0 -68.5 3.83301 -99.5 11.5s-59.833 16.334 -86.5 26.001l-73.5 26
c-22.333 7.66699 -42.5 11.5 -60.5 11.5c-32 0 -56.667 -10.333 -74 -31s-26 -51 -26 -91h-250c0 56.667 7.5 107.667 22.5 153s36.5 83.833 64.5 115.5s62.5 56 103.5 73s87.5 25.5 139.5 25.5c35.333 0 68.5 -3.83301 99.5 -11.5s59.833 -16.334 86.5 -26.001l73.5 -26
c22.333 -7.66699 42.5 -11.5 60.5 -11.5z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="386" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="752" 
d="M234 -329v527c0 61.333 3.66699 122.333 11 183s17 122 29 184h210c12 -62 21.667 -123.333 29 -184s11 -121.667 11 -183v-527h-290zM200 882c0 24 4.5 46.5 13.5 67.5s21.333 39.167 37 54.5s34.167 27.333 55.5 36s44.666 13 69.999 13
c24.667 0 47.667 -4.33301 69 -13s40 -20.667 56 -36s28.5 -33.5 37.5 -54.5s13.5 -43.5 13.5 -67.5s-4.5 -46.333 -13.5 -67s-21.5 -38.667 -37.5 -54s-34.667 -27.5 -56 -36.5s-44.333 -13.5 -69 -13.5c-25.333 0 -48.666 4.5 -69.999 13.5s-39.833 21.167 -55.5 36.5
s-28 33.333 -37 54s-13.5 43 -13.5 67z" />
    <glyph glyph-name="cent" unicode="&#xa2;" 
d="M518 -6c-57.333 10.667 -110.999 29.999 -160.999 57.999s-93.5 64.333 -130.5 109s-66 97 -87 157s-31.5 127 -31.5 201c0 72 11.167 139.5 33.5 202.5s55.5 118.167 99.5 165.5s98.167 85.333 162.5 114s138.5 45 222.5 49l14 140c2 18 10.333 34.667 25 50
s33.667 23 57 23h114l-23 -227c50 -11.333 96 -28.333 138 -51s81 -51 117 -85l-80 -105c-8.66699 -11.333 -17.167 -20 -25.5 -26s-20.5 -9 -36.5 -9c-10 0 -19.667 1.66699 -29 5s-19.333 7.5 -30 12.5l-35 16c-12.667 5.66699 -26.667 11.167 -42 16.5l-61 -598
c28.667 4 52.667 10.5 72 19.5s36.5 18 51.5 27s28.833 16.833 41.5 23.5s26.667 10 42 10c12 0 22.833 -2.33301 32.5 -7s17.5 -10.667 23.5 -18l86 -107c-26.667 -29.333 -54.834 -54.166 -84.501 -74.499s-60.167 -37.333 -91.5 -51s-63.5 -24.167 -96.5 -31.5
s-66.167 -12.333 -99.5 -15l-12 -126c-2 -18 -10.333 -34.667 -25 -50s-33.667 -23 -57 -23h-114zM414.001 518.999c0 -72 11 -131.667 33 -179s54 -82 96 -104l59 584c-65.333 -13.333 -113 -46 -143 -98s-45 -119.667 -45 -203z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" 
d="M28 685c0 26.667 8.50098 49.8301 25.501 69.4971s42.5 29.5 76.5 29.5h84v216c0 63.333 9.66699 123.5 29 180.5s48.833 107.167 88.5 150.5s89.334 77.833 149.001 103.5s130.167 38.5 211.5 38.5c54 0 102.833 -6.83301 146.5 -20.5s82.667 -32.667 117 -57
s64.5 -53 90.5 -86s48.667 -68.5 68 -106.5l-124 -78c-24.667 -13.333 -48.334 -20 -71.001 -20c-30.667 0 -58.334 12.667 -83.001 38c-25.333 26 -48.333 45.333 -69 58s-45.667 19 -75 19c-54 0 -94.167 -18.833 -120.5 -56.5s-39.5 -91.834 -39.5 -162.501v-217h380
v-118c0 -10 -2.16699 -19.5 -6.5 -28.5s-10.333 -17.167 -18 -24.5s-16.667 -13.166 -27 -17.499s-21.833 -6.5 -34.5 -6.5h-294v-156c0 -40 -7.5 -76.167 -22.5 -108.5s-35.167 -62.5 -60.5 -90.5c30 6.66699 60 11.834 90 15.501s60.333 5.5 91 5.5h502v-127
c0 -14.667 -3.16699 -29.667 -9.5 -45s-15.166 -29.166 -26.499 -41.499s-25.166 -22.333 -41.499 -30s-34.5 -11.5 -54.5 -11.5h-954v186c22 4 43.167 10.333 63.5 19s38.166 19.667 53.499 33s27.666 29.333 36.999 48s14 40.334 14 65.001v238h-186v96z" />
    <glyph glyph-name="currency" unicode="&#xa4;" 
d="M200 674c0 32.667 3.83301 64.002 11.5 94.002s18.5 58.333 32.5 85l-154 153l156 148l150 -148c27.333 14.667 56.333 26 87 34s63 12 97 12c32 0 62.833 -3.83301 92.5 -11.5s57.834 -18.5 84.501 -32.5l153 152l154 -150l-153 -151
c15.333 -27.333 27.333 -56.5 36 -87.5s13 -63.5 13 -97.5c0 -32.667 -3.83301 -64 -11.5 -94s-18.834 -58.333 -33.501 -85l155 -152l-156 -149l-151 148c-27.333 -14.667 -56.333 -26 -87 -34s-62.667 -12 -96 -12c-32 0 -62.833 3.66699 -92.5 11
s-57.834 17.666 -84.501 30.999l-153 -150l-154 150l152 150c-15.333 27.333 -27.166 56.666 -35.499 87.999s-12.5 64 -12.5 98zM428 674.002c0 -21.333 4 -41.666 12 -60.999s18.833 -36.333 32.5 -51s29.667 -26.167 48 -34.5s38.166 -12.5 59.499 -12.5
s41.333 4.16699 60 12.5s35 19.833 49 34.5s25 31.667 33 51s12 39.666 12 60.999c0 22.667 -4 43.667 -12 63s-19 36.333 -33 51s-30.333 26.167 -49 34.5s-38.667 12.5 -60 12.5s-41.166 -4.16699 -59.499 -12.5s-34.333 -19.833 -48 -34.5s-24.5 -31.667 -32.5 -51
s-12 -40.333 -12 -63z" />
    <glyph glyph-name="yen" unicode="&#xa5;" 
d="M122 639h248l-390 818h258c29.333 0 53 -6.5 71 -19.5s31.667 -30.833 41 -53.5l166 -425l37.5 -98.5c11 -29.667 19.833 -59.167 26.5 -88.5c6 30 14.167 59.833 24.5 89.5l35.5 97.5l164 425c7.33301 19.333 20.5 36.333 39.5 51s42.5 22 70.5 22h260l-391 -818h249
v-165h-300v-88h300v-165h-300v-221h-310v221h-300v165h300v88h-300v165z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="600" 
d="M180 1557h240v-819h-240v819zM180 490h240v-819h-240v819z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="1014" 
d="M834 1228c-8 -12.667 -16.4961 -21.5039 -25.4961 -26.5039s-20.5 -7.5 -34.5 -7.5c-14.667 0 -29.334 3 -44.001 9s-30.5 12.833 -47.5 20.5s-36.167 14.5 -57.5 20.5s-45.666 9 -72.999 9c-48 0 -83.667 -9 -107 -27s-35 -41 -35 -69c0 -17.333 7.66699 -33 23 -47
s35.666 -27.333 60.999 -40s54.166 -25.334 86.499 -38.001l99.5 -41c34 -14.667 67.167 -31 99.5 -49s61.166 -38.833 86.499 -62.5s45.666 -50.834 60.999 -81.501s23 -65.667 23 -105c0 -53.333 -12.333 -102.166 -37 -146.499s-64.334 -80.5 -119.001 -108.5
c28.667 -24.667 51.834 -53.167 69.501 -85.5s26.5 -71.166 26.5 -116.499c0 -51.333 -9.33301 -98.666 -28 -141.999s-46.5 -80.666 -83.5 -111.999s-82.667 -55.666 -137 -72.999s-116.833 -26 -187.5 -26c-35.333 0 -70.666 3.33301 -105.999 10s-69.5 15.667 -102.5 27
s-63.5 24.833 -91.5 40.5s-52 32.5 -72 50.5l72 114c8.66699 13.333 19 23.833 31 31.5s27.667 11.5 47 11.5c18 0 34.167 -4 48.5 -12l48 -26c17.667 -9.33301 38.334 -18 62.001 -26s54.167 -12 91.5 -12c43.333 0 77.166 7.83301 101.499 23.5s36.5 39.167 36.5 70.5
c0 24.667 -7.83301 45.5 -23.5 62.5s-36.167 32.167 -61.5 45.5s-54.333 25.666 -87 36.999s-66 23.666 -100 36.999s-67.333 28.333 -100 45s-61.667 36.834 -87 60.501s-45.833 51.834 -61.5 84.501s-23.5 71.667 -23.5 117c0 52 13 99 39 141s65.667 75.667 119 101
c-29.333 26.667 -53 58 -71 94s-27 80 -27 132c0 44 8.5 85.833 25.5 125.5s42.5 74.5 76.5 104.5s76.667 53.833 128 71.5s111.333 26.5 180 26.5c37.333 0 73.5 -3.33301 108.5 -10s67.833 -16 98.5 -28s58.667 -26.167 84 -42.5s47.666 -34.166 66.999 -53.499z
M350.004 723.996c0 -23.333 7.99902 -43.6689 23.999 -61.002s37 -33.166 63 -47.499s55.667 -28 89 -41l102 -41.5c18.667 11.333 32 25 40 41s12 33 12 51c0 24 -8 44.5 -24 61.5s-37 32.5 -63 46.5s-55.5 27.333 -88.5 40s-66.5 26.667 -100.5 42
c-19.333 -12.667 -33.166 -26.167 -41.499 -40.5s-12.5 -31.166 -12.5 -50.499z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="660" 
d="M298 1295c0 -21.333 -4.33301 -41.333 -13 -60s-20.5 -34.834 -35.5 -48.501s-32.333 -24.5 -52 -32.5s-40.834 -12 -63.501 -12c-21.333 0 -41.5 4 -60.5 12s-35.667 18.833 -50 32.5s-25.833 29.834 -34.5 48.501s-13 38.667 -13 60c0 22 4.33301 42.667 13 62
s20.167 36.166 34.5 50.499s31 25.666 50 33.999s39.167 12.5 60.5 12.5c22.667 0 43.834 -4.16699 63.501 -12.5s37 -19.666 52 -33.999s26.833 -31.166 35.5 -50.499s13 -40 13 -62zM684 1295c0 -21.333 -4.16699 -41.333 -12.5 -60s-19.833 -34.834 -34.5 -48.501
s-31.834 -24.5 -51.501 -32.5s-40.834 -12 -63.501 -12c-22 0 -42.667 4 -62 12s-36.333 18.833 -51 32.5s-26.167 29.834 -34.5 48.501s-12.5 38.667 -12.5 60c0 22 4.16699 42.667 12.5 62s19.833 36.166 34.5 50.499s31.667 25.666 51 33.999s40 12.5 62 12.5
c22.667 0 43.834 -4.16699 63.501 -12.5s36.834 -19.666 51.501 -33.999s26.167 -31.166 34.5 -50.499s12.5 -40 12.5 -62z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="1570" 
d="M984 542c8 0 15.667 -1.99805 23 -5.99805s13.666 -9 18.999 -15l102 -105c-36 -50 -83.167 -87.833 -141.5 -113.5s-125.833 -38.5 -202.5 -38.5c-67.333 0 -128.166 12.167 -182.499 36.5s-100.5 57.666 -138.5 99.999s-67.333 91.833 -88 148.5s-31 117.667 -31 183
c0 68 12 130.5 36 187.5s56.833 106.333 98.5 148s90.5 74 146.5 97s116.333 34.5 181 34.5c76.667 0 142.667 -13.333 198 -40s100 -60 134 -100l-82 -109c-5.33301 -5.33301 -12 -11.5 -20 -18.5s-18.667 -10.5 -32 -10.5s-24.833 3 -34.5 9l-32.5 20
c-12 7.33301 -26.833 14 -44.5 20s-41.167 9 -70.5 9c-67.333 0 -118.166 -22.167 -152.499 -66.5s-51.5 -104.5 -51.5 -180.5c0 -40 4.5 -75.333 13.5 -106s21.5 -56.334 37.5 -77.001s35.167 -36.334 57.5 -47.001s46.833 -16 73.5 -16
c31.333 0 56.166 2.83301 74.499 8.5s33.5 11.834 45.5 18.501s22.333 13 31 19s19.667 9.33301 33 10zM40 729.002c0 68.667 8.83203 134.667 26.499 198s42.667 122.5 75 177.5s71.166 105.167 116.499 150.5s95.666 84.166 150.999 116.499s114.5 57.333 177.5 75
s128.833 26.5 197.5 26.5s134.834 -8.83301 198.501 -26.5s123 -42.667 178 -75s105.333 -71.166 151 -116.499s84.667 -95.5 117 -150.5s57.333 -114.167 75 -177.5s26.5 -129.333 26.5 -198c0 -68 -8.83301 -133.667 -26.5 -197s-42.667 -122.5 -75 -177.5
s-71.333 -105.167 -117 -150.5s-96 -84.166 -151 -116.499s-114.333 -57.333 -178 -75s-129.834 -26.5 -198.501 -26.5c-68 0 -133.667 8.83301 -197 26.5s-122.666 42.667 -177.999 75s-105.666 71.166 -150.999 116.499s-84.166 95.5 -116.499 150.5s-57.333 114 -75 177
s-26.5 128.833 -26.5 197.5zM205.999 729.002c0 -84.667 14.832 -163.667 44.499 -237s70.5 -137.166 122.5 -191.499s113.167 -97 183.5 -128s146.166 -46.5 227.499 -46.5c82 0 158.5 15.5 229.5 46.5s132.833 73.667 185.5 128s94 118.166 124 191.499s45 152.333 45 237
c0 56.667 -6.83301 111 -20.5 163s-33 100.5 -58 145.5s-55.167 86 -90.5 123s-74.666 68.5 -117.999 94.5s-90 46.167 -140 60.5s-102.333 21.5 -157 21.5c-81.333 0 -157.166 -15.667 -227.499 -47s-131.5 -74.5 -183.5 -129.5s-92.833 -119.5 -122.5 -193.5
s-44.5 -153.333 -44.5 -238z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="706" 
d="M646 842h-104.001c-20 0 -35.167 2.66699 -45.5 8s-19.833 16.666 -28.5 33.999l-12 27c-15.333 -13.333 -30 -24.666 -44 -33.999s-28.5 -17.333 -43.5 -24s-31 -11.5 -48 -14.5s-35.833 -4.5 -56.5 -4.5c-60.667 0 -108.167 15.333 -142.5 46s-51.5 74 -51.5 130
c0 21.333 5.16699 43.5 15.5 66.5s28.833 44.333 55.5 64s63.334 36.167 110.001 49.5s106.334 21 179.001 23v16c0 30.667 -6.5 51.5 -19.5 62.5s-30.5 16.5 -52.5 16.5c-20 0 -36.333 -2 -49 -6s-24.167 -8.33301 -34.5 -13s-20.833 -9 -31.5 -13s-24.334 -6 -41.001 -6
c-15.333 0 -28 4.33301 -38 13s-18.667 17.667 -26 27l-38 69c39.333 35.333 83 60.833 131 76.5s99.667 23.5 155 23.5c39.333 0 75 -6.33301 107 -19s59.333 -30.334 82 -53.001s40.167 -49.167 52.5 -79.5s18.5 -63.166 18.5 -98.499v-387zM335.999 980
c18 0 34.167 2.83301 48.5 8.5s29.5 16.167 45.5 31.5v63c-31.333 -1.33301 -56.666 -3.83301 -75.999 -7.5s-34.333 -8.33398 -45 -14.001s-17.834 -12.167 -21.501 -19.5s-5.5 -14.666 -5.5 -21.999c0 -15.333 4 -25.833 12 -31.5s22 -8.5 42 -8.5z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="1012" 
d="M110 525v39l262 402l102 -46c16 -7.33301 27.833 -16.333 35.5 -27s11.5 -22.667 11.5 -36c0 -16 -5 -33 -15 -51l-120 -215c-11.333 -20 -23.333 -35.667 -36 -47c12 -10.667 24 -26 36 -46l120 -216c10 -17.333 15 -34 15 -50c0 -13.333 -3.83301 -25.333 -11.5 -36
s-19.5 -19.667 -35.5 -27l-102 -46zM470 525v39l262 402l102 -46c16 -7.33301 27.833 -16.333 35.5 -27s11.5 -22.667 11.5 -36c0 -16 -5 -33 -15 -51l-120 -215c-11.333 -20 -23.333 -35.667 -36 -47c12 -10.667 24 -26 36 -46l120 -216c10 -17.333 15 -34 15 -50
c0 -13.333 -3.83301 -25.333 -11.5 -36s-19.5 -19.667 -35.5 -27l-102 -46z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" 
d="M124 783h910v-501h-262v279h-648v222z" />
    <glyph glyph-name="uni00AD" unicode="&#xad;" horiz-adv-x="740" 
d="M100 733h540v-250h-540v250z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="1570" 
d="M40 729c0 68.667 8.83203 134.667 26.499 198s42.667 122.5 75 177.5s71.166 105.167 116.499 150.5s95.666 84.166 150.999 116.499s114.5 57.333 177.5 75s128.833 26.5 197.5 26.5s134.834 -8.83301 198.501 -26.5s123 -42.667 178 -75s105.333 -71.166 151 -116.499
s84.667 -95.5 117 -150.5s57.333 -114.167 75 -177.5s26.5 -129.333 26.5 -198c0 -68 -8.83301 -133.667 -26.5 -197s-42.667 -122.5 -75 -177.5s-71.333 -105.167 -117 -150.5s-96 -84.166 -151 -116.499s-114.333 -57.333 -178 -75s-129.834 -26.5 -198.501 -26.5
c-68 0 -133.667 8.83301 -197 26.5s-122.666 42.667 -177.999 75s-105.666 71.166 -150.999 116.499s-84.166 95.5 -116.499 150.5s-57.333 114 -75 177s-26.5 128.833 -26.5 197.5zM205.999 729c0 -84.667 14.833 -163.667 44.5 -237s70.5 -137.166 122.5 -191.499
s113.167 -97 183.5 -128s146.166 -46.5 227.499 -46.5c76.667 0 148.5 13.667 215.5 41s126.5 65.333 178.5 114h-216c-36.667 0 -62 13 -76 39l-92 231c-7.33301 11.333 -15.166 19.5 -23.499 24.5s-20.5 7.5 -36.5 7.5h-32v-302h-268v901h352
c66.667 0 124.167 -6 172.5 -18s88 -29.333 119 -52s53.833 -50.667 68.5 -84s22 -71.333 22 -114c0 -28 -3.33301 -54.833 -10 -80.5s-16.834 -49.334 -30.501 -71.001s-31.334 -41.167 -53.001 -58.5s-47.5 -31.666 -77.5 -42.999c19.333 -10 35.666 -22.833 48.999 -38.5
s25.333 -34.5 36 -56.5l124 -257c50.667 54 90.334 116.667 119.001 188s43 148.666 43 231.999c0 56.667 -6.83301 111 -20.5 163s-33 100.5 -58 145.5s-55.167 86 -90.5 123s-74.666 68.5 -117.999 94.5s-90 46.167 -140 60.5s-102.333 21.5 -157 21.5
c-81.333 0 -157.166 -15.667 -227.499 -47s-131.5 -74.5 -183.5 -129.5s-92.833 -119.5 -122.5 -193.5s-44.5 -153.333 -44.5 -238zM701.999 767l64.0029 0.000976562c32 0 57.167 2.5 75.5 7.5s32.166 12.167 41.499 21.5s15.166 21.166 17.499 35.499s3.5 30.5 3.5 48.5
c0 17.333 -1.16699 32.5 -3.5 45.5s-7.5 24 -15.5 33s-19.833 15.833 -35.5 20.5s-36.834 7 -63.501 7h-84v-219z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="660" 
d="M20 1391h620v-190h-620v190z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="810" 
d="M40 1119c0 49.333 9.33301 95.666 28 138.999s44.334 81 77.001 113s71.167 57.333 115.5 76s92.166 28 143.499 28s99.333 -9.33301 144 -28s83.5 -44 116.5 -76s58.833 -69.667 77.5 -113s28 -89.666 28 -138.999c0 -48 -9.33301 -93.5 -28 -136.5
s-44.5 -80.5 -77.5 -112.5s-71.833 -57.5 -116.5 -76.5s-92.667 -28.5 -144 -28.5s-99.166 9.5 -143.499 28.5s-82.833 44.5 -115.5 76.5s-58.334 69.5 -77.001 112.5s-28 88.5 -28 136.5zM254 1117c0 -22 3.66699 -42.5 11 -61.5s17.666 -35.667 30.999 -50
s29.166 -25.5 47.499 -33.5s38.5 -12 60.5 -12s42.167 4 60.5 12s34.166 19.167 47.499 33.5s23.666 31 30.999 50s11 39.5 11 61.5c0 22.667 -3.66699 43.834 -11 63.501s-17.666 36.667 -30.999 51s-29.166 25.666 -47.499 33.999s-38.5 12.5 -60.5 12.5
s-42.167 -4.16699 -60.5 -12.5s-34.166 -19.666 -47.499 -33.999s-23.666 -31.333 -30.999 -51s-11 -40.834 -11 -63.501z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" 
d="M704 1272v-336h370v-223h-370v-319h-252v319h-368v223h368v336h252zM84 302h990v-222h-990v222z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="668" 
d="M352 1658c42.667 0 80.165 -5.99805 112.498 -17.998s59.333 -28.333 81 -49s37.834 -44.5 48.501 -71.5s16 -55.5 16 -85.5c0 -27.333 -4.16699 -52.333 -12.5 -75s-19.333 -44.334 -33 -65.001s-29.5 -40.667 -47.5 -60l-55 -59l-101 -103c22 6.66699 43.667 12 65 16
s40.666 6 57.999 6h68c23.333 0 41.5 -6.16699 54.5 -18.5s19.5 -28.833 19.5 -49.5v-126h-570v69c0 13.333 2.66699 27.666 8 42.999s14.666 29.333 27.999 42l198 194c13.333 13.333 25.833 27.833 37.5 43.5s21.834 31.334 30.501 47.001s15.5 31 20.5 46
s7.5 28.833 7.5 41.5c0 16 -3.33301 30.167 -10 42.5s-18.667 18.5 -36 18.5c-14 0 -25.167 -3.33301 -33.5 -10s-16.5 -17.334 -24.5 -32.001c-9.33301 -13.333 -19 -23.833 -29 -31.5s-24.333 -11.5 -43 -11.5c-9.33301 0 -19.333 1 -30 3l-124 19
c6 40.667 17.333 75.667 34 105s37.834 53.5 63.501 72.5s55.334 33.167 89.001 42.5s70.167 14 109.5 14z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="668" 
d="M364 1658c41.333 0 77.835 -5.83691 109.502 -17.5039s58 -27 79 -46s36.833 -40.167 47.5 -63.5s16 -46.666 16 -69.999c0 -41.333 -7.83301 -76.5 -23.5 -105.5s-41.834 -52.167 -78.501 -69.5c38 -12.667 66.5 -30.667 85.5 -54s28.5 -54 28.5 -92
c0 -44 -8.16699 -81.667 -24.5 -113s-37.5 -57 -63.5 -77s-55.333 -34.667 -88 -44s-65.334 -14 -98.001 -14c-34.667 0 -66.5 2.83301 -95.5 8.5s-55.5 16.334 -79.5 32.001s-45.5 36.834 -64.5 63.501s-35.833 61 -50.5 103l96 38c16.667 6 32.667 9 48 9
c31.333 0 52.666 -10.667 63.999 -32c9.33301 -17.333 20.333 -29.333 33 -36s25 -10 37 -10c20 0 35.667 5.83301 47 17.5s17 27.167 17 46.5c0 16.667 -1.83301 30 -5.5 40s-10.334 17.667 -20.001 23s-22.667 8.83301 -39 10.5s-36.833 2.5 -61.5 2.5v140
c26.667 0 48.167 1.66699 64.5 5s29 8 38 14s14.833 13.667 17.5 23s4 20 4 32c0 17.333 -3.33301 31.833 -10 43.5s-20.667 17.5 -42 17.5c-16 0 -28.167 -3 -36.5 -9s-15.5 -15 -21.5 -27c-8.66699 -15.333 -17.5 -26.666 -26.5 -33.999s-21.833 -11 -38.5 -11
c-9.33301 0 -20.333 1 -33 3l-114 19c6 40.667 17.333 75.667 34 105s37.334 53.5 62.001 72.5s52.834 33.167 84.501 42.5s65.5 14 101.5 14z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="660" 
d="M726 1473l-259.998 -263c-15.333 -15.333 -30 -26.166 -44 -32.499s-32.667 -9.5 -56 -9.5h-186l136 245c11.333 19.333 24.666 34.166 39.999 44.499s38.666 15.5 69.999 15.5h300z" />
    <glyph glyph-name="mu" unicode="&#xb5;" 
d="M410 1037l-0.000976562 -665.999c0 -48.667 12 -86.334 36 -113.001s58 -40 102 -40c34 0 65.833 7 95.5 21s58.5 33.667 86.5 59v739h310v-1037h-192c-19.333 -0 -35.5 4.33301 -48.5 13s-22.167 21.667 -27.5 39l-19 61c-19.333 -17.333 -37.666 -31.833 -54.999 -43.5
s-34.833 -21 -52.5 -28s-36 -12 -55 -15s-39.833 -4.5 -62.5 -4.5c-32 0 -61.667 4.5 -89 13.5s-52.333 21.833 -75 38.5c10 -30.667 16.833 -63.167 20.5 -97.5s5.5 -66.5 5.5 -96.5v-209h-154c-43.333 0 -76.833 10.667 -100.5 32s-35.5 52 -35.5 92v1242h310z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="1458" 
d="M1432 1457v-251.001h-210v-1422h-270v1422h-196v-1422h-270v822c-69.333 0 -132.5 11.333 -189.5 34s-105.667 53.167 -146 91.5s-71.5 83 -93.5 134s-33 105.167 -33 162.5c0 62.667 11 120.334 33 173.001s53.167 98 93.5 136s89 67.5 146 88.5
s120.167 31.5 189.5 31.5h946z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="570" 
d="M60 596c0 30 5.83301 58.333 17.5 85s27.667 49.834 48 69.501s44 35.167 71 46.5s56.167 17 87.5 17s60.833 -5.66699 88.5 -17s51.667 -26.833 72 -46.5s36.333 -42.834 48 -69.501s17.5 -55 17.5 -85s-5.83301 -58.167 -17.5 -84.5s-27.667 -49.333 -48 -69
s-44.333 -35.167 -72 -46.5s-57.167 -17 -88.5 -17s-60.5 5.66699 -87.5 17s-50.667 26.833 -71 46.5s-36.333 42.667 -48 69s-17.5 54.5 -17.5 84.5z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="660" 
d="M194 -210c10 0 19.668 -1.33203 29.001 -3.99902s21 -4 35 -4c17.333 0 29.666 3.16699 36.999 9.5s11 13.166 11 20.499c0 14 -10.167 24.5 -30.5 31.5s-54.833 13.833 -103.5 20.5l46 153h192l-17 -60c30 -8 55 -17.333 75 -28s35.833 -22.334 47.5 -35.001
s20 -26.5 25 -41.5s7.5 -30.5 7.5 -46.5c0 -23.333 -6.5 -44.333 -19.5 -63s-31.167 -34.667 -54.5 -48s-51.5 -23.666 -84.5 -30.999s-69.5 -11 -109.5 -11c-27.333 0 -52.5 1.33301 -75.5 4s-47.167 6.66699 -72.5 12l28 94c4 18 15.333 27 34 27z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="668" 
d="M150 1036h130.001v298l6 54l-50 -37c-13.333 -9.33301 -27.333 -14 -42 -14c-12 0 -22.833 2.5 -32.5 7.5s-16.834 10.5 -21.501 16.5l-68 90l242 199h186v-614h102v-136h-452v136z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="788" 
d="M396 1477c50 0 96 -7.33301 138 -22s78 -35.834 108 -63.501s53.5 -61.334 70.5 -101.001s25.5 -84.5 25.5 -134.5c0 -50.667 -8.5 -96.167 -25.5 -136.5s-40.5 -74.5 -70.5 -102.5s-66 -49.5 -108 -64.5s-88 -22.5 -138 -22.5c-50.667 0 -97.167 7.5 -139.5 22.5
s-78.833 36.5 -109.5 64.5s-54.5 62.167 -71.5 102.5s-25.5 85.833 -25.5 136.5c0 50 8.5 94.833 25.5 134.5s40.833 73.334 71.5 101.001s67.167 48.834 109.5 63.501s88.833 22 139.5 22zM396 1004c34 0 59.167 11.5 75.5 34.5s24.5 61.5 24.5 115.5
s-8.16699 92.333 -24.5 115s-41.5 34 -75.5 34c-37.333 0 -64 -11.333 -80 -34s-24 -61 -24 -115s8 -92.5 24 -115.5s42.667 -34.5 80 -34.5z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="1012" 
d="M280 123l-102 46c-16 7.33301 -27.833 16.333 -35.5 27s-11.5 22.667 -11.5 36c0 16 5 32.667 15 50l120 216c10 18.667 22 34 36 46c-6.66699 6 -13 13 -19 21s-11.667 16.667 -17 26l-120 215c-10 18 -15 35 -15 51c0 13.333 3.83301 25.333 11.5 36
s19.5 19.667 35.5 27l102 46l262 -402v-39zM902 564v-39l-262 -402l-102 46c-16 7.33301 -27.833 16.333 -35.5 27s-11.5 22.667 -11.5 36c0 16 5 32.667 15 50l120 216c10 18.667 22 34 36 46c-6.66699 6 -13 13 -19 21s-11.667 16.667 -17 26l-120 215
c-10 18 -15 35 -15 51c0 13.333 3.83301 25.333 11.5 36s19.5 19.667 35.5 27l102 46z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="1424" 
d="M1342 295h80v-107c0 -10 -3.66699 -19 -11 -27s-17 -12 -29 -12h-40v-149h-184v149h-274c-19.333 0 -34.5 4.16699 -45.5 12.5s-17.833 18.833 -20.5 31.5l-18 92l328 465h214v-455zM130 844h130.001v298l6 54l-50 -37c-13.333 -9.33301 -27.333 -14 -42 -14
c-12 0 -22.833 2.5 -32.5 7.5s-16.834 10.5 -21.501 16.5l-68 90l242 199h186v-614h102v-136h-452v136zM1158 404c0 17.333 0.5 36.333 1.5 57s2.83301 42 5.5 64l-166 -230h159v109zM476.001 85c-10.667 -16.667 -21.5 -30.5 -32.5 -41.5s-22.667 -19.667 -35 -26
s-25.5 -10.833 -39.5 -13.5s-29 -4 -45 -4h-132l848 1359c20 31.333 42.667 55.5 68 72.5s56 25.5 92 25.5h132z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="1424" 
d="M1128 758c42.667 0 80.165 -5.99805 112.498 -17.998s59.333 -28.333 81 -49s37.834 -44.5 48.501 -71.5s16 -55.5 16 -85.5c0 -27.333 -4.16699 -52.333 -12.5 -75s-19.333 -44.334 -33 -65.001s-29.5 -40.667 -47.5 -60l-55 -59l-101 -103c22 6.66699 43.667 12 65 16
s40.666 6 57.999 6h68c23.333 0 41.5 -6.16699 54.5 -18.5s19.5 -28.833 19.5 -49.5v-126h-570v69c0 13.333 2.66699 27.666 8 42.999s14.666 29.333 27.999 42l198 194c13.333 13.333 25.833 27.833 37.5 43.5s21.834 31.334 30.501 47.001s15.5 31 20.5 46
s7.5 28.833 7.5 41.5c0 16 -3.33301 30.167 -10 42.5s-18.667 18.5 -36 18.5c-14 0 -25.167 -3.33301 -33.5 -10s-16.5 -17.334 -24.5 -32.001c-9.33301 -13.333 -19 -23.833 -29 -31.5s-24.333 -11.5 -43 -11.5c-9.33301 0 -19.333 1 -30 3l-124 19
c6 40.667 17.333 75.667 34 105s37.834 53.5 63.501 72.5s55.334 33.167 89.001 42.5s70.167 14 109.5 14zM129.998 844.002h130.001v298l6 54l-50 -37c-13.333 -9.33301 -27.333 -14 -42 -14c-12 0 -22.833 2.5 -32.5 7.5s-16.834 10.5 -21.501 16.5l-68 90l242 199h186
v-614h102v-136h-452v136zM437.999 85.002c-10.667 -16.667 -21.5 -30.5 -32.5 -41.5s-22.667 -19.667 -35 -26s-25.5 -10.833 -39.5 -13.5s-29 -4 -45 -4h-132l848 1359c20 31.333 42.667 55.5 68 72.5s56 25.5 92 25.5h132z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="1424" 
d="M1342 295h80v-107c0 -10 -3.66699 -19 -11 -27s-17 -12 -29 -12h-40v-149h-184v149h-274c-19.333 0 -34.5 4.16699 -45.5 12.5s-17.833 18.833 -20.5 31.5l-18 92l328 465h214v-455zM344 1466c41.333 0 77.835 -5.83691 109.502 -17.5039s58 -27 79 -46
s36.833 -40.167 47.5 -63.5s16 -46.666 16 -69.999c0 -41.333 -7.83301 -76.5 -23.5 -105.5s-41.834 -52.167 -78.501 -69.5c38 -12.667 66.5 -30.667 85.5 -54s28.5 -54 28.5 -92c0 -44 -8.16699 -81.667 -24.5 -113s-37.5 -57 -63.5 -77s-55.333 -34.667 -88 -44
s-65.334 -14 -98.001 -14c-34.667 0 -66.5 2.83301 -95.5 8.5s-55.5 16.334 -79.5 32.001s-45.5 36.834 -64.5 63.501s-35.833 61 -50.5 103l96 38c16.667 6 32.667 9 48 9c31.333 0 52.666 -10.667 63.999 -32c9.33301 -17.333 20.333 -29.333 33 -36s25 -10 37 -10
c20 0 35.667 5.83301 47 17.5s17 27.167 17 46.5c0 16.667 -1.83301 30 -5.5 40s-10.334 17.667 -20.001 23s-22.667 8.83301 -39 10.5s-36.833 2.5 -61.5 2.5v140c26.667 0 48.167 1.66699 64.5 5s29 8 38 14s14.833 13.667 17.5 23s4 20 4 32
c0 17.333 -3.33301 31.833 -10 43.5s-20.667 17.5 -42 17.5c-16 0 -28.167 -3 -36.5 -9s-15.5 -15 -21.5 -27c-8.66699 -15.333 -17.5 -26.666 -26.5 -33.999s-21.833 -11 -38.5 -11c-9.33301 0 -20.333 1 -33 3l-114 19c6 40.667 17.333 75.667 34 105
s37.334 53.5 62.001 72.5s52.834 33.167 84.501 42.5s65.5 14 101.5 14zM1158 403.996c0 17.333 0.5 36.333 1.5 57s2.83301 42 5.5 64l-166 -230h159v109zM476.002 84.9961c-10.667 -16.667 -21.5 -30.5 -32.5 -41.5s-22.667 -19.667 -35 -26s-25.5 -10.833 -39.5 -13.5
s-29 -4 -45 -4h-132l848 1359c20 31.333 42.667 55.5 68 72.5s56 25.5 92 25.5h132z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="882" 
d="M864 -196c-25.333 -20.667 -52.3369 -39.998 -81.0039 -57.998s-59.667 -33.833 -93 -47.5s-69.166 -24.334 -107.499 -32.001s-79.833 -11.5 -124.5 -11.5c-62.667 0 -119.167 8 -169.5 24s-93.166 38.833 -128.499 68.5s-62.5 65.834 -81.5 108.501
s-28.5 90.667 -28.5 144c0 50.667 7 93.667 21 129s31.833 65.166 53.5 89.499s45.334 45 71.001 62l73 47c23 14.333 43 29 60 44s27.5 33.167 31.5 54.5l28 139h212l22 -160c0.666992 -6 1.16699 -11.167 1.5 -15.5s0.5 -8.5 0.5 -12.5c0 -30.667 -7 -56.334 -21 -77.001
s-31.667 -38.667 -53 -54s-44.333 -29.5 -69 -42.5s-47.667 -27.333 -69 -43s-39 -34 -53 -55s-21 -47.167 -21 -78.5c0 -40 13.167 -71.833 39.5 -95.5s62.5 -35.5 108.5 -35.5c34.667 0 64 3.66699 88 11s44.5 15.5 61.5 24.5s31.833 17.167 44.5 24.5
s25.334 11 38.001 11c28.667 0 50 -12.333 64 -37zM323.996 883.002c0 24 4.5 46.333 13.5 67s21.333 38.667 37 54s34.167 27.5 55.5 36.5s44.666 13.5 69.999 13.5c24.667 0 47.667 -4.5 69 -13.5s40 -21.167 56 -36.5s28.5 -33.333 37.5 -54s13.5 -43 13.5 -67
s-4.5 -46.5 -13.5 -67.5s-21.5 -39.167 -37.5 -54.5s-34.667 -27.333 -56 -36s-44.333 -13 -69 -13c-25.333 0 -48.666 4.33301 -69.999 13s-39.833 20.667 -55.5 36s-28 33.5 -37 54.5s-13.5 43.5 -13.5 67.5z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="1474" 
d="M1476 0h-262c-29.333 -0 -53.5 6.83301 -72.5 20.5s-32.167 31.167 -39.5 52.5l-86 254h-554l-86 -254c-6.66699 -18.667 -19.667 -35.5 -39 -50.5s-43 -22.5 -71 -22.5h-264l564 1457h346zM540 557l398 0.000976562l-134 399l-30.5 89.5
c-11.667 34.333 -23.167 71.5 -34.5 111.5c-10.667 -40.667 -21.667 -78.334 -33 -113.001s-22 -64.667 -32 -90zM612 1798c15.333 0 28.834 -0.833008 40.501 -2.5s22 -4.33398 31 -8.00098s17.5 -8.66699 25.5 -15s16.333 -14.166 25 -23.499l182 -198h-246
c-12 0 -22.333 0.333008 -31 1s-16.834 2.16699 -24.501 4.5s-15 5.33301 -22 9s-14.5 8.5 -22.5 14.5l-300 218h342z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="1474" 
d="M1476 0h-262c-29.333 -0 -53.5 6.83301 -72.5 20.5s-32.167 31.167 -39.5 52.5l-86 254h-554l-86 -254c-6.66699 -18.667 -19.667 -35.5 -39 -50.5s-43 -22.5 -71 -22.5h-264l564 1457h346zM540 557l398 0.000976562l-134 399l-30.5 89.5
c-11.667 34.333 -23.167 71.5 -34.5 111.5c-10.667 -40.667 -21.667 -78.334 -33 -113.001s-22 -64.667 -32 -90zM1162 1798l-294.997 -214.001c-17.333 -13.333 -33.5 -22.166 -48.5 -26.499s-33.833 -6.5 -56.5 -6.5h-246l181 197c8.66699 10 17.167 18.167 25.5 24.5
s17.166 11.5 26.499 15.5s19.666 6.66699 30.999 8s24.666 2 39.999 2h342z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="1474" 
d="M1476 0h-262c-29.333 -0 -53.5 6.83301 -72.5 20.5s-32.167 31.167 -39.5 52.5l-86 254h-554l-86 -254c-6.66699 -18.667 -19.667 -35.5 -39 -50.5s-43 -22.5 -71 -22.5h-264l564 1457h346zM540 557l398 0.000976562l-134 399l-30.5 89.5
c-11.667 34.333 -23.167 71.5 -34.5 111.5c-10.667 -40.667 -21.667 -78.334 -33 -113.001s-22 -64.667 -32 -90zM1138 1551h-234c-12 0 -25 1.66699 -39 5s-25.667 7.66602 -35 12.999l-74 45c-3.33301 1.33301 -6.33301 3 -9 5s-5.66699 4 -9 6
c-3.33301 -2 -6.33301 -4 -9 -6s-5.66699 -3.66699 -9 -5l-74 -45c-9.33301 -5.33301 -21 -9.66602 -35 -12.999s-27 -5 -39 -5h-234l244 227h312z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="1474" 
d="M1476 0h-262c-29.333 -0 -53.5 6.83301 -72.5 20.5s-32.167 31.167 -39.5 52.5l-86 254h-554l-86 -254c-6.66699 -18.667 -19.667 -35.5 -39 -50.5s-43 -22.5 -71 -22.5h-264l564 1457h346zM540 557l398 0.000976562l-134 399l-30.5 89.5
c-11.667 34.333 -23.167 71.5 -34.5 111.5c-10.667 -40.667 -21.667 -78.334 -33 -113.001s-22 -64.667 -32 -90zM862 1729c14 0 25.833 3.66895 35.5 11.002s14.5 20.666 14.5 39.999h168c0 -36.667 -4.83301 -70 -14.5 -100s-23.5 -55.833 -41.5 -77.5
s-40 -38.334 -66 -50.001s-55.333 -17.5 -88 -17.5c-24.667 0 -48.834 3 -72.501 9s-46 12.833 -67 20.5l-57.5 20.5c-17.333 6 -32.333 9 -45 9c-14 0 -25.5 -4.16699 -34.5 -12.5s-13.5 -21.833 -13.5 -40.5h-170c0 36.667 5 70 15 100s24 55.833 42 77.5s40 38.5 66 50.5
s55 18 87 18c24.667 0 48.834 -3 72.501 -9s46 -12.667 67 -20l57.5 -20c17.333 -6 32.333 -9 45 -9z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="1474" 
d="M1476 0h-262c-29.333 -0 -53.5 6.83301 -72.5 20.5s-32.167 31.167 -39.5 52.5l-86 254h-554l-86 -254c-6.66699 -18.667 -19.667 -35.5 -39 -50.5s-43 -22.5 -71 -22.5h-264l564 1457h346zM540 557l398 0.000976562l-134 399l-30.5 89.5
c-11.667 34.333 -23.167 71.5 -34.5 111.5c-10.667 -40.667 -21.667 -78.334 -33 -113.001s-22 -64.667 -32 -90zM674 1668c0 -21.333 -4.16699 -41 -12.5 -59s-19.833 -33.833 -34.5 -47.5s-31.667 -24.334 -51 -32.001s-40 -11.5 -62 -11.5
c-20 0 -39.167 3.83301 -57.5 11.5s-34.5 18.334 -48.5 32.001s-25.167 29.5 -33.5 47.5s-12.5 37.667 -12.5 59c0 20.667 4.16699 40.334 12.5 59.001s19.5 35 33.5 49s30.167 25 48.5 33s37.5 12 57.5 12c22 0 42.667 -4 62 -12s36.333 -19 51 -33
s26.167 -30.333 34.5 -49s12.5 -38.334 12.5 -59.001zM1114 1668c0 -21.333 -4.16699 -41 -12.5 -59s-19.5 -33.833 -33.5 -47.5s-30.5 -24.334 -49.5 -32.001s-39.167 -11.5 -60.5 -11.5s-41.5 3.83301 -60.5 11.5s-35.5 18.334 -49.5 32.001s-25.167 29.5 -33.5 47.5
s-12.5 37.667 -12.5 59c0 20.667 4.16699 40.334 12.5 59.001s19.5 35 33.5 49s30.5 25 49.5 33s39.167 12 60.5 12s41.5 -4 60.5 -12s35.5 -19 49.5 -33s25.167 -30.333 33.5 -49s12.5 -38.334 12.5 -59.001z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="1474" 
d="M1476 0h-262c-29.333 -0 -53.5 6.83301 -72.5 20.5s-32.167 31.167 -39.5 52.5l-86 254h-554l-86 -254c-6.66699 -18.667 -19.667 -35.5 -39 -50.5s-43 -22.5 -71 -22.5h-264l564 1457h346zM540 557l398 0.000976562l-134 399l-30.5 89.5
c-11.667 34.333 -23.167 71.5 -34.5 111.5c-10.667 -40.667 -21.667 -78.334 -33 -113.001s-22 -64.667 -32 -90zM518 1701c0 30.667 6.16699 58.5 18.5 83.5s28.666 46.333 48.999 64s43.833 31.334 70.5 41.001s54.667 14.5 84 14.5c30.667 0 59.667 -4.83301 87 -14.5
s51.5 -23.334 72.5 -41.001s37.667 -39 50 -64s18.5 -52.833 18.5 -83.5c0 -29.333 -6.16699 -56.166 -18.5 -80.499s-29 -45.166 -50 -62.499s-45.167 -30.666 -72.5 -39.999s-56.333 -14 -87 -14c-29.333 0 -57.333 4.66699 -84 14s-50.167 22.666 -70.5 39.999
s-36.666 38.166 -48.999 62.499s-18.5 51.166 -18.5 80.499zM664 1701c0 -23.333 6.5 -42.5 19.5 -57.5s33.167 -22.5 60.5 -22.5c24 0 43 7.5 57 22.5s21 34.167 21 57.5c0 25.333 -7 45.333 -21 60s-33 22 -57 22c-27.333 0 -47.5 -7.33301 -60.5 -22
s-19.5 -34.667 -19.5 -60z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="1876" 
d="M684 1457h1120v-260h-645l41 -340h464v-250h-434l42 -347h532v-260h-820l-40 327h-483l-111 -254c-9.33301 -22 -25.166 -39.667 -47.499 -53s-48.5 -20 -78.5 -20h-256zM561.999 557h353.998l-78 652c-13.333 -39.333 -26.666 -76 -39.999 -110
s-26.666 -65.333 -39.999 -94z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="1314" 
d="M622 -210c10 0 19.666 -1.33496 28.999 -4.00195s21 -4 35 -4c17.333 0 29.666 3.16699 36.999 9.5s11 13.166 11 20.499c0 14 -10.167 24.5 -30.5 31.5s-54.833 13.833 -103.5 20.5l38 127c-94.667 12 -179.167 38.833 -253.5 80.5s-137.333 94.834 -189 159.501
s-91.167 139.334 -118.5 224.001s-41 176 -41 274c0 108.667 18.667 208.5 56 299.5s89.5 169.5 156.5 235.5s147.167 117.333 240.5 154s196.333 55 309 55c54.667 0 106.167 -4.83301 154.5 -14.5s93.5 -23 135.5 -40s80.667 -37.333 116 -61
s66.666 -49.834 93.999 -78.501l-114 -153c-7.33301 -9.33301 -16 -17.833 -26 -25.5s-24 -11.5 -42 -11.5c-12 0 -23.333 2.66699 -34 8s-22 11.833 -34 19.5l-39.5 25c-14.333 9 -31.333 17.333 -51 25s-42.667 14.167 -69 19.5s-57.166 8 -92.499 8
c-61.333 0 -117.333 -10.833 -168 -32.5s-94.167 -52.667 -130.5 -93s-64.666 -89.166 -84.999 -146.499s-30.5 -121.666 -30.5 -192.999c0 -74.667 10.167 -141 30.5 -199s48 -106.833 83 -146.5s75.833 -69.834 122.5 -90.501s96.667 -31 150 -31
c30.667 0 58.667 1.5 84 4.5s48.833 8.16699 70.5 15.5s42.334 16.833 62.001 28.5s39.5 26.167 59.5 43.5c8 6.66699 16.667 12.167 26 16.5s19 6.5 29 6.5c8 0 16 -1.5 24 -4.5s15.667 -8.16699 23 -15.5l134 -141c-51.333 -67.333 -114.5 -120.166 -189.5 -158.499
s-162.167 -61.5 -261.5 -69.5l-8 -31c30 -8 55 -17.333 75 -28s35.833 -22.334 47.5 -35.001s20 -26.5 25 -41.5s7.5 -30.5 7.5 -46.5c0 -23.333 -6.5 -44.333 -19.5 -63s-31.167 -34.667 -54.5 -48s-51.5 -23.666 -84.5 -30.999s-69.5 -11 -109.5 -11
c-27.333 0 -52.5 1.33301 -75.5 4s-47.167 6.66699 -72.5 12l28 94c4 18 15.333 27 34 27z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="1130" 
d="M1058 1457v-260h-598v-340h458v-250h-458v-347h598v-260h-938v1457h938zM480 1798c15.333 0 28.834 -0.833008 40.501 -2.5s22 -4.33398 31 -8.00098s17.5 -8.66699 25.5 -15s16.333 -14.166 25 -23.499l182 -198h-246c-12 0 -22.333 0.333008 -31 1
s-16.834 2.16699 -24.501 4.5s-15 5.33301 -22 9s-14.5 8.5 -22.5 14.5l-300 218h342z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="1130" 
d="M1058 1457v-260h-598v-340h458v-250h-458v-347h598v-260h-938v1457h938zM1030 1798l-294.997 -214.001c-17.333 -13.333 -33.5 -22.166 -48.5 -26.499s-33.833 -6.5 -56.5 -6.5h-246l181 197c8.66699 10 17.167 18.167 25.5 24.5s17.166 11.5 26.499 15.5
s19.666 6.66699 30.999 8s24.666 2 39.999 2h342z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="1130" 
d="M1058 1457v-260h-598v-340h458v-250h-458v-347h598v-260h-938v1457h938zM1006 1551h-234c-12 0 -25 1.66699 -39 5s-25.667 7.66602 -35 12.999l-74 45c-3.33301 1.33301 -6.33301 3 -9 5s-5.66699 4 -9 6c-3.33301 -2 -6.33301 -4 -9 -6s-5.66699 -3.66699 -9 -5
l-74 -45c-9.33301 -5.33301 -21 -9.66602 -35 -12.999s-27 -5 -39 -5h-234l244 227h312z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="1130" 
d="M1058 1457v-260h-598v-340h458v-250h-458v-347h598v-260h-938v1457h938zM542 1668c0 -21.333 -4.16699 -41 -12.5 -59s-19.833 -33.833 -34.5 -47.5s-31.667 -24.334 -51 -32.001s-40 -11.5 -62 -11.5c-20 0 -39.167 3.83301 -57.5 11.5s-34.5 18.334 -48.5 32.001
s-25.167 29.5 -33.5 47.5s-12.5 37.667 -12.5 59c0 20.667 4.16699 40.334 12.5 59.001s19.5 35 33.5 49s30.167 25 48.5 33s37.5 12 57.5 12c22 0 42.667 -4 62 -12s36.333 -19 51 -33s26.167 -30.333 34.5 -49s12.5 -38.334 12.5 -59.001zM982 1668
c0 -21.333 -4.16699 -41 -12.5 -59s-19.5 -33.833 -33.5 -47.5s-30.5 -24.334 -49.5 -32.001s-39.167 -11.5 -60.5 -11.5s-41.5 3.83301 -60.5 11.5s-35.5 18.334 -49.5 32.001s-25.167 29.5 -33.5 47.5s-12.5 37.667 -12.5 59c0 20.667 4.16699 40.334 12.5 59.001
s19.5 35 33.5 49s30.5 25 49.5 33s39.167 12 60.5 12s41.5 -4 60.5 -12s35.5 -19 49.5 -33s25.167 -30.333 33.5 -49s12.5 -38.334 12.5 -59.001z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="640" 
d="M490 0h-340v1457h340v-1457zM199 1798c15.333 0 28.834 -0.833008 40.501 -2.5s22 -4.33398 31 -8.00098s17.5 -8.66699 25.5 -15s16.333 -14.166 25 -23.499l182 -198h-246c-12 0 -22.333 0.333008 -31 1s-16.834 2.16699 -24.501 4.5s-15 5.33301 -22 9
s-14.5 8.5 -22.5 14.5l-300 218h342z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="640" 
d="M490 0h-340v1457h340v-1457zM749 1798l-294.997 -214.001c-17.333 -13.333 -33.5 -22.166 -48.5 -26.499s-33.833 -6.5 -56.5 -6.5h-246l181 197c8.66699 10 17.167 18.167 25.5 24.5s17.166 11.5 26.499 15.5s19.666 6.66699 30.999 8s24.666 2 39.999 2h342z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="640" 
d="M490 0h-340v1457h340v-1457zM725 1551h-234c-12 0 -25 1.66699 -39 5s-25.667 7.66602 -35 12.999l-74 45c-3.33301 1.33301 -6.33301 3 -9 5s-5.66699 4 -9 6c-3.33301 -2 -6.33301 -4 -9 -6s-5.66699 -3.66699 -9 -5l-74 -45
c-9.33301 -5.33301 -21 -9.66602 -35 -12.999s-27 -5 -39 -5h-234l244 227h312z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="640" 
d="M490 0h-340v1457h340v-1457zM261 1668c0 -21.333 -4.16699 -41 -12.5 -59s-19.833 -33.833 -34.5 -47.5s-31.667 -24.334 -51 -32.001s-40 -11.5 -62 -11.5c-20 0 -39.167 3.83301 -57.5 11.5s-34.5 18.334 -48.5 32.001s-25.167 29.5 -33.5 47.5s-12.5 37.667 -12.5 59
c0 20.667 4.16699 40.334 12.5 59.001s19.5 35 33.5 49s30.167 25 48.5 33s37.5 12 57.5 12c22 0 42.667 -4 62 -12s36.333 -19 51 -33s26.167 -30.333 34.5 -49s12.5 -38.334 12.5 -59.001zM701 1668c0 -21.333 -4.16699 -41 -12.5 -59s-19.5 -33.833 -33.5 -47.5
s-30.5 -24.334 -49.5 -32.001s-39.167 -11.5 -60.5 -11.5s-41.5 3.83301 -60.5 11.5s-35.5 18.334 -49.5 32.001s-25.167 29.5 -33.5 47.5s-12.5 37.667 -12.5 59c0 20.667 4.16699 40.334 12.5 59.001s19.5 35 33.5 49s30.5 25 49.5 33s39.167 12 60.5 12s41.5 -4 60.5 -12
s35.5 -19 49.5 -33s25.167 -30.333 33.5 -49s12.5 -38.334 12.5 -59.001z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="1584" 
d="M54 823l166 0.000976562v634h566c113.333 0 216.5 -18.333 309.5 -55s172.5 -87.334 238.5 -152.001s117.167 -141.5 153.5 -230.5s54.5 -185.833 54.5 -290.5c0 -105.333 -18.167 -202.5 -54.5 -291.5s-87.5 -166 -153.5 -231s-145.5 -115.667 -238.5 -152
s-196.167 -54.5 -309.5 -54.5h-566v639h-166v184zM1196 729.001c0 72 -9.33301 137 -28 195s-45.5 107.167 -80.5 147.5s-77.833 71.333 -128.5 93s-108.334 32.5 -173.001 32.5h-226v-374h344v-184h-344v-379h226c64.667 0 122.334 10.833 173.001 32.5
s93.5 52.667 128.5 93s61.833 89.5 80.5 147.5s28 123.333 28 196z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="1512" 
d="M298 1457c14.667 0 27.001 -0.666992 37.001 -2s19 -3.83301 27 -7.5s15.667 -8.83398 23 -15.501s15.666 -15.667 24.999 -27l692 -875c-2.66699 28 -4.66699 55.167 -6 81.5s-2 51.166 -2 74.499v771h298v-1457h-176c-26 0 -48 4 -66 12s-35.333 22.667 -52 44
l-687 868c2 -25.333 3.66699 -50.166 5 -74.499s2 -47.166 2 -68.499v-781h-298v1457h178v0zM888.001 1729c14 0 25.833 3.66895 35.5 11.002s14.5 20.666 14.5 39.999h168c0 -36.667 -4.83301 -70 -14.5 -100s-23.5 -55.833 -41.5 -77.5s-40 -38.334 -66 -50.001
s-55.333 -17.5 -88 -17.5c-24.667 0 -48.834 3 -72.501 9s-46 12.833 -67 20.5l-57.5 20.5c-17.333 6 -32.333 9 -45 9c-14 0 -25.5 -4.16699 -34.5 -12.5s-13.5 -21.833 -13.5 -40.5h-170c0 36.667 5 70 15 100s24 55.833 42 77.5s40 38.5 66 50.5s55 18 87 18
c24.667 0 48.834 -3 72.501 -9s46 -12.667 67 -20l57.5 -20c17.333 -6 32.333 -9 45 -9z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="1600" 
d="M1558 729c0 -105.333 -18.167 -203.332 -54.5 -293.999s-87.5 -169.5 -153.5 -236.5s-145.5 -119.5 -238.5 -157.5s-196.167 -57 -309.5 -57s-216.666 19 -309.999 57s-173.166 90.5 -239.499 157.5s-117.666 145.833 -153.999 236.5s-54.5 188.667 -54.5 294
s18.167 203.333 54.5 294s87.666 169.334 153.999 236.001s146.166 119 239.499 157s196.666 57 309.999 57s216.5 -19.167 309.5 -57.5s172.5 -90.833 238.5 -157.5s117.167 -145.334 153.5 -236.001s54.5 -188.334 54.5 -293.001zM1212 729.001
c0 72 -9.33301 136.833 -28 194.5s-45.5 106.5 -80.5 146.5s-77.833 70.667 -128.5 92s-108.334 32 -173.001 32c-65.333 0 -123.5 -10.667 -174.5 -32s-94 -52 -129 -92s-61.833 -88.833 -80.5 -146.5s-28 -122.5 -28 -194.5c0 -72.667 9.33301 -137.834 28 -195.501
s45.5 -106.5 80.5 -146.5s78 -70.5 129 -91.5s109.167 -31.5 174.5 -31.5c64.667 0 122.334 10.5 173.001 31.5s93.5 51.5 128.5 91.5s61.833 88.833 80.5 146.5s28 122.834 28 195.501zM678 1798c15.333 0 28.834 -0.833008 40.501 -2.5s22 -4.33398 31 -8.00098
s17.5 -8.66699 25.5 -15s16.333 -14.166 25 -23.499l182 -198h-246c-12 0 -22.333 0.333008 -31 1s-16.834 2.16699 -24.501 4.5s-15 5.33301 -22 9s-14.5 8.5 -22.5 14.5l-300 218h342z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="1600" 
d="M1558 729c0 -105.333 -18.167 -203.332 -54.5 -293.999s-87.5 -169.5 -153.5 -236.5s-145.5 -119.5 -238.5 -157.5s-196.167 -57 -309.5 -57s-216.666 19 -309.999 57s-173.166 90.5 -239.499 157.5s-117.666 145.833 -153.999 236.5s-54.5 188.667 -54.5 294
s18.167 203.333 54.5 294s87.666 169.334 153.999 236.001s146.166 119 239.499 157s196.666 57 309.999 57s216.5 -19.167 309.5 -57.5s172.5 -90.833 238.5 -157.5s117.167 -145.334 153.5 -236.001s54.5 -188.334 54.5 -293.001zM1212 729.001
c0 72 -9.33301 136.833 -28 194.5s-45.5 106.5 -80.5 146.5s-77.833 70.667 -128.5 92s-108.334 32 -173.001 32c-65.333 0 -123.5 -10.667 -174.5 -32s-94 -52 -129 -92s-61.833 -88.833 -80.5 -146.5s-28 -122.5 -28 -194.5c0 -72.667 9.33301 -137.834 28 -195.501
s45.5 -106.5 80.5 -146.5s78 -70.5 129 -91.5s109.167 -31.5 174.5 -31.5c64.667 0 122.334 10.5 173.001 31.5s93.5 51.5 128.5 91.5s61.833 88.833 80.5 146.5s28 122.834 28 195.501zM1228 1798l-294.997 -214.001c-17.333 -13.333 -33.5 -22.166 -48.5 -26.499
s-33.833 -6.5 -56.5 -6.5h-246l181 197c8.66699 10 17.167 18.167 25.5 24.5s17.166 11.5 26.499 15.5s19.666 6.66699 30.999 8s24.666 2 39.999 2h342z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="1600" 
d="M1558 729c0 -105.333 -18.167 -203.332 -54.5 -293.999s-87.5 -169.5 -153.5 -236.5s-145.5 -119.5 -238.5 -157.5s-196.167 -57 -309.5 -57s-216.666 19 -309.999 57s-173.166 90.5 -239.499 157.5s-117.666 145.833 -153.999 236.5s-54.5 188.667 -54.5 294
s18.167 203.333 54.5 294s87.666 169.334 153.999 236.001s146.166 119 239.499 157s196.666 57 309.999 57s216.5 -19.167 309.5 -57.5s172.5 -90.833 238.5 -157.5s117.167 -145.334 153.5 -236.001s54.5 -188.334 54.5 -293.001zM1212 729.001
c0 72 -9.33301 136.833 -28 194.5s-45.5 106.5 -80.5 146.5s-77.833 70.667 -128.5 92s-108.334 32 -173.001 32c-65.333 0 -123.5 -10.667 -174.5 -32s-94 -52 -129 -92s-61.833 -88.833 -80.5 -146.5s-28 -122.5 -28 -194.5c0 -72.667 9.33301 -137.834 28 -195.501
s45.5 -106.5 80.5 -146.5s78 -70.5 129 -91.5s109.167 -31.5 174.5 -31.5c64.667 0 122.334 10.5 173.001 31.5s93.5 51.5 128.5 91.5s61.833 88.833 80.5 146.5s28 122.834 28 195.501zM1204 1551h-234c-12 0 -25 1.66699 -39 5s-25.667 7.66602 -35 12.999l-74 45
c-3.33301 1.33301 -6.33301 3 -9 5s-5.66699 4 -9 6c-3.33301 -2 -6.33301 -4 -9 -6s-5.66699 -3.66699 -9 -5l-74 -45c-9.33301 -5.33301 -21 -9.66602 -35 -12.999s-27 -5 -39 -5h-234l244 227h312z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="1600" 
d="M1558 729c0 -105.333 -18.167 -203.332 -54.5 -293.999s-87.5 -169.5 -153.5 -236.5s-145.5 -119.5 -238.5 -157.5s-196.167 -57 -309.5 -57s-216.666 19 -309.999 57s-173.166 90.5 -239.499 157.5s-117.666 145.833 -153.999 236.5s-54.5 188.667 -54.5 294
s18.167 203.333 54.5 294s87.666 169.334 153.999 236.001s146.166 119 239.499 157s196.666 57 309.999 57s216.5 -19.167 309.5 -57.5s172.5 -90.833 238.5 -157.5s117.167 -145.334 153.5 -236.001s54.5 -188.334 54.5 -293.001zM1212 729.001
c0 72 -9.33301 136.833 -28 194.5s-45.5 106.5 -80.5 146.5s-77.833 70.667 -128.5 92s-108.334 32 -173.001 32c-65.333 0 -123.5 -10.667 -174.5 -32s-94 -52 -129 -92s-61.833 -88.833 -80.5 -146.5s-28 -122.5 -28 -194.5c0 -72.667 9.33301 -137.834 28 -195.501
s45.5 -106.5 80.5 -146.5s78 -70.5 129 -91.5s109.167 -31.5 174.5 -31.5c64.667 0 122.334 10.5 173.001 31.5s93.5 51.5 128.5 91.5s61.833 88.833 80.5 146.5s28 122.834 28 195.501zM928 1729c14 0 25.833 3.66895 35.5 11.002s14.5 20.666 14.5 39.999h168
c0 -36.667 -4.83301 -70 -14.5 -100s-23.5 -55.833 -41.5 -77.5s-40 -38.334 -66 -50.001s-55.333 -17.5 -88 -17.5c-24.667 0 -48.834 3 -72.501 9s-46 12.833 -67 20.5l-57.5 20.5c-17.333 6 -32.333 9 -45 9c-14 0 -25.5 -4.16699 -34.5 -12.5s-13.5 -21.833 -13.5 -40.5
h-170c0 36.667 5 70 15 100s24 55.833 42 77.5s40 38.5 66 50.5s55 18 87 18c24.667 0 48.834 -3 72.501 -9s46 -12.667 67 -20l57.5 -20c17.333 -6 32.333 -9 45 -9z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="1600" 
d="M1558 729c0 -105.333 -18.167 -203.332 -54.5 -293.999s-87.5 -169.5 -153.5 -236.5s-145.5 -119.5 -238.5 -157.5s-196.167 -57 -309.5 -57s-216.666 19 -309.999 57s-173.166 90.5 -239.499 157.5s-117.666 145.833 -153.999 236.5s-54.5 188.667 -54.5 294
s18.167 203.333 54.5 294s87.666 169.334 153.999 236.001s146.166 119 239.499 157s196.666 57 309.999 57s216.5 -19.167 309.5 -57.5s172.5 -90.833 238.5 -157.5s117.167 -145.334 153.5 -236.001s54.5 -188.334 54.5 -293.001zM1212 729.001
c0 72 -9.33301 136.833 -28 194.5s-45.5 106.5 -80.5 146.5s-77.833 70.667 -128.5 92s-108.334 32 -173.001 32c-65.333 0 -123.5 -10.667 -174.5 -32s-94 -52 -129 -92s-61.833 -88.833 -80.5 -146.5s-28 -122.5 -28 -194.5c0 -72.667 9.33301 -137.834 28 -195.501
s45.5 -106.5 80.5 -146.5s78 -70.5 129 -91.5s109.167 -31.5 174.5 -31.5c64.667 0 122.334 10.5 173.001 31.5s93.5 51.5 128.5 91.5s61.833 88.833 80.5 146.5s28 122.834 28 195.501zM740 1668c0 -21.333 -4.16699 -41 -12.5 -59s-19.833 -33.833 -34.5 -47.5
s-31.667 -24.334 -51 -32.001s-40 -11.5 -62 -11.5c-20 0 -39.167 3.83301 -57.5 11.5s-34.5 18.334 -48.5 32.001s-25.167 29.5 -33.5 47.5s-12.5 37.667 -12.5 59c0 20.667 4.16699 40.334 12.5 59.001s19.5 35 33.5 49s30.167 25 48.5 33s37.5 12 57.5 12
c22 0 42.667 -4 62 -12s36.333 -19 51 -33s26.167 -30.333 34.5 -49s12.5 -38.334 12.5 -59.001zM1180 1668c0 -21.333 -4.16699 -41 -12.5 -59s-19.5 -33.833 -33.5 -47.5s-30.5 -24.334 -49.5 -32.001s-39.167 -11.5 -60.5 -11.5s-41.5 3.83301 -60.5 11.5
s-35.5 18.334 -49.5 32.001s-25.167 29.5 -33.5 47.5s-12.5 37.667 -12.5 59c0 20.667 4.16699 40.334 12.5 59.001s19.5 35 33.5 49s30.5 25 49.5 33s39.167 12 60.5 12s41.5 -4 60.5 -12s35.5 -19 49.5 -33s25.167 -30.333 33.5 -49s12.5 -38.334 12.5 -59.001z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" 
d="M1048 982l-310 -310l330 -329l-164 -157l-326 326l-328 -328l-164 157l331 331l-311 312l162 157l309 -309l307 307z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="1600" 
d="M1558 729c0 -105.333 -18.1641 -203.334 -54.4971 -294.001s-87.5 -169.5 -153.5 -236.5s-145.5 -119.5 -238.5 -157.5s-196.167 -57 -309.5 -57c-62.667 0 -122 5.83301 -178 17.5s-109 28.5 -159 50.5l-57 -79c-11.333 -16 -24.333 -29.5 -39 -40.5
s-30 -19.833 -46 -26.5s-32.5 -11.5 -49.5 -14.5s-33.5 -4.5 -49.5 -4.5h-132l199 276c-78.667 68 -139.5 150.333 -182.5 247s-64.5 203 -64.5 319c0 105.333 18.167 203.333 54.5 294s87.666 169.334 153.999 236.001s146.166 119 239.499 157s196.666 57 309.999 57
c70 0 135.833 -7.33301 197.5 -22s119.5 -36 173.5 -64l41 57c10.667 14.667 20.5 27 29.5 37s18.5 18.167 28.5 24.5s21.333 10.833 34 13.5s28 4 46 4h172l-184 -255c70 -68 123.833 -148 161.5 -240s56.5 -191.667 56.5 -299zM390.003 728.999
c0 -128 28 -231.668 84 -311.001l527 731c-28 14.667 -58.667 25.834 -92 33.501s-69 11.5 -107 11.5c-65.333 0 -123.5 -10.667 -174.5 -32s-94 -52 -129 -92s-61.833 -88.833 -80.5 -146.5s-28 -122.5 -28 -194.5zM1212 728.998c0 54 -5.33398 103.999 -16.001 149.999
s-26 87.333 -46 124l-512 -711c47.333 -18.667 102 -28 164 -28c64.667 0 122.334 10.5 173.001 31.5s93.5 51.5 128.5 91.5s61.833 88.833 80.5 146.5s28 122.834 28 195.501z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="1452" 
d="M726 267c45.333 0 85.8301 7.49902 121.497 22.499s65.834 36.5 90.501 64.5s43.5 62.167 56.5 102.5s19.5 86.166 19.5 137.499v863h338v-863c0 -89.333 -14.5 -171.333 -43.5 -246s-70.5 -139 -124.5 -193s-119.667 -96 -197 -126s-164.333 -45 -261 -45
c-97.333 0 -184.666 15 -261.999 45s-143 72 -197 126s-95.333 118.333 -124 193s-43 156.667 -43 246v863h338v-862c0 -51.333 6.5 -97.166 19.5 -137.499s31.833 -74.666 56.5 -102.999s54.834 -50 90.501 -65s76.167 -22.5 121.5 -22.5zM599.997 1798
c15.333 0 28.834 -0.833008 40.501 -2.5s22 -4.33398 31 -8.00098s17.5 -8.66699 25.5 -15s16.333 -14.166 25 -23.499l182 -198h-246c-12 0 -22.333 0.333008 -31 1s-16.834 2.16699 -24.501 4.5s-15 5.33301 -22 9s-14.5 8.5 -22.5 14.5l-300 218h342z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="1452" 
d="M726 267c45.333 0 85.8301 7.49902 121.497 22.499s65.834 36.5 90.501 64.5s43.5 62.167 56.5 102.5s19.5 86.166 19.5 137.499v863h338v-863c0 -89.333 -14.5 -171.333 -43.5 -246s-70.5 -139 -124.5 -193s-119.667 -96 -197 -126s-164.333 -45 -261 -45
c-97.333 0 -184.666 15 -261.999 45s-143 72 -197 126s-95.333 118.333 -124 193s-43 156.667 -43 246v863h338v-862c0 -51.333 6.5 -97.166 19.5 -137.499s31.833 -74.666 56.5 -102.999s54.834 -50 90.501 -65s76.167 -22.5 121.5 -22.5zM1150 1798l-294.997 -214.001
c-17.333 -13.333 -33.5 -22.166 -48.5 -26.499s-33.833 -6.5 -56.5 -6.5h-246l181 197c8.66699 10 17.167 18.167 25.5 24.5s17.166 11.5 26.499 15.5s19.666 6.66699 30.999 8s24.666 2 39.999 2h342z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="1452" 
d="M726 267c45.333 0 85.8301 7.49902 121.497 22.499s65.834 36.5 90.501 64.5s43.5 62.167 56.5 102.5s19.5 86.166 19.5 137.499v863h338v-863c0 -89.333 -14.5 -171.333 -43.5 -246s-70.5 -139 -124.5 -193s-119.667 -96 -197 -126s-164.333 -45 -261 -45
c-97.333 0 -184.666 15 -261.999 45s-143 72 -197 126s-95.333 118.333 -124 193s-43 156.667 -43 246v863h338v-862c0 -51.333 6.5 -97.166 19.5 -137.499s31.833 -74.666 56.5 -102.999s54.834 -50 90.501 -65s76.167 -22.5 121.5 -22.5zM1126 1551h-234
c-12 0 -25 1.66699 -39 5s-25.667 7.66602 -35 12.999l-74 45c-3.33301 1.33301 -6.33301 3 -9 5s-5.66699 4 -9 6c-3.33301 -2 -6.33301 -4 -9 -6s-5.66699 -3.66699 -9 -5l-74 -45c-9.33301 -5.33301 -21 -9.66602 -35 -12.999s-27 -5 -39 -5h-234l244 227h312z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="1452" 
d="M726 267c45.333 0 85.8301 7.49902 121.497 22.499s65.834 36.5 90.501 64.5s43.5 62.167 56.5 102.5s19.5 86.166 19.5 137.499v863h338v-863c0 -89.333 -14.5 -171.333 -43.5 -246s-70.5 -139 -124.5 -193s-119.667 -96 -197 -126s-164.333 -45 -261 -45
c-97.333 0 -184.666 15 -261.999 45s-143 72 -197 126s-95.333 118.333 -124 193s-43 156.667 -43 246v863h338v-862c0 -51.333 6.5 -97.166 19.5 -137.499s31.833 -74.666 56.5 -102.999s54.834 -50 90.501 -65s76.167 -22.5 121.5 -22.5zM661.997 1668
c0 -21.333 -4.16699 -41 -12.5 -59s-19.833 -33.833 -34.5 -47.5s-31.667 -24.334 -51 -32.001s-40 -11.5 -62 -11.5c-20 0 -39.167 3.83301 -57.5 11.5s-34.5 18.334 -48.5 32.001s-25.167 29.5 -33.5 47.5s-12.5 37.667 -12.5 59c0 20.667 4.16699 40.334 12.5 59.001
s19.5 35 33.5 49s30.167 25 48.5 33s37.5 12 57.5 12c22 0 42.667 -4 62 -12s36.333 -19 51 -33s26.167 -30.333 34.5 -49s12.5 -38.334 12.5 -59.001zM1102 1668c0 -21.333 -4.16699 -41 -12.5 -59s-19.5 -33.833 -33.5 -47.5s-30.5 -24.334 -49.5 -32.001
s-39.167 -11.5 -60.5 -11.5s-41.5 3.83301 -60.5 11.5s-35.5 18.334 -49.5 32.001s-25.167 29.5 -33.5 47.5s-12.5 37.667 -12.5 59c0 20.667 4.16699 40.334 12.5 59.001s19.5 35 33.5 49s30.5 25 49.5 33s39.167 12 60.5 12s41.5 -4 60.5 -12s35.5 -19 49.5 -33
s25.167 -30.333 33.5 -49s12.5 -38.334 12.5 -59.001z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="1354" 
d="M846 554l0.000976562 -554.002h-338v554l-528 903h298c29.333 0 52.666 -6.83301 69.999 -20.5s31.333 -31.167 42 -52.5l206 -424l46 -94c14 -28.667 26.333 -57 37 -85c10 28.667 22 57.334 36 86.001l45 93l204 424c4 8.66699 9.5 17.334 16.5 26.001s15 16.5 24 23.5
s19.333 12.667 31 17s24.5 6.5 38.5 6.5h300zM1104 1798l-294.997 -214.001c-17.333 -13.333 -33.5 -22.166 -48.5 -26.499s-33.833 -6.5 -56.5 -6.5h-246l181 197c8.66699 10 17.167 18.167 25.5 24.5s17.166 11.5 26.499 15.5s19.666 6.66699 30.999 8s24.666 2 39.999 2
h342z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="1276" 
d="M458 252l-0.00195312 -252h-338v1457h338v-235h176c102.667 0 190.834 -12.167 264.501 -36.5s134.334 -57.833 182.001 -100.5s82.834 -93 105.501 -151s34 -120.667 34 -188c0 -72.667 -11.667 -139.334 -35 -200.001s-59 -112.667 -107 -156
s-108.833 -77.166 -182.5 -101.499s-160.834 -36.5 -261.501 -36.5h-176zM457.998 507h176c88 0 151.333 21 190 63s58 100.667 58 176c0 33.333 -5 63.666 -15 90.999s-25.167 50.833 -45.5 70.5s-46 34.834 -77 45.501s-67.833 16 -110.5 16h-176v-462z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="1316" 
d="M724 1485c86.667 0 160.002 -12.3359 220.002 -37.0029s108.5 -55.334 145.5 -92.001s63.667 -75.667 80 -117s24.5 -79 24.5 -113c0 -37.333 -6 -69.333 -18 -96s-27 -49.667 -45 -69s-37.333 -36.166 -58 -50.499l-58 -40.5c-18 -12.667 -33 -25.5 -45 -38.5
s-18 -28.167 -18 -45.5c0 -18.667 7.83301 -35.5 23.5 -50.5s35 -30.5 58 -46.5l75.5 -52c27.333 -18.667 52.5 -41.167 75.5 -67.5s42.333 -57.666 58 -93.999s23.5 -79.833 23.5 -130.5c0 -60.667 -11.5 -113.5 -34.5 -158.5s-53.5 -82.5 -91.5 -112.5
s-82 -52.5 -132 -67.5s-102 -22.5 -156 -22.5c-29.333 0 -59.333 3.33301 -90 10s-60.5 15.667 -89.5 27s-56.333 24.833 -82 40.5s-48.5 32.5 -68.5 50.5l72 114c8.66699 13.333 19 23.833 31 31.5s27.667 11.5 47 11.5c18 0 34.333 -4 49 -12s29.5 -16.667 44.5 -26
s31.5 -18 49.5 -26s39.667 -12 65 -12c26 0 48.5 9.5 67.5 28.5s28.5 44.167 28.5 75.5c0 26.667 -8.66699 49.334 -26 68.001s-39 36 -65 52s-54 32.167 -84 48.5s-58 36 -84 59s-47.667 50.333 -65 82s-26 71.167 -26 118.5c0 40 6.5 74.333 19.5 103s29.5 53.5 49.5 74.5
s41.333 39.833 64 56.5l64 49c20 16 36.5 33.167 49.5 51.5s19.5 39.833 19.5 64.5c0 44.667 -14.5 79.834 -43.5 105.501s-75.167 38.5 -138.5 38.5c-42 0 -77.833 -6.83301 -107.5 -20.5s-53.834 -33.334 -72.501 -59.001s-32.334 -57.167 -41.001 -94.5
s-13 -79.333 -13 -126v-968h-310v978c0 71.333 12.5 137.833 37.5 199.5s61.5 115.334 109.5 161.001s106.5 81.5 175.5 107.5s147.5 39 235.5 39z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="1078" 
d="M836 0c-29.333 0 -51.501 3.99902 -66.501 11.999s-27.5 24.667 -37.5 50l-22 57c-26 -22 -51 -41.333 -75 -58s-48.833 -30.834 -74.5 -42.501s-53 -20.334 -82 -26.001s-61.167 -8.5 -96.5 -8.5c-47.333 0 -90.333 6.16699 -129 18.5s-71.5 30.5 -98.5 54.5
s-47.833 53.833 -62.5 89.5s-22 76.834 -22 123.501c0 36.667 9.33301 74.334 28 113.001s51 73.834 97 105.501s108 58.167 186 79.5s176.333 33.333 295 36v44c0 60.667 -12.5 104.5 -37.5 131.5s-60.5 40.5 -106.5 40.5c-36.667 0 -66.667 -4 -90 -12
s-44.166 -17 -62.499 -27s-36.166 -19 -53.499 -27s-38 -12 -62 -12c-21.333 0 -39.5 5.16699 -54.5 15.5s-26.833 23.166 -35.5 38.499l-56 97c62.667 55.333 132.167 96.5 208.5 123.5s158.166 40.5 245.499 40.5c62.667 0 119.167 -10.167 169.5 -30.5
s93 -48.666 128 -84.999s61.833 -79.5 80.5 -129.5s28 -104.667 28 -164v-648h-142zM479.999 188.999c40 0 75.001 6.99902 105.001 20.999s60.333 36.667 91 68v144c-62 -2.66699 -113 -8 -153 -16s-71.667 -18 -95 -30s-39.5 -25.667 -48.5 -41s-13.5 -32 -13.5 -50
c0 -35.333 9.66699 -60.166 29 -74.499s47.666 -21.5 84.999 -21.5zM484 1473c31.333 0 54.668 -5.16699 70.001 -15.5s28.666 -25.166 39.999 -44.499l136 -245h-176c-23.333 0 -42 3.16699 -56 9.5s-28.667 17.166 -44 32.499l-260 263h290z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="1078" 
d="M836 0c-29.333 0 -51.501 3.99902 -66.501 11.999s-27.5 24.667 -37.5 50l-22 57c-26 -22 -51 -41.333 -75 -58s-48.833 -30.834 -74.5 -42.501s-53 -20.334 -82 -26.001s-61.167 -8.5 -96.5 -8.5c-47.333 0 -90.333 6.16699 -129 18.5s-71.5 30.5 -98.5 54.5
s-47.833 53.833 -62.5 89.5s-22 76.834 -22 123.501c0 36.667 9.33301 74.334 28 113.001s51 73.834 97 105.501s108 58.167 186 79.5s176.333 33.333 295 36v44c0 60.667 -12.5 104.5 -37.5 131.5s-60.5 40.5 -106.5 40.5c-36.667 0 -66.667 -4 -90 -12
s-44.166 -17 -62.499 -27s-36.166 -19 -53.499 -27s-38 -12 -62 -12c-21.333 0 -39.5 5.16699 -54.5 15.5s-26.833 23.166 -35.5 38.499l-56 97c62.667 55.333 132.167 96.5 208.5 123.5s158.166 40.5 245.499 40.5c62.667 0 119.167 -10.167 169.5 -30.5
s93 -48.666 128 -84.999s61.833 -79.5 80.5 -129.5s28 -104.667 28 -164v-648h-142zM479.999 188.999c40 0 75.001 6.99902 105.001 20.999s60.333 36.667 91 68v144c-62 -2.66699 -113 -8 -153 -16s-71.667 -18 -95 -30s-39.5 -25.667 -48.5 -41s-13.5 -32 -13.5 -50
c0 -35.333 9.66699 -60.166 29 -74.499s47.666 -21.5 84.999 -21.5zM958 1473l-259.998 -263c-15.333 -15.333 -30 -26.166 -44 -32.499s-32.667 -9.5 -56 -9.5h-186l136 245c11.333 19.333 24.666 34.166 39.999 44.499s38.666 15.5 69.999 15.5h300z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="1078" 
d="M836 0c-29.333 0 -51.501 3.99902 -66.501 11.999s-27.5 24.667 -37.5 50l-22 57c-26 -22 -51 -41.333 -75 -58s-48.833 -30.834 -74.5 -42.501s-53 -20.334 -82 -26.001s-61.167 -8.5 -96.5 -8.5c-47.333 0 -90.333 6.16699 -129 18.5s-71.5 30.5 -98.5 54.5
s-47.833 53.833 -62.5 89.5s-22 76.834 -22 123.501c0 36.667 9.33301 74.334 28 113.001s51 73.834 97 105.501s108 58.167 186 79.5s176.333 33.333 295 36v44c0 60.667 -12.5 104.5 -37.5 131.5s-60.5 40.5 -106.5 40.5c-36.667 0 -66.667 -4 -90 -12
s-44.166 -17 -62.499 -27s-36.166 -19 -53.499 -27s-38 -12 -62 -12c-21.333 0 -39.5 5.16699 -54.5 15.5s-26.833 23.166 -35.5 38.499l-56 97c62.667 55.333 132.167 96.5 208.5 123.5s158.166 40.5 245.499 40.5c62.667 0 119.167 -10.167 169.5 -30.5
s93 -48.666 128 -84.999s61.833 -79.5 80.5 -129.5s28 -104.667 28 -164v-648h-142zM479.999 188.999c40 0 75.001 6.99902 105.001 20.999s60.333 36.667 91 68v144c-62 -2.66699 -113 -8 -153 -16s-71.667 -18 -95 -30s-39.5 -25.667 -48.5 -41s-13.5 -32 -13.5 -50
c0 -35.333 9.66699 -60.166 29 -74.499s47.666 -21.5 84.999 -21.5zM942 1168h-206c-12 0 -23 1.83301 -33 5.5s-19 8.5 -27 14.5l-84 72c-4.66699 3.33301 -9 7 -13 11s-8 8.66699 -12 14c-4 -5.33301 -8.16699 -10 -12.5 -14s-8.5 -7.66699 -12.5 -11l-86 -72
c-6 -5.33301 -14.5 -10 -25.5 -14s-22.5 -6 -34.5 -6h-214l234 289h292z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="1078" 
d="M836 0c-29.333 0 -51.501 3.99902 -66.501 11.999s-27.5 24.667 -37.5 50l-22 57c-26 -22 -51 -41.333 -75 -58s-48.833 -30.834 -74.5 -42.501s-53 -20.334 -82 -26.001s-61.167 -8.5 -96.5 -8.5c-47.333 0 -90.333 6.16699 -129 18.5s-71.5 30.5 -98.5 54.5
s-47.833 53.833 -62.5 89.5s-22 76.834 -22 123.501c0 36.667 9.33301 74.334 28 113.001s51 73.834 97 105.501s108 58.167 186 79.5s176.333 33.333 295 36v44c0 60.667 -12.5 104.5 -37.5 131.5s-60.5 40.5 -106.5 40.5c-36.667 0 -66.667 -4 -90 -12
s-44.166 -17 -62.499 -27s-36.166 -19 -53.499 -27s-38 -12 -62 -12c-21.333 0 -39.5 5.16699 -54.5 15.5s-26.833 23.166 -35.5 38.499l-56 97c62.667 55.333 132.167 96.5 208.5 123.5s158.166 40.5 245.499 40.5c62.667 0 119.167 -10.167 169.5 -30.5
s93 -48.666 128 -84.999s61.833 -79.5 80.5 -129.5s28 -104.667 28 -164v-648h-142zM479.999 188.999c40 0 75.001 6.99902 105.001 20.999s60.333 36.667 91 68v144c-62 -2.66699 -113 -8 -153 -16s-71.667 -18 -95 -30s-39.5 -25.667 -48.5 -41s-13.5 -32 -13.5 -50
c0 -35.333 9.66699 -60.166 29 -74.499s47.666 -21.5 84.999 -21.5zM666 1387c16 0 28.999 4.16992 38.999 12.5029s15 26.166 15 53.499h188c0 -42 -5.83301 -79.833 -17.5 -113.5s-27.834 -62.334 -48.501 -86.001s-45 -41.834 -73 -54.501s-58.333 -19 -91 -19
c-24.667 0 -47.667 3.83301 -69 11.5s-41.166 16.167 -59.499 25.5l-50 25.5c-15 7.66699 -28.167 11.5 -39.5 11.5c-16 0 -28.667 -4.5 -38 -13.5s-14 -27.167 -14 -54.5h-190c0 42 6 79.833 18 113.5s28.333 62.5 49 86.5s45 42.5 73 55.5s58 19.5 90 19.5
c24.667 0 47.834 -3.83301 69.501 -11.5s41.5 -16.167 59.5 -25.5l49.5 -25.5c15 -7.66699 28.167 -11.5 39.5 -11.5z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="1078" 
d="M836 0c-29.333 0 -51.501 3.99902 -66.501 11.999s-27.5 24.667 -37.5 50l-22 57c-26 -22 -51 -41.333 -75 -58s-48.833 -30.834 -74.5 -42.501s-53 -20.334 -82 -26.001s-61.167 -8.5 -96.5 -8.5c-47.333 0 -90.333 6.16699 -129 18.5s-71.5 30.5 -98.5 54.5
s-47.833 53.833 -62.5 89.5s-22 76.834 -22 123.501c0 36.667 9.33301 74.334 28 113.001s51 73.834 97 105.501s108 58.167 186 79.5s176.333 33.333 295 36v44c0 60.667 -12.5 104.5 -37.5 131.5s-60.5 40.5 -106.5 40.5c-36.667 0 -66.667 -4 -90 -12
s-44.166 -17 -62.499 -27s-36.166 -19 -53.499 -27s-38 -12 -62 -12c-21.333 0 -39.5 5.16699 -54.5 15.5s-26.833 23.166 -35.5 38.499l-56 97c62.667 55.333 132.167 96.5 208.5 123.5s158.166 40.5 245.499 40.5c62.667 0 119.167 -10.167 169.5 -30.5
s93 -48.666 128 -84.999s61.833 -79.5 80.5 -129.5s28 -104.667 28 -164v-648h-142zM479.999 188.999c40 0 75.001 6.99902 105.001 20.999s60.333 36.667 91 68v144c-62 -2.66699 -113 -8 -153 -16s-71.667 -18 -95 -30s-39.5 -25.667 -48.5 -41s-13.5 -32 -13.5 -50
c0 -35.333 9.66699 -60.166 29 -74.499s47.666 -21.5 84.999 -21.5zM530 1295c0 -21.333 -4.33301 -41.333 -13 -60s-20.5 -34.834 -35.5 -48.501s-32.333 -24.5 -52 -32.5s-40.834 -12 -63.501 -12c-21.333 0 -41.5 4 -60.5 12s-35.667 18.833 -50 32.5
s-25.833 29.834 -34.5 48.501s-13 38.667 -13 60c0 22 4.33301 42.667 13 62s20.167 36.166 34.5 50.499s31 25.666 50 33.999s39.167 12.5 60.5 12.5c22.667 0 43.834 -4.16699 63.501 -12.5s37 -19.666 52 -33.999s26.833 -31.166 35.5 -50.499s13 -40 13 -62zM916 1295
c0 -21.333 -4.16699 -41.333 -12.5 -60s-19.833 -34.834 -34.5 -48.501s-31.834 -24.5 -51.501 -32.5s-40.834 -12 -63.501 -12c-22 0 -42.667 4 -62 12s-36.333 18.833 -51 32.5s-26.167 29.834 -34.5 48.501s-12.5 38.667 -12.5 60c0 22 4.16699 42.667 12.5 62
s19.833 36.166 34.5 50.499s31.667 25.666 51 33.999s40 12.5 62 12.5c22.667 0 43.834 -4.16699 63.501 -12.5s36.834 -19.666 51.501 -33.999s26.167 -31.166 34.5 -50.499s12.5 -40 12.5 -62z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="1078" 
d="M836 0c-29.333 0 -51.501 3.99902 -66.501 11.999s-27.5 24.667 -37.5 50l-22 57c-26 -22 -51 -41.333 -75 -58s-48.833 -30.834 -74.5 -42.501s-53 -20.334 -82 -26.001s-61.167 -8.5 -96.5 -8.5c-47.333 0 -90.333 6.16699 -129 18.5s-71.5 30.5 -98.5 54.5
s-47.833 53.833 -62.5 89.5s-22 76.834 -22 123.501c0 36.667 9.33301 74.334 28 113.001s51 73.834 97 105.501s108 58.167 186 79.5s176.333 33.333 295 36v44c0 60.667 -12.5 104.5 -37.5 131.5s-60.5 40.5 -106.5 40.5c-36.667 0 -66.667 -4 -90 -12
s-44.166 -17 -62.499 -27s-36.166 -19 -53.499 -27s-38 -12 -62 -12c-21.333 0 -39.5 5.16699 -54.5 15.5s-26.833 23.166 -35.5 38.499l-56 97c62.667 55.333 132.167 96.5 208.5 123.5s158.166 40.5 245.499 40.5c62.667 0 119.167 -10.167 169.5 -30.5
s93 -48.666 128 -84.999s61.833 -79.5 80.5 -129.5s28 -104.667 28 -164v-648h-142zM479.999 188.999c40 0 75.001 6.99902 105.001 20.999s60.333 36.667 91 68v144c-62 -2.66699 -113 -8 -153 -16s-71.667 -18 -95 -30s-39.5 -25.667 -48.5 -41s-13.5 -32 -13.5 -50
c0 -35.333 9.66699 -60.166 29 -74.499s47.666 -21.5 84.999 -21.5zM320 1334c0 33.333 6.66699 63.5 20 90.5s31 50.167 53 69.5s47.667 34.166 77 44.499s60 15.5 92 15.5c32.667 0 64 -5.16699 94 -15.5s56.5 -25.166 79.5 -44.499s41.167 -42.5 54.5 -69.5
s20 -57.167 20 -90.5c0 -32.667 -6.66699 -62.167 -20 -88.5s-31.5 -48.833 -54.5 -67.5s-49.5 -33.167 -79.5 -43.5s-61.333 -15.5 -94 -15.5c-32 0 -62.667 5.16699 -92 15.5s-55 24.833 -77 43.5s-39.667 41.167 -53 67.5s-20 55.833 -20 88.5zM486 1334
c0 -23.333 6.5 -42.5 19.5 -57.5s33.167 -22.5 60.5 -22.5c24 0 43 7.5 57 22.5s21 34.167 21 57.5c0 25.333 -7 45.333 -21 60s-33 22 -57 22c-27.333 0 -47.5 -7.33301 -60.5 -22s-19.5 -34.667 -19.5 -60z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="1668" 
d="M1180 1053c61.333 0 118.335 -11.6641 171.002 -34.9971s98.167 -56.5 136.5 -99.5s68.333 -95 90 -156s32.5 -129.167 32.5 -204.5c0 -23.333 -1 -42.333 -3 -57s-5.5 -26.334 -10.5 -35.001s-11.833 -14.667 -20.5 -18s-20 -5 -34 -5h-570
c11.333 -79.333 37.166 -137 77.499 -173s91.166 -54 152.499 -54c44.667 0 80.167 4 106.5 12s48 16.667 65 26s31.667 18 44 26s27.166 12 44.499 12c28.667 0 49.334 -9 62.001 -27l84 -103c-32 -36.667 -66.167 -66.5 -102.5 -89.5s-73.5 -41 -111.5 -54
s-75.833 -22 -113.5 -27s-73.834 -7.5 -108.501 -7.5c-34 0 -67.667 3.5 -101 10.5s-65.333 18 -96 33s-59.5 34.167 -86.5 57.5s-51.5 51 -73.5 83c-21.333 -32 -46.833 -59.667 -76.5 -83s-62.167 -42.5 -97.5 -57.5s-73 -26 -113 -33s-81.333 -10.5 -124 -10.5
c-106.667 0 -189 26 -247 78s-87 125 -87 219c0 24.667 4 50 12 76s21.167 51.5 39.5 76.5s42.666 48.667 72.999 71s68 42.166 113 59.499s97.833 31.333 158.5 42s130.667 17 210 19v23c0 60.667 -12.5 106 -37.5 136s-60.5 45 -106.5 45
c-36.667 0 -66.667 -4.5 -90 -13.5s-44.166 -19 -62.499 -30s-36.166 -21 -53.499 -30s-38 -13.5 -62 -13.5c-21.333 0 -39.5 5.16699 -54.5 15.5s-26.833 23.166 -35.5 38.499l-56 97c62.667 55.333 128 96.5 196 123.5s142.667 40.5 224 40.5c75.333 0 138 -13 188 -39
s89 -62 117 -108c39.333 44 87 78.833 143 104.5s120.667 38.5 194 38.5zM1166 839.003c-58.667 0 -103.832 -18.502 -135.499 -55.502s-51.834 -90.5 -60.501 -160.5h364c0 24.667 -2.83301 49.834 -8.5 75.501s-15 48.834 -28 69.501s-30.333 37.667 -52 51
s-48.167 20 -79.5 20zM676.004 443.001c-62 -3.33301 -112.999 -9.50195 -152.999 -18.502s-71.667 -20.167 -95 -33.5s-39.5 -28.166 -48.5 -44.499s-13.5 -33.166 -13.5 -50.499c0 -36 9.66699 -62.833 29 -80.5s47.666 -26.5 84.999 -26.5c28 0 54 4.16699 78 12.5
s44.667 21.833 62 40.5s31 43.167 41 73.5s15 67.5 15 111.5v16z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="972" 
d="M410 -210c10 0 19.6689 -1.33789 29.002 -4.00488s21 -4 35 -4c17.333 0 29.666 3.16699 36.999 9.5s11 13.166 11 20.499c0 14 -10.167 24.5 -30.5 31.5s-54.833 13.833 -103.5 20.5l39 131c-52 10.667 -100.833 30.334 -146.5 59.001s-85.5 65.334 -119.5 110.001
s-60.667 96.834 -80 156.501s-29 126.5 -29 200.5c0 74.667 11.167 144.5 33.5 209.5s55.333 121.333 99 169s97.667 85.167 162 112.5s138.833 41 223.5 41c81.333 0 152.666 -13 213.999 -39s116.666 -64 165.999 -114l-82 -109c-9.33301 -11.333 -18.333 -20.333 -27 -27
s-21 -10 -37 -10c-15.333 0 -29.166 3.66699 -41.499 11l-41.5 24.5c-15.333 9 -33.333 17.167 -54 24.5s-46.334 11 -77.001 11c-38 0 -70.833 -7 -98.5 -21s-50.5 -34 -68.5 -60s-31.333 -57.833 -40 -95.5s-13 -80.167 -13 -127.5
c0 -99.333 19.167 -175.666 57.5 -228.999s91.166 -80 158.499 -80c36 0 64.5 4.5 85.5 13.5s38.833 19 53.5 30l40.5 30.5c12.333 9.33301 27.833 14 46.5 14c24.667 0 43.334 -9 56.001 -27l90 -111c-25.333 -29.333 -51.833 -54.166 -79.5 -74.499
s-56 -37.166 -85 -50.499s-58.167 -23.833 -87.5 -31.5s-58.666 -13.167 -87.999 -16.5l-9 -32c30 -8 55 -17.333 75 -28s35.833 -22.334 47.5 -35.001s20 -26.5 25 -41.5s7.5 -30.5 7.5 -46.5c0 -23.333 -6.5 -44.333 -19.5 -63s-31.167 -34.667 -54.5 -48
s-51.5 -23.666 -84.5 -30.999s-69.5 -11 -109.5 -11c-27.333 0 -52.5 1.33301 -75.5 4s-47.167 6.66699 -72.5 12l28 94c4 18 15.333 27 34 27z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="1088" 
d="M564 1053c68.667 0 131.502 -10.668 188.502 -32.001s106 -52.333 147 -93s73 -90.5 96 -149.5s34.5 -125.833 34.5 -200.5c0 -23.333 -1 -42.333 -3 -57s-5.66699 -26.334 -11 -35.001s-12.5 -14.667 -21.5 -18s-20.5 -5 -34.5 -5h-598
c10 -86.667 36.333 -149.5 79 -188.5s97.667 -58.5 165 -58.5c36 0 67 4.33301 93 13s49.167 18.334 69.5 29.001l56 29c17 8.66699 34.833 13 53.5 13c24.667 0 43.334 -9 56.001 -27l90 -111c-32 -36.667 -66.5 -66.5 -103.5 -89.5s-75 -41 -114 -54s-78 -22 -117 -27
s-76.167 -7.5 -111.5 -7.5c-72.667 0 -140.834 11.833 -204.501 35.5s-119.334 58.834 -167.001 105.501s-85.334 104.667 -113.001 174s-41.5 150 -41.5 242c0 69.333 11.833 135 35.5 197s57.667 116.333 102 163s98 83.667 161 111s134.167 41 213.5 41zM570.002 838.999
c-59.333 0 -105.67 -16.8311 -139.003 -50.498s-55.333 -82.167 -66 -145.5h387c0 24.667 -3.16699 48.667 -9.5 72s-16.666 44.166 -30.999 62.499s-33.166 33.166 -56.499 44.499s-51.666 17 -84.999 17zM493.998 1473c31.333 0 54.668 -5.16699 70.001 -15.5
s28.666 -25.166 39.999 -44.499l136 -245h-176c-23.333 0 -42 3.16699 -56 9.5s-28.667 17.166 -44 32.499l-260 263h290z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="1088" 
d="M564 1053c68.667 0 131.502 -10.668 188.502 -32.001s106 -52.333 147 -93s73 -90.5 96 -149.5s34.5 -125.833 34.5 -200.5c0 -23.333 -1 -42.333 -3 -57s-5.66699 -26.334 -11 -35.001s-12.5 -14.667 -21.5 -18s-20.5 -5 -34.5 -5h-598
c10 -86.667 36.333 -149.5 79 -188.5s97.667 -58.5 165 -58.5c36 0 67 4.33301 93 13s49.167 18.334 69.5 29.001l56 29c17 8.66699 34.833 13 53.5 13c24.667 0 43.334 -9 56.001 -27l90 -111c-32 -36.667 -66.5 -66.5 -103.5 -89.5s-75 -41 -114 -54s-78 -22 -117 -27
s-76.167 -7.5 -111.5 -7.5c-72.667 0 -140.834 11.833 -204.501 35.5s-119.334 58.834 -167.001 105.501s-85.334 104.667 -113.001 174s-41.5 150 -41.5 242c0 69.333 11.833 135 35.5 197s57.667 116.333 102 163s98 83.667 161 111s134.167 41 213.5 41zM570.002 838.999
c-59.333 0 -105.67 -16.8311 -139.003 -50.498s-55.333 -82.167 -66 -145.5h387c0 24.667 -3.16699 48.667 -9.5 72s-16.666 44.166 -30.999 62.499s-33.166 33.166 -56.499 44.499s-51.666 17 -84.999 17zM967.998 1473l-259.998 -263
c-15.333 -15.333 -30 -26.166 -44 -32.499s-32.667 -9.5 -56 -9.5h-186l136 245c11.333 19.333 24.666 34.166 39.999 44.499s38.666 15.5 69.999 15.5h300z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="1088" 
d="M564 1053c68.667 0 131.502 -10.668 188.502 -32.001s106 -52.333 147 -93s73 -90.5 96 -149.5s34.5 -125.833 34.5 -200.5c0 -23.333 -1 -42.333 -3 -57s-5.66699 -26.334 -11 -35.001s-12.5 -14.667 -21.5 -18s-20.5 -5 -34.5 -5h-598
c10 -86.667 36.333 -149.5 79 -188.5s97.667 -58.5 165 -58.5c36 0 67 4.33301 93 13s49.167 18.334 69.5 29.001l56 29c17 8.66699 34.833 13 53.5 13c24.667 0 43.334 -9 56.001 -27l90 -111c-32 -36.667 -66.5 -66.5 -103.5 -89.5s-75 -41 -114 -54s-78 -22 -117 -27
s-76.167 -7.5 -111.5 -7.5c-72.667 0 -140.834 11.833 -204.501 35.5s-119.334 58.834 -167.001 105.501s-85.334 104.667 -113.001 174s-41.5 150 -41.5 242c0 69.333 11.833 135 35.5 197s57.667 116.333 102 163s98 83.667 161 111s134.167 41 213.5 41zM570.002 838.999
c-59.333 0 -105.67 -16.8311 -139.003 -50.498s-55.333 -82.167 -66 -145.5h387c0 24.667 -3.16699 48.667 -9.5 72s-16.666 44.166 -30.999 62.499s-33.166 33.166 -56.499 44.499s-51.666 17 -84.999 17zM951.998 1168h-206c-12 0 -23 1.83301 -33 5.5s-19 8.5 -27 14.5
l-84 72c-4.66699 3.33301 -9 7 -13 11s-8 8.66699 -12 14c-4 -5.33301 -8.16699 -10 -12.5 -14s-8.5 -7.66699 -12.5 -11l-86 -72c-6 -5.33301 -14.5 -10 -25.5 -14s-22.5 -6 -34.5 -6h-214l234 289h292z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="1088" 
d="M564 1053c68.667 0 131.502 -10.668 188.502 -32.001s106 -52.333 147 -93s73 -90.5 96 -149.5s34.5 -125.833 34.5 -200.5c0 -23.333 -1 -42.333 -3 -57s-5.66699 -26.334 -11 -35.001s-12.5 -14.667 -21.5 -18s-20.5 -5 -34.5 -5h-598
c10 -86.667 36.333 -149.5 79 -188.5s97.667 -58.5 165 -58.5c36 0 67 4.33301 93 13s49.167 18.334 69.5 29.001l56 29c17 8.66699 34.833 13 53.5 13c24.667 0 43.334 -9 56.001 -27l90 -111c-32 -36.667 -66.5 -66.5 -103.5 -89.5s-75 -41 -114 -54s-78 -22 -117 -27
s-76.167 -7.5 -111.5 -7.5c-72.667 0 -140.834 11.833 -204.501 35.5s-119.334 58.834 -167.001 105.501s-85.334 104.667 -113.001 174s-41.5 150 -41.5 242c0 69.333 11.833 135 35.5 197s57.667 116.333 102 163s98 83.667 161 111s134.167 41 213.5 41zM570.002 838.999
c-59.333 0 -105.67 -16.8311 -139.003 -50.498s-55.333 -82.167 -66 -145.5h387c0 24.667 -3.16699 48.667 -9.5 72s-16.666 44.166 -30.999 62.499s-33.166 33.166 -56.499 44.499s-51.666 17 -84.999 17zM539.998 1295c0 -21.333 -4.33301 -41.333 -13 -60
s-20.5 -34.834 -35.5 -48.501s-32.333 -24.5 -52 -32.5s-40.834 -12 -63.501 -12c-21.333 0 -41.5 4 -60.5 12s-35.667 18.833 -50 32.5s-25.833 29.834 -34.5 48.501s-13 38.667 -13 60c0 22 4.33301 42.667 13 62s20.167 36.166 34.5 50.499s31 25.666 50 33.999
s39.167 12.5 60.5 12.5c22.667 0 43.834 -4.16699 63.501 -12.5s37 -19.666 52 -33.999s26.833 -31.166 35.5 -50.499s13 -40 13 -62zM925.998 1295c0 -21.333 -4.16699 -41.333 -12.5 -60s-19.833 -34.834 -34.5 -48.501s-31.834 -24.5 -51.501 -32.5
s-40.834 -12 -63.501 -12c-22 0 -42.667 4 -62 12s-36.333 18.833 -51 32.5s-26.167 29.834 -34.5 48.501s-12.5 38.667 -12.5 60c0 22 4.16699 42.667 12.5 62s19.833 36.166 34.5 50.499s31.667 25.666 51 33.999s40 12.5 62 12.5c22.667 0 43.834 -4.16699 63.501 -12.5
s36.834 -19.666 51.501 -33.999s26.167 -31.166 34.5 -50.499s12.5 -40 12.5 -62z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="570" 
d="M440 1037v-1037h-310v1037h310zM217 1473c31.333 0 54.668 -5.16699 70.001 -15.5s28.666 -25.166 39.999 -44.499l136 -245h-176c-23.333 0 -42 3.16699 -56 9.5s-28.667 17.166 -44 32.499l-260 263h290z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="570" 
d="M440 1037v-1037h-310v1037h310zM691 1473l-259.998 -263c-15.333 -15.333 -30 -26.166 -44 -32.499s-32.667 -9.5 -56 -9.5h-186l136 245c11.333 19.333 24.666 34.166 39.999 44.499s38.666 15.5 69.999 15.5h300z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="570" 
d="M440 1037v-1037h-310v1037h310zM675 1168h-206c-12 0 -23 1.83301 -33 5.5s-19 8.5 -27 14.5l-84 72c-4.66699 3.33301 -9 7 -13 11s-8 8.66699 -12 14c-4 -5.33301 -8.16699 -10 -12.5 -14s-8.5 -7.66699 -12.5 -11l-86 -72c-6 -5.33301 -14.5 -10 -25.5 -14
s-22.5 -6 -34.5 -6h-214l234 289h292z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="570" 
d="M440 1037v-1037h-310v1037h310zM263 1295c0 -21.333 -4.33301 -41.333 -13 -60s-20.5 -34.834 -35.5 -48.501s-32.333 -24.5 -52 -32.5s-40.834 -12 -63.501 -12c-21.333 0 -41.5 4 -60.5 12s-35.667 18.833 -50 32.5s-25.833 29.834 -34.5 48.501s-13 38.667 -13 60
c0 22 4.33301 42.667 13 62s20.167 36.166 34.5 50.499s31 25.666 50 33.999s39.167 12.5 60.5 12.5c22.667 0 43.834 -4.16699 63.501 -12.5s37 -19.666 52 -33.999s26.833 -31.166 35.5 -50.499s13 -40 13 -62zM649 1295c0 -21.333 -4.16699 -41.333 -12.5 -60
s-19.833 -34.834 -34.5 -48.501s-31.834 -24.5 -51.501 -32.5s-40.834 -12 -63.501 -12c-22 0 -42.667 4 -62 12s-36.333 18.833 -51 32.5s-26.167 29.834 -34.5 48.501s-12.5 38.667 -12.5 60c0 22 4.16699 42.667 12.5 62s19.833 36.166 34.5 50.499
s31.667 25.666 51 33.999s40 12.5 62 12.5c22.667 0 43.834 -4.16699 63.501 -12.5s36.834 -19.666 51.501 -33.999s26.167 -31.166 34.5 -50.499s12.5 -40 12.5 -62z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="1162" 
d="M358 1059c-8.66699 14 -12.999 27.3359 -12.999 40.0029c0 11.333 3.5 22 10.5 32s17.167 18 30.5 24l46 22l-40 13.5c-13.333 4.33301 -27.333 8.5 -42 12.5c-20.667 5.33301 -37.5 15.166 -50.5 29.499s-19.5 33.5 -19.5 57.5c0 16 3.33301 33.667 10 53l36 103
c70.667 -11.333 138.334 -27.666 203.001 -48.999s125.334 -48.333 182.001 -81l183 107l62 -98c7.33301 -11.333 11 -23.333 11 -36c0 -11.333 -3 -21.833 -9 -31.5s-14.667 -17.167 -26 -22.5l-66 -33c72.667 -68.667 129.834 -151.834 171.501 -249.501
s62.5 -211.5 62.5 -341.5c0 -100 -11.833 -188.833 -35.5 -266.5s-58.334 -143.334 -104.001 -197.001s-101.834 -94.334 -168.501 -122.001s-142.667 -41.5 -228 -41.5c-72.667 0 -140 11.167 -202 33.5s-115.667 54.833 -161 97.5s-80.833 94.667 -106.5 156
s-38.5 131 -38.5 209c0 60 11.167 117.5 33.5 172.5s54 103.5 95 145.5s90.167 75.333 147.5 100s120.666 37 189.999 37c60 0 113.5 -9.33301 160.5 -28s89.5 -45 127.5 -79c-13.333 54.667 -34.333 103 -63 145s-68.334 80 -119.001 114l-212 -124zM572.001 212.003
c30 0 58.6699 5.49805 86.0029 16.498s51.833 29.667 73.5 56s39.167 62 52.5 107s21 101.5 23 169.5c-19.333 42 -46.833 76.833 -82.5 104.5s-81.834 41.5 -138.501 41.5c-36 0 -67.833 -6 -95.5 -18s-50.834 -28.333 -69.501 -49s-32.834 -44.834 -42.501 -72.501
s-14.5 -57.5 -14.5 -89.5c0 -43.333 5.33301 -81.666 16 -114.999s25.334 -61.166 44.001 -83.499s40.667 -39.166 66 -50.499s52.666 -17 81.999 -17z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" 
d="M120 0l0.000976562 1037h192c19.333 0 35.5 -4.33301 48.5 -13s22.167 -21.667 27.5 -39l18 -60c20 18.667 40.667 36 62 52s44.166 29.5 68.499 40.5s50.666 19.667 78.999 26s59.166 9.5 92.499 9.5c56.667 0 106.834 -9.83301 150.501 -29.5s80.5 -47 110.5 -82
s52.667 -76.667 68 -125s23 -100.833 23 -157.5v-659h-310v659c0 50.667 -11.667 90.167 -35 118.5s-57.666 42.5 -102.999 42.5c-34 0 -66 -7.33301 -96 -22s-58.667 -34.334 -86 -59.001v-739h-310zM694.001 1387c16 0 28.999 4.16992 38.999 12.5029s15 26.166 15 53.499
h188c0 -42 -5.83301 -79.833 -17.5 -113.5s-27.834 -62.334 -48.501 -86.001s-45 -41.834 -73 -54.501s-58.333 -19 -91 -19c-24.667 0 -47.667 3.83301 -69 11.5s-41.166 16.167 -59.499 25.5l-50 25.5c-15 7.66699 -28.167 11.5 -39.5 11.5c-16 0 -28.667 -4.5 -38 -13.5
s-14 -27.167 -14 -54.5h-190c0 42 6 79.833 18 113.5s28.333 62.5 49 86.5s45 42.5 73 55.5s58 19.5 90 19.5c24.667 0 47.834 -3.83301 69.501 -11.5s41.5 -16.167 59.5 -25.5l49.5 -25.5c15 -7.66699 28.167 -11.5 39.5 -11.5z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" 
d="M582 1053c78.667 0 150.334 -12.333 215.001 -37s120.167 -60 166.5 -106s82.333 -101.833 108 -167.5s38.5 -139.5 38.5 -221.5c0 -82.667 -12.833 -157.167 -38.5 -223.5s-61.667 -122.666 -108 -168.999s-101.833 -82 -166.5 -107s-136.334 -37.5 -215.001 -37.5
c-79.333 0 -151.5 12.5 -216.5 37.5s-121 60.667 -168 107s-83.333 102.666 -109 168.999s-38.5 140.833 -38.5 223.5c0 82 12.833 155.833 38.5 221.5s62 121.5 109 167.5s103 81.333 168 106s137.167 37 216.5 37zM582 214c71.333 0 123.833 25.167 157.5 75.5
s50.5 126.833 50.5 229.5s-16.833 179 -50.5 229s-86.167 75 -157.5 75c-73.333 0 -127 -25 -161 -75s-51 -126.333 -51 -229s17 -179.167 51 -229.5s87.667 -75.5 161 -75.5zM504 1473c31.333 0 54.668 -5.16699 70.001 -15.5s28.666 -25.166 39.999 -44.499l136 -245h-176
c-23.333 0 -42 3.16699 -56 9.5s-28.667 17.166 -44 32.499l-260 263h290z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" 
d="M582 1053c78.667 0 150.334 -12.333 215.001 -37s120.167 -60 166.5 -106s82.333 -101.833 108 -167.5s38.5 -139.5 38.5 -221.5c0 -82.667 -12.833 -157.167 -38.5 -223.5s-61.667 -122.666 -108 -168.999s-101.833 -82 -166.5 -107s-136.334 -37.5 -215.001 -37.5
c-79.333 0 -151.5 12.5 -216.5 37.5s-121 60.667 -168 107s-83.333 102.666 -109 168.999s-38.5 140.833 -38.5 223.5c0 82 12.833 155.833 38.5 221.5s62 121.5 109 167.5s103 81.333 168 106s137.167 37 216.5 37zM582 214c71.333 0 123.833 25.167 157.5 75.5
s50.5 126.833 50.5 229.5s-16.833 179 -50.5 229s-86.167 75 -157.5 75c-73.333 0 -127 -25 -161 -75s-51 -126.333 -51 -229s17 -179.167 51 -229.5s87.667 -75.5 161 -75.5zM978 1473l-259.998 -263c-15.333 -15.333 -30 -26.166 -44 -32.499s-32.667 -9.5 -56 -9.5h-186
l136 245c11.333 19.333 24.666 34.166 39.999 44.499s38.666 15.5 69.999 15.5h300z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" 
d="M582 1053c78.667 0 150.334 -12.333 215.001 -37s120.167 -60 166.5 -106s82.333 -101.833 108 -167.5s38.5 -139.5 38.5 -221.5c0 -82.667 -12.833 -157.167 -38.5 -223.5s-61.667 -122.666 -108 -168.999s-101.833 -82 -166.5 -107s-136.334 -37.5 -215.001 -37.5
c-79.333 0 -151.5 12.5 -216.5 37.5s-121 60.667 -168 107s-83.333 102.666 -109 168.999s-38.5 140.833 -38.5 223.5c0 82 12.833 155.833 38.5 221.5s62 121.5 109 167.5s103 81.333 168 106s137.167 37 216.5 37zM582 214c71.333 0 123.833 25.167 157.5 75.5
s50.5 126.833 50.5 229.5s-16.833 179 -50.5 229s-86.167 75 -157.5 75c-73.333 0 -127 -25 -161 -75s-51 -126.333 -51 -229s17 -179.167 51 -229.5s87.667 -75.5 161 -75.5zM962 1168h-206c-12 0 -23 1.83301 -33 5.5s-19 8.5 -27 14.5l-84 72
c-4.66699 3.33301 -9 7 -13 11s-8 8.66699 -12 14c-4 -5.33301 -8.16699 -10 -12.5 -14s-8.5 -7.66699 -12.5 -11l-86 -72c-6 -5.33301 -14.5 -10 -25.5 -14s-22.5 -6 -34.5 -6h-214l234 289h292z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" 
d="M582 1053c78.667 0 150.334 -12.333 215.001 -37s120.167 -60 166.5 -106s82.333 -101.833 108 -167.5s38.5 -139.5 38.5 -221.5c0 -82.667 -12.833 -157.167 -38.5 -223.5s-61.667 -122.666 -108 -168.999s-101.833 -82 -166.5 -107s-136.334 -37.5 -215.001 -37.5
c-79.333 0 -151.5 12.5 -216.5 37.5s-121 60.667 -168 107s-83.333 102.666 -109 168.999s-38.5 140.833 -38.5 223.5c0 82 12.833 155.833 38.5 221.5s62 121.5 109 167.5s103 81.333 168 106s137.167 37 216.5 37zM582 214c71.333 0 123.833 25.167 157.5 75.5
s50.5 126.833 50.5 229.5s-16.833 179 -50.5 229s-86.167 75 -157.5 75c-73.333 0 -127 -25 -161 -75s-51 -126.333 -51 -229s17 -179.167 51 -229.5s87.667 -75.5 161 -75.5zM686 1387c16 0 28.999 4.16992 38.999 12.5029s15 26.166 15 53.499h188
c0 -42 -5.83301 -79.833 -17.5 -113.5s-27.834 -62.334 -48.501 -86.001s-45 -41.834 -73 -54.501s-58.333 -19 -91 -19c-24.667 0 -47.667 3.83301 -69 11.5s-41.166 16.167 -59.499 25.5l-50 25.5c-15 7.66699 -28.167 11.5 -39.5 11.5c-16 0 -28.667 -4.5 -38 -13.5
s-14 -27.167 -14 -54.5h-190c0 42 6 79.833 18 113.5s28.333 62.5 49 86.5s45 42.5 73 55.5s58 19.5 90 19.5c24.667 0 47.834 -3.83301 69.501 -11.5s41.5 -16.167 59.5 -25.5l49.5 -25.5c15 -7.66699 28.167 -11.5 39.5 -11.5z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" 
d="M582 1053c78.667 0 150.334 -12.333 215.001 -37s120.167 -60 166.5 -106s82.333 -101.833 108 -167.5s38.5 -139.5 38.5 -221.5c0 -82.667 -12.833 -157.167 -38.5 -223.5s-61.667 -122.666 -108 -168.999s-101.833 -82 -166.5 -107s-136.334 -37.5 -215.001 -37.5
c-79.333 0 -151.5 12.5 -216.5 37.5s-121 60.667 -168 107s-83.333 102.666 -109 168.999s-38.5 140.833 -38.5 223.5c0 82 12.833 155.833 38.5 221.5s62 121.5 109 167.5s103 81.333 168 106s137.167 37 216.5 37zM582 214c71.333 0 123.833 25.167 157.5 75.5
s50.5 126.833 50.5 229.5s-16.833 179 -50.5 229s-86.167 75 -157.5 75c-73.333 0 -127 -25 -161 -75s-51 -126.333 -51 -229s17 -179.167 51 -229.5s87.667 -75.5 161 -75.5zM550 1295c0 -21.333 -4.33301 -41.333 -13 -60s-20.5 -34.834 -35.5 -48.501
s-32.333 -24.5 -52 -32.5s-40.834 -12 -63.501 -12c-21.333 0 -41.5 4 -60.5 12s-35.667 18.833 -50 32.5s-25.833 29.834 -34.5 48.501s-13 38.667 -13 60c0 22 4.33301 42.667 13 62s20.167 36.166 34.5 50.499s31 25.666 50 33.999s39.167 12.5 60.5 12.5
c22.667 0 43.834 -4.16699 63.501 -12.5s37 -19.666 52 -33.999s26.833 -31.166 35.5 -50.499s13 -40 13 -62zM936 1295c0 -21.333 -4.16699 -41.333 -12.5 -60s-19.833 -34.834 -34.5 -48.501s-31.834 -24.5 -51.501 -32.5s-40.834 -12 -63.501 -12c-22 0 -42.667 4 -62 12
s-36.333 18.833 -51 32.5s-26.167 29.834 -34.5 48.501s-12.5 38.667 -12.5 60c0 22 4.16699 42.667 12.5 62s19.833 36.166 34.5 50.499s31.667 25.666 51 33.999s40 12.5 62 12.5c22.667 0 43.834 -4.16699 63.501 -12.5s36.834 -19.666 51.501 -33.999
s26.167 -31.166 34.5 -50.499s12.5 -40 12.5 -62z" />
    <glyph glyph-name="divide" unicode="&#xf7;" 
d="M84 783h990v-222h-990v222zM402 1028c0 24 4.5 46.333 13.5 67s21.333 38.667 37 54s34.167 27.5 55.5 36.5s44.666 13.5 69.999 13.5c24.667 0 47.667 -4.5 69 -13.5s40 -21.167 56 -36.5s28.5 -33.333 37.5 -54s13.5 -43 13.5 -67s-4.5 -46.5 -13.5 -67.5
s-21.5 -39.167 -37.5 -54.5s-34.667 -27.333 -56 -36s-44.333 -13 -69 -13c-25.333 0 -48.666 4.33301 -69.999 13s-39.833 20.667 -55.5 36s-28 33.5 -37 54.5s-13.5 43.5 -13.5 67.5zM402 316c0 24 4.5 46.333 13.5 67s21.333 38.667 37 54s34.167 27.5 55.5 36.5
s44.666 13.5 69.999 13.5c24.667 0 47.667 -4.5 69 -13.5s40 -21.167 56 -36.5s28.5 -33.333 37.5 -54s13.5 -43 13.5 -67s-4.5 -46.5 -13.5 -67.5s-21.5 -39.167 -37.5 -54.5s-34.667 -27.333 -56 -36s-44.333 -13 -69 -13c-25.333 0 -48.666 4.33301 -69.999 13
s-39.833 20.667 -55.5 36s-28 33.5 -37 54.5s-13.5 43.5 -13.5 67.5z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" 
d="M974 899c43.333 -46 76.8311 -100.833 100.498 -164.5s35.5 -134.834 35.5 -213.501c0 -82.667 -12.833 -157.167 -38.5 -223.5s-61.667 -122.666 -108 -168.999s-101.833 -82 -166.5 -107s-136.334 -37.5 -215.001 -37.5c-90 0 -171.667 16.333 -245 49l-17 -24
c-11.333 -16.667 -24.166 -30.5 -38.499 -41.5s-29.666 -19.833 -45.999 -26.5s-33 -11.5 -50 -14.5s-33.5 -4.5 -49.5 -4.5h-112l161 219c-43.333 46 -76.666 101 -99.999 165s-35 135.667 -35 215c0 82 12.833 155.833 38.5 221.5s62 121.5 109 167.5s103 81.333 168 106
s137.167 37 216.5 37c88 0 167.333 -15.667 238 -47l46 62c10.667 14 20.5 26 29.5 36s18.5 18.167 28.5 24.5s21.333 10.833 34 13.5s28 4 46 4h152zM581.998 205c71.333 0 127.166 26.667 167.499 80s60.5 131.333 60.5 234c0 52 -4.66699 97.667 -14 137l-318 -431
c28.667 -13.333 63.334 -20 104.001 -20zM349.998 519c0 -53.333 4.33203 -98.333 12.999 -135l316 430c-27.333 12.667 -59.666 19 -96.999 19c-73.333 0 -130.333 -26.667 -171 -80s-61 -131.333 -61 -234z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" 
d="M410 1037l0.000976562 -658c0 -51.333 11.667 -91 35 -119s57.666 -42 102.999 -42c34 0 65.833 7 95.5 21s58.5 33.667 86.5 59v739h310v-1037h-192c-38.667 -0 -64 17.333 -76 52l-19 60c-20 -19.333 -40.667 -36.833 -62 -52.5s-44.166 -29 -68.499 -40
s-50.5 -19.667 -78.5 -26s-58.667 -9.5 -92 -9.5c-56.667 0 -106.834 9.83301 -150.501 29.5s-80.5 47.167 -110.5 82.5s-52.667 77 -68 125s-23 100.667 -23 158v658h310zM500.001 1473c31.333 0 54.668 -5.16699 70.001 -15.5s28.666 -25.166 39.999 -44.499l136 -245
h-176c-23.333 0 -42 3.16699 -56 9.5s-28.667 17.166 -44 32.499l-260 263h290z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" 
d="M410 1037l0.000976562 -658c0 -51.333 11.667 -91 35 -119s57.666 -42 102.999 -42c34 0 65.833 7 95.5 21s58.5 33.667 86.5 59v739h310v-1037h-192c-38.667 -0 -64 17.333 -76 52l-19 60c-20 -19.333 -40.667 -36.833 -62 -52.5s-44.166 -29 -68.499 -40
s-50.5 -19.667 -78.5 -26s-58.667 -9.5 -92 -9.5c-56.667 0 -106.834 9.83301 -150.501 29.5s-80.5 47.167 -110.5 82.5s-52.667 77 -68 125s-23 100.667 -23 158v658h310zM974.001 1473l-259.998 -263c-15.333 -15.333 -30 -26.166 -44 -32.499s-32.667 -9.5 -56 -9.5h-186
l136 245c11.333 19.333 24.666 34.166 39.999 44.499s38.666 15.5 69.999 15.5h300z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" 
d="M410 1037l0.000976562 -658c0 -51.333 11.667 -91 35 -119s57.666 -42 102.999 -42c34 0 65.833 7 95.5 21s58.5 33.667 86.5 59v739h310v-1037h-192c-38.667 -0 -64 17.333 -76 52l-19 60c-20 -19.333 -40.667 -36.833 -62 -52.5s-44.166 -29 -68.499 -40
s-50.5 -19.667 -78.5 -26s-58.667 -9.5 -92 -9.5c-56.667 0 -106.834 9.83301 -150.501 29.5s-80.5 47.167 -110.5 82.5s-52.667 77 -68 125s-23 100.667 -23 158v658h310zM958.001 1168h-206c-12 0 -23 1.83301 -33 5.5s-19 8.5 -27 14.5l-84 72
c-4.66699 3.33301 -9 7 -13 11s-8 8.66699 -12 14c-4 -5.33301 -8.16699 -10 -12.5 -14s-8.5 -7.66699 -12.5 -11l-86 -72c-6 -5.33301 -14.5 -10 -25.5 -14s-22.5 -6 -34.5 -6h-214l234 289h292z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" 
d="M410 1037l0.000976562 -658c0 -51.333 11.667 -91 35 -119s57.666 -42 102.999 -42c34 0 65.833 7 95.5 21s58.5 33.667 86.5 59v739h310v-1037h-192c-38.667 -0 -64 17.333 -76 52l-19 60c-20 -19.333 -40.667 -36.833 -62 -52.5s-44.166 -29 -68.499 -40
s-50.5 -19.667 -78.5 -26s-58.667 -9.5 -92 -9.5c-56.667 0 -106.834 9.83301 -150.501 29.5s-80.5 47.167 -110.5 82.5s-52.667 77 -68 125s-23 100.667 -23 158v658h310zM546.001 1295c0 -21.333 -4.33301 -41.333 -13 -60s-20.5 -34.834 -35.5 -48.501
s-32.333 -24.5 -52 -32.5s-40.834 -12 -63.501 -12c-21.333 0 -41.5 4 -60.5 12s-35.667 18.833 -50 32.5s-25.833 29.834 -34.5 48.501s-13 38.667 -13 60c0 22 4.33301 42.667 13 62s20.167 36.166 34.5 50.499s31 25.666 50 33.999s39.167 12.5 60.5 12.5
c22.667 0 43.834 -4.16699 63.501 -12.5s37 -19.666 52 -33.999s26.833 -31.166 35.5 -50.499s13 -40 13 -62zM932.001 1295c0 -21.333 -4.16699 -41.333 -12.5 -60s-19.833 -34.834 -34.5 -48.501s-31.834 -24.5 -51.501 -32.5s-40.834 -12 -63.501 -12
c-22 0 -42.667 4 -62 12s-36.333 18.833 -51 32.5s-26.167 29.834 -34.5 48.501s-12.5 38.667 -12.5 60c0 22 4.16699 42.667 12.5 62s19.833 36.166 34.5 50.499s31.667 25.666 51 33.999s40 12.5 62 12.5c22.667 0 43.834 -4.16699 63.501 -12.5
s36.834 -19.666 51.501 -33.999s26.167 -31.166 34.5 -50.499s12.5 -40 12.5 -62z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="1106" 
d="M544 -269c-9.33301 -20.667 -20.667 -35.832 -34 -45.499s-34.666 -14.5 -63.999 -14.5h-232l200 420l-414 946h274c24 0 42.667 -5.33301 56 -16s22.666 -23.334 27.999 -38.001l170 -447c16 -41.333 29 -82.666 39 -123.999c6.66699 21.333 14 42.333 22 63
s15.667 41.667 23 63l154 445c5.33301 15.333 15.833 28.166 31.5 38.499s32.5 15.5 50.5 15.5h250zM965.999 1473l-259.998 -263c-15.333 -15.333 -30 -26.166 -44 -32.499s-32.667 -9.5 -56 -9.5h-186l136 245c11.333 19.333 24.666 34.166 39.999 44.499
s38.666 15.5 69.999 15.5h300z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="1156" 
d="M120 -329v1826h310v-570c38.667 38 82.5 68.5 131.5 91.5s106.5 34.5 172.5 34.5c54 0 104 -11.667 150 -35s85.667 -57.5 119 -102.5s59.5 -100.167 78.5 -165.5s28.5 -139.666 28.5 -222.999c0 -78 -10.833 -150 -32.5 -216s-52 -123.333 -91 -172
s-85.667 -86.667 -140 -114s-114.5 -41 -180.5 -41c-27.333 0 -52.333 2.5 -75 7.5s-43.5 12 -62.5 21s-36.5 19.833 -52.5 32.5s-31.333 26.667 -46 42v-416h-310zM626 819.999c-22.667 0 -43.167 -2.16699 -61.5 -6.5s-35.333 -10.5 -51 -18.5
s-30.167 -18.167 -43.5 -30.5s-26.666 -26.5 -39.999 -42.5v-440c23.333 -26 48.666 -44.167 75.999 -54.5s56 -15.5 86 -15.5c28.667 0 55 5.66699 79 17s44.833 29.5 62.5 54.5s31.5 57.5 41.5 97.5s15 88.667 15 146c0 54.667 -4 100.667 -12 138
s-19.167 67.5 -33.5 90.5s-31.5 39.5 -51.5 49.5s-42.333 15 -67 15z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="1106" 
d="M544 -269c-9.33301 -20.667 -20.667 -35.832 -34 -45.499s-34.666 -14.5 -63.999 -14.5h-232l200 420l-414 946h274c24 0 42.667 -5.33301 56 -16s22.666 -23.334 27.999 -38.001l170 -447c16 -41.333 29 -82.666 39 -123.999c6.66699 21.333 14 42.333 22 63
s15.667 41.667 23 63l154 445c5.33301 15.333 15.833 28.166 31.5 38.499s32.5 15.5 50.5 15.5h250zM537.999 1295c0 -21.333 -4.33301 -41.333 -13 -60s-20.5 -34.834 -35.5 -48.501s-32.333 -24.5 -52 -32.5s-40.834 -12 -63.501 -12c-21.333 0 -41.5 4 -60.5 12
s-35.667 18.833 -50 32.5s-25.833 29.834 -34.5 48.501s-13 38.667 -13 60c0 22 4.33301 42.667 13 62s20.167 36.166 34.5 50.499s31 25.666 50 33.999s39.167 12.5 60.5 12.5c22.667 0 43.834 -4.16699 63.501 -12.5s37 -19.666 52 -33.999s26.833 -31.166 35.5 -50.499
s13 -40 13 -62zM923.999 1295c0 -21.333 -4.16699 -41.333 -12.5 -60s-19.833 -34.834 -34.5 -48.501s-31.834 -24.5 -51.501 -32.5s-40.834 -12 -63.501 -12c-22 0 -42.667 4 -62 12s-36.333 18.833 -51 32.5s-26.167 29.834 -34.5 48.501s-12.5 38.667 -12.5 60
c0 22 4.16699 42.667 12.5 62s19.833 36.166 34.5 50.499s31.667 25.666 51 33.999s40 12.5 62 12.5c22.667 0 43.834 -4.16699 63.501 -12.5s36.834 -19.666 51.501 -33.999s26.167 -31.166 34.5 -50.499s12.5 -40 12.5 -62z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="1474" 
d="M1426 -167c14 0 23.333 -6.33301 28 -19l46 -111c-23.333 -14 -52.333 -25.833 -87 -35.5s-71.667 -14.5 -111 -14.5c-71.333 0 -125.5 14 -162.5 42s-55.5 63.667 -55.5 107c0 34 11.167 68 33.5 102s58.5 66 108.5 96h-12c-29.333 -0 -53.5 6.83301 -72.5 20.5
s-32.167 31.167 -39.5 52.5l-86 254h-554l-86 -254c-6.66699 -18.667 -19.667 -35.5 -39 -50.5s-43 -22.5 -71 -22.5h-264l564 1457h346l564 -1457h-68c-26 -14 -48.167 -31.5 -66.5 -52.5s-27.5 -44.167 -27.5 -69.5c0 -16 4.5 -28.833 13.5 -38.5s22.5 -14.5 40.5 -14.5
c10 0 18 0.333008 24 1s11 1.5 15 2.5s7.33301 2 10 3s5.66699 1.5 9 1.5zM540 557l397.999 0.00195312l-134 399c-10 25.333 -20.667 55.166 -32 89.499s-22.666 71.5 -33.999 111.5c-10.667 -40.667 -21.5 -78.334 -32.5 -113.001s-21.5 -64.667 -31.5 -90z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="1078" 
d="M996 -167c14 0 23.3301 -6.33398 27.9971 -19.001l46 -111c-23.333 -14 -52.333 -25.833 -87 -35.5s-71.667 -14.5 -111 -14.5c-71.333 0 -125.5 14 -162.5 42s-55.5 63.667 -55.5 107c0 34.667 11.667 69.167 35 103.5s60.333 66.5 111 96.5
c-17.333 2.66699 -31.166 8.5 -41.499 17.5s-19.166 23.167 -26.499 42.5l-22 57c-26 -22 -51 -41.333 -75 -58s-48.833 -30.834 -74.5 -42.501s-53 -20.334 -82 -26.001s-61.167 -8.5 -96.5 -8.5c-47.333 0 -90.333 6.16699 -129 18.5s-71.5 30.5 -98.5 54.5
s-47.833 53.833 -62.5 89.5s-22 76.834 -22 123.501c0 36.667 9.33301 74.334 28 113.001s51 73.834 97 105.501s108 58.167 186 79.5s176.333 33.333 295 36v44c0 60.667 -12.5 104.5 -37.5 131.5s-60.5 40.5 -106.5 40.5c-36.667 0 -66.667 -4 -90 -12
s-44.166 -17 -62.499 -27s-36.166 -19 -53.499 -27s-38 -12 -62 -12c-21.333 0 -39.5 5.16699 -54.5 15.5s-26.833 23.166 -35.5 38.499l-56 97c62.667 55.333 132.167 96.5 208.5 123.5s158.166 40.5 245.499 40.5c62.667 0 119.167 -10.167 169.5 -30.5
s93 -48.666 128 -84.999s61.833 -79.5 80.5 -129.5s28 -104.667 28 -164v-648c-26 -14 -48.167 -31.5 -66.5 -52.5s-27.5 -44.167 -27.5 -69.5c0 -16 4.5 -28.833 13.5 -38.5s22.5 -14.5 40.5 -14.5c10 0 18 0.333008 24 1s11 1.5 15 2.5s7.33301 2 10 3s5.66699 1.5 9 1.5z
M479.997 188.999c40 0 75.001 6.99902 105.001 20.999s60.333 36.667 91 68v144c-62 -2.66699 -113 -8 -153 -16s-71.667 -18 -95 -30s-39.5 -25.667 -48.5 -41s-13.5 -32 -13.5 -50c0 -35.333 9.66699 -60.166 29 -74.499s47.666 -21.5 84.999 -21.5z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="1314" 
d="M1099 377c8 0 15.999 -1.5 23.999 -4.5s15.667 -8.16699 23 -15.5l134 -141c-58.667 -76.667 -132.167 -134.5 -220.5 -173.5s-192.833 -58.5 -313.5 -58.5c-110.667 0 -209.834 18.833 -297.501 56.5s-162 89.667 -223 156s-107.833 145 -140.5 236
s-49 189.833 -49 296.5c0 108.667 18.667 208.5 56 299.5s89.5 169.5 156.5 235.5s147.167 117.333 240.5 154s196.333 55 309 55c54.667 0 106.167 -4.83301 154.5 -14.5s93.5 -23 135.5 -40s80.667 -37.333 116 -61s66.666 -49.834 93.999 -78.501l-114 -153
c-7.33301 -9.33301 -16 -17.833 -26 -25.5s-24 -11.5 -42 -11.5c-12 0 -23.333 2.66699 -34 8s-22 11.833 -34 19.5l-39.5 25c-14.333 9 -31.333 17.333 -51 25s-42.667 14.167 -69 19.5s-57.166 8 -92.499 8c-61.333 0 -117.333 -10.833 -168 -32.5
s-94.167 -52.667 -130.5 -93s-64.666 -89.166 -84.999 -146.499s-30.5 -121.666 -30.5 -192.999c0 -74.667 10.167 -141 30.5 -199s48 -106.833 83 -146.5s75.833 -69.834 122.5 -90.501s96.667 -31 150 -31c30.667 0 58.667 1.5 84 4.5s48.833 8.16699 70.5 15.5
s42.334 16.833 62.001 28.5s39.5 26.167 59.5 43.5c8 6.66699 16.667 12.167 26 16.5s19 6.5 29 6.5zM1210 1798l-294.997 -214.001c-17.333 -13.333 -33.5 -22.166 -48.5 -26.499s-33.833 -6.5 -56.5 -6.5h-246l181 197c8.66699 10 17.167 18.167 25.5 24.5
s17.166 11.5 26.499 15.5s19.666 6.66699 30.999 8s24.666 2 39.999 2h342z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="972" 
d="M868 791c-9.33301 -11.333 -18.3301 -20.335 -26.9971 -27.002s-21 -10 -37 -10c-15.333 0 -29.166 3.66699 -41.499 11l-41.5 24.5c-15.333 9 -33.333 17.167 -54 24.5s-46.334 11 -77.001 11c-38 0 -70.833 -7 -98.5 -21s-50.5 -34 -68.5 -60
s-31.333 -57.833 -40 -95.5s-13 -80.167 -13 -127.5c0 -99.333 19.167 -175.666 57.5 -228.999s91.166 -80 158.499 -80c36 0 64.5 4.5 85.5 13.5s38.833 19 53.5 30l40.5 30.5c12.333 9.33301 27.833 14 46.5 14c24.667 0 43.334 -9 56.001 -27l90 -111
c-32 -36.667 -65.667 -66.5 -101 -89.5s-71.5 -41 -108.5 -54s-74.167 -22 -111.5 -27s-73.666 -7.5 -108.999 -7.5c-63.333 0 -123.833 12 -181.5 36s-108.334 58.833 -152.001 104.5s-78.334 101.834 -104.001 168.501s-38.5 142.667 -38.5 228
c0 74.667 11.167 144.5 33.5 209.5s55.333 121.333 99 169s97.667 85.167 162 112.5s138.833 41 223.5 41c81.333 0 152.666 -13 213.999 -39s116.666 -64 165.999 -114zM962.003 1473l-259.998 -263c-15.333 -15.333 -30 -26.166 -44 -32.499s-32.667 -9.5 -56 -9.5h-186
l136 245c11.333 19.333 24.666 34.166 39.999 44.499s38.666 15.5 69.999 15.5h300z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="1130" 
d="M984 -167c14 0 23.333 -6.33301 28 -19l46 -111c-23.333 -14 -52.333 -25.833 -87 -35.5s-71.667 -14.5 -111 -14.5c-71.333 0 -125.5 14 -162.5 42s-55.5 63.667 -55.5 107c0 34 11.167 68 33.5 102s58.5 66 108.5 96h-664v1457h938v-260h-598v-340h458v-250h-458v-347
h598v-260h-92c-26 -14 -48.167 -31.5 -66.5 -52.5s-27.5 -44.167 -27.5 -69.5c0 -16 4.5 -28.833 13.5 -38.5s22.5 -14.5 40.5 -14.5c10 0 18 0.333008 24 1s11 1.5 15 2.5s7.33301 2 10 3s5.66699 1.5 9 1.5z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="1088" 
d="M744 -167c14 0 23.3291 -6.33105 27.9961 -18.998l46 -111c-23.333 -14 -52.333 -25.833 -87 -35.5s-71.667 -14.5 -111 -14.5c-71.333 0 -125.5 14 -162.5 42s-55.5 63.667 -55.5 107c0 32 9.66699 63.833 29 95.5s50 61.5 92 89.5
c-66 5.33301 -127.667 21.166 -185 47.499s-107.166 62.666 -149.499 108.999s-75.666 102.666 -99.999 168.999s-36.5 142.5 -36.5 228.5c0 69.333 11.833 135 35.5 197s57.667 116.333 102 163s98 83.667 161 111s134.167 41 213.5 41c68.667 0 131.5 -10.667 188.5 -32
s106 -52.333 147 -93s73 -90.5 96 -149.5s34.5 -125.833 34.5 -200.5c0 -23.333 -1 -42.333 -3 -57s-5.66699 -26.334 -11 -35.001s-12.5 -14.667 -21.5 -18s-20.5 -5 -34.5 -5h-598c10 -86.667 36.333 -149.5 79 -188.5s97.667 -58.5 165 -58.5c36 0 67 4.33301 93 13
s49.167 18.334 69.5 29.001l56 29c17 8.66699 34.833 13 53.5 13c24.667 0 43.334 -9 56.001 -27l90 -111c-44 -50.667 -92.667 -88.667 -146 -114s-106.666 -42.666 -159.999 -51.999c-24 -14 -44.333 -31 -61 -51s-25 -42.333 -25 -67c0 -16 4.5 -28.833 13.5 -38.5
s22.5 -14.5 40.5 -14.5c10 0 18 0.333008 24 1s11 1.5 15 2.5s7.33301 2 10 3s5.66699 1.5 9 1.5zM569.996 839.002c-59.333 0 -105.67 -16.8311 -139.003 -50.498s-55.333 -82.167 -66 -145.5h387c0 24.667 -3.16699 48.667 -9.5 72s-16.666 44.166 -30.999 62.499
s-33.166 33.166 -56.499 44.499s-51.666 17 -84.999 17z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="570" 
d="M440 1037v-1037h-310v1037h310z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="1116" 
d="M536 923l334 165v-221c0 -32 -13.333 -52.667 -40 -62l-294 -152v-383h540v-270h-878v505l-174 -85v228c0 26 12 44 36 54l138 71v684h338v-534z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="710" 
d="M510 1497v-546l150 59v-174c0 -30 -13.333 -50.667 -40 -62l-110 -46v-728h-310v624l-150 -58v180c0 26 12 44 36 54l114 47v650h310z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="1512" 
d="M298 1457c14.667 0 27.001 -0.666992 37.001 -2s19 -3.83301 27 -7.5s15.667 -8.83398 23 -15.501s15.666 -15.667 24.999 -27l692 -875c-2.66699 28 -4.66699 55.167 -6 81.5s-2 51.166 -2 74.499v771h298v-1457h-176c-26 0 -48 4 -66 12s-35.333 22.667 -52 44
l-687 868c2 -25.333 3.66699 -50.166 5 -74.499s2 -47.166 2 -68.499v-781h-298v1457h178v0zM1188 1798l-294.997 -214.001c-17.333 -13.333 -33.5 -22.166 -48.5 -26.499s-33.833 -6.5 -56.5 -6.5h-246l181 197c8.66699 10 17.167 18.167 25.5 24.5
s17.166 11.5 26.499 15.5s19.666 6.66699 30.999 8s24.666 2 39.999 2h342z" />
    <glyph glyph-name="nacute" unicode="&#x144;" 
d="M120 0l0.000976562 1037h192c19.333 0 35.5 -4.33301 48.5 -13s22.167 -21.667 27.5 -39l18 -60c20 18.667 40.667 36 62 52s44.166 29.5 68.499 40.5s50.666 19.667 78.999 26s59.166 9.5 92.499 9.5c56.667 0 106.834 -9.83301 150.501 -29.5s80.5 -47 110.5 -82
s52.667 -76.667 68 -125s23 -100.833 23 -157.5v-659h-310v659c0 50.667 -11.667 90.167 -35 118.5s-57.666 42.5 -102.999 42.5c-34 0 -66 -7.33301 -96 -22s-58.667 -34.334 -86 -59.001v-739h-310zM994.001 1473l-259.998 -263c-15.333 -15.333 -30 -26.166 -44 -32.499
s-32.667 -9.5 -56 -9.5h-186l136 245c11.333 19.333 24.666 34.166 39.999 44.499s38.666 15.5 69.999 15.5h300z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="2136" 
d="M2064 1457v-260.001h-598v-340h458v-250h-458v-347h598v-260h-898v148c-57.333 -52 -122.833 -92.333 -196.5 -121s-154.834 -43 -243.501 -43c-102.667 0 -195.834 19 -279.501 57s-155.334 90.5 -215.001 157.5s-105.834 145.833 -138.501 236.5s-49 188.667 -49 294
s16.333 203.333 49 294s78.834 169.334 138.501 236.001s131.334 119 215.001 157s176.834 57 279.501 57c88.667 0 169.834 -14.5 243.501 -43.5s139.167 -69.5 196.5 -121.5v149h898zM1126 728.999c0 72 -8.16699 137.333 -24.5 196s-40.166 108.5 -71.499 149.5
s-69.666 72.667 -114.999 95s-97 33.5 -155 33.5c-59.333 0 -111.833 -11.167 -157.5 -33.5s-84.334 -54 -116.001 -95s-55.667 -90.833 -72 -149.5s-24.5 -124 -24.5 -196c0 -72.667 8.16699 -138.334 24.5 -197.001s40.333 -108.5 72 -149.5s70.334 -72.667 116.001 -95
s98.167 -33.5 157.5 -33.5c58 0 109.667 11.167 155 33.5s83.666 54 114.999 95s55.166 90.833 71.499 149.5s24.5 124.334 24.5 197.001z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="1762" 
d="M1274 1053c61.333 0 118.335 -11.666 171.002 -34.999s98.167 -56.5 136.5 -99.5s68.333 -95 90 -156s32.5 -129.167 32.5 -204.5c0 -23.333 -1 -42.333 -3 -57s-5.5 -26.334 -10.5 -35.001s-11.833 -14.667 -20.5 -18s-20 -5 -34 -5h-570
c11.333 -79.333 37.166 -137 77.499 -173s91.166 -54 152.499 -54c34 0 63.167 4.33301 87.5 13s46 18.334 65 29.001l53.5 29c16.667 8.66699 34.667 13 54 13c11.333 0 21.5 -2.5 30.5 -7.5s16.833 -11.5 23.5 -19.5l92 -111c-32 -36.667 -66.167 -66.5 -102.5 -89.5
s-73.5 -41 -111.5 -54s-75.833 -22 -113.5 -27s-73.834 -7.5 -108.501 -7.5c-34 0 -67.333 3.5 -100 10.5s-64.167 17.833 -94.5 32.5s-59 33.167 -86 55.5s-51.167 48.833 -72.5 79.5c-42 -57.333 -95 -101.333 -159 -132s-137.333 -46 -220 -46
c-68.667 0 -132.5 12.5 -191.5 37.5s-110.167 60.667 -153.5 107s-77.333 102.666 -102 168.999s-37 140.833 -37 223.5c0 82 12.5 155.833 37.5 221.5s59.5 121.5 103.5 167.5s96.333 81.333 157 106s126.667 37 198 37c78.667 0 148.834 -15 210.501 -45
s113.5 -73.333 155.5 -130c38.667 54 88.167 96.667 148.5 128s131.5 47 213.5 47zM564.002 214.001c66.667 0 115.334 25.167 146.001 75.5s46 126.833 46 229.5s-15.333 179 -46 229s-79.334 75 -146.001 75c-67.333 0 -116.5 -25 -147.5 -75s-46.5 -126.333 -46.5 -229
s15.5 -179.167 46.5 -229.5s80.167 -75.5 147.5 -75.5zM1260 839.001c-58.667 0 -103.832 -18.502 -135.499 -55.502s-51.834 -90.5 -60.501 -160.5h364c0 24.667 -2.83301 49.834 -8.5 75.501s-15 48.834 -28 69.501s-30.333 37.667 -52 51s-48.167 20 -79.5 20z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="1118" 
d="M932 1161c-10 -16 -20.4941 -28.001 -31.4941 -36.001s-25.167 -12 -42.5 -12c-15.333 0 -31.833 4.83301 -49.5 14.5l-60 32.5c-22.333 12 -47.833 22.833 -76.5 32.5s-61.334 14.5 -98.001 14.5c-63.333 0 -110.5 -13.5 -141.5 -40.5s-46.5 -63.5 -46.5 -109.5
c0 -29.333 9.33301 -53.666 28 -72.999s43.167 -36 73.5 -50s65 -26.833 104 -38.5s78.833 -24.667 119.5 -39s80.5 -31.166 119.5 -50.499s73.667 -44 104 -74s54.833 -66.5 73.5 -109.5s28 -94.833 28 -155.5c0 -67.333 -11.667 -130.333 -35 -189
s-57.166 -109.834 -101.499 -153.501s-99 -78 -164 -103s-138.833 -37.5 -221.5 -37.5c-45.333 0 -91.5 4.66699 -138.5 14s-92.5 22.5 -136.5 39.5s-85.333 37.167 -124 60.5s-72.334 49.333 -101.001 78l100 158c7.33301 12 17.666 21.667 30.999 29s27.666 11 42.999 11
c20 0 40.167 -6.33301 60.5 -19l69 -42c25.667 -15.333 55.167 -29.333 88.5 -42s72.666 -19 117.999 -19c61.333 0 109 13.5 143 40.5s51 69.833 51 128.5c0 34 -9.33301 61.667 -28 83s-43.167 39 -73.5 53s-64.833 26.333 -103.5 37s-78.334 22.5 -119.001 35.5
s-80.334 29 -119.001 48s-73.167 44 -103.5 75s-54.833 69.667 -73.5 116s-28 103.5 -28 171.5c0 54.667 11 108 33 160s54.333 98.333 97 139s95 73.167 157 97.5s133 36.5 213 36.5c44.667 0 88.167 -3.5 130.5 -10.5s82.5 -17.333 120.5 -31s73.5 -30 106.5 -49
s62.5 -40.5 88.5 -64.5zM1028.01 1798l-294.997 -214.001c-17.333 -13.333 -33.5 -22.166 -48.5 -26.499s-33.833 -6.5 -56.5 -6.5h-246l181 197c8.66699 10 17.167 18.167 25.5 24.5s17.166 11.5 26.499 15.5s19.666 6.66699 30.999 8s24.666 2 39.999 2h342z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="880" 
d="M754 810c-8 -12.667 -16.5039 -21.667 -25.5039 -27s-20.5 -8 -34.5 -8c-14.667 0 -29.334 3.16699 -44.001 9.5l-47.5 20.5c-17 7.33301 -36.167 14.166 -57.5 20.499s-45.666 9.5 -72.999 9.5c-39.333 0 -69.5 -7.5 -90.5 -22.5s-31.5 -35.833 -31.5 -62.5
c0 -19.333 6.83301 -35.333 20.5 -48s31.834 -23.834 54.501 -33.501s48.334 -18.834 77.001 -27.501s58 -18.334 88 -29.001s59.333 -23.167 88 -37.5s54.334 -32 77.001 -53s40.834 -46.5 54.501 -76.5s20.5 -66 20.5 -108c0 -51.333 -9.33301 -98.666 -28 -141.999
s-46.5 -80.666 -83.5 -111.999s-82.667 -55.666 -137 -72.999s-116.833 -26 -187.5 -26c-35.333 0 -70.666 3.33301 -105.999 10s-69.5 15.667 -102.5 27s-63.5 24.833 -91.5 40.5s-52 32.5 -72 50.5l72 114c8.66699 13.333 19 23.833 31 31.5s27.667 11.5 47 11.5
c18 0 34.167 -4 48.5 -12l46.5 -26c16.667 -9.33301 36.167 -18 58.5 -26s50.5 -12 84.5 -12c24 0 44.333 2.5 61 7.5s30 11.833 40 20.5s17.333 18.334 22 29.001s7 21.667 7 33c0 20.667 -7 37.667 -21 51s-32.333 24.833 -55 34.5s-48.5 18.667 -77.5 27
s-58.5 17.833 -88.5 28.5s-59.5 23.5 -88.5 38.5s-54.833 34 -77.5 57s-41 51.167 -55 84.5s-21 73.666 -21 120.999c0 44 8.5 85.833 25.5 125.5s42.5 74.5 76.5 104.5s76.667 53.833 128 71.5s111.333 26.5 180 26.5c37.333 0 73.5 -3.33301 108.5 -10
s67.833 -16 98.5 -28s58.667 -26.167 84 -42.5s47.666 -34.166 66.999 -53.499zM859.996 1473l-259.998 -263c-15.333 -15.333 -30 -26.166 -44 -32.499s-32.667 -9.5 -56 -9.5h-186l136 245c11.333 19.333 24.666 34.166 39.999 44.499s38.666 15.5 69.999 15.5h300z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="1084" 
d="M932 1161c-10 -16 -20.4941 -28.001 -31.4941 -36.001s-25.167 -12 -42.5 -12c-15.333 0 -31.833 4.83301 -49.5 14.5l-60 32.5c-22.333 12 -47.833 22.833 -76.5 32.5s-61.334 14.5 -98.001 14.5c-63.333 0 -110.5 -13.5 -141.5 -40.5s-46.5 -63.5 -46.5 -109.5
c0 -29.333 9.33301 -53.666 28 -72.999s43.167 -36 73.5 -50s65 -26.833 104 -38.5s78.833 -24.667 119.5 -39s80.5 -31.166 119.5 -50.499s73.667 -44 104 -74s54.833 -66.5 73.5 -109.5s28 -94.833 28 -155.5c0 -67.333 -11.667 -130.333 -35 -189
s-57.166 -109.834 -101.499 -153.501s-99 -78 -164 -103s-138.833 -37.5 -221.5 -37.5c-45.333 0 -91.5 4.66699 -138.5 14s-92.5 22.5 -136.5 39.5s-85.333 37.167 -124 60.5s-72.334 49.333 -101.001 78l100 158c7.33301 12 17.666 21.667 30.999 29s27.666 11 42.999 11
c20 0 40.167 -6.33301 60.5 -19l69 -42c25.667 -15.333 55.167 -29.333 88.5 -42s72.666 -19 117.999 -19c61.333 0 109 13.5 143 40.5s51 69.833 51 128.5c0 34 -9.33301 61.667 -28 83s-43.167 39 -73.5 53s-64.833 26.333 -103.5 37s-78.334 22.5 -119.001 35.5
s-80.334 29 -119.001 48s-73.167 44 -103.5 75s-54.833 69.667 -73.5 116s-28 103.5 -28 171.5c0 54.667 11 108 33 160s54.333 98.333 97 139s95 73.167 157 97.5s133 36.5 213 36.5c44.667 0 88.167 -3.5 130.5 -10.5s82.5 -17.333 120.5 -31s73.5 -30 106.5 -49
s62.5 -40.5 88.5 -64.5zM186.006 1778l234 0.000976562c12 0 25 -1.66699 39 -5s25.667 -7.66602 35 -12.999l74 -45l9 -5.5c2.66699 -1.66699 5.66699 -3.83398 9 -6.50098c2.66699 2 5.5 4 8.5 6s6.16699 4 9.5 6l74 45c9.33301 5.33301 21 9.66602 35 12.999s27 5 39 5
h234l-244 -227h-312z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="880" 
d="M754 810c-8 -12.667 -16.5039 -21.667 -25.5039 -27s-20.5 -8 -34.5 -8c-14.667 0 -29.334 3.16699 -44.001 9.5l-47.5 20.5c-17 7.33301 -36.167 14.166 -57.5 20.499s-45.666 9.5 -72.999 9.5c-39.333 0 -69.5 -7.5 -90.5 -22.5s-31.5 -35.833 -31.5 -62.5
c0 -19.333 6.83301 -35.333 20.5 -48s31.834 -23.834 54.501 -33.501s48.334 -18.834 77.001 -27.501s58 -18.334 88 -29.001s59.333 -23.167 88 -37.5s54.334 -32 77.001 -53s40.834 -46.5 54.501 -76.5s20.5 -66 20.5 -108c0 -51.333 -9.33301 -98.666 -28 -141.999
s-46.5 -80.666 -83.5 -111.999s-82.667 -55.666 -137 -72.999s-116.833 -26 -187.5 -26c-35.333 0 -70.666 3.33301 -105.999 10s-69.5 15.667 -102.5 27s-63.5 24.833 -91.5 40.5s-52 32.5 -72 50.5l72 114c8.66699 13.333 19 23.833 31 31.5s27.667 11.5 47 11.5
c18 0 34.167 -4 48.5 -12l46.5 -26c16.667 -9.33301 36.167 -18 58.5 -26s50.5 -12 84.5 -12c24 0 44.333 2.5 61 7.5s30 11.833 40 20.5s17.333 18.334 22 29.001s7 21.667 7 33c0 20.667 -7 37.667 -21 51s-32.333 24.833 -55 34.5s-48.5 18.667 -77.5 27
s-58.5 17.833 -88.5 28.5s-59.5 23.5 -88.5 38.5s-54.833 34 -77.5 57s-41 51.167 -55 84.5s-21 73.666 -21 120.999c0 44 8.5 85.833 25.5 125.5s42.5 74.5 76.5 104.5s76.667 53.833 128 71.5s111.333 26.5 180 26.5c37.333 0 73.5 -3.33301 108.5 -10
s67.833 -16 98.5 -28s58.667 -26.167 84 -42.5s47.666 -34.166 66.999 -53.499zM81.9961 1457h214c12 0 23.5 -1.83301 34.5 -5.5s19.5 -8.16699 25.5 -13.5l84 -72c4 -3.33301 8.33301 -7.33301 13 -12s9 -9.66699 13 -15c7.33301 10.667 16 19.667 26 27l84 72
c6 4 14.5 8.16699 25.5 12.5s22.5 6.5 34.5 6.5h206l-234 -289h-292z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="1354" 
d="M846 554l0.000976562 -554.002h-338v554l-528 903h298c29.333 0 52.666 -6.83301 69.999 -20.5s31.333 -31.167 42 -52.5l206 -424l46 -94c14 -28.667 26.333 -57 37 -85c10 28.667 22 57.334 36 86.001l45 93l204 424c4 8.66699 9.5 17.334 16.5 26.001s15 16.5 24 23.5
s19.333 12.667 31 17s24.5 6.5 38.5 6.5h300zM616.001 1668c0 -21.333 -4.16699 -41 -12.5 -59s-19.833 -33.833 -34.5 -47.5s-31.667 -24.334 -51 -32.001s-40 -11.5 -62 -11.5c-20 0 -39.167 3.83301 -57.5 11.5s-34.5 18.334 -48.5 32.001s-25.167 29.5 -33.5 47.5
s-12.5 37.667 -12.5 59c0 20.667 4.16699 40.334 12.5 59.001s19.5 35 33.5 49s30.167 25 48.5 33s37.5 12 57.5 12c22 0 42.667 -4 62 -12s36.333 -19 51 -33s26.167 -30.333 34.5 -49s12.5 -38.334 12.5 -59.001zM1056 1668c0 -21.333 -4.16699 -41 -12.5 -59
s-19.5 -33.833 -33.5 -47.5s-30.5 -24.334 -49.5 -32.001s-39.167 -11.5 -60.5 -11.5s-41.5 3.83301 -60.5 11.5s-35.5 18.334 -49.5 32.001s-25.167 29.5 -33.5 47.5s-12.5 37.667 -12.5 59c0 20.667 4.16699 40.334 12.5 59.001s19.5 35 33.5 49s30.5 25 49.5 33
s39.167 12 60.5 12s41.5 -4 60.5 -12s35.5 -19 49.5 -33s25.167 -30.333 33.5 -49s12.5 -38.334 12.5 -59.001z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="1220" 
d="M1186 1457l0.000976562 -121.999c0 -17.333 -2.83301 -34.333 -8.5 -51s-13.5 -32.334 -23.5 -47.001l-684 -977h694v-260h-1124v130c0 15.333 2.66699 30.5 8 45.5s12.666 28.833 21.999 41.5l686 980h-660v260h1090zM1068 1798l-294.997 -214.001
c-17.333 -13.333 -33.5 -22.166 -48.5 -26.499s-33.833 -6.5 -56.5 -6.5h-246l181 197c8.66699 10 17.167 18.167 25.5 24.5s17.166 11.5 26.499 15.5s19.666 6.66699 30.999 8s24.666 2 39.999 2h342z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="954" 
d="M894 913c0 -21.333 -4 -41.667 -12 -61s-17.333 -36 -28 -50l-433 -572h463v-230h-820v126c0 12.667 3.5 28.5 10.5 47.5s16.833 37.167 29.5 54.5l437 579h-449v230h802v-124zM904 1473l-259.998 -263c-15.333 -15.333 -30 -26.166 -44 -32.499s-32.667 -9.5 -56 -9.5
h-186l136 245c11.333 19.333 24.666 34.166 39.999 44.499s38.666 15.5 69.999 15.5h300z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="1220" 
d="M1186 1457l0.000976562 -121.999c0 -17.333 -2.83301 -34.333 -8.5 -51s-13.5 -32.334 -23.5 -47.001l-684 -977h694v-260h-1124v130c0 15.333 2.66699 30.5 8 45.5s12.666 28.833 21.999 41.5l686 980h-660v260h1090zM822.001 1701
c0 -22.667 -4.83301 -44.334 -14.5 -65.001s-22.667 -38.667 -39 -54s-35.666 -27.666 -57.999 -36.999s-45.833 -14 -70.5 -14c-23.333 0 -45.5 4.66699 -66.5 14s-39.5 21.666 -55.5 36.999s-28.667 33.333 -38 54s-14 42.334 -14 65.001c0 24 4.66699 46.5 14 67.5
s22 39.5 38 55.5s34.5 28.667 55.5 38s43.167 14 66.5 14c24.667 0 48.167 -4.66699 70.5 -14s41.666 -22 57.999 -38s29.333 -34.5 39 -55.5s14.5 -43.5 14.5 -67.5z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="954" 
d="M894 913c0 -21.333 -4 -41.667 -12 -61s-17.333 -36 -28 -50l-433 -572h463v-230h-820v126c0 12.667 3.5 28.5 10.5 47.5s16.833 37.167 29.5 54.5l437 579h-449v230h802v-124zM698 1322c0 -24.667 -5 -48 -15 -70s-23.667 -41.167 -41 -57.5
s-37.5 -29.166 -60.5 -38.499s-47.5 -14 -73.5 -14c-25.333 0 -49 4.66699 -71 14s-41.5 22.166 -58.5 38.499s-30.333 35.5 -40 57.5s-14.5 45.333 -14.5 70s4.83301 48.167 14.5 70.5s23 41.666 40 57.999s36.5 29.333 58.5 39s45.667 14.5 71 14.5
c26 0 50.5 -4.83301 73.5 -14.5s43.167 -22.667 60.5 -39s31 -35.666 41 -57.999s15 -45.833 15 -70.5z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="1220" 
d="M1186 1457l0.000976562 -121.999c0 -17.333 -2.83301 -34.333 -8.5 -51s-13.5 -32.334 -23.5 -47.001l-684 -977h694v-260h-1124v130c0 15.333 2.66699 30.5 8 45.5s12.666 28.833 21.999 41.5l686 980h-660v260h1090zM244.001 1777l234 0.000976562
c12 0 25 -1.66699 39 -5s25.667 -7.66602 35 -12.999l74 -45l9 -5.5c2.66699 -1.66699 5.66699 -3.83398 9 -6.50098c2.66699 2 5.5 4 8.5 6s6.16699 4 9.5 6l74 45c9.33301 5.33301 21 9.66602 35 12.999s27 5 39 5h234l-244 -227h-312z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="954" 
d="M894 913c0 -21.333 -4 -41.667 -12 -61s-17.333 -36 -28 -50l-433 -572h463v-230h-820v126c0 12.667 3.5 28.5 10.5 47.5s16.833 37.167 29.5 54.5l437 579h-449v230h802v-124zM128 1457h214c12 0 23.5 -1.83301 34.5 -5.5s19.5 -8.16699 25.5 -13.5l84 -72
c4 -3.33301 8.33301 -7.33301 13 -12s9 -9.66699 13 -15c7.33301 10.667 16 19.667 26 27l84 72c6 4 14.5 8.16699 25.5 12.5s22.5 6.5 34.5 6.5h206l-234 -289h-292z" />
    <glyph glyph-name="florin" unicode="&#x192;" 
d="M737 727l-78.999 -654.998c-8.66699 -72.667 -26.167 -134.834 -52.5 -186.501s-62.5 -94.167 -108.5 -127.5s-102.667 -57.833 -170 -73.5s-146.333 -23.5 -237 -23.5v157c0 32 7.66699 55.333 23 70s39.666 22 72.999 22c24 0 45.333 3 64 9s34.667 15.833 48 29.5
s24.333 31.5 33 53.5s15 48.667 19 80l79 640l-95 20c-22 5.33301 -38.833 13.833 -50.5 25.5s-17.5 28.167 -17.5 49.5v122h189l17 134c8.66699 68 26 127.667 52 179s62 94.166 108 128.499s102.667 60.333 170 78s146.666 26.5 237.999 26.5v-165
c0 -32 -7.66699 -54 -23 -66s-39.666 -18 -72.999 -18c-48 0 -86.5 -13 -115.5 -39s-47.833 -70.333 -56.5 -133l-17 -125h269v-212h-287z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="660" 
d="M710 1168h-206c-12 0 -23 1.83301 -33 5.5s-19 8.5 -27 14.5l-84 72c-4.66699 3.33301 -9 7 -13 11s-8 8.66699 -12 14c-4 -5.33301 -8.16699 -10 -12.5 -14s-8.5 -7.66699 -12.5 -11l-86 -72c-6 -5.33301 -14.5 -10 -25.5 -14s-22.5 -6 -34.5 -6h-214l234 289h292z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="660" 
d="M-50 1457h214c12 0 23.5 -1.83301 34.5 -5.5s19.5 -8.16699 25.5 -13.5l84 -72c4 -3.33301 8.33301 -7.33301 13 -12s9 -9.66699 13 -15c7.33301 10.667 16 19.667 26 27l84 72c6 4 14.5 8.16699 25.5 12.5s22.5 6.5 34.5 6.5h206l-234 -289h-292z" />
    <glyph glyph-name="uni02C9" unicode="&#x2c9;" horiz-adv-x="660" 
d="M20 1391h620v-190h-620v190z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="660" 
d="M330 1159c-62.667 0 -114.999 8 -156.999 24s-75.667 37.5 -101 64.5s-43.333 58.5 -54 94.5s-16 74.333 -16 115h216c0 -18 1.5 -34 4.5 -48s8.66699 -25.5 17 -34.5s19.666 -15.833 33.999 -20.5s33.166 -7 56.499 -7c22.667 0 41.334 2.33301 56.001 7
s26.167 11.5 34.5 20.5s14 20.5 17 34.5s4.5 30 4.5 48h216c0 -40.667 -5.33301 -79 -16 -115s-28.667 -67.5 -54 -94.5s-59 -48.5 -101 -64.5s-94.333 -24 -157 -24z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="660" 
d="M518 1322c0 -24.667 -5 -48 -15 -70s-23.667 -41.167 -41 -57.5s-37.5 -29.166 -60.5 -38.499s-47.5 -14 -73.5 -14c-25.333 0 -49 4.66699 -71 14s-41.5 22.166 -58.5 38.499s-30.333 35.5 -40 57.5s-14.5 45.333 -14.5 70s4.83301 48.167 14.5 70.5
s23 41.666 40 57.999s36.5 29.333 58.5 39s45.667 14.5 71 14.5c26 0 50.5 -4.83301 73.5 -14.5s43.167 -22.667 60.5 -39s31 -35.666 41 -57.999s15 -45.833 15 -70.5z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="660" 
d="M86 1334c0 33.333 6.66699 63.5 20 90.5s31 50.167 53 69.5s47.667 34.166 77 44.499s60 15.5 92 15.5c32.667 0 64 -5.16699 94 -15.5s56.5 -25.166 79.5 -44.499s41.167 -42.5 54.5 -69.5s20 -57.167 20 -90.5c0 -32.667 -6.66699 -62.167 -20 -88.5
s-31.5 -48.833 -54.5 -67.5s-49.5 -33.167 -79.5 -43.5s-61.333 -15.5 -94 -15.5c-32 0 -62.667 5.16699 -92 15.5s-55 24.833 -77 43.5s-39.667 41.167 -53 67.5s-20 55.833 -20 88.5zM252 1334c0 -23.333 6.5 -42.5 19.5 -57.5s33.167 -22.5 60.5 -22.5
c24 0 43 7.5 57 22.5s21 34.167 21 57.5c0 25.333 -7 45.333 -21 60s-33 22 -57 22c-27.333 0 -47.5 -7.33301 -60.5 -22s-19.5 -34.667 -19.5 -60z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="660" 
d="M470 -167c14 0 23.333 -6.33301 28 -19l46 -111c-23.333 -14 -52.333 -25.833 -87 -35.5s-71.667 -14.5 -111 -14.5c-71.333 0 -125.5 14 -162.5 42s-55.5 63.667 -55.5 107c0 36.667 13 73.167 39 109.5s67.667 69.833 125 100.5l160 -12
c-26 -14 -48.167 -31.5 -66.5 -52.5s-27.5 -44.167 -27.5 -69.5c0 -16 4.5 -28.833 13.5 -38.5s22.5 -14.5 40.5 -14.5c10 0 18 0.333008 24 1s11 1.5 15 2.5s7.33301 2 10 3s5.66699 1.5 9 1.5z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="660" 
d="M434 1387c16 0 28.999 4.16992 38.999 12.5029s15 26.166 15 53.499h188c0 -42 -5.83301 -79.833 -17.5 -113.5s-27.834 -62.334 -48.501 -86.001s-45 -41.834 -73 -54.501s-58.333 -19 -91 -19c-24.667 0 -47.667 3.83301 -69 11.5s-41.166 16.167 -59.499 25.5
l-50 25.5c-15 7.66699 -28.167 11.5 -39.5 11.5c-16 0 -28.667 -4.5 -38 -13.5s-14 -27.167 -14 -54.5h-190c0 42 6 79.833 18 113.5s28.333 62.5 49 86.5s45 42.5 73 55.5s58 19.5 90 19.5c24.667 0 47.834 -3.83301 69.501 -11.5s41.5 -16.167 59.5 -25.5l49.5 -25.5
c15 -7.66699 28.167 -11.5 39.5 -11.5z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="660" 
d="M472 1473l-199.998 -262.999c-13.333 -16.667 -27.5 -27.834 -42.5 -33.501s-34.167 -8.5 -57.5 -8.5h-126l116 245c9.33301 20 22.166 35 38.499 45s40.166 15 71.499 15h200zM852.002 1473l-259.999 -262.999c-15.333 -15.333 -30 -26.166 -44 -32.499
s-32.667 -9.5 -56 -9.5h-146l176 245c6.66699 9.33301 13.167 17.666 19.5 24.999s13.666 13.666 21.999 18.999s18 9.33301 29 12s24.167 4 39.5 4h220z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="1336" 
d="M1298 1037v-119.996c0 -25.333 -8.16699 -47.166 -24.5 -65.499s-39.5 -27.5 -69.5 -27.5h-80v-824h-310v824h-286v-540c0 -44 -5.33301 -84.333 -16 -121s-28 -68.334 -52 -95.001s-55.5 -47.334 -94.5 -62.001s-86.833 -22 -143.5 -22
c-25.333 0 -52.833 2.83301 -82.5 8.5s-58.167 15.167 -85.5 28.5l8 127c0.666992 13.333 5 25.166 13 35.499s25 15.5 51 15.5c18.667 0 34 1.5 46 4.5s21.5 8 28.5 15s11.667 16.167 14 27.5s3.5 25 3.5 41v537h-170v108c0 11.333 2.5 23.166 7.5 35.499
s12.5 23.666 22.5 33.999s22 18.833 36 25.5s30 10 48 10h1136z" />
    <glyph glyph-name="endash" unicode="&#x2013;" 
d="M170 713h820v-212h-820v212z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="1722" 
d="M170 713h1382v-212h-1382v212z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="472" 
d="M164 949c-26.667 42 -46.499 83.833 -59.499 125.5s-19.5 83.167 -19.5 124.5c0 71.333 18.667 139.833 56 205.5s92.333 126.167 165 181.5l98 -58c8 -5.33301 13.833 -11.166 17.5 -17.499s5.5 -12.833 5.5 -19.5c0 -7.33301 -1.66699 -14.333 -5 -21
s-7.33301 -12.334 -12 -17.001c-7.33301 -8 -15.5 -17.833 -24.5 -29.5s-17.5 -25.334 -25.5 -41.001s-14.833 -33 -20.5 -52s-8.5 -39.5 -8.5 -61.5c0 -24 4.16699 -49.667 12.5 -77s22.166 -56.666 41.499 -87.999c6.66699 -10.667 10 -21.334 10 -32.001
c0 -12 -4 -22.667 -12 -32s-19 -16.666 -33 -21.999z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="472" 
d="M338 1551c26.667 -42 46.499 -84 59.499 -126s19.5 -83.667 19.5 -125c0 -71.333 -18.667 -139.666 -56 -204.999s-92.333 -125.666 -165 -180.999l-98 58c-8 5.33301 -13.833 11.166 -17.5 17.499s-5.5 12.833 -5.5 19.5c0 7.33301 1.66699 14.333 5 21
s7.33301 12.334 12 17.001c7.33301 8 15.5 17.833 24.5 29.5s17.5 25.167 25.5 40.5s14.833 32.666 20.5 51.999s8.5 40 8.5 62c0 24 -4.16699 49.667 -12.5 77s-22.166 56.666 -41.499 87.999c-6.66699 10.667 -10 21.334 -10 32.001c0 12 4 22.667 12 32
s19 16.666 33 21.999z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="472" 
d="M338 332c26.667 -42 46.499 -84 59.499 -126s19.5 -83.667 19.5 -125c0 -71.333 -18.667 -139.666 -56 -204.999s-92.333 -125.666 -165 -180.999l-98 58c-8 5.33301 -13.833 11.166 -17.5 17.499s-5.5 12.833 -5.5 19.5c0 7.33301 1.66699 14.333 5 21
s7.33301 12.334 12 17.001c7.33301 8 15.5 17.833 24.5 29.5s17.5 25.167 25.5 40.5s14.833 32.666 20.5 51.999s8.5 40 8.5 62c0 24 -4.16699 49.667 -12.5 77s-22.166 56.666 -41.499 87.999c-6.66699 10.667 -10 21.334 -10 32.001c0 12 4 22.667 12 32
s19 16.666 33 21.999z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="830" 
d="M164 949c-26.667 42 -46.499 83.833 -59.499 125.5s-19.5 83.167 -19.5 124.5c0 71.333 18.667 139.833 56 205.5s92.333 126.167 165 181.5l98 -58c8 -5.33301 13.833 -11.166 17.5 -17.499s5.5 -12.833 5.5 -19.5c0 -7.33301 -1.66699 -14.333 -5 -21
s-7.33301 -12.334 -12 -17.001c-7.33301 -8 -15.5 -17.833 -24.5 -29.5s-17.5 -25.334 -25.5 -41.001s-14.833 -33 -20.5 -52s-8.5 -39.5 -8.5 -61.5c0 -24 4.16699 -49.667 12.5 -77s22.166 -56.666 41.499 -87.999c6.66699 -10.667 10 -21.334 10 -32.001
c0 -12 -4 -22.667 -12 -32s-19 -16.666 -33 -21.999zM522.001 949c-26.667 42 -46.499 83.833 -59.499 125.5s-19.5 83.167 -19.5 124.5c0 71.333 18.667 139.833 56 205.5s92.333 126.167 165 181.5l98 -58c8 -5.33301 13.833 -11.166 17.5 -17.499s5.5 -12.833 5.5 -19.5
c0 -7.33301 -1.66699 -14.333 -5 -21s-7.33301 -12.334 -12 -17.001c-7.33301 -8 -15.5 -17.833 -24.5 -29.5s-17.5 -25.334 -25.5 -41.001s-14.833 -33 -20.5 -52s-8.5 -39.5 -8.5 -61.5c0 -24 4.16699 -49.667 12.5 -77s22.166 -56.666 41.499 -87.999
c6.66699 -10.667 10 -21.334 10 -32.001c0 -12 -4 -22.667 -12 -32s-19 -16.666 -33 -21.999z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="830" 
d="M338 1551c26.667 -42 46.499 -84 59.499 -126s19.5 -83.667 19.5 -125c0 -71.333 -18.667 -139.666 -56 -204.999s-92.333 -125.666 -165 -180.999l-98 58c-8 5.33301 -13.833 11.166 -17.5 17.499s-5.5 12.833 -5.5 19.5c0 7.33301 1.66699 14.333 5 21
s7.33301 12.334 12 17.001c7.33301 8 15.5 17.833 24.5 29.5s17.5 25.167 25.5 40.5s14.833 32.666 20.5 51.999s8.5 40 8.5 62c0 24 -4.16699 49.667 -12.5 77s-22.166 56.666 -41.499 87.999c-6.66699 10.667 -10 21.334 -10 32.001c0 12 4 22.667 12 32
s19 16.666 33 21.999zM695.999 1551c26.667 -42 46.499 -84 59.499 -126s19.5 -83.667 19.5 -125c0 -71.333 -18.667 -139.666 -56 -204.999s-92.333 -125.666 -165 -180.999l-98 58c-8 5.33301 -13.833 11.166 -17.5 17.499s-5.5 12.833 -5.5 19.5
c0 7.33301 1.66699 14.333 5 21s7.33301 12.334 12 17.001c7.33301 8 15.5 17.833 24.5 29.5s17.5 25.167 25.5 40.5s14.833 32.666 20.5 51.999s8.5 40 8.5 62c0 24 -4.16699 49.667 -12.5 77s-22.166 56.666 -41.499 87.999c-6.66699 10.667 -10 21.334 -10 32.001
c0 12 4 22.667 12 32s19 16.666 33 21.999z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="830" 
d="M338 332c26.667 -42 46.499 -84 59.499 -126s19.5 -83.667 19.5 -125c0 -71.333 -18.667 -139.666 -56 -204.999s-92.333 -125.666 -165 -180.999l-98 58c-8 5.33301 -13.833 11.166 -17.5 17.499s-5.5 12.833 -5.5 19.5c0 7.33301 1.66699 14.333 5 21
s7.33301 12.334 12 17.001c7.33301 8 15.5 17.833 24.5 29.5s17.5 25.167 25.5 40.5s14.833 32.666 20.5 51.999s8.5 40 8.5 62c0 24 -4.16699 49.667 -12.5 77s-22.166 56.666 -41.499 87.999c-6.66699 10.667 -10 21.334 -10 32.001c0 12 4 22.667 12 32
s19 16.666 33 21.999zM695.999 332c26.667 -42 46.499 -84 59.499 -126s19.5 -83.667 19.5 -125c0 -71.333 -18.667 -139.666 -56 -204.999s-92.333 -125.666 -165 -180.999l-98 58c-8 5.33301 -13.833 11.166 -17.5 17.499s-5.5 12.833 -5.5 19.5
c0 7.33301 1.66699 14.333 5 21s7.33301 12.334 12 17.001c7.33301 8 15.5 17.833 24.5 29.5s17.5 25.167 25.5 40.5s14.833 32.666 20.5 51.999s8.5 40 8.5 62c0 24 -4.16699 49.667 -12.5 77s-22.166 56.666 -41.499 87.999c-6.66699 10.667 -10 21.334 -10 32.001
c0 12 4 22.667 12 32s19 16.666 33 21.999z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" 
d="M52 909c0 16.667 2.83008 32.667 8.49707 48s14.334 29 26.001 41s26.5 21.5 44.5 28.5s39.667 10.5 65 10.5c18.667 0 38.834 -1.66699 60.501 -5s43.834 -7.5 66.501 -12.5l69.5 -16c23.667 -5.66699 47.167 -10.834 70.5 -15.501l-39 489
c22 12.667 46.667 22.167 74 28.5s56 9.5 86 9.5s58.667 -3.16699 86 -9.5s52 -15.833 74 -28.5l-40 -489c23.333 4.66699 46.833 9.83398 70.5 15.501l70 16c23 5 45.167 9.16699 66.5 12.5s41.666 5 60.999 5c24.667 0 46.167 -3.5 64.5 -10.5s33.333 -16.5 45 -28.5
s20.334 -25.667 26.001 -41s8.5 -31.333 8.5 -48v-100h-412v-304l40 -819c-22 -12.667 -46.667 -22.334 -74 -29.001s-56 -10 -86 -10s-58.667 3.33301 -86 10s-52 16.334 -74 29.001l40 819v304h-412v100z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" 
d="M52 353h412v456h-412v100c0 16.667 2.83301 32.667 8.5 48s14.334 29 26.001 41s26.5 21.5 44.5 28.5s39.667 10.5 65 10.5c18.667 0 38.834 -1.66699 60.501 -5s43.834 -7.5 66.501 -12.5l69.5 -16c23.667 -5.66699 47.167 -10.834 70.5 -15.501l-39 489
c22 12.667 46.667 22.167 74 28.5s56 9.5 86 9.5s58.667 -3.16699 86 -9.5s52 -15.833 74 -28.5l-40 -489c23.333 4.66699 46.833 9.83398 70.5 15.501l70 16c23 5 45.167 9.16699 66.5 12.5s41.666 5 60.999 5c24.667 0 46.167 -3.5 64.5 -10.5s33.333 -16.5 45 -28.5
s20.334 -25.667 26.001 -41s8.5 -31.333 8.5 -48v-100h-412v-456h412v-99c0 -16.667 -2.83301 -32.834 -8.5 -48.501s-14.334 -29.5 -26.001 -41.5s-26.667 -21.5 -45 -28.5s-39.833 -10.5 -64.5 -10.5c-19.333 0 -39.666 1.66699 -60.999 5s-43.5 7.5 -66.5 12.5l-70 16
c-23.667 5.66699 -47.167 10.834 -70.5 15.501l40 -488c-22 -12.667 -46.667 -22.334 -74 -29.001s-56 -10 -86 -10s-58.667 3.33301 -86 10s-52 16.334 -74 29.001l39 488c-23.333 -4.66699 -46.833 -9.83398 -70.5 -15.501l-69.5 -16
c-22.667 -5 -44.834 -9.16699 -66.501 -12.5s-41.834 -5 -60.501 -5c-25.333 0 -47 3.5 -65 10.5s-32.833 16.5 -44.5 28.5s-20.334 25.833 -26.001 41.5s-8.5 31.834 -8.5 48.501v99z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" 
d="M84 592c0 66.667 13 129.334 39 188.001s61.333 110 106 154s97 78.667 157 104s124 38 192 38c68.667 0 133 -12.667 193 -38s112.5 -60 157.5 -104s80.5 -95.333 106.5 -154s39 -121.334 39 -188.001c0 -66 -13 -128.333 -39 -187s-61.5 -109.667 -106.5 -153
s-97.5 -77.666 -157.5 -102.999s-124.333 -38 -193 -38c-68 0 -132 12.667 -192 38s-112.333 59.666 -157 102.999s-80 94.333 -106 153s-39 121 -39 187z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="1576" 
d="M60 157c0 24 4.5 46.333 13.5 67s21.333 38.667 37 54s34.167 27.5 55.5 36.5s44.666 13.5 69.999 13.5c24.667 0 47.667 -4.5 69 -13.5s40 -21.167 56 -36.5s28.5 -33.333 37.5 -54s13.5 -43 13.5 -67s-4.5 -46.5 -13.5 -67.5s-21.5 -39.167 -37.5 -54.5
s-34.667 -27.333 -56 -36s-44.333 -13 -69 -13c-25.333 0 -48.666 4.33301 -69.999 13s-39.833 20.667 -55.5 36s-28 33.5 -37 54.5s-13.5 43.5 -13.5 67.5zM1164 157c0 24 4.5 46.333 13.5 67s21.333 38.667 37 54s34.167 27.5 55.5 36.5s44.666 13.5 69.999 13.5
c24.667 0 47.667 -4.5 69 -13.5s40 -21.167 56 -36.5s28.5 -33.333 37.5 -54s13.5 -43 13.5 -67s-4.5 -46.5 -13.5 -67.5s-21.5 -39.167 -37.5 -54.5s-34.667 -27.333 -56 -36s-44.333 -13 -69 -13c-25.333 0 -48.666 4.33301 -69.999 13s-39.833 20.667 -55.5 36
s-28 33.5 -37 54.5s-13.5 43.5 -13.5 67.5zM612 157c0 24 4.5 46.333 13.5 67s21.333 38.667 37 54s34.167 27.5 55.5 36.5s44.666 13.5 69.999 13.5c24.667 0 47.667 -4.5 69 -13.5s40 -21.167 56 -36.5s28.5 -33.333 37.5 -54s13.5 -43 13.5 -67s-4.5 -46.5 -13.5 -67.5
s-21.5 -39.167 -37.5 -54.5s-34.667 -27.333 -56 -36s-44.333 -13 -69 -13c-25.333 0 -48.666 4.33301 -69.999 13s-39.833 20.667 -55.5 36s-28 33.5 -37 54.5s-13.5 43.5 -13.5 67.5z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="2384" 
d="M748 1104c0 -53.333 -9.66699 -102 -29 -146s-45 -81.833 -77 -113.5s-69.167 -56.167 -111.5 -73.5s-86.5 -26 -132.5 -26c-50.667 0 -97.167 8.66699 -139.5 26s-79 41.833 -110 73.5s-55.167 69.5 -72.5 113.5s-26 92.667 -26 146c0 55.333 8.66699 105.5 26 150.5
s41.5 83.5 72.5 115.5s67.667 56.833 110 74.5s88.833 26.5 139.5 26.5s97.5 -8.83301 140.5 -26.5s80 -42.5 111 -74.5s55.167 -70.5 72.5 -115.5s26 -95.167 26 -150.5zM510 1104c0 34 -2.83301 62.167 -8.5 84.5s-13.5 40.166 -23.5 53.499s-21.833 22.666 -35.5 27.999
s-28.5 8 -44.5 8s-30.667 -2.66699 -44 -8s-24.666 -14.666 -33.999 -27.999s-16.666 -31.166 -21.999 -53.499s-8 -50.5 -8 -84.5c0 -32 2.66699 -58.667 8 -80s12.666 -38.333 21.999 -51s20.666 -21.667 33.999 -27s28 -8 44 -8s30.833 2.66699 44.5 8
s25.5 14.333 35.5 27s17.833 29.667 23.5 51s8.5 48 8.5 80zM1206 1411c10 11.333 22.333 21.833 37 31.5s35 14.5 61 14.5h226l-1098 -1413c-10 -12.667 -22.5 -23.167 -37.5 -31.5s-33.167 -12.5 -54.5 -12.5h-232zM1586 342c0 -53.333 -9.66699 -102.166 -29 -146.499
s-45 -82.333 -77 -114s-69.167 -56.167 -111.5 -73.5s-86.5 -26 -132.5 -26c-50.667 0 -97.167 8.66699 -139.5 26s-79 41.833 -110 73.5s-55.167 69.667 -72.5 114s-26 93.166 -26 146.499c0 55.333 8.66699 105.5 26 150.5s41.5 83.5 72.5 115.5s67.667 56.833 110 74.5
s88.833 26.5 139.5 26.5s97.5 -8.83301 140.5 -26.5s80 -42.5 111 -74.5s55.167 -70.5 72.5 -115.5s26 -95.167 26 -150.5zM1348 342c0 33.333 -2.83301 61.166 -8.5 83.499s-13.5 40.166 -23.5 53.499s-21.833 22.666 -35.5 27.999s-28.5 8 -44.5 8
s-30.667 -2.66699 -44 -8s-24.666 -14.666 -33.999 -27.999s-16.666 -31.166 -21.999 -53.499s-8 -50.166 -8 -83.499c0 -32 2.66699 -58.667 8 -80s12.666 -38.333 21.999 -51s20.666 -21.667 33.999 -27s28 -8 44 -8s30.833 2.66699 44.5 8s25.5 14.333 35.5 27
s17.833 29.667 23.5 51s8.5 48 8.5 80zM2334 342c0 -53.333 -9.66699 -102.166 -29 -146.499s-45 -82.333 -77 -114s-69.167 -56.167 -111.5 -73.5s-86.5 -26 -132.5 -26c-50.667 0 -97.167 8.66699 -139.5 26s-79 41.833 -110 73.5s-55.167 69.667 -72.5 114
s-26 93.166 -26 146.499c0 55.333 8.66699 105.5 26 150.5s41.5 83.5 72.5 115.5s67.667 56.833 110 74.5s88.833 26.5 139.5 26.5s97.5 -8.83301 140.5 -26.5s80 -42.5 111 -74.5s55.167 -70.5 72.5 -115.5s26 -95.167 26 -150.5zM2096 342
c0 33.333 -2.83301 61.166 -8.5 83.499s-13.5 40.166 -23.5 53.499s-21.833 22.666 -35.5 27.999s-28.5 8 -44.5 8s-30.667 -2.66699 -44 -8s-24.666 -14.666 -33.999 -27.999s-16.666 -31.166 -21.999 -53.499s-8 -50.166 -8 -83.499c0 -32 2.66699 -58.667 8 -80
s12.666 -38.333 21.999 -51s20.666 -21.667 33.999 -27s28 -8 44 -8s30.833 2.66699 44.5 8s25.5 14.333 35.5 27s17.833 29.667 23.5 51s8.5 48 8.5 80z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="652" 
d="M110 525v39l262 402l102 -46c16 -7.33301 27.833 -16.333 35.5 -27s11.5 -22.667 11.5 -36c0 -16 -5 -33 -15 -51l-120 -215c-11.333 -20 -23.333 -35.667 -36 -47c12 -10.667 24 -26 36 -46l120 -216c10 -17.333 15 -34 15 -50c0 -13.333 -3.83301 -25.333 -11.5 -36
s-19.5 -19.667 -35.5 -27l-102 -46z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="652" 
d="M280 123l-102 46c-16 7.33301 -27.833 16.333 -35.5 27s-11.5 22.667 -11.5 36c0 16 5 32.667 15 50l120 216c10 18.667 22 34 36 46c-6.66699 6 -13 13 -19 21s-11.667 16.667 -17 26l-120 215c-10 18 -15 35 -15 51c0 13.333 3.83301 25.333 11.5 36
s19.5 19.667 35.5 27l102 46l262 -402v-39z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="800" 
d="M118 85c-10.667 -16.667 -21.5 -30.5 -32.5 -41.5s-22.667 -19.667 -35 -26s-25.5 -10.833 -39.5 -13.5s-29 -4 -45 -4h-132l848 1359c20 31.333 42.667 55.5 68 72.5s56 25.5 92 25.5h132z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" 
d="M40 950l120.002 0.00488281c16.667 78.667 42.334 150.167 77.001 214.5s76.334 119.166 125.001 164.499s103.667 80.333 165 105s127 37 197 37c94.667 0 176.5 -18.833 245.5 -56.5s124.5 -87.5 166.5 -149.5l-114 -118c-7.33301 -8 -15.5 -15.167 -24.5 -21.5
s-20.833 -9.5 -35.5 -9.5c-16 0 -29.5 5 -40.5 15l-38.5 33.5c-14.667 12.333 -33 23.5 -55 33.5s-52 15 -90 15c-62.667 0 -116.167 -22.167 -160.5 -66.5s-76.833 -109.833 -97.5 -196.5h460v-92c0 -8.66699 -2 -17.334 -6 -26.001s-9.66699 -16.5 -17 -23.5
s-16.166 -12.667 -26.499 -17s-21.833 -6.5 -34.5 -6.5h-397c-0.666992 -9.33301 -1 -18.666 -1 -27.999v-28v-35h386v-92c0 -8.66699 -2 -17.334 -6 -26.001s-9.66699 -16.5 -17 -23.5s-16.166 -12.667 -26.499 -17s-21.833 -6.5 -34.5 -6.5h-286
c17.333 -96.667 47.666 -168.334 90.999 -215.001s96.333 -70 159 -70c28 0 52 2.83301 72 8.5s37 12.5 51 20.5s25.833 17 35.5 27l27 27c8.33301 8 16.666 14.833 24.999 20.5s18.166 8.5 29.499 8.5c9.33301 0 17.166 -1.33301 23.499 -4s13.166 -7.66699 20.499 -15
l136 -121c-46 -75.333 -106.333 -132.5 -181 -171.5s-159.667 -58.5 -255 -58.5c-79.333 0 -150.5 13.167 -213.5 39.5s-117.667 63.166 -164 110.499s-84.333 104.333 -114 171s-50.834 140.667 -63.501 222h-113v165h98v35v28c0 9.33301 0.333008 18.666 1 27.999h-99v165
z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="1462" 
d="M956 1209c12.667 -28 23.6719 -56 33.0049 -84c4.66699 14 10 28 16 42s12.333 28 19 42l100 217c11.333 13.333 21.166 21.833 29.499 25.5s21.166 5.5 38.499 5.5h184v-613h-186v252l14 113l-126 -269c-6.66699 -14 -16.334 -24.833 -29.001 -32.5s-27 -11.5 -43 -11.5
h-30c-16 0 -30.333 3.83301 -43 11.5s-22.334 18.5 -29.001 32.5l-120 264l12 -108v-252h-186v613h184c8.66699 0 16.167 -0.333008 22.5 -1s12 -2.16699 17 -4.5s9.66699 -5.5 14 -9.5s9.16602 -9.33301 14.499 -16zM560.005 1457v-169h-150v-444h-212v444h-148v169h510z
" />
    <glyph glyph-name="Omega" unicode="&#x2126;" horiz-adv-x="1468" 
d="M788 0l0.00292969 544c38.667 5.33301 74.834 15.5 108.501 30.5s63 35.833 88 62.5s44.833 59.5 59.5 98.5s22 85.167 22 138.5c0 108 -28.167 191.5 -84.5 250.5s-138.833 88.5 -247.5 88.5c-109.333 0 -192 -29.5 -248 -88.5s-84 -142.5 -84 -250.5
c0 -53.333 7.33301 -99.5 22 -138.5s34.5 -71.833 59.5 -98.5s54.333 -47.5 88 -62.5s69.834 -25.167 108.501 -30.5v-544h-510c-34 0 -61.167 9.5 -81.5 28.5s-30.5 43.5 -30.5 73.5v166h362v110c-60 17.333 -112.833 42.666 -158.5 75.999s-83.667 72.333 -114 117
s-53.166 94.167 -68.499 148.5s-23 111.5 -23 171.5c0 51.333 7.16699 101.333 21.5 150s35 94.5 62 137.5s60.167 82.667 99.5 119s84.166 67.5 134.499 93.5s105.666 46.167 165.999 60.5s125.166 21.5 194.499 21.5s134.166 -7.16699 194.499 -21.5
s115.666 -34.5 165.999 -60.5s95.166 -57.167 134.499 -93.5s72.5 -76 99.5 -119s47.667 -88.833 62 -137.5s21.5 -98.667 21.5 -150c0 -60 -7.66699 -117.167 -23 -171.5s-38.333 -103.833 -69 -148.5s-68.667 -83.667 -114 -117s-98 -58.666 -158 -75.999v-110h362v-166
c0 -30 -10.167 -54.5 -30.5 -73.5s-47.5 -28.5 -81.5 -28.5h-510z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" 
d="M266 1333c28 21.333 56.3291 40.502 84.9961 57.502s58.834 31.333 90.501 43s65.834 20.667 102.501 27s77.334 9.5 122.001 9.5c64 0 123.333 -12.833 178 -38.5s102 -62.834 142 -111.501s71.333 -107.667 94 -177s34 -147.666 34 -234.999
c0 -141.333 -12.833 -269 -38.5 -383s-64.5 -211 -116.5 -291s-117.333 -141.667 -196 -185s-171 -65 -277 -65c-63.333 0 -122 10.333 -176 31s-100.667 50 -140 88s-70.166 84 -92.499 138s-33.5 114.667 -33.5 182c0 78.667 13.667 151.5 41 218.5s64.833 125 112.5 174
s103.667 87.5 168 115.5s133.166 42 206.499 42c60.667 0 112.334 -9 155.001 -27s79 -43.667 109 -77v37c0 55.333 -5.33301 103.666 -16 144.999s-25.5 75.5 -44.5 102.5s-41.667 47.167 -68 60.5s-54.833 20 -85.5 20c-22 0 -43.667 -3.33301 -65 -10
s-41.5 -14 -60.5 -22l-52.5 -22c-16 -6.66699 -30 -10 -42 -10c-11.333 0 -22.5 3.16699 -33.5 9.5s-22.5 19.166 -34.5 38.499zM521.996 216.002c28 0 55.832 5.83203 83.499 17.499s53.667 31.334 78 59.001s46.166 64.334 65.499 110.001s35 102.167 47 169.5
c-6 22.667 -13.833 44.167 -23.5 64.5s-22 38.333 -37 54s-33.333 28 -55 37s-47.167 13.5 -76.5 13.5c-42 0 -78.167 -7.66699 -108.5 -23s-55.166 -36.833 -74.499 -64.5s-33.666 -60.834 -42.999 -99.501s-14 -81 -14 -127c0 -67.333 13.833 -119.333 41.5 -156
s66.5 -55 116.5 -55z" />
    <glyph glyph-name="Delta" unicode="&#x2206;" horiz-adv-x="1534" 
d="M614 1457h306l604 -1457h-1514zM453 258l628.001 0.000976562l-257 698l-27.5 75.5c-10.333 28.333 -20.166 59.166 -29.499 92.499c-8.66699 -33.333 -17.834 -64.5 -27.501 -93.5s-18.834 -54.5 -27.501 -76.5z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="1376" 
d="M1336 1457v-251h-150v-1535h-320v1535h-356v-1535h-320v1535h-150v251h1296z" />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="1376" 
d="M80 1457l1216 -0.000976562v-251h-742l464 -598v-88l-465 -599h743v-250h-1216v103c0 14.667 2.66699 29.834 8 45.501s13.333 30.167 24 43.5l564 705l-564 696c-11.333 13.333 -19.5 28 -24.5 44s-7.5 31.333 -7.5 46v103z" />
    <glyph glyph-name="minus" unicode="&#x2212;" 
d="M124 783h910v-222h-910v222z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="1182" 
d="M247 624h-73.0039c-15.333 0 -30 2.16699 -44 6.5s-26.333 11.333 -37 21s-19.167 22.167 -25.5 37.5s-9.5 34 -9.5 56v96h368c22 0 40.833 -5.16699 56.5 -15.5s25.5 -23.166 29.5 -38.499l58 -231c10.667 -32 19.334 -64.5 26.001 -97.5s12.334 -66.5 17.001 -100.5
c4.66699 26 9.83398 52.167 15.501 78.5s12.834 53.5 21.501 81.5l344 1129c5.33301 15.333 15.333 28.166 30 38.499s32 15.5 52 15.5h206l-546 -1701h-262z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="1376" 
d="M997 214c-36 0 -69.167 4.33301 -99.5 13s-58 20.5 -83 35.5s-47.833 32.667 -68.5 53s-39.667 42.166 -57 65.499c-18 -23.333 -37.333 -45.166 -58 -65.499s-43.5 -38 -68.5 -53s-52.5 -26.833 -82.5 -35.5s-63 -13 -99 -13c-46.667 0 -91 9.33301 -133 28
s-78.833 44.834 -110.5 78.501s-56.834 73.5 -75.501 119.5s-28 96.667 -28 152c0 54.667 9.33301 105.167 28 151.5s43.834 86.166 75.501 119.499s68.5 59.333 110.5 78s86.333 28 133 28c36 0 69 -4.33301 99 -13s57.5 -20.667 82.5 -36s47.833 -33.166 68.5 -53.499
s40 -41.833 58 -64.5c17.333 22.667 36.333 44.167 57 64.5s43.5 38.166 68.5 53.499s52.667 27.333 83 36s63.5 13 99.5 13c46.667 0 91 -9.33301 133 -28s78.833 -44.667 110.5 -78s56.834 -73.166 75.501 -119.499s28 -96.833 28 -151.5
c0 -55.333 -9.33301 -106 -28 -152s-43.834 -85.833 -75.501 -119.5s-68.5 -59.834 -110.5 -78.501s-86.333 -28 -133 -28zM394 456c16.667 0 32.5 3.5 47.5 10.5s29 16.5 42 28.5s25.5 26.333 37.5 43s24 34.667 36 54c-12 19.333 -24 37.333 -36 54s-24.5 31 -37.5 43
s-27 21.5 -42 28.5s-30.833 10.5 -47.5 10.5c-15.333 0 -30 -2.33301 -44 -7s-26.5 -12.334 -37.5 -23.001s-19.833 -24.667 -26.5 -42s-10 -38.666 -10 -63.999s3.33301 -46.666 10 -63.999s15.5 -31.333 26.5 -42s23.5 -18.334 37.5 -23.001s28.667 -7 44 -7zM985 456
c15.333 0 30.001 2.33301 44.001 7s26.5 12.334 37.5 23.001s19.667 24.667 26 42s9.5 38.666 9.5 63.999s-3.33301 46.666 -10 63.999s-15.334 31.333 -26.001 42s-23.167 18.334 -37.5 23.001s-28.833 7 -43.5 7c-17.333 0 -33.333 -3.5 -48 -10.5
s-28.667 -16.5 -42 -28.5s-26 -26.333 -38 -43s-24 -34.667 -36 -54c12 -19.333 24 -37.333 36 -54s24.667 -31 38 -43s27.333 -21.5 42 -28.5s30.667 -10.5 48 -10.5z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="850" 
d="M345 1097c6.66699 58 19.3311 110.168 37.998 156.501s43.334 85.666 74.001 117.999s67.834 57.333 111.501 75s94.167 26.5 151.5 26.5c25.333 0 52.833 -2.83301 82.5 -8.5s58.167 -15.167 85.5 -28.5l-14 -147c-0.666992 -7.33301 -2 -15 -4 -23
s-5.16699 -15.5 -9.5 -22.5s-10.666 -12.833 -18.999 -17.5s-18.833 -7 -31.5 -7c-31.333 0 -57 -3.33301 -77 -10s-36.333 -16.834 -49 -30.501s-22.334 -30.667 -29.001 -51s-11.667 -43.833 -15 -70.5l-127 -985c-9.33301 -70 -25.666 -130.667 -48.999 -182
s-53 -93.833 -89 -127.5s-77.667 -58.5 -125 -74.5s-99.333 -24 -156 -24c-25.333 0 -52.833 2.83301 -82.5 8.5s-58.167 15.5 -85.5 29.5l16 126c1.33301 10 3.16602 18.833 5.49902 26.5s6.16602 14.167 11.499 19.5s12.833 9.33301 22.5 12s22.5 4 38.5 4
c38 0 69 3.33301 93 10s43.333 17 58 31s25.334 31.833 32.001 53.5s11.667 47.834 15 78.501z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" 
d="M796 926c22.667 0 44.501 2.66699 65.501 8s40 11.5 57 18.5s31.5 14.167 43.5 21.5s20 13 24 17l34 -191c-14 -18 -30.333 -33.333 -49 -46s-38.5 -23 -59.5 -31s-43.167 -13.833 -66.5 -17.5s-46.333 -5.5 -69 -5.5c-35.333 0 -70.833 4.83301 -106.5 14.5
s-70.5 20.5 -104.5 32.5s-67 22.833 -99 32.5s-62 14.5 -90 14.5c-23.333 0 -45.666 -2.5 -66.999 -7.5s-40.833 -11.167 -58.5 -18.5s-32.5 -15 -44.5 -23s-20 -14.667 -24 -20l-42 184c14 20 30.5 37 49.5 51s39.667 25.5 62 34.5s45.666 15.5 69.999 19.5
s49.166 6 74.499 6c35.333 0 70.833 -4.83301 106.5 -14.5s70.5 -20.5 104.5 -32.5s67 -22.833 99 -32.5s62 -14.5 90 -14.5zM796.001 556c22.667 0 44.501 2.5 65.501 7.5s40 11 57 18s31.5 14.167 43.5 21.5s20 13.333 24 18l34 -192c-14 -18 -30.333 -33.167 -49 -45.5
s-38.5 -22.5 -59.5 -30.5s-43.167 -13.833 -66.5 -17.5s-46.333 -5.5 -69 -5.5c-35.333 0 -70.833 4.83301 -106.5 14.5s-70.5 20.334 -104.5 32.001s-67 22.334 -99 32.001s-62 14.5 -90 14.5c-23.333 0 -45.666 -2.5 -66.999 -7.5s-40.833 -11.167 -58.5 -18.5
s-32.5 -14.833 -44.5 -22.5s-20 -14.167 -24 -19.5l-42 184c14 19.333 30.5 36 49.5 50s39.667 25.5 62 34.5s45.666 15.5 69.999 19.5s49.166 6 74.499 6c35.333 0 70.833 -4.83301 106.5 -14.5s70.5 -20.334 104.5 -32.001s67 -22.334 99 -32.001s62 -14.5 90 -14.5z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" 
d="M124 966h512l108 204h220l-108 -204h178v-223h-295l-76 -144h371v-222h-487l-117 -223h-220l117 223h-203v222h319l76 144h-395v223z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" 
d="M130 878l810 380.998v-206c0 -15.333 -4.33301 -29.833 -13 -43.5s-25.667 -26.167 -51 -37.5l-270 -117c-40.667 -14 -84 -26 -130 -36c24 -5.33301 46.833 -11.166 68.5 -17.499s42.167 -13.166 61.5 -20.499l269 -121c25.333 -11.333 42.5 -23.833 51.5 -37.5
s13.5 -28.167 13.5 -43.5v-206l-810 384v121zM130 301.998h810v-222h-810v222z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" 
d="M1030 878v-120.998l-810 -384v206c0 15.333 4.5 29.833 13.5 43.5s26.167 26.167 51.5 37.5l269 121c19.333 7.33301 39.833 14.166 61.5 20.499s44.167 12.166 67.5 17.499c-47.333 10 -90.333 22 -129 36l-270 117c-25.333 11.333 -42.333 23.833 -51 37.5
s-13 28.167 -13 43.5v206zM1030 80.002h-810v222h810v-222z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" 
d="M100 734l372 804h216l372 -804l-372 -804h-216zM352 734l188.001 -435.999c8.66699 -20 16.167 -38.833 22.5 -56.5s12.166 -34.834 17.499 -51.501l17 51.5c6 17.667 13.667 36.5 23 56.5l194 436l-194 437c-16.667 38 -30 73.667 -40 107
c-5.33301 -16.667 -11.333 -33.667 -18 -51l-22 -56z" />
    <glyph glyph-name="uni2669" unicode="&#x2669;" horiz-adv-x="0" 
d="M-2 1497h4v-1826h-4v1826z" />
    <glyph glyph-name="undercommaaccent" horiz-adv-x="660" 
d="M434 -66c10.667 0 19.333 -1.33301 26 -4s11.5 -6.16699 14.5 -10.5s5 -8.83301 6 -13.5s1.5 -9 1.5 -13c0 -6 -1.16699 -13.5 -3.5 -22.5s-7.16602 -22.167 -14.499 -39.5l-56 -128c-5.33301 -11.333 -11.333 -20.5 -18 -27.5s-14.334 -12.333 -23.001 -16
s-18.5 -6 -29.5 -7s-22.833 -1.5 -35.5 -1.5h-110l52 283h190z" />
    <glyph glyph-name="grave.case" horiz-adv-x="660" 
d="M224 1798c15.333 0 28.834 -0.833008 40.501 -2.5s22 -4.33398 31 -8.00098s17.5 -8.66699 25.5 -15s16.333 -14.166 25 -23.499l182 -198h-246c-12 0 -22.333 0.333008 -31 1s-16.834 2.16699 -24.501 4.5s-15 5.33301 -22 9s-14.5 8.5 -22.5 14.5l-300 218h342z" />
    <glyph glyph-name="dieresis.case" horiz-adv-x="660" 
d="M266 1668c0 -21.333 -4.16699 -41 -12.5 -59s-19.833 -33.833 -34.5 -47.5s-31.667 -24.334 -51 -32.001s-40 -11.5 -62 -11.5c-20 0 -39.167 3.83301 -57.5 11.5s-34.5 18.334 -48.5 32.001s-25.167 29.5 -33.5 47.5s-12.5 37.667 -12.5 59
c0 20.667 4.16699 40.334 12.5 59.001s19.5 35 33.5 49s30.167 25 48.5 33s37.5 12 57.5 12c22 0 42.667 -4 62 -12s36.333 -19 51 -33s26.167 -30.333 34.5 -49s12.5 -38.334 12.5 -59.001zM706 1668c0 -21.333 -4.16699 -41 -12.5 -59s-19.5 -33.833 -33.5 -47.5
s-30.5 -24.334 -49.5 -32.001s-39.167 -11.5 -60.5 -11.5s-41.5 3.83301 -60.5 11.5s-35.5 18.334 -49.5 32.001s-25.167 29.5 -33.5 47.5s-12.5 37.667 -12.5 59c0 20.667 4.16699 40.334 12.5 59.001s19.5 35 33.5 49s30.5 25 49.5 33s39.167 12 60.5 12s41.5 -4 60.5 -12
s35.5 -19 49.5 -33s25.167 -30.333 33.5 -49s12.5 -38.334 12.5 -59.001z" />
    <glyph glyph-name="macron.case" horiz-adv-x="660" 
d="M40 1725h580v-171h-580v171z" />
    <glyph glyph-name="acute.case" horiz-adv-x="660" 
d="M774 1798l-294.997 -214.001c-17.333 -13.333 -33.5 -22.166 -48.5 -26.499s-33.833 -6.5 -56.5 -6.5h-246l181 197c8.66699 10 17.167 18.167 25.5 24.5s17.166 11.5 26.499 15.5s19.666 6.66699 30.999 8s24.666 2 39.999 2h342z" />
    <glyph glyph-name="circumflex.case" horiz-adv-x="660" 
d="M730 1551h-234c-12 0 -25 1.66699 -39 5s-25.667 7.66602 -35 12.999l-74 45c-3.33301 1.33301 -6.33301 3 -9 5s-5.66699 4 -9 6c-3.33301 -2 -6.33301 -4 -9 -6s-5.66699 -3.66699 -9 -5l-74 -45c-9.33301 -5.33301 -21 -9.66602 -35 -12.999s-27 -5 -39 -5h-234
l244 227h312z" />
    <glyph glyph-name="caron.case" horiz-adv-x="660" 
d="M-70 1778l234 0.000976562c12 0 25 -1.66699 39 -5s25.667 -7.66602 35 -12.999l74 -45l9 -5.5c2.66699 -1.66699 5.66699 -3.83398 9 -6.50098c2.66699 2 5.5 4 8.5 6s6.16699 4 9.5 6l74 45c9.33301 5.33301 21 9.66602 35 12.999s27 5 39 5h234l-244 -227h-312z" />
    <glyph glyph-name="breve.case" horiz-adv-x="660" 
d="M330 1526c-52.667 0 -99.832 5.00098 -141.499 15.001s-77 25.5 -106 46.5s-51.333 47.167 -67 78.5s-23.5 68.666 -23.5 111.999h196c0 -28.667 12.167 -48.334 36.5 -59.001s59.5 -16 105.5 -16s81.167 5.33301 105.5 16s36.5 30.334 36.5 59.001h196
c0 -38 -7.83301 -72.5 -23.5 -103.5s-38 -57.5 -67 -79.5s-64.333 -39 -106 -51s-88.834 -18 -141.501 -18z" />
    <glyph glyph-name="dotaccent.case" horiz-adv-x="660" 
d="M508 1702c0 -22.667 -4.83301 -44.334 -14.5 -65.001s-22.667 -38.667 -39 -54s-35.666 -27.666 -57.999 -36.999s-45.833 -14 -70.5 -14c-23.333 0 -45.5 4.66699 -66.5 14s-39.5 21.666 -55.5 36.999s-28.667 33.333 -38 54s-14 42.334 -14 65.001
c0 24 4.66699 46.5 14 67.5s22 39.5 38 55.5s34.5 28.667 55.5 38s43.167 14 66.5 14c24.667 0 48.167 -4.66699 70.5 -14s41.666 -22 57.999 -38s29.333 -34.5 39 -55.5s14.5 -43.5 14.5 -67.5z" />
    <glyph glyph-name="ring.case" horiz-adv-x="660" 
d="M106 1701c0 30.667 6.16699 58.5 18.5 83.5s28.666 46.333 48.999 64s43.833 31.334 70.5 41.001s54.667 14.5 84 14.5c30.667 0 59.667 -4.83301 87 -14.5s51.5 -23.334 72.5 -41.001s37.667 -39 50 -64s18.5 -52.833 18.5 -83.5
c0 -29.333 -6.16699 -56.166 -18.5 -80.499s-29 -45.166 -50 -62.499s-45.167 -30.666 -72.5 -39.999s-56.333 -14 -87 -14c-29.333 0 -57.333 4.66699 -84 14s-50.167 22.666 -70.5 39.999s-36.666 38.166 -48.999 62.499s-18.5 51.166 -18.5 80.499zM252 1701
c0 -23.333 6.5 -42.5 19.5 -57.5s33.167 -22.5 60.5 -22.5c24 0 43 7.5 57 22.5s21 34.167 21 57.5c0 25.333 -7 45.333 -21 60s-33 22 -57 22c-27.333 0 -47.5 -7.33301 -60.5 -22s-19.5 -34.667 -19.5 -60z" />
    <glyph glyph-name="tilde.case" horiz-adv-x="660" 
d="M454 1729c14 0 25.833 3.66895 35.5 11.002s14.5 20.666 14.5 39.999h168c0 -36.667 -4.83301 -70 -14.5 -100s-23.5 -55.833 -41.5 -77.5s-40 -38.334 -66 -50.001s-55.333 -17.5 -88 -17.5c-24.667 0 -48.834 3 -72.501 9s-46 12.833 -67 20.5l-57.5 20.5
c-17.333 6 -32.333 9 -45 9c-14 0 -25.5 -4.16699 -34.5 -12.5s-13.5 -21.833 -13.5 -40.5h-170c0 36.667 5 70 15 100s24 55.833 42 77.5s40 38.5 66 50.5s55 18 87 18c24.667 0 48.834 -3 72.501 -9s46 -12.667 67 -20l57.5 -20c17.333 -6 32.333 -9 45 -9z" />
    <glyph glyph-name="hungarumlaut.case" horiz-adv-x="660" 
d="M484 1779l-202 -184.999c-15.333 -14 -30 -24.667 -44 -32s-32.667 -11 -56 -11h-146l124 168c13.333 17.333 29.5 31.666 48.5 42.999s44.167 17 75.5 17h200zM874 1779l-262 -185c-18 -12.667 -35 -23 -51 -31s-35.667 -12 -59 -12h-166l176 168c16.667 16 35 30 55 42
s45.667 18 77 18h230z" />
    <glyph glyph-name="caron.salt" horiz-adv-x="660" 
d="M454 1499c10.667 0 19.332 -1.33105 25.999 -3.99805s11.5 -6.16699 14.5 -10.5s5 -8.83301 6 -13.5s1.5 -9 1.5 -13c0 -6.66699 -1.66699 -15.334 -5 -26.001s-10.666 -29.334 -21.999 -56.001l-81 -187c-10 -22.667 -23.667 -37 -41 -43s-39 -9 -65 -9h-110l66 362h210
z" />
    <hkern u1="&#x22;" u2="&#x2206;" k="200" />
    <hkern u1="&#x22;" u2="&#x203a;" k="160" />
    <hkern u1="&#x22;" u2="&#x2039;" k="160" />
    <hkern u1="&#x22;" u2="&#x2022;" k="160" />
    <hkern u1="&#x22;" u2="&#x201e;" k="200" />
    <hkern u1="&#x22;" u2="&#x201a;" k="200" />
    <hkern u1="&#x22;" u2="&#x2014;" k="160" />
    <hkern u1="&#x22;" u2="&#x2013;" k="160" />
    <hkern u1="&#x22;" u2="&#x178;" k="-40" />
    <hkern u1="&#x22;" u2="&#x153;" k="100" />
    <hkern u1="&#x22;" u2="&#x152;" k="40" />
    <hkern u1="&#x22;" u2="&#x119;" k="100" />
    <hkern u1="&#x22;" u2="&#x107;" k="100" />
    <hkern u1="&#x22;" u2="&#x106;" k="40" />
    <hkern u1="&#x22;" u2="&#x105;" k="68" />
    <hkern u1="&#x22;" u2="&#x104;" k="200" />
    <hkern u1="&#x22;" u2="&#xf8;" k="100" />
    <hkern u1="&#x22;" u2="&#xf6;" k="100" />
    <hkern u1="&#x22;" u2="&#xf5;" k="100" />
    <hkern u1="&#x22;" u2="&#xf4;" k="100" />
    <hkern u1="&#x22;" u2="&#xf3;" k="100" />
    <hkern u1="&#x22;" u2="&#xf2;" k="100" />
    <hkern u1="&#x22;" u2="&#xf0;" k="100" />
    <hkern u1="&#x22;" u2="&#xeb;" k="100" />
    <hkern u1="&#x22;" u2="&#xea;" k="100" />
    <hkern u1="&#x22;" u2="&#xe9;" k="100" />
    <hkern u1="&#x22;" u2="&#xe8;" k="100" />
    <hkern u1="&#x22;" u2="&#xe7;" k="100" />
    <hkern u1="&#x22;" u2="&#xe6;" k="68" />
    <hkern u1="&#x22;" u2="&#xe5;" k="68" />
    <hkern u1="&#x22;" u2="&#xe4;" k="68" />
    <hkern u1="&#x22;" u2="&#xe3;" k="68" />
    <hkern u1="&#x22;" u2="&#xe2;" k="68" />
    <hkern u1="&#x22;" u2="&#xe1;" k="68" />
    <hkern u1="&#x22;" u2="&#xe0;" k="68" />
    <hkern u1="&#x22;" u2="&#xdd;" k="-40" />
    <hkern u1="&#x22;" u2="&#xd8;" k="40" />
    <hkern u1="&#x22;" u2="&#xd6;" k="40" />
    <hkern u1="&#x22;" u2="&#xd5;" k="40" />
    <hkern u1="&#x22;" u2="&#xd4;" k="40" />
    <hkern u1="&#x22;" u2="&#xd3;" k="40" />
    <hkern u1="&#x22;" u2="&#xd2;" k="40" />
    <hkern u1="&#x22;" u2="&#xc7;" k="40" />
    <hkern u1="&#x22;" u2="&#xc6;" k="200" />
    <hkern u1="&#x22;" u2="&#xc5;" k="200" />
    <hkern u1="&#x22;" u2="&#xc4;" k="200" />
    <hkern u1="&#x22;" u2="&#xc3;" k="200" />
    <hkern u1="&#x22;" u2="&#xc2;" k="200" />
    <hkern u1="&#x22;" u2="&#xc1;" k="200" />
    <hkern u1="&#x22;" u2="&#xc0;" k="200" />
    <hkern u1="&#x22;" u2="&#xbb;" k="160" />
    <hkern u1="&#x22;" u2="&#xb7;" k="160" />
    <hkern u1="&#x22;" u2="&#xad;" k="160" />
    <hkern u1="&#x22;" u2="&#xab;" k="160" />
    <hkern u1="&#x22;" u2="q" k="100" />
    <hkern u1="&#x22;" u2="o" k="100" />
    <hkern u1="&#x22;" u2="e" k="100" />
    <hkern u1="&#x22;" u2="d" k="100" />
    <hkern u1="&#x22;" u2="c" k="100" />
    <hkern u1="&#x22;" u2="a" k="68" />
    <hkern u1="&#x22;" u2="\" k="-40" />
    <hkern u1="&#x22;" u2="Y" k="-40" />
    <hkern u1="&#x22;" u2="W" k="-40" />
    <hkern u1="&#x22;" u2="V" k="-40" />
    <hkern u1="&#x22;" u2="Q" k="40" />
    <hkern u1="&#x22;" u2="O" k="40" />
    <hkern u1="&#x22;" u2="G" k="40" />
    <hkern u1="&#x22;" u2="C" k="40" />
    <hkern u1="&#x22;" u2="A" k="200" />
    <hkern u1="&#x22;" u2="&#x40;" k="40" />
    <hkern u1="&#x22;" u2="&#x2f;" k="200" />
    <hkern u1="&#x22;" u2="&#x2e;" k="200" />
    <hkern u1="&#x22;" u2="&#x2d;" k="160" />
    <hkern u1="&#x22;" u2="&#x2c;" k="200" />
    <hkern u1="&#x22;" u2="&#x26;" k="200" />
    <hkern u1="&#x27;" u2="&#x2206;" k="200" />
    <hkern u1="&#x27;" u2="&#x203a;" k="160" />
    <hkern u1="&#x27;" u2="&#x2039;" k="160" />
    <hkern u1="&#x27;" u2="&#x2022;" k="160" />
    <hkern u1="&#x27;" u2="&#x201e;" k="200" />
    <hkern u1="&#x27;" u2="&#x201a;" k="200" />
    <hkern u1="&#x27;" u2="&#x2014;" k="160" />
    <hkern u1="&#x27;" u2="&#x2013;" k="160" />
    <hkern u1="&#x27;" u2="&#x178;" k="-40" />
    <hkern u1="&#x27;" u2="&#x153;" k="100" />
    <hkern u1="&#x27;" u2="&#x152;" k="40" />
    <hkern u1="&#x27;" u2="&#x119;" k="100" />
    <hkern u1="&#x27;" u2="&#x107;" k="100" />
    <hkern u1="&#x27;" u2="&#x106;" k="40" />
    <hkern u1="&#x27;" u2="&#x105;" k="68" />
    <hkern u1="&#x27;" u2="&#x104;" k="200" />
    <hkern u1="&#x27;" u2="&#xf8;" k="100" />
    <hkern u1="&#x27;" u2="&#xf6;" k="100" />
    <hkern u1="&#x27;" u2="&#xf5;" k="100" />
    <hkern u1="&#x27;" u2="&#xf4;" k="100" />
    <hkern u1="&#x27;" u2="&#xf3;" k="100" />
    <hkern u1="&#x27;" u2="&#xf2;" k="100" />
    <hkern u1="&#x27;" u2="&#xf0;" k="100" />
    <hkern u1="&#x27;" u2="&#xeb;" k="100" />
    <hkern u1="&#x27;" u2="&#xea;" k="100" />
    <hkern u1="&#x27;" u2="&#xe9;" k="100" />
    <hkern u1="&#x27;" u2="&#xe8;" k="100" />
    <hkern u1="&#x27;" u2="&#xe7;" k="100" />
    <hkern u1="&#x27;" u2="&#xe6;" k="68" />
    <hkern u1="&#x27;" u2="&#xe5;" k="68" />
    <hkern u1="&#x27;" u2="&#xe4;" k="68" />
    <hkern u1="&#x27;" u2="&#xe3;" k="68" />
    <hkern u1="&#x27;" u2="&#xe2;" k="68" />
    <hkern u1="&#x27;" u2="&#xe1;" k="68" />
    <hkern u1="&#x27;" u2="&#xe0;" k="68" />
    <hkern u1="&#x27;" u2="&#xdd;" k="-40" />
    <hkern u1="&#x27;" u2="&#xd8;" k="40" />
    <hkern u1="&#x27;" u2="&#xd6;" k="40" />
    <hkern u1="&#x27;" u2="&#xd5;" k="40" />
    <hkern u1="&#x27;" u2="&#xd4;" k="40" />
    <hkern u1="&#x27;" u2="&#xd3;" k="40" />
    <hkern u1="&#x27;" u2="&#xd2;" k="40" />
    <hkern u1="&#x27;" u2="&#xc7;" k="40" />
    <hkern u1="&#x27;" u2="&#xc6;" k="200" />
    <hkern u1="&#x27;" u2="&#xc5;" k="200" />
    <hkern u1="&#x27;" u2="&#xc4;" k="200" />
    <hkern u1="&#x27;" u2="&#xc3;" k="200" />
    <hkern u1="&#x27;" u2="&#xc2;" k="200" />
    <hkern u1="&#x27;" u2="&#xc1;" k="200" />
    <hkern u1="&#x27;" u2="&#xc0;" k="200" />
    <hkern u1="&#x27;" u2="&#xbb;" k="160" />
    <hkern u1="&#x27;" u2="&#xb7;" k="160" />
    <hkern u1="&#x27;" u2="&#xad;" k="160" />
    <hkern u1="&#x27;" u2="&#xab;" k="160" />
    <hkern u1="&#x27;" u2="q" k="100" />
    <hkern u1="&#x27;" u2="o" k="100" />
    <hkern u1="&#x27;" u2="e" k="100" />
    <hkern u1="&#x27;" u2="d" k="100" />
    <hkern u1="&#x27;" u2="c" k="100" />
    <hkern u1="&#x27;" u2="a" k="68" />
    <hkern u1="&#x27;" u2="\" k="-40" />
    <hkern u1="&#x27;" u2="Y" k="-40" />
    <hkern u1="&#x27;" u2="W" k="-40" />
    <hkern u1="&#x27;" u2="V" k="-40" />
    <hkern u1="&#x27;" u2="Q" k="40" />
    <hkern u1="&#x27;" u2="O" k="40" />
    <hkern u1="&#x27;" u2="G" k="40" />
    <hkern u1="&#x27;" u2="C" k="40" />
    <hkern u1="&#x27;" u2="A" k="200" />
    <hkern u1="&#x27;" u2="&#x40;" k="40" />
    <hkern u1="&#x27;" u2="&#x2f;" k="200" />
    <hkern u1="&#x27;" u2="&#x2e;" k="200" />
    <hkern u1="&#x27;" u2="&#x2d;" k="160" />
    <hkern u1="&#x27;" u2="&#x2c;" k="200" />
    <hkern u1="&#x27;" u2="&#x26;" k="200" />
    <hkern u1="&#x28;" u2="&#x153;" k="40" />
    <hkern u1="&#x28;" u2="&#x152;" k="40" />
    <hkern u1="&#x28;" u2="&#x119;" k="40" />
    <hkern u1="&#x28;" u2="&#x107;" k="40" />
    <hkern u1="&#x28;" u2="&#x106;" k="40" />
    <hkern u1="&#x28;" u2="&#xf8;" k="40" />
    <hkern u1="&#x28;" u2="&#xf6;" k="40" />
    <hkern u1="&#x28;" u2="&#xf5;" k="40" />
    <hkern u1="&#x28;" u2="&#xf4;" k="40" />
    <hkern u1="&#x28;" u2="&#xf3;" k="40" />
    <hkern u1="&#x28;" u2="&#xf2;" k="40" />
    <hkern u1="&#x28;" u2="&#xf0;" k="40" />
    <hkern u1="&#x28;" u2="&#xeb;" k="40" />
    <hkern u1="&#x28;" u2="&#xea;" k="40" />
    <hkern u1="&#x28;" u2="&#xe9;" k="40" />
    <hkern u1="&#x28;" u2="&#xe8;" k="40" />
    <hkern u1="&#x28;" u2="&#xe7;" k="40" />
    <hkern u1="&#x28;" u2="&#xd8;" k="40" />
    <hkern u1="&#x28;" u2="&#xd6;" k="40" />
    <hkern u1="&#x28;" u2="&#xd5;" k="40" />
    <hkern u1="&#x28;" u2="&#xd4;" k="40" />
    <hkern u1="&#x28;" u2="&#xd3;" k="40" />
    <hkern u1="&#x28;" u2="&#xd2;" k="40" />
    <hkern u1="&#x28;" u2="&#xc7;" k="40" />
    <hkern u1="&#x28;" u2="q" k="40" />
    <hkern u1="&#x28;" u2="o" k="40" />
    <hkern u1="&#x28;" u2="e" k="40" />
    <hkern u1="&#x28;" u2="d" k="40" />
    <hkern u1="&#x28;" u2="c" k="40" />
    <hkern u1="&#x28;" u2="Q" k="40" />
    <hkern u1="&#x28;" u2="O" k="40" />
    <hkern u1="&#x28;" u2="G" k="40" />
    <hkern u1="&#x28;" u2="C" k="40" />
    <hkern u1="&#x28;" u2="&#x40;" k="40" />
    <hkern u1="&#x2a;" u2="&#x2206;" k="200" />
    <hkern u1="&#x2a;" u2="&#x203a;" k="160" />
    <hkern u1="&#x2a;" u2="&#x2039;" k="160" />
    <hkern u1="&#x2a;" u2="&#x2022;" k="160" />
    <hkern u1="&#x2a;" u2="&#x201e;" k="200" />
    <hkern u1="&#x2a;" u2="&#x201a;" k="200" />
    <hkern u1="&#x2a;" u2="&#x2014;" k="160" />
    <hkern u1="&#x2a;" u2="&#x2013;" k="160" />
    <hkern u1="&#x2a;" u2="&#x178;" k="-40" />
    <hkern u1="&#x2a;" u2="&#x153;" k="100" />
    <hkern u1="&#x2a;" u2="&#x152;" k="40" />
    <hkern u1="&#x2a;" u2="&#x119;" k="100" />
    <hkern u1="&#x2a;" u2="&#x107;" k="100" />
    <hkern u1="&#x2a;" u2="&#x106;" k="40" />
    <hkern u1="&#x2a;" u2="&#x105;" k="68" />
    <hkern u1="&#x2a;" u2="&#x104;" k="200" />
    <hkern u1="&#x2a;" u2="&#xf8;" k="100" />
    <hkern u1="&#x2a;" u2="&#xf6;" k="100" />
    <hkern u1="&#x2a;" u2="&#xf5;" k="100" />
    <hkern u1="&#x2a;" u2="&#xf4;" k="100" />
    <hkern u1="&#x2a;" u2="&#xf3;" k="100" />
    <hkern u1="&#x2a;" u2="&#xf2;" k="100" />
    <hkern u1="&#x2a;" u2="&#xf0;" k="100" />
    <hkern u1="&#x2a;" u2="&#xeb;" k="100" />
    <hkern u1="&#x2a;" u2="&#xea;" k="100" />
    <hkern u1="&#x2a;" u2="&#xe9;" k="100" />
    <hkern u1="&#x2a;" u2="&#xe8;" k="100" />
    <hkern u1="&#x2a;" u2="&#xe7;" k="100" />
    <hkern u1="&#x2a;" u2="&#xe6;" k="68" />
    <hkern u1="&#x2a;" u2="&#xe5;" k="68" />
    <hkern u1="&#x2a;" u2="&#xe4;" k="68" />
    <hkern u1="&#x2a;" u2="&#xe3;" k="68" />
    <hkern u1="&#x2a;" u2="&#xe2;" k="68" />
    <hkern u1="&#x2a;" u2="&#xe1;" k="68" />
    <hkern u1="&#x2a;" u2="&#xe0;" k="68" />
    <hkern u1="&#x2a;" u2="&#xdd;" k="-40" />
    <hkern u1="&#x2a;" u2="&#xd8;" k="40" />
    <hkern u1="&#x2a;" u2="&#xd6;" k="40" />
    <hkern u1="&#x2a;" u2="&#xd5;" k="40" />
    <hkern u1="&#x2a;" u2="&#xd4;" k="40" />
    <hkern u1="&#x2a;" u2="&#xd3;" k="40" />
    <hkern u1="&#x2a;" u2="&#xd2;" k="40" />
    <hkern u1="&#x2a;" u2="&#xc7;" k="40" />
    <hkern u1="&#x2a;" u2="&#xc6;" k="200" />
    <hkern u1="&#x2a;" u2="&#xc5;" k="200" />
    <hkern u1="&#x2a;" u2="&#xc4;" k="200" />
    <hkern u1="&#x2a;" u2="&#xc3;" k="200" />
    <hkern u1="&#x2a;" u2="&#xc2;" k="200" />
    <hkern u1="&#x2a;" u2="&#xc1;" k="200" />
    <hkern u1="&#x2a;" u2="&#xc0;" k="200" />
    <hkern u1="&#x2a;" u2="&#xbb;" k="160" />
    <hkern u1="&#x2a;" u2="&#xb7;" k="160" />
    <hkern u1="&#x2a;" u2="&#xad;" k="160" />
    <hkern u1="&#x2a;" u2="&#xab;" k="160" />
    <hkern u1="&#x2a;" u2="q" k="100" />
    <hkern u1="&#x2a;" u2="o" k="100" />
    <hkern u1="&#x2a;" u2="e" k="100" />
    <hkern u1="&#x2a;" u2="d" k="100" />
    <hkern u1="&#x2a;" u2="c" k="100" />
    <hkern u1="&#x2a;" u2="a" k="68" />
    <hkern u1="&#x2a;" u2="\" k="-40" />
    <hkern u1="&#x2a;" u2="Y" k="-40" />
    <hkern u1="&#x2a;" u2="W" k="-40" />
    <hkern u1="&#x2a;" u2="V" k="-40" />
    <hkern u1="&#x2a;" u2="Q" k="40" />
    <hkern u1="&#x2a;" u2="O" k="40" />
    <hkern u1="&#x2a;" u2="G" k="40" />
    <hkern u1="&#x2a;" u2="C" k="40" />
    <hkern u1="&#x2a;" u2="A" k="200" />
    <hkern u1="&#x2a;" u2="&#x40;" k="40" />
    <hkern u1="&#x2a;" u2="&#x2f;" k="200" />
    <hkern u1="&#x2a;" u2="&#x2e;" k="200" />
    <hkern u1="&#x2a;" u2="&#x2d;" k="160" />
    <hkern u1="&#x2a;" u2="&#x2c;" k="200" />
    <hkern u1="&#x2a;" u2="&#x26;" k="200" />
    <hkern u1="&#x2c;" u2="&#x2122;" k="200" />
    <hkern u1="&#x2c;" u2="&#x203a;" k="130" />
    <hkern u1="&#x2c;" u2="&#x2039;" k="130" />
    <hkern u1="&#x2c;" u2="&#x2022;" k="130" />
    <hkern u1="&#x2c;" u2="&#x201d;" k="200" />
    <hkern u1="&#x2c;" u2="&#x201c;" k="200" />
    <hkern u1="&#x2c;" u2="&#x2019;" k="200" />
    <hkern u1="&#x2c;" u2="&#x2018;" k="200" />
    <hkern u1="&#x2c;" u2="&#x2014;" k="130" />
    <hkern u1="&#x2c;" u2="&#x2013;" k="130" />
    <hkern u1="&#x2c;" u2="&#x178;" k="180" />
    <hkern u1="&#x2c;" u2="&#x152;" k="50" />
    <hkern u1="&#x2c;" u2="&#x106;" k="50" />
    <hkern u1="&#x2c;" u2="&#xdd;" k="180" />
    <hkern u1="&#x2c;" u2="&#xd8;" k="50" />
    <hkern u1="&#x2c;" u2="&#xd6;" k="50" />
    <hkern u1="&#x2c;" u2="&#xd5;" k="50" />
    <hkern u1="&#x2c;" u2="&#xd4;" k="50" />
    <hkern u1="&#x2c;" u2="&#xd3;" k="50" />
    <hkern u1="&#x2c;" u2="&#xd2;" k="50" />
    <hkern u1="&#x2c;" u2="&#xc7;" k="50" />
    <hkern u1="&#x2c;" u2="&#xbb;" k="130" />
    <hkern u1="&#x2c;" u2="&#xba;" k="200" />
    <hkern u1="&#x2c;" u2="&#xb7;" k="130" />
    <hkern u1="&#x2c;" u2="&#xb0;" k="200" />
    <hkern u1="&#x2c;" u2="&#xad;" k="130" />
    <hkern u1="&#x2c;" u2="&#xab;" k="130" />
    <hkern u1="&#x2c;" u2="&#xaa;" k="200" />
    <hkern u1="&#x2c;" u2="y" k="140" />
    <hkern u1="&#x2c;" u2="w" k="80" />
    <hkern u1="&#x2c;" u2="v" k="140" />
    <hkern u1="&#x2c;" u2="\" k="180" />
    <hkern u1="&#x2c;" u2="Y" k="180" />
    <hkern u1="&#x2c;" u2="W" k="140" />
    <hkern u1="&#x2c;" u2="V" k="180" />
    <hkern u1="&#x2c;" u2="T" k="180" />
    <hkern u1="&#x2c;" u2="Q" k="50" />
    <hkern u1="&#x2c;" u2="O" k="50" />
    <hkern u1="&#x2c;" u2="G" k="50" />
    <hkern u1="&#x2c;" u2="C" k="50" />
    <hkern u1="&#x2c;" u2="&#x40;" k="50" />
    <hkern u1="&#x2c;" u2="&#x2d;" k="130" />
    <hkern u1="&#x2c;" u2="&#x2a;" k="200" />
    <hkern u1="&#x2c;" u2="&#x27;" k="200" />
    <hkern u1="&#x2c;" u2="&#x22;" k="200" />
    <hkern u1="&#x2d;" u2="&#x2206;" k="80" />
    <hkern u1="&#x2d;" u2="&#x2122;" k="160" />
    <hkern u1="&#x2d;" u2="&#x201e;" k="130" />
    <hkern u1="&#x2d;" u2="&#x201d;" k="160" />
    <hkern u1="&#x2d;" u2="&#x201c;" k="160" />
    <hkern u1="&#x2d;" u2="&#x201a;" k="130" />
    <hkern u1="&#x2d;" u2="&#x2019;" k="160" />
    <hkern u1="&#x2d;" u2="&#x2018;" k="160" />
    <hkern u1="&#x2d;" u2="&#x17d;" k="50" />
    <hkern u1="&#x2d;" u2="&#x17b;" k="50" />
    <hkern u1="&#x2d;" u2="&#x179;" k="50" />
    <hkern u1="&#x2d;" u2="&#x178;" k="160" />
    <hkern u1="&#x2d;" u2="&#x104;" k="80" />
    <hkern u1="&#x2d;" u2="&#xdd;" k="160" />
    <hkern u1="&#x2d;" u2="&#xc6;" k="80" />
    <hkern u1="&#x2d;" u2="&#xc5;" k="80" />
    <hkern u1="&#x2d;" u2="&#xc4;" k="80" />
    <hkern u1="&#x2d;" u2="&#xc3;" k="80" />
    <hkern u1="&#x2d;" u2="&#xc2;" k="80" />
    <hkern u1="&#x2d;" u2="&#xc1;" k="80" />
    <hkern u1="&#x2d;" u2="&#xc0;" k="80" />
    <hkern u1="&#x2d;" u2="&#xba;" k="160" />
    <hkern u1="&#x2d;" u2="&#xb0;" k="160" />
    <hkern u1="&#x2d;" u2="&#xaa;" k="160" />
    <hkern u1="&#x2d;" u2="\" k="120" />
    <hkern u1="&#x2d;" u2="Z" k="50" />
    <hkern u1="&#x2d;" u2="Y" k="160" />
    <hkern u1="&#x2d;" u2="X" k="70" />
    <hkern u1="&#x2d;" u2="W" k="40" />
    <hkern u1="&#x2d;" u2="V" k="120" />
    <hkern u1="&#x2d;" u2="T" k="180" />
    <hkern u1="&#x2d;" u2="A" k="80" />
    <hkern u1="&#x2d;" u2="&#x2f;" k="80" />
    <hkern u1="&#x2d;" u2="&#x2e;" k="130" />
    <hkern u1="&#x2d;" u2="&#x2c;" k="130" />
    <hkern u1="&#x2d;" u2="&#x2a;" k="160" />
    <hkern u1="&#x2d;" u2="&#x27;" k="160" />
    <hkern u1="&#x2d;" u2="&#x26;" k="80" />
    <hkern u1="&#x2d;" u2="&#x22;" k="160" />
    <hkern u1="&#x2e;" u2="&#x2122;" k="200" />
    <hkern u1="&#x2e;" u2="&#x203a;" k="130" />
    <hkern u1="&#x2e;" u2="&#x2039;" k="130" />
    <hkern u1="&#x2e;" u2="&#x2022;" k="130" />
    <hkern u1="&#x2e;" u2="&#x201d;" k="200" />
    <hkern u1="&#x2e;" u2="&#x201c;" k="200" />
    <hkern u1="&#x2e;" u2="&#x2019;" k="200" />
    <hkern u1="&#x2e;" u2="&#x2018;" k="200" />
    <hkern u1="&#x2e;" u2="&#x2014;" k="130" />
    <hkern u1="&#x2e;" u2="&#x2013;" k="130" />
    <hkern u1="&#x2e;" u2="&#x178;" k="180" />
    <hkern u1="&#x2e;" u2="&#x152;" k="50" />
    <hkern u1="&#x2e;" u2="&#x106;" k="50" />
    <hkern u1="&#x2e;" u2="&#xdd;" k="180" />
    <hkern u1="&#x2e;" u2="&#xd8;" k="50" />
    <hkern u1="&#x2e;" u2="&#xd6;" k="50" />
    <hkern u1="&#x2e;" u2="&#xd5;" k="50" />
    <hkern u1="&#x2e;" u2="&#xd4;" k="50" />
    <hkern u1="&#x2e;" u2="&#xd3;" k="50" />
    <hkern u1="&#x2e;" u2="&#xd2;" k="50" />
    <hkern u1="&#x2e;" u2="&#xc7;" k="50" />
    <hkern u1="&#x2e;" u2="&#xbb;" k="130" />
    <hkern u1="&#x2e;" u2="&#xba;" k="200" />
    <hkern u1="&#x2e;" u2="&#xb7;" k="130" />
    <hkern u1="&#x2e;" u2="&#xb0;" k="200" />
    <hkern u1="&#x2e;" u2="&#xad;" k="130" />
    <hkern u1="&#x2e;" u2="&#xab;" k="130" />
    <hkern u1="&#x2e;" u2="&#xaa;" k="200" />
    <hkern u1="&#x2e;" u2="y" k="140" />
    <hkern u1="&#x2e;" u2="w" k="80" />
    <hkern u1="&#x2e;" u2="v" k="140" />
    <hkern u1="&#x2e;" u2="\" k="180" />
    <hkern u1="&#x2e;" u2="Y" k="180" />
    <hkern u1="&#x2e;" u2="W" k="140" />
    <hkern u1="&#x2e;" u2="V" k="180" />
    <hkern u1="&#x2e;" u2="T" k="180" />
    <hkern u1="&#x2e;" u2="Q" k="50" />
    <hkern u1="&#x2e;" u2="O" k="50" />
    <hkern u1="&#x2e;" u2="G" k="50" />
    <hkern u1="&#x2e;" u2="C" k="50" />
    <hkern u1="&#x2e;" u2="&#x40;" k="50" />
    <hkern u1="&#x2e;" u2="&#x2d;" k="130" />
    <hkern u1="&#x2e;" u2="&#x2a;" k="200" />
    <hkern u1="&#x2e;" u2="&#x27;" k="200" />
    <hkern u1="&#x2e;" u2="&#x22;" k="200" />
    <hkern u1="&#x2f;" u2="&#x2206;" k="200" />
    <hkern u1="&#x2f;" u2="&#x2122;" k="-40" />
    <hkern u1="&#x2f;" u2="&#x203a;" k="120" />
    <hkern u1="&#x2f;" u2="&#x2039;" k="120" />
    <hkern u1="&#x2f;" u2="&#x2022;" k="120" />
    <hkern u1="&#x2f;" u2="&#x201e;" k="200" />
    <hkern u1="&#x2f;" u2="&#x201d;" k="-40" />
    <hkern u1="&#x2f;" u2="&#x201c;" k="-40" />
    <hkern u1="&#x2f;" u2="&#x201a;" k="200" />
    <hkern u1="&#x2f;" u2="&#x2019;" k="-40" />
    <hkern u1="&#x2f;" u2="&#x2018;" k="-40" />
    <hkern u1="&#x2f;" u2="&#x2014;" k="120" />
    <hkern u1="&#x2f;" u2="&#x2013;" k="120" />
    <hkern u1="&#x2f;" u2="&#x153;" k="130" />
    <hkern u1="&#x2f;" u2="&#x152;" k="60" />
    <hkern u1="&#x2f;" u2="&#x144;" k="110" />
    <hkern u1="&#x2f;" u2="&#x119;" k="130" />
    <hkern u1="&#x2f;" u2="&#x107;" k="130" />
    <hkern u1="&#x2f;" u2="&#x106;" k="60" />
    <hkern u1="&#x2f;" u2="&#x105;" k="130" />
    <hkern u1="&#x2f;" u2="&#x104;" k="200" />
    <hkern u1="&#x2f;" u2="&#xfc;" k="110" />
    <hkern u1="&#x2f;" u2="&#xfb;" k="110" />
    <hkern u1="&#x2f;" u2="&#xfa;" k="110" />
    <hkern u1="&#x2f;" u2="&#xf9;" k="110" />
    <hkern u1="&#x2f;" u2="&#xf8;" k="130" />
    <hkern u1="&#x2f;" u2="&#xf6;" k="130" />
    <hkern u1="&#x2f;" u2="&#xf5;" k="130" />
    <hkern u1="&#x2f;" u2="&#xf4;" k="130" />
    <hkern u1="&#x2f;" u2="&#xf3;" k="130" />
    <hkern u1="&#x2f;" u2="&#xf2;" k="130" />
    <hkern u1="&#x2f;" u2="&#xf1;" k="110" />
    <hkern u1="&#x2f;" u2="&#xf0;" k="130" />
    <hkern u1="&#x2f;" u2="&#xeb;" k="130" />
    <hkern u1="&#x2f;" u2="&#xea;" k="130" />
    <hkern u1="&#x2f;" u2="&#xe9;" k="130" />
    <hkern u1="&#x2f;" u2="&#xe8;" k="130" />
    <hkern u1="&#x2f;" u2="&#xe7;" k="130" />
    <hkern u1="&#x2f;" u2="&#xe6;" k="130" />
    <hkern u1="&#x2f;" u2="&#xe5;" k="130" />
    <hkern u1="&#x2f;" u2="&#xe4;" k="130" />
    <hkern u1="&#x2f;" u2="&#xe3;" k="130" />
    <hkern u1="&#x2f;" u2="&#xe2;" k="130" />
    <hkern u1="&#x2f;" u2="&#xe1;" k="130" />
    <hkern u1="&#x2f;" u2="&#xe0;" k="130" />
    <hkern u1="&#x2f;" u2="&#xd8;" k="60" />
    <hkern u1="&#x2f;" u2="&#xd6;" k="60" />
    <hkern u1="&#x2f;" u2="&#xd5;" k="60" />
    <hkern u1="&#x2f;" u2="&#xd4;" k="60" />
    <hkern u1="&#x2f;" u2="&#xd3;" k="60" />
    <hkern u1="&#x2f;" u2="&#xd2;" k="60" />
    <hkern u1="&#x2f;" u2="&#xc7;" k="60" />
    <hkern u1="&#x2f;" u2="&#xc6;" k="200" />
    <hkern u1="&#x2f;" u2="&#xc5;" k="200" />
    <hkern u1="&#x2f;" u2="&#xc4;" k="200" />
    <hkern u1="&#x2f;" u2="&#xc3;" k="200" />
    <hkern u1="&#x2f;" u2="&#xc2;" k="200" />
    <hkern u1="&#x2f;" u2="&#xc1;" k="200" />
    <hkern u1="&#x2f;" u2="&#xc0;" k="200" />
    <hkern u1="&#x2f;" u2="&#xbb;" k="120" />
    <hkern u1="&#x2f;" u2="&#xba;" k="-40" />
    <hkern u1="&#x2f;" u2="&#xb9;" k="-40" />
    <hkern u1="&#x2f;" u2="&#xb7;" k="120" />
    <hkern u1="&#x2f;" u2="&#xb5;" k="110" />
    <hkern u1="&#x2f;" u2="&#xb3;" k="-40" />
    <hkern u1="&#x2f;" u2="&#xb2;" k="-40" />
    <hkern u1="&#x2f;" u2="&#xb0;" k="-40" />
    <hkern u1="&#x2f;" u2="&#xad;" k="120" />
    <hkern u1="&#x2f;" u2="&#xab;" k="120" />
    <hkern u1="&#x2f;" u2="&#xaa;" k="-40" />
    <hkern u1="&#x2f;" u2="z" k="100" />
    <hkern u1="&#x2f;" u2="y" k="70" />
    <hkern u1="&#x2f;" u2="x" k="80" />
    <hkern u1="&#x2f;" u2="v" k="70" />
    <hkern u1="&#x2f;" u2="u" k="110" />
    <hkern u1="&#x2f;" u2="t" k="50" />
    <hkern u1="&#x2f;" u2="s" k="110" />
    <hkern u1="&#x2f;" u2="r" k="110" />
    <hkern u1="&#x2f;" u2="q" k="130" />
    <hkern u1="&#x2f;" u2="p" k="110" />
    <hkern u1="&#x2f;" u2="o" k="130" />
    <hkern u1="&#x2f;" u2="n" k="110" />
    <hkern u1="&#x2f;" u2="m" k="110" />
    <hkern u1="&#x2f;" u2="g" k="140" />
    <hkern u1="&#x2f;" u2="f" k="30" />
    <hkern u1="&#x2f;" u2="e" k="130" />
    <hkern u1="&#x2f;" u2="d" k="130" />
    <hkern u1="&#x2f;" u2="c" k="130" />
    <hkern u1="&#x2f;" u2="a" k="130" />
    <hkern u1="&#x2f;" u2="Q" k="60" />
    <hkern u1="&#x2f;" u2="O" k="60" />
    <hkern u1="&#x2f;" u2="J" k="160" />
    <hkern u1="&#x2f;" u2="G" k="60" />
    <hkern u1="&#x2f;" u2="C" k="60" />
    <hkern u1="&#x2f;" u2="A" k="200" />
    <hkern u1="&#x2f;" u2="&#x40;" k="60" />
    <hkern u1="&#x2f;" u2="&#x3f;" k="-30" />
    <hkern u1="&#x2f;" u2="&#x3b;" k="110" />
    <hkern u1="&#x2f;" u2="&#x3a;" k="110" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="200" />
    <hkern u1="&#x2f;" u2="&#x2e;" k="200" />
    <hkern u1="&#x2f;" u2="&#x2d;" k="120" />
    <hkern u1="&#x2f;" u2="&#x2c;" k="200" />
    <hkern u1="&#x2f;" u2="&#x2a;" k="-40" />
    <hkern u1="&#x2f;" u2="&#x27;" k="-40" />
    <hkern u1="&#x2f;" u2="&#x26;" k="200" />
    <hkern u1="&#x2f;" u2="&#x22;" k="-40" />
    <hkern u1="&#x40;" u2="&#x2206;" k="60" />
    <hkern u1="&#x40;" u2="&#x2122;" k="40" />
    <hkern u1="&#x40;" u2="&#x201e;" k="50" />
    <hkern u1="&#x40;" u2="&#x201d;" k="40" />
    <hkern u1="&#x40;" u2="&#x201c;" k="40" />
    <hkern u1="&#x40;" u2="&#x201a;" k="50" />
    <hkern u1="&#x40;" u2="&#x2019;" k="40" />
    <hkern u1="&#x40;" u2="&#x2018;" k="40" />
    <hkern u1="&#x40;" u2="&#x17d;" k="60" />
    <hkern u1="&#x40;" u2="&#x17b;" k="60" />
    <hkern u1="&#x40;" u2="&#x179;" k="60" />
    <hkern u1="&#x40;" u2="&#x178;" k="80" />
    <hkern u1="&#x40;" u2="&#x104;" k="60" />
    <hkern u1="&#x40;" u2="&#xdd;" k="80" />
    <hkern u1="&#x40;" u2="&#xc6;" k="60" />
    <hkern u1="&#x40;" u2="&#xc5;" k="60" />
    <hkern u1="&#x40;" u2="&#xc4;" k="60" />
    <hkern u1="&#x40;" u2="&#xc3;" k="60" />
    <hkern u1="&#x40;" u2="&#xc2;" k="60" />
    <hkern u1="&#x40;" u2="&#xc1;" k="60" />
    <hkern u1="&#x40;" u2="&#xc0;" k="60" />
    <hkern u1="&#x40;" u2="&#xba;" k="40" />
    <hkern u1="&#x40;" u2="&#xb0;" k="40" />
    <hkern u1="&#x40;" u2="&#xaa;" k="40" />
    <hkern u1="&#x40;" u2="&#x7d;" k="40" />
    <hkern u1="&#x40;" u2="]" k="40" />
    <hkern u1="&#x40;" u2="\" k="60" />
    <hkern u1="&#x40;" u2="Z" k="60" />
    <hkern u1="&#x40;" u2="Y" k="80" />
    <hkern u1="&#x40;" u2="X" k="30" />
    <hkern u1="&#x40;" u2="V" k="60" />
    <hkern u1="&#x40;" u2="T" k="60" />
    <hkern u1="&#x40;" u2="A" k="60" />
    <hkern u1="&#x40;" u2="&#x2f;" k="60" />
    <hkern u1="&#x40;" u2="&#x2e;" k="50" />
    <hkern u1="&#x40;" u2="&#x2c;" k="50" />
    <hkern u1="&#x40;" u2="&#x2a;" k="40" />
    <hkern u1="&#x40;" u2="&#x29;" k="40" />
    <hkern u1="&#x40;" u2="&#x27;" k="40" />
    <hkern u1="&#x40;" u2="&#x26;" k="60" />
    <hkern u1="&#x40;" u2="&#x22;" k="40" />
    <hkern u1="A" u2="&#x2122;" k="200" />
    <hkern u1="A" u2="&#x203a;" k="80" />
    <hkern u1="A" u2="&#x2039;" k="80" />
    <hkern u1="A" u2="&#x2022;" k="80" />
    <hkern u1="A" u2="&#x201d;" k="200" />
    <hkern u1="A" u2="&#x201c;" k="200" />
    <hkern u1="A" u2="&#x2019;" k="200" />
    <hkern u1="A" u2="&#x2018;" k="200" />
    <hkern u1="A" u2="&#x2014;" k="80" />
    <hkern u1="A" u2="&#x2013;" k="80" />
    <hkern u1="A" u2="&#x178;" k="200" />
    <hkern u1="A" u2="&#x152;" k="60" />
    <hkern u1="A" u2="&#x106;" k="60" />
    <hkern u1="A" u2="&#xdd;" k="200" />
    <hkern u1="A" u2="&#xdc;" k="50" />
    <hkern u1="A" u2="&#xdb;" k="50" />
    <hkern u1="A" u2="&#xda;" k="50" />
    <hkern u1="A" u2="&#xd9;" k="50" />
    <hkern u1="A" u2="&#xd8;" k="60" />
    <hkern u1="A" u2="&#xd6;" k="60" />
    <hkern u1="A" u2="&#xd5;" k="60" />
    <hkern u1="A" u2="&#xd4;" k="60" />
    <hkern u1="A" u2="&#xd3;" k="60" />
    <hkern u1="A" u2="&#xd2;" k="60" />
    <hkern u1="A" u2="&#xc7;" k="60" />
    <hkern u1="A" u2="&#xbb;" k="80" />
    <hkern u1="A" u2="&#xba;" k="200" />
    <hkern u1="A" u2="&#xb9;" k="220" />
    <hkern u1="A" u2="&#xb7;" k="80" />
    <hkern u1="A" u2="&#xb3;" k="220" />
    <hkern u1="A" u2="&#xb2;" k="220" />
    <hkern u1="A" u2="&#xb0;" k="200" />
    <hkern u1="A" u2="&#xad;" k="80" />
    <hkern u1="A" u2="&#xab;" k="80" />
    <hkern u1="A" u2="&#xaa;" k="200" />
    <hkern u1="A" u2="y" k="100" />
    <hkern u1="A" u2="v" k="100" />
    <hkern u1="A" u2="\" k="200" />
    <hkern u1="A" u2="Y" k="200" />
    <hkern u1="A" u2="W" k="120" />
    <hkern u1="A" u2="V" k="200" />
    <hkern u1="A" u2="U" k="50" />
    <hkern u1="A" u2="T" k="160" />
    <hkern u1="A" u2="Q" k="60" />
    <hkern u1="A" u2="O" k="60" />
    <hkern u1="A" u2="J" k="-60" />
    <hkern u1="A" u2="G" k="60" />
    <hkern u1="A" u2="C" k="60" />
    <hkern u1="A" u2="&#x40;" k="60" />
    <hkern u1="A" u2="&#x3f;" k="70" />
    <hkern u1="A" u2="&#x2d;" k="80" />
    <hkern u1="A" u2="&#x2a;" k="200" />
    <hkern u1="A" u2="&#x27;" k="200" />
    <hkern u1="A" u2="&#x22;" k="200" />
    <hkern u1="C" u2="&#x203a;" k="140" />
    <hkern u1="C" u2="&#x2039;" k="140" />
    <hkern u1="C" u2="&#x2022;" k="140" />
    <hkern u1="C" u2="&#x2014;" k="140" />
    <hkern u1="C" u2="&#x2013;" k="140" />
    <hkern u1="C" u2="&#xbb;" k="140" />
    <hkern u1="C" u2="&#xb7;" k="140" />
    <hkern u1="C" u2="&#xad;" k="140" />
    <hkern u1="C" u2="&#xab;" k="140" />
    <hkern u1="C" u2="&#x2d;" k="140" />
    <hkern u1="D" u2="&#x2206;" k="60" />
    <hkern u1="D" u2="&#x2122;" k="40" />
    <hkern u1="D" u2="&#x201e;" k="50" />
    <hkern u1="D" u2="&#x201d;" k="40" />
    <hkern u1="D" u2="&#x201c;" k="40" />
    <hkern u1="D" u2="&#x201a;" k="50" />
    <hkern u1="D" u2="&#x2019;" k="40" />
    <hkern u1="D" u2="&#x2018;" k="40" />
    <hkern u1="D" u2="&#x17d;" k="60" />
    <hkern u1="D" u2="&#x17b;" k="60" />
    <hkern u1="D" u2="&#x179;" k="60" />
    <hkern u1="D" u2="&#x178;" k="80" />
    <hkern u1="D" u2="&#x104;" k="60" />
    <hkern u1="D" u2="&#xdd;" k="80" />
    <hkern u1="D" u2="&#xc6;" k="60" />
    <hkern u1="D" u2="&#xc5;" k="60" />
    <hkern u1="D" u2="&#xc4;" k="60" />
    <hkern u1="D" u2="&#xc3;" k="60" />
    <hkern u1="D" u2="&#xc2;" k="60" />
    <hkern u1="D" u2="&#xc1;" k="60" />
    <hkern u1="D" u2="&#xc0;" k="60" />
    <hkern u1="D" u2="&#xba;" k="40" />
    <hkern u1="D" u2="&#xb0;" k="40" />
    <hkern u1="D" u2="&#xaa;" k="40" />
    <hkern u1="D" u2="&#x7d;" k="40" />
    <hkern u1="D" u2="]" k="40" />
    <hkern u1="D" u2="\" k="60" />
    <hkern u1="D" u2="Z" k="60" />
    <hkern u1="D" u2="Y" k="80" />
    <hkern u1="D" u2="X" k="30" />
    <hkern u1="D" u2="V" k="60" />
    <hkern u1="D" u2="T" k="60" />
    <hkern u1="D" u2="A" k="60" />
    <hkern u1="D" u2="&#x2f;" k="60" />
    <hkern u1="D" u2="&#x2e;" k="50" />
    <hkern u1="D" u2="&#x2c;" k="50" />
    <hkern u1="D" u2="&#x2a;" k="40" />
    <hkern u1="D" u2="&#x29;" k="40" />
    <hkern u1="D" u2="&#x27;" k="40" />
    <hkern u1="D" u2="&#x26;" k="60" />
    <hkern u1="D" u2="&#x22;" k="40" />
    <hkern u1="F" u2="&#x2206;" k="160" />
    <hkern u1="F" u2="&#x201e;" k="180" />
    <hkern u1="F" u2="&#x201a;" k="180" />
    <hkern u1="F" u2="&#x153;" k="60" />
    <hkern u1="F" u2="&#x144;" k="60" />
    <hkern u1="F" u2="&#x119;" k="60" />
    <hkern u1="F" u2="&#x107;" k="60" />
    <hkern u1="F" u2="&#x104;" k="160" />
    <hkern u1="F" u2="&#xfc;" k="60" />
    <hkern u1="F" u2="&#xfb;" k="60" />
    <hkern u1="F" u2="&#xfa;" k="60" />
    <hkern u1="F" u2="&#xf9;" k="60" />
    <hkern u1="F" u2="&#xf8;" k="60" />
    <hkern u1="F" u2="&#xf6;" k="60" />
    <hkern u1="F" u2="&#xf5;" k="60" />
    <hkern u1="F" u2="&#xf4;" k="60" />
    <hkern u1="F" u2="&#xf3;" k="60" />
    <hkern u1="F" u2="&#xf2;" k="60" />
    <hkern u1="F" u2="&#xf1;" k="60" />
    <hkern u1="F" u2="&#xf0;" k="60" />
    <hkern u1="F" u2="&#xeb;" k="60" />
    <hkern u1="F" u2="&#xea;" k="60" />
    <hkern u1="F" u2="&#xe9;" k="60" />
    <hkern u1="F" u2="&#xe8;" k="60" />
    <hkern u1="F" u2="&#xe7;" k="60" />
    <hkern u1="F" u2="&#xc6;" k="160" />
    <hkern u1="F" u2="&#xc5;" k="160" />
    <hkern u1="F" u2="&#xc4;" k="160" />
    <hkern u1="F" u2="&#xc3;" k="160" />
    <hkern u1="F" u2="&#xc2;" k="160" />
    <hkern u1="F" u2="&#xc1;" k="160" />
    <hkern u1="F" u2="&#xc0;" k="160" />
    <hkern u1="F" u2="&#xb5;" k="60" />
    <hkern u1="F" u2="u" k="60" />
    <hkern u1="F" u2="r" k="60" />
    <hkern u1="F" u2="q" k="60" />
    <hkern u1="F" u2="p" k="60" />
    <hkern u1="F" u2="o" k="60" />
    <hkern u1="F" u2="n" k="60" />
    <hkern u1="F" u2="m" k="60" />
    <hkern u1="F" u2="e" k="60" />
    <hkern u1="F" u2="d" k="60" />
    <hkern u1="F" u2="c" k="60" />
    <hkern u1="F" u2="J" k="180" />
    <hkern u1="F" u2="A" k="160" />
    <hkern u1="F" u2="&#x3f;" k="-30" />
    <hkern u1="F" u2="&#x3b;" k="60" />
    <hkern u1="F" u2="&#x3a;" k="60" />
    <hkern u1="F" u2="&#x2f;" k="160" />
    <hkern u1="F" u2="&#x2e;" k="180" />
    <hkern u1="F" u2="&#x2c;" k="180" />
    <hkern u1="F" u2="&#x26;" k="160" />
    <hkern u1="J" u2="&#x2206;" k="50" />
    <hkern u1="J" u2="&#x201e;" k="50" />
    <hkern u1="J" u2="&#x201a;" k="50" />
    <hkern u1="J" u2="&#x104;" k="50" />
    <hkern u1="J" u2="&#xc6;" k="50" />
    <hkern u1="J" u2="&#xc5;" k="50" />
    <hkern u1="J" u2="&#xc4;" k="50" />
    <hkern u1="J" u2="&#xc3;" k="50" />
    <hkern u1="J" u2="&#xc2;" k="50" />
    <hkern u1="J" u2="&#xc1;" k="50" />
    <hkern u1="J" u2="&#xc0;" k="50" />
    <hkern u1="J" u2="A" k="50" />
    <hkern u1="J" u2="&#x2f;" k="50" />
    <hkern u1="J" u2="&#x2e;" k="50" />
    <hkern u1="J" u2="&#x2c;" k="50" />
    <hkern u1="J" u2="&#x26;" k="50" />
    <hkern u1="K" u2="&#x203a;" k="70" />
    <hkern u1="K" u2="&#x2039;" k="70" />
    <hkern u1="K" u2="&#x2022;" k="70" />
    <hkern u1="K" u2="&#x2014;" k="70" />
    <hkern u1="K" u2="&#x2013;" k="70" />
    <hkern u1="K" u2="&#x153;" k="50" />
    <hkern u1="K" u2="&#x152;" k="30" />
    <hkern u1="K" u2="&#x119;" k="50" />
    <hkern u1="K" u2="&#x107;" k="50" />
    <hkern u1="K" u2="&#x106;" k="30" />
    <hkern u1="K" u2="&#xf8;" k="50" />
    <hkern u1="K" u2="&#xf6;" k="50" />
    <hkern u1="K" u2="&#xf5;" k="50" />
    <hkern u1="K" u2="&#xf4;" k="50" />
    <hkern u1="K" u2="&#xf3;" k="50" />
    <hkern u1="K" u2="&#xf2;" k="50" />
    <hkern u1="K" u2="&#xf0;" k="50" />
    <hkern u1="K" u2="&#xeb;" k="50" />
    <hkern u1="K" u2="&#xea;" k="50" />
    <hkern u1="K" u2="&#xe9;" k="50" />
    <hkern u1="K" u2="&#xe8;" k="50" />
    <hkern u1="K" u2="&#xe7;" k="50" />
    <hkern u1="K" u2="&#xd8;" k="30" />
    <hkern u1="K" u2="&#xd6;" k="30" />
    <hkern u1="K" u2="&#xd5;" k="30" />
    <hkern u1="K" u2="&#xd4;" k="30" />
    <hkern u1="K" u2="&#xd3;" k="30" />
    <hkern u1="K" u2="&#xd2;" k="30" />
    <hkern u1="K" u2="&#xc7;" k="30" />
    <hkern u1="K" u2="&#xbb;" k="70" />
    <hkern u1="K" u2="&#xb7;" k="70" />
    <hkern u1="K" u2="&#xad;" k="70" />
    <hkern u1="K" u2="&#xab;" k="70" />
    <hkern u1="K" u2="y" k="80" />
    <hkern u1="K" u2="w" k="50" />
    <hkern u1="K" u2="v" k="80" />
    <hkern u1="K" u2="t" k="100" />
    <hkern u1="K" u2="q" k="50" />
    <hkern u1="K" u2="o" k="50" />
    <hkern u1="K" u2="f" k="60" />
    <hkern u1="K" u2="e" k="50" />
    <hkern u1="K" u2="d" k="50" />
    <hkern u1="K" u2="c" k="50" />
    <hkern u1="K" u2="Q" k="30" />
    <hkern u1="K" u2="O" k="30" />
    <hkern u1="K" u2="G" k="30" />
    <hkern u1="K" u2="C" k="30" />
    <hkern u1="K" u2="&#x40;" k="30" />
    <hkern u1="K" u2="&#x2d;" k="70" />
    <hkern u1="L" u2="&#x2122;" k="280" />
    <hkern u1="L" u2="&#x203a;" k="160" />
    <hkern u1="L" u2="&#x2039;" k="160" />
    <hkern u1="L" u2="&#x2022;" k="160" />
    <hkern u1="L" u2="&#x201e;" k="-60" />
    <hkern u1="L" u2="&#x201d;" k="280" />
    <hkern u1="L" u2="&#x201c;" k="280" />
    <hkern u1="L" u2="&#x201a;" k="-60" />
    <hkern u1="L" u2="&#x2019;" k="280" />
    <hkern u1="L" u2="&#x2018;" k="280" />
    <hkern u1="L" u2="&#x2014;" k="160" />
    <hkern u1="L" u2="&#x2013;" k="160" />
    <hkern u1="L" u2="&#x178;" k="240" />
    <hkern u1="L" u2="&#x153;" k="40" />
    <hkern u1="L" u2="&#x152;" k="80" />
    <hkern u1="L" u2="&#x119;" k="40" />
    <hkern u1="L" u2="&#x107;" k="40" />
    <hkern u1="L" u2="&#x106;" k="80" />
    <hkern u1="L" u2="&#xf8;" k="40" />
    <hkern u1="L" u2="&#xf6;" k="40" />
    <hkern u1="L" u2="&#xf5;" k="40" />
    <hkern u1="L" u2="&#xf4;" k="40" />
    <hkern u1="L" u2="&#xf3;" k="40" />
    <hkern u1="L" u2="&#xf2;" k="40" />
    <hkern u1="L" u2="&#xf0;" k="40" />
    <hkern u1="L" u2="&#xeb;" k="40" />
    <hkern u1="L" u2="&#xea;" k="40" />
    <hkern u1="L" u2="&#xe9;" k="40" />
    <hkern u1="L" u2="&#xe8;" k="40" />
    <hkern u1="L" u2="&#xe7;" k="40" />
    <hkern u1="L" u2="&#xdd;" k="240" />
    <hkern u1="L" u2="&#xd8;" k="80" />
    <hkern u1="L" u2="&#xd6;" k="80" />
    <hkern u1="L" u2="&#xd5;" k="80" />
    <hkern u1="L" u2="&#xd4;" k="80" />
    <hkern u1="L" u2="&#xd3;" k="80" />
    <hkern u1="L" u2="&#xd2;" k="80" />
    <hkern u1="L" u2="&#xc7;" k="80" />
    <hkern u1="L" u2="&#xbb;" k="160" />
    <hkern u1="L" u2="&#xba;" k="280" />
    <hkern u1="L" u2="&#xb9;" k="220" />
    <hkern u1="L" u2="&#xb7;" k="160" />
    <hkern u1="L" u2="&#xb3;" k="220" />
    <hkern u1="L" u2="&#xb2;" k="220" />
    <hkern u1="L" u2="&#xb0;" k="280" />
    <hkern u1="L" u2="&#xad;" k="160" />
    <hkern u1="L" u2="&#xab;" k="160" />
    <hkern u1="L" u2="&#xaa;" k="280" />
    <hkern u1="L" u2="y" k="130" />
    <hkern u1="L" u2="w" k="80" />
    <hkern u1="L" u2="v" k="130" />
    <hkern u1="L" u2="q" k="40" />
    <hkern u1="L" u2="o" k="40" />
    <hkern u1="L" u2="e" k="40" />
    <hkern u1="L" u2="d" k="40" />
    <hkern u1="L" u2="c" k="40" />
    <hkern u1="L" u2="\" k="200" />
    <hkern u1="L" u2="Y" k="240" />
    <hkern u1="L" u2="W" k="180" />
    <hkern u1="L" u2="V" k="200" />
    <hkern u1="L" u2="T" k="180" />
    <hkern u1="L" u2="Q" k="80" />
    <hkern u1="L" u2="O" k="80" />
    <hkern u1="L" u2="G" k="80" />
    <hkern u1="L" u2="C" k="80" />
    <hkern u1="L" u2="&#x40;" k="80" />
    <hkern u1="L" u2="&#x3f;" k="50" />
    <hkern u1="L" u2="&#x2e;" k="-60" />
    <hkern u1="L" u2="&#x2d;" k="160" />
    <hkern u1="L" u2="&#x2c;" k="-60" />
    <hkern u1="L" u2="&#x2a;" k="280" />
    <hkern u1="L" u2="&#x27;" k="280" />
    <hkern u1="L" u2="&#x22;" k="280" />
    <hkern u1="O" u2="&#x2206;" k="60" />
    <hkern u1="O" u2="&#x2122;" k="40" />
    <hkern u1="O" u2="&#x201e;" k="50" />
    <hkern u1="O" u2="&#x201d;" k="40" />
    <hkern u1="O" u2="&#x201c;" k="40" />
    <hkern u1="O" u2="&#x201a;" k="50" />
    <hkern u1="O" u2="&#x2019;" k="40" />
    <hkern u1="O" u2="&#x2018;" k="40" />
    <hkern u1="O" u2="&#x17d;" k="60" />
    <hkern u1="O" u2="&#x17b;" k="60" />
    <hkern u1="O" u2="&#x179;" k="60" />
    <hkern u1="O" u2="&#x178;" k="80" />
    <hkern u1="O" u2="&#x104;" k="60" />
    <hkern u1="O" u2="&#xdd;" k="80" />
    <hkern u1="O" u2="&#xc6;" k="60" />
    <hkern u1="O" u2="&#xc5;" k="60" />
    <hkern u1="O" u2="&#xc4;" k="60" />
    <hkern u1="O" u2="&#xc3;" k="60" />
    <hkern u1="O" u2="&#xc2;" k="60" />
    <hkern u1="O" u2="&#xc1;" k="60" />
    <hkern u1="O" u2="&#xc0;" k="60" />
    <hkern u1="O" u2="&#xba;" k="40" />
    <hkern u1="O" u2="&#xb0;" k="40" />
    <hkern u1="O" u2="&#xaa;" k="40" />
    <hkern u1="O" u2="&#x7d;" k="40" />
    <hkern u1="O" u2="]" k="40" />
    <hkern u1="O" u2="\" k="60" />
    <hkern u1="O" u2="Z" k="60" />
    <hkern u1="O" u2="Y" k="80" />
    <hkern u1="O" u2="X" k="30" />
    <hkern u1="O" u2="V" k="60" />
    <hkern u1="O" u2="T" k="60" />
    <hkern u1="O" u2="A" k="60" />
    <hkern u1="O" u2="&#x2f;" k="60" />
    <hkern u1="O" u2="&#x2e;" k="50" />
    <hkern u1="O" u2="&#x2c;" k="50" />
    <hkern u1="O" u2="&#x2a;" k="40" />
    <hkern u1="O" u2="&#x29;" k="40" />
    <hkern u1="O" u2="&#x27;" k="40" />
    <hkern u1="O" u2="&#x26;" k="60" />
    <hkern u1="O" u2="&#x22;" k="40" />
    <hkern u1="P" u2="&#x2206;" k="170" />
    <hkern u1="P" u2="&#x201e;" k="280" />
    <hkern u1="P" u2="&#x201a;" k="280" />
    <hkern u1="P" u2="&#x153;" k="30" />
    <hkern u1="P" u2="&#x119;" k="30" />
    <hkern u1="P" u2="&#x107;" k="30" />
    <hkern u1="P" u2="&#x105;" k="50" />
    <hkern u1="P" u2="&#x104;" k="170" />
    <hkern u1="P" u2="&#xf8;" k="30" />
    <hkern u1="P" u2="&#xf6;" k="30" />
    <hkern u1="P" u2="&#xf5;" k="30" />
    <hkern u1="P" u2="&#xf4;" k="30" />
    <hkern u1="P" u2="&#xf3;" k="30" />
    <hkern u1="P" u2="&#xf2;" k="30" />
    <hkern u1="P" u2="&#xf0;" k="30" />
    <hkern u1="P" u2="&#xeb;" k="30" />
    <hkern u1="P" u2="&#xea;" k="30" />
    <hkern u1="P" u2="&#xe9;" k="30" />
    <hkern u1="P" u2="&#xe8;" k="30" />
    <hkern u1="P" u2="&#xe7;" k="30" />
    <hkern u1="P" u2="&#xe6;" k="50" />
    <hkern u1="P" u2="&#xe5;" k="50" />
    <hkern u1="P" u2="&#xe4;" k="50" />
    <hkern u1="P" u2="&#xe3;" k="50" />
    <hkern u1="P" u2="&#xe2;" k="50" />
    <hkern u1="P" u2="&#xe1;" k="50" />
    <hkern u1="P" u2="&#xe0;" k="50" />
    <hkern u1="P" u2="&#xc6;" k="170" />
    <hkern u1="P" u2="&#xc5;" k="170" />
    <hkern u1="P" u2="&#xc4;" k="170" />
    <hkern u1="P" u2="&#xc3;" k="170" />
    <hkern u1="P" u2="&#xc2;" k="170" />
    <hkern u1="P" u2="&#xc1;" k="170" />
    <hkern u1="P" u2="&#xc0;" k="170" />
    <hkern u1="P" u2="q" k="30" />
    <hkern u1="P" u2="o" k="30" />
    <hkern u1="P" u2="e" k="30" />
    <hkern u1="P" u2="d" k="30" />
    <hkern u1="P" u2="c" k="30" />
    <hkern u1="P" u2="a" k="50" />
    <hkern u1="P" u2="J" k="200" />
    <hkern u1="P" u2="A" k="170" />
    <hkern u1="P" u2="&#x2f;" k="170" />
    <hkern u1="P" u2="&#x2e;" k="280" />
    <hkern u1="P" u2="&#x2c;" k="280" />
    <hkern u1="P" u2="&#x26;" k="170" />
    <hkern u1="Q" u2="&#x2206;" k="60" />
    <hkern u1="Q" u2="&#x2122;" k="40" />
    <hkern u1="Q" u2="&#x201e;" k="50" />
    <hkern u1="Q" u2="&#x201d;" k="40" />
    <hkern u1="Q" u2="&#x201c;" k="40" />
    <hkern u1="Q" u2="&#x201a;" k="50" />
    <hkern u1="Q" u2="&#x2019;" k="40" />
    <hkern u1="Q" u2="&#x2018;" k="40" />
    <hkern u1="Q" u2="&#x17d;" k="60" />
    <hkern u1="Q" u2="&#x17b;" k="60" />
    <hkern u1="Q" u2="&#x179;" k="60" />
    <hkern u1="Q" u2="&#x178;" k="80" />
    <hkern u1="Q" u2="&#x104;" k="60" />
    <hkern u1="Q" u2="&#xdd;" k="80" />
    <hkern u1="Q" u2="&#xc6;" k="60" />
    <hkern u1="Q" u2="&#xc5;" k="60" />
    <hkern u1="Q" u2="&#xc4;" k="60" />
    <hkern u1="Q" u2="&#xc3;" k="60" />
    <hkern u1="Q" u2="&#xc2;" k="60" />
    <hkern u1="Q" u2="&#xc1;" k="60" />
    <hkern u1="Q" u2="&#xc0;" k="60" />
    <hkern u1="Q" u2="&#xba;" k="40" />
    <hkern u1="Q" u2="&#xb0;" k="40" />
    <hkern u1="Q" u2="&#xaa;" k="40" />
    <hkern u1="Q" u2="&#x7d;" k="40" />
    <hkern u1="Q" u2="]" k="40" />
    <hkern u1="Q" u2="\" k="60" />
    <hkern u1="Q" u2="Z" k="60" />
    <hkern u1="Q" u2="Y" k="80" />
    <hkern u1="Q" u2="X" k="30" />
    <hkern u1="Q" u2="V" k="60" />
    <hkern u1="Q" u2="T" k="60" />
    <hkern u1="Q" u2="A" k="60" />
    <hkern u1="Q" u2="&#x2f;" k="60" />
    <hkern u1="Q" u2="&#x2e;" k="50" />
    <hkern u1="Q" u2="&#x2c;" k="50" />
    <hkern u1="Q" u2="&#x2a;" k="40" />
    <hkern u1="Q" u2="&#x29;" k="40" />
    <hkern u1="Q" u2="&#x27;" k="40" />
    <hkern u1="Q" u2="&#x26;" k="60" />
    <hkern u1="Q" u2="&#x22;" k="40" />
    <hkern u1="R" u2="&#x152;" k="50" />
    <hkern u1="R" u2="&#x106;" k="50" />
    <hkern u1="R" u2="&#xdc;" k="60" />
    <hkern u1="R" u2="&#xdb;" k="60" />
    <hkern u1="R" u2="&#xda;" k="60" />
    <hkern u1="R" u2="&#xd9;" k="60" />
    <hkern u1="R" u2="&#xd8;" k="50" />
    <hkern u1="R" u2="&#xd6;" k="50" />
    <hkern u1="R" u2="&#xd5;" k="50" />
    <hkern u1="R" u2="&#xd4;" k="50" />
    <hkern u1="R" u2="&#xd3;" k="50" />
    <hkern u1="R" u2="&#xd2;" k="50" />
    <hkern u1="R" u2="&#xc7;" k="50" />
    <hkern u1="R" u2="U" k="60" />
    <hkern u1="R" u2="T" k="60" />
    <hkern u1="R" u2="Q" k="50" />
    <hkern u1="R" u2="O" k="50" />
    <hkern u1="R" u2="G" k="50" />
    <hkern u1="R" u2="C" k="50" />
    <hkern u1="R" u2="&#x40;" k="50" />
    <hkern u1="T" u2="&#x2206;" k="160" />
    <hkern u1="T" u2="&#x203a;" k="180" />
    <hkern u1="T" u2="&#x2039;" k="180" />
    <hkern u1="T" u2="&#x2022;" k="180" />
    <hkern u1="T" u2="&#x201e;" k="180" />
    <hkern u1="T" u2="&#x201a;" k="180" />
    <hkern u1="T" u2="&#x2014;" k="180" />
    <hkern u1="T" u2="&#x2013;" k="180" />
    <hkern u1="T" u2="&#x153;" k="200" />
    <hkern u1="T" u2="&#x152;" k="60" />
    <hkern u1="T" u2="&#x144;" k="160" />
    <hkern u1="T" u2="&#x119;" k="200" />
    <hkern u1="T" u2="&#x107;" k="200" />
    <hkern u1="T" u2="&#x106;" k="60" />
    <hkern u1="T" u2="&#x105;" k="240" />
    <hkern u1="T" u2="&#x104;" k="160" />
    <hkern u1="T" u2="&#xfc;" k="160" />
    <hkern u1="T" u2="&#xfb;" k="160" />
    <hkern u1="T" u2="&#xfa;" k="160" />
    <hkern u1="T" u2="&#xf9;" k="160" />
    <hkern u1="T" u2="&#xf8;" k="200" />
    <hkern u1="T" u2="&#xf6;" k="200" />
    <hkern u1="T" u2="&#xf5;" k="200" />
    <hkern u1="T" u2="&#xf4;" k="200" />
    <hkern u1="T" u2="&#xf3;" k="200" />
    <hkern u1="T" u2="&#xf2;" k="200" />
    <hkern u1="T" u2="&#xf1;" k="160" />
    <hkern u1="T" u2="&#xf0;" k="200" />
    <hkern u1="T" u2="&#xeb;" k="200" />
    <hkern u1="T" u2="&#xea;" k="200" />
    <hkern u1="T" u2="&#xe9;" k="200" />
    <hkern u1="T" u2="&#xe8;" k="200" />
    <hkern u1="T" u2="&#xe7;" k="200" />
    <hkern u1="T" u2="&#xe6;" k="240" />
    <hkern u1="T" u2="&#xe5;" k="240" />
    <hkern u1="T" u2="&#xe4;" k="240" />
    <hkern u1="T" u2="&#xe3;" k="240" />
    <hkern u1="T" u2="&#xe2;" k="240" />
    <hkern u1="T" u2="&#xe1;" k="240" />
    <hkern u1="T" u2="&#xe0;" k="240" />
    <hkern u1="T" u2="&#xd8;" k="60" />
    <hkern u1="T" u2="&#xd6;" k="60" />
    <hkern u1="T" u2="&#xd5;" k="60" />
    <hkern u1="T" u2="&#xd4;" k="60" />
    <hkern u1="T" u2="&#xd3;" k="60" />
    <hkern u1="T" u2="&#xd2;" k="60" />
    <hkern u1="T" u2="&#xc7;" k="60" />
    <hkern u1="T" u2="&#xc6;" k="160" />
    <hkern u1="T" u2="&#xc5;" k="160" />
    <hkern u1="T" u2="&#xc4;" k="160" />
    <hkern u1="T" u2="&#xc3;" k="160" />
    <hkern u1="T" u2="&#xc2;" k="160" />
    <hkern u1="T" u2="&#xc1;" k="160" />
    <hkern u1="T" u2="&#xc0;" k="160" />
    <hkern u1="T" u2="&#xbb;" k="180" />
    <hkern u1="T" u2="&#xb7;" k="180" />
    <hkern u1="T" u2="&#xb5;" k="160" />
    <hkern u1="T" u2="&#xad;" k="180" />
    <hkern u1="T" u2="&#xab;" k="180" />
    <hkern u1="T" u2="z" k="120" />
    <hkern u1="T" u2="y" k="180" />
    <hkern u1="T" u2="x" k="130" />
    <hkern u1="T" u2="w" k="140" />
    <hkern u1="T" u2="v" k="180" />
    <hkern u1="T" u2="u" k="160" />
    <hkern u1="T" u2="s" k="120" />
    <hkern u1="T" u2="r" k="160" />
    <hkern u1="T" u2="q" k="200" />
    <hkern u1="T" u2="p" k="160" />
    <hkern u1="T" u2="o" k="200" />
    <hkern u1="T" u2="n" k="160" />
    <hkern u1="T" u2="m" k="160" />
    <hkern u1="T" u2="g" k="175" />
    <hkern u1="T" u2="e" k="200" />
    <hkern u1="T" u2="d" k="200" />
    <hkern u1="T" u2="c" k="200" />
    <hkern u1="T" u2="a" k="240" />
    <hkern u1="T" u2="Q" k="60" />
    <hkern u1="T" u2="O" k="60" />
    <hkern u1="T" u2="J" k="200" />
    <hkern u1="T" u2="G" k="60" />
    <hkern u1="T" u2="C" k="60" />
    <hkern u1="T" u2="A" k="160" />
    <hkern u1="T" u2="&#x40;" k="60" />
    <hkern u1="T" u2="&#x3b;" k="160" />
    <hkern u1="T" u2="&#x3a;" k="160" />
    <hkern u1="T" u2="&#x2f;" k="160" />
    <hkern u1="T" u2="&#x2e;" k="180" />
    <hkern u1="T" u2="&#x2d;" k="180" />
    <hkern u1="T" u2="&#x2c;" k="180" />
    <hkern u1="T" u2="&#x26;" k="160" />
    <hkern u1="U" u2="&#x2206;" k="50" />
    <hkern u1="U" u2="&#x201e;" k="50" />
    <hkern u1="U" u2="&#x201a;" k="50" />
    <hkern u1="U" u2="&#x104;" k="50" />
    <hkern u1="U" u2="&#xc6;" k="50" />
    <hkern u1="U" u2="&#xc5;" k="50" />
    <hkern u1="U" u2="&#xc4;" k="50" />
    <hkern u1="U" u2="&#xc3;" k="50" />
    <hkern u1="U" u2="&#xc2;" k="50" />
    <hkern u1="U" u2="&#xc1;" k="50" />
    <hkern u1="U" u2="&#xc0;" k="50" />
    <hkern u1="U" u2="A" k="50" />
    <hkern u1="U" u2="&#x2f;" k="50" />
    <hkern u1="U" u2="&#x2e;" k="50" />
    <hkern u1="U" u2="&#x2c;" k="50" />
    <hkern u1="U" u2="&#x26;" k="50" />
    <hkern u1="V" u2="&#x2206;" k="200" />
    <hkern u1="V" u2="&#x2122;" k="-40" />
    <hkern u1="V" u2="&#x203a;" k="120" />
    <hkern u1="V" u2="&#x2039;" k="120" />
    <hkern u1="V" u2="&#x2022;" k="120" />
    <hkern u1="V" u2="&#x201e;" k="200" />
    <hkern u1="V" u2="&#x201d;" k="-40" />
    <hkern u1="V" u2="&#x201c;" k="-40" />
    <hkern u1="V" u2="&#x201a;" k="200" />
    <hkern u1="V" u2="&#x2019;" k="-40" />
    <hkern u1="V" u2="&#x2018;" k="-40" />
    <hkern u1="V" u2="&#x2014;" k="120" />
    <hkern u1="V" u2="&#x2013;" k="120" />
    <hkern u1="V" u2="&#x153;" k="130" />
    <hkern u1="V" u2="&#x152;" k="60" />
    <hkern u1="V" u2="&#x144;" k="110" />
    <hkern u1="V" u2="&#x119;" k="130" />
    <hkern u1="V" u2="&#x107;" k="130" />
    <hkern u1="V" u2="&#x106;" k="60" />
    <hkern u1="V" u2="&#x105;" k="130" />
    <hkern u1="V" u2="&#x104;" k="200" />
    <hkern u1="V" u2="&#xfc;" k="110" />
    <hkern u1="V" u2="&#xfb;" k="110" />
    <hkern u1="V" u2="&#xfa;" k="110" />
    <hkern u1="V" u2="&#xf9;" k="110" />
    <hkern u1="V" u2="&#xf8;" k="130" />
    <hkern u1="V" u2="&#xf6;" k="130" />
    <hkern u1="V" u2="&#xf5;" k="130" />
    <hkern u1="V" u2="&#xf4;" k="130" />
    <hkern u1="V" u2="&#xf3;" k="130" />
    <hkern u1="V" u2="&#xf2;" k="130" />
    <hkern u1="V" u2="&#xf1;" k="110" />
    <hkern u1="V" u2="&#xf0;" k="130" />
    <hkern u1="V" u2="&#xeb;" k="130" />
    <hkern u1="V" u2="&#xea;" k="130" />
    <hkern u1="V" u2="&#xe9;" k="130" />
    <hkern u1="V" u2="&#xe8;" k="130" />
    <hkern u1="V" u2="&#xe7;" k="130" />
    <hkern u1="V" u2="&#xe6;" k="130" />
    <hkern u1="V" u2="&#xe5;" k="130" />
    <hkern u1="V" u2="&#xe4;" k="130" />
    <hkern u1="V" u2="&#xe3;" k="130" />
    <hkern u1="V" u2="&#xe2;" k="130" />
    <hkern u1="V" u2="&#xe1;" k="130" />
    <hkern u1="V" u2="&#xe0;" k="130" />
    <hkern u1="V" u2="&#xd8;" k="60" />
    <hkern u1="V" u2="&#xd6;" k="60" />
    <hkern u1="V" u2="&#xd5;" k="60" />
    <hkern u1="V" u2="&#xd4;" k="60" />
    <hkern u1="V" u2="&#xd3;" k="60" />
    <hkern u1="V" u2="&#xd2;" k="60" />
    <hkern u1="V" u2="&#xc7;" k="60" />
    <hkern u1="V" u2="&#xc6;" k="200" />
    <hkern u1="V" u2="&#xc5;" k="200" />
    <hkern u1="V" u2="&#xc4;" k="200" />
    <hkern u1="V" u2="&#xc3;" k="200" />
    <hkern u1="V" u2="&#xc2;" k="200" />
    <hkern u1="V" u2="&#xc1;" k="200" />
    <hkern u1="V" u2="&#xc0;" k="200" />
    <hkern u1="V" u2="&#xbb;" k="120" />
    <hkern u1="V" u2="&#xba;" k="-40" />
    <hkern u1="V" u2="&#xb9;" k="-40" />
    <hkern u1="V" u2="&#xb7;" k="120" />
    <hkern u1="V" u2="&#xb5;" k="110" />
    <hkern u1="V" u2="&#xb3;" k="-40" />
    <hkern u1="V" u2="&#xb2;" k="-40" />
    <hkern u1="V" u2="&#xb0;" k="-40" />
    <hkern u1="V" u2="&#xad;" k="120" />
    <hkern u1="V" u2="&#xab;" k="120" />
    <hkern u1="V" u2="&#xaa;" k="-40" />
    <hkern u1="V" u2="z" k="100" />
    <hkern u1="V" u2="y" k="70" />
    <hkern u1="V" u2="x" k="80" />
    <hkern u1="V" u2="v" k="70" />
    <hkern u1="V" u2="u" k="110" />
    <hkern u1="V" u2="t" k="50" />
    <hkern u1="V" u2="s" k="110" />
    <hkern u1="V" u2="r" k="110" />
    <hkern u1="V" u2="q" k="130" />
    <hkern u1="V" u2="p" k="110" />
    <hkern u1="V" u2="o" k="130" />
    <hkern u1="V" u2="n" k="110" />
    <hkern u1="V" u2="m" k="110" />
    <hkern u1="V" u2="g" k="140" />
    <hkern u1="V" u2="f" k="30" />
    <hkern u1="V" u2="e" k="130" />
    <hkern u1="V" u2="d" k="130" />
    <hkern u1="V" u2="c" k="130" />
    <hkern u1="V" u2="a" k="130" />
    <hkern u1="V" u2="Q" k="60" />
    <hkern u1="V" u2="O" k="60" />
    <hkern u1="V" u2="J" k="160" />
    <hkern u1="V" u2="G" k="60" />
    <hkern u1="V" u2="C" k="60" />
    <hkern u1="V" u2="A" k="200" />
    <hkern u1="V" u2="&#x40;" k="60" />
    <hkern u1="V" u2="&#x3f;" k="-30" />
    <hkern u1="V" u2="&#x3b;" k="110" />
    <hkern u1="V" u2="&#x3a;" k="110" />
    <hkern u1="V" u2="&#x2f;" k="200" />
    <hkern u1="V" u2="&#x2e;" k="200" />
    <hkern u1="V" u2="&#x2d;" k="120" />
    <hkern u1="V" u2="&#x2c;" k="200" />
    <hkern u1="V" u2="&#x2a;" k="-40" />
    <hkern u1="V" u2="&#x27;" k="-40" />
    <hkern u1="V" u2="&#x26;" k="200" />
    <hkern u1="V" u2="&#x22;" k="-40" />
    <hkern u1="W" u2="&#x2206;" k="140" />
    <hkern u1="W" u2="&#x2122;" k="-40" />
    <hkern u1="W" u2="&#x203a;" k="40" />
    <hkern u1="W" u2="&#x2039;" k="40" />
    <hkern u1="W" u2="&#x2022;" k="40" />
    <hkern u1="W" u2="&#x201e;" k="140" />
    <hkern u1="W" u2="&#x201d;" k="-40" />
    <hkern u1="W" u2="&#x201c;" k="-40" />
    <hkern u1="W" u2="&#x201a;" k="140" />
    <hkern u1="W" u2="&#x2019;" k="-40" />
    <hkern u1="W" u2="&#x2018;" k="-40" />
    <hkern u1="W" u2="&#x2014;" k="40" />
    <hkern u1="W" u2="&#x2013;" k="40" />
    <hkern u1="W" u2="&#x153;" k="50" />
    <hkern u1="W" u2="&#x119;" k="50" />
    <hkern u1="W" u2="&#x107;" k="50" />
    <hkern u1="W" u2="&#x105;" k="110" />
    <hkern u1="W" u2="&#x104;" k="140" />
    <hkern u1="W" u2="&#xf8;" k="50" />
    <hkern u1="W" u2="&#xf6;" k="50" />
    <hkern u1="W" u2="&#xf5;" k="50" />
    <hkern u1="W" u2="&#xf4;" k="50" />
    <hkern u1="W" u2="&#xf3;" k="50" />
    <hkern u1="W" u2="&#xf2;" k="50" />
    <hkern u1="W" u2="&#xf0;" k="50" />
    <hkern u1="W" u2="&#xeb;" k="50" />
    <hkern u1="W" u2="&#xea;" k="50" />
    <hkern u1="W" u2="&#xe9;" k="50" />
    <hkern u1="W" u2="&#xe8;" k="50" />
    <hkern u1="W" u2="&#xe7;" k="50" />
    <hkern u1="W" u2="&#xe6;" k="110" />
    <hkern u1="W" u2="&#xe5;" k="110" />
    <hkern u1="W" u2="&#xe4;" k="110" />
    <hkern u1="W" u2="&#xe3;" k="110" />
    <hkern u1="W" u2="&#xe2;" k="110" />
    <hkern u1="W" u2="&#xe1;" k="110" />
    <hkern u1="W" u2="&#xe0;" k="110" />
    <hkern u1="W" u2="&#xc6;" k="140" />
    <hkern u1="W" u2="&#xc5;" k="140" />
    <hkern u1="W" u2="&#xc4;" k="140" />
    <hkern u1="W" u2="&#xc3;" k="140" />
    <hkern u1="W" u2="&#xc2;" k="140" />
    <hkern u1="W" u2="&#xc1;" k="140" />
    <hkern u1="W" u2="&#xc0;" k="140" />
    <hkern u1="W" u2="&#xbb;" k="40" />
    <hkern u1="W" u2="&#xba;" k="-40" />
    <hkern u1="W" u2="&#xb9;" k="-40" />
    <hkern u1="W" u2="&#xb7;" k="40" />
    <hkern u1="W" u2="&#xb3;" k="-40" />
    <hkern u1="W" u2="&#xb2;" k="-40" />
    <hkern u1="W" u2="&#xb0;" k="-40" />
    <hkern u1="W" u2="&#xad;" k="40" />
    <hkern u1="W" u2="&#xab;" k="40" />
    <hkern u1="W" u2="&#xaa;" k="-40" />
    <hkern u1="W" u2="s" k="60" />
    <hkern u1="W" u2="q" k="50" />
    <hkern u1="W" u2="o" k="50" />
    <hkern u1="W" u2="g" k="95" />
    <hkern u1="W" u2="e" k="50" />
    <hkern u1="W" u2="d" k="50" />
    <hkern u1="W" u2="c" k="50" />
    <hkern u1="W" u2="a" k="110" />
    <hkern u1="W" u2="J" k="120" />
    <hkern u1="W" u2="A" k="140" />
    <hkern u1="W" u2="&#x3f;" k="-30" />
    <hkern u1="W" u2="&#x2f;" k="140" />
    <hkern u1="W" u2="&#x2e;" k="140" />
    <hkern u1="W" u2="&#x2d;" k="40" />
    <hkern u1="W" u2="&#x2c;" k="140" />
    <hkern u1="W" u2="&#x2a;" k="-40" />
    <hkern u1="W" u2="&#x27;" k="-40" />
    <hkern u1="W" u2="&#x26;" k="140" />
    <hkern u1="W" u2="&#x22;" k="-40" />
    <hkern u1="X" u2="&#x203a;" k="70" />
    <hkern u1="X" u2="&#x2039;" k="70" />
    <hkern u1="X" u2="&#x2022;" k="70" />
    <hkern u1="X" u2="&#x2014;" k="70" />
    <hkern u1="X" u2="&#x2013;" k="70" />
    <hkern u1="X" u2="&#x153;" k="50" />
    <hkern u1="X" u2="&#x152;" k="30" />
    <hkern u1="X" u2="&#x119;" k="50" />
    <hkern u1="X" u2="&#x107;" k="50" />
    <hkern u1="X" u2="&#x106;" k="30" />
    <hkern u1="X" u2="&#xf8;" k="50" />
    <hkern u1="X" u2="&#xf6;" k="50" />
    <hkern u1="X" u2="&#xf5;" k="50" />
    <hkern u1="X" u2="&#xf4;" k="50" />
    <hkern u1="X" u2="&#xf3;" k="50" />
    <hkern u1="X" u2="&#xf2;" k="50" />
    <hkern u1="X" u2="&#xf0;" k="50" />
    <hkern u1="X" u2="&#xeb;" k="50" />
    <hkern u1="X" u2="&#xea;" k="50" />
    <hkern u1="X" u2="&#xe9;" k="50" />
    <hkern u1="X" u2="&#xe8;" k="50" />
    <hkern u1="X" u2="&#xe7;" k="50" />
    <hkern u1="X" u2="&#xd8;" k="30" />
    <hkern u1="X" u2="&#xd6;" k="30" />
    <hkern u1="X" u2="&#xd5;" k="30" />
    <hkern u1="X" u2="&#xd4;" k="30" />
    <hkern u1="X" u2="&#xd3;" k="30" />
    <hkern u1="X" u2="&#xd2;" k="30" />
    <hkern u1="X" u2="&#xc7;" k="30" />
    <hkern u1="X" u2="&#xbb;" k="70" />
    <hkern u1="X" u2="&#xb7;" k="70" />
    <hkern u1="X" u2="&#xad;" k="70" />
    <hkern u1="X" u2="&#xab;" k="70" />
    <hkern u1="X" u2="y" k="80" />
    <hkern u1="X" u2="w" k="50" />
    <hkern u1="X" u2="v" k="80" />
    <hkern u1="X" u2="t" k="100" />
    <hkern u1="X" u2="q" k="50" />
    <hkern u1="X" u2="o" k="50" />
    <hkern u1="X" u2="f" k="60" />
    <hkern u1="X" u2="e" k="50" />
    <hkern u1="X" u2="d" k="50" />
    <hkern u1="X" u2="c" k="50" />
    <hkern u1="X" u2="Q" k="30" />
    <hkern u1="X" u2="O" k="30" />
    <hkern u1="X" u2="G" k="30" />
    <hkern u1="X" u2="C" k="30" />
    <hkern u1="X" u2="&#x40;" k="30" />
    <hkern u1="X" u2="&#x2d;" k="70" />
    <hkern u1="Y" u2="&#x2206;" k="200" />
    <hkern u1="Y" u2="&#x2122;" k="-40" />
    <hkern u1="Y" u2="&#x203a;" k="160" />
    <hkern u1="Y" u2="&#x2039;" k="160" />
    <hkern u1="Y" u2="&#x2022;" k="160" />
    <hkern u1="Y" u2="&#x201e;" k="180" />
    <hkern u1="Y" u2="&#x201d;" k="-40" />
    <hkern u1="Y" u2="&#x201c;" k="-40" />
    <hkern u1="Y" u2="&#x201a;" k="180" />
    <hkern u1="Y" u2="&#x2019;" k="-40" />
    <hkern u1="Y" u2="&#x2018;" k="-40" />
    <hkern u1="Y" u2="&#x2014;" k="160" />
    <hkern u1="Y" u2="&#x2013;" k="160" />
    <hkern u1="Y" u2="&#x153;" k="160" />
    <hkern u1="Y" u2="&#x152;" k="80" />
    <hkern u1="Y" u2="&#x144;" k="140" />
    <hkern u1="Y" u2="&#x119;" k="160" />
    <hkern u1="Y" u2="&#x107;" k="160" />
    <hkern u1="Y" u2="&#x106;" k="80" />
    <hkern u1="Y" u2="&#x105;" k="160" />
    <hkern u1="Y" u2="&#x104;" k="200" />
    <hkern u1="Y" u2="&#xfc;" k="140" />
    <hkern u1="Y" u2="&#xfb;" k="140" />
    <hkern u1="Y" u2="&#xfa;" k="140" />
    <hkern u1="Y" u2="&#xf9;" k="140" />
    <hkern u1="Y" u2="&#xf8;" k="160" />
    <hkern u1="Y" u2="&#xf6;" k="160" />
    <hkern u1="Y" u2="&#xf5;" k="160" />
    <hkern u1="Y" u2="&#xf4;" k="160" />
    <hkern u1="Y" u2="&#xf3;" k="160" />
    <hkern u1="Y" u2="&#xf2;" k="160" />
    <hkern u1="Y" u2="&#xf1;" k="140" />
    <hkern u1="Y" u2="&#xf0;" k="160" />
    <hkern u1="Y" u2="&#xeb;" k="160" />
    <hkern u1="Y" u2="&#xea;" k="160" />
    <hkern u1="Y" u2="&#xe9;" k="160" />
    <hkern u1="Y" u2="&#xe8;" k="160" />
    <hkern u1="Y" u2="&#xe7;" k="160" />
    <hkern u1="Y" u2="&#xe6;" k="160" />
    <hkern u1="Y" u2="&#xe5;" k="160" />
    <hkern u1="Y" u2="&#xe4;" k="160" />
    <hkern u1="Y" u2="&#xe3;" k="160" />
    <hkern u1="Y" u2="&#xe2;" k="160" />
    <hkern u1="Y" u2="&#xe1;" k="160" />
    <hkern u1="Y" u2="&#xe0;" k="160" />
    <hkern u1="Y" u2="&#xd8;" k="80" />
    <hkern u1="Y" u2="&#xd6;" k="80" />
    <hkern u1="Y" u2="&#xd5;" k="80" />
    <hkern u1="Y" u2="&#xd4;" k="80" />
    <hkern u1="Y" u2="&#xd3;" k="80" />
    <hkern u1="Y" u2="&#xd2;" k="80" />
    <hkern u1="Y" u2="&#xc7;" k="80" />
    <hkern u1="Y" u2="&#xc6;" k="200" />
    <hkern u1="Y" u2="&#xc5;" k="200" />
    <hkern u1="Y" u2="&#xc4;" k="200" />
    <hkern u1="Y" u2="&#xc3;" k="200" />
    <hkern u1="Y" u2="&#xc2;" k="200" />
    <hkern u1="Y" u2="&#xc1;" k="200" />
    <hkern u1="Y" u2="&#xc0;" k="200" />
    <hkern u1="Y" u2="&#xbb;" k="160" />
    <hkern u1="Y" u2="&#xba;" k="-40" />
    <hkern u1="Y" u2="&#xb9;" k="-60" />
    <hkern u1="Y" u2="&#xb7;" k="160" />
    <hkern u1="Y" u2="&#xb5;" k="140" />
    <hkern u1="Y" u2="&#xb3;" k="-60" />
    <hkern u1="Y" u2="&#xb2;" k="-60" />
    <hkern u1="Y" u2="&#xb0;" k="-40" />
    <hkern u1="Y" u2="&#xad;" k="160" />
    <hkern u1="Y" u2="&#xab;" k="160" />
    <hkern u1="Y" u2="&#xaa;" k="-40" />
    <hkern u1="Y" u2="y" k="100" />
    <hkern u1="Y" u2="x" k="140" />
    <hkern u1="Y" u2="w" k="100" />
    <hkern u1="Y" u2="v" k="100" />
    <hkern u1="Y" u2="u" k="140" />
    <hkern u1="Y" u2="s" k="150" />
    <hkern u1="Y" u2="r" k="140" />
    <hkern u1="Y" u2="q" k="160" />
    <hkern u1="Y" u2="p" k="140" />
    <hkern u1="Y" u2="o" k="160" />
    <hkern u1="Y" u2="n" k="140" />
    <hkern u1="Y" u2="m" k="140" />
    <hkern u1="Y" u2="g" k="180" />
    <hkern u1="Y" u2="e" k="160" />
    <hkern u1="Y" u2="d" k="160" />
    <hkern u1="Y" u2="c" k="160" />
    <hkern u1="Y" u2="a" k="160" />
    <hkern u1="Y" u2="Q" k="80" />
    <hkern u1="Y" u2="O" k="80" />
    <hkern u1="Y" u2="J" k="200" />
    <hkern u1="Y" u2="G" k="80" />
    <hkern u1="Y" u2="C" k="80" />
    <hkern u1="Y" u2="A" k="200" />
    <hkern u1="Y" u2="&#x40;" k="80" />
    <hkern u1="Y" u2="&#x3f;" k="-30" />
    <hkern u1="Y" u2="&#x3b;" k="140" />
    <hkern u1="Y" u2="&#x3a;" k="140" />
    <hkern u1="Y" u2="&#x2f;" k="200" />
    <hkern u1="Y" u2="&#x2e;" k="180" />
    <hkern u1="Y" u2="&#x2d;" k="160" />
    <hkern u1="Y" u2="&#x2c;" k="180" />
    <hkern u1="Y" u2="&#x2a;" k="-40" />
    <hkern u1="Y" u2="&#x27;" k="-40" />
    <hkern u1="Y" u2="&#x26;" k="200" />
    <hkern u1="Y" u2="&#x22;" k="-40" />
    <hkern u1="Z" u2="&#x203a;" k="60" />
    <hkern u1="Z" u2="&#x2039;" k="60" />
    <hkern u1="Z" u2="&#x2022;" k="60" />
    <hkern u1="Z" u2="&#x2014;" k="60" />
    <hkern u1="Z" u2="&#x2013;" k="60" />
    <hkern u1="Z" u2="&#x153;" k="20" />
    <hkern u1="Z" u2="&#x152;" k="40" />
    <hkern u1="Z" u2="&#x119;" k="20" />
    <hkern u1="Z" u2="&#x107;" k="20" />
    <hkern u1="Z" u2="&#x106;" k="40" />
    <hkern u1="Z" u2="&#xf8;" k="20" />
    <hkern u1="Z" u2="&#xf6;" k="20" />
    <hkern u1="Z" u2="&#xf5;" k="20" />
    <hkern u1="Z" u2="&#xf4;" k="20" />
    <hkern u1="Z" u2="&#xf3;" k="20" />
    <hkern u1="Z" u2="&#xf2;" k="20" />
    <hkern u1="Z" u2="&#xf0;" k="20" />
    <hkern u1="Z" u2="&#xeb;" k="20" />
    <hkern u1="Z" u2="&#xea;" k="20" />
    <hkern u1="Z" u2="&#xe9;" k="20" />
    <hkern u1="Z" u2="&#xe8;" k="20" />
    <hkern u1="Z" u2="&#xe7;" k="20" />
    <hkern u1="Z" u2="&#xd8;" k="40" />
    <hkern u1="Z" u2="&#xd6;" k="40" />
    <hkern u1="Z" u2="&#xd5;" k="40" />
    <hkern u1="Z" u2="&#xd4;" k="40" />
    <hkern u1="Z" u2="&#xd3;" k="40" />
    <hkern u1="Z" u2="&#xd2;" k="40" />
    <hkern u1="Z" u2="&#xc7;" k="40" />
    <hkern u1="Z" u2="&#xbb;" k="60" />
    <hkern u1="Z" u2="&#xb7;" k="60" />
    <hkern u1="Z" u2="&#xad;" k="60" />
    <hkern u1="Z" u2="&#xab;" k="60" />
    <hkern u1="Z" u2="y" k="30" />
    <hkern u1="Z" u2="v" k="30" />
    <hkern u1="Z" u2="s" k="10" />
    <hkern u1="Z" u2="q" k="20" />
    <hkern u1="Z" u2="o" k="20" />
    <hkern u1="Z" u2="e" k="20" />
    <hkern u1="Z" u2="d" k="20" />
    <hkern u1="Z" u2="c" k="20" />
    <hkern u1="Z" u2="Q" k="40" />
    <hkern u1="Z" u2="O" k="40" />
    <hkern u1="Z" u2="G" k="40" />
    <hkern u1="Z" u2="C" k="40" />
    <hkern u1="Z" u2="&#x40;" k="40" />
    <hkern u1="Z" u2="&#x3f;" k="-30" />
    <hkern u1="Z" u2="&#x2d;" k="60" />
    <hkern u1="[" u2="&#x153;" k="40" />
    <hkern u1="[" u2="&#x152;" k="40" />
    <hkern u1="[" u2="&#x119;" k="40" />
    <hkern u1="[" u2="&#x107;" k="40" />
    <hkern u1="[" u2="&#x106;" k="40" />
    <hkern u1="[" u2="&#xf8;" k="40" />
    <hkern u1="[" u2="&#xf6;" k="40" />
    <hkern u1="[" u2="&#xf5;" k="40" />
    <hkern u1="[" u2="&#xf4;" k="40" />
    <hkern u1="[" u2="&#xf3;" k="40" />
    <hkern u1="[" u2="&#xf2;" k="40" />
    <hkern u1="[" u2="&#xf0;" k="40" />
    <hkern u1="[" u2="&#xeb;" k="40" />
    <hkern u1="[" u2="&#xea;" k="40" />
    <hkern u1="[" u2="&#xe9;" k="40" />
    <hkern u1="[" u2="&#xe8;" k="40" />
    <hkern u1="[" u2="&#xe7;" k="40" />
    <hkern u1="[" u2="&#xd8;" k="40" />
    <hkern u1="[" u2="&#xd6;" k="40" />
    <hkern u1="[" u2="&#xd5;" k="40" />
    <hkern u1="[" u2="&#xd4;" k="40" />
    <hkern u1="[" u2="&#xd3;" k="40" />
    <hkern u1="[" u2="&#xd2;" k="40" />
    <hkern u1="[" u2="&#xc7;" k="40" />
    <hkern u1="[" u2="q" k="40" />
    <hkern u1="[" u2="o" k="40" />
    <hkern u1="[" u2="e" k="40" />
    <hkern u1="[" u2="d" k="40" />
    <hkern u1="[" u2="c" k="40" />
    <hkern u1="[" u2="Q" k="40" />
    <hkern u1="[" u2="O" k="40" />
    <hkern u1="[" u2="G" k="40" />
    <hkern u1="[" u2="C" k="40" />
    <hkern u1="[" u2="&#x40;" k="40" />
    <hkern u1="\" u2="&#x2122;" k="200" />
    <hkern u1="\" u2="&#x203a;" k="80" />
    <hkern u1="\" u2="&#x2039;" k="80" />
    <hkern u1="\" u2="&#x2022;" k="80" />
    <hkern u1="\" u2="&#x201d;" k="200" />
    <hkern u1="\" u2="&#x201c;" k="200" />
    <hkern u1="\" u2="&#x2019;" k="200" />
    <hkern u1="\" u2="&#x2018;" k="200" />
    <hkern u1="\" u2="&#x2014;" k="80" />
    <hkern u1="\" u2="&#x2013;" k="80" />
    <hkern u1="\" u2="&#x178;" k="200" />
    <hkern u1="\" u2="&#x152;" k="60" />
    <hkern u1="\" u2="&#x106;" k="60" />
    <hkern u1="\" u2="&#xdd;" k="200" />
    <hkern u1="\" u2="&#xdc;" k="50" />
    <hkern u1="\" u2="&#xdb;" k="50" />
    <hkern u1="\" u2="&#xda;" k="50" />
    <hkern u1="\" u2="&#xd9;" k="50" />
    <hkern u1="\" u2="&#xd8;" k="60" />
    <hkern u1="\" u2="&#xd6;" k="60" />
    <hkern u1="\" u2="&#xd5;" k="60" />
    <hkern u1="\" u2="&#xd4;" k="60" />
    <hkern u1="\" u2="&#xd3;" k="60" />
    <hkern u1="\" u2="&#xd2;" k="60" />
    <hkern u1="\" u2="&#xc7;" k="60" />
    <hkern u1="\" u2="&#xbb;" k="80" />
    <hkern u1="\" u2="&#xba;" k="200" />
    <hkern u1="\" u2="&#xb9;" k="220" />
    <hkern u1="\" u2="&#xb7;" k="80" />
    <hkern u1="\" u2="&#xb3;" k="220" />
    <hkern u1="\" u2="&#xb2;" k="220" />
    <hkern u1="\" u2="&#xb0;" k="200" />
    <hkern u1="\" u2="&#xad;" k="80" />
    <hkern u1="\" u2="&#xab;" k="80" />
    <hkern u1="\" u2="&#xaa;" k="200" />
    <hkern u1="\" u2="y" k="100" />
    <hkern u1="\" u2="v" k="100" />
    <hkern u1="\" u2="\" k="200" />
    <hkern u1="\" u2="Y" k="200" />
    <hkern u1="\" u2="W" k="120" />
    <hkern u1="\" u2="V" k="200" />
    <hkern u1="\" u2="U" k="50" />
    <hkern u1="\" u2="T" k="160" />
    <hkern u1="\" u2="Q" k="60" />
    <hkern u1="\" u2="O" k="60" />
    <hkern u1="\" u2="J" k="-60" />
    <hkern u1="\" u2="G" k="60" />
    <hkern u1="\" u2="C" k="60" />
    <hkern u1="\" u2="&#x40;" k="60" />
    <hkern u1="\" u2="&#x3f;" k="70" />
    <hkern u1="\" u2="&#x2d;" k="80" />
    <hkern u1="\" u2="&#x2a;" k="200" />
    <hkern u1="\" u2="&#x27;" k="200" />
    <hkern u1="\" u2="&#x22;" k="200" />
    <hkern u1="a" u2="&#x2122;" k="80" />
    <hkern u1="a" u2="&#x201d;" k="80" />
    <hkern u1="a" u2="&#x201c;" k="80" />
    <hkern u1="a" u2="&#x2019;" k="80" />
    <hkern u1="a" u2="&#x2018;" k="80" />
    <hkern u1="a" u2="&#xba;" k="80" />
    <hkern u1="a" u2="&#xb9;" k="80" />
    <hkern u1="a" u2="&#xb3;" k="80" />
    <hkern u1="a" u2="&#xb2;" k="80" />
    <hkern u1="a" u2="&#xb0;" k="80" />
    <hkern u1="a" u2="&#xaa;" k="80" />
    <hkern u1="a" u2="y" k="40" />
    <hkern u1="a" u2="w" k="20" />
    <hkern u1="a" u2="v" k="40" />
    <hkern u1="a" u2="&#x2a;" k="80" />
    <hkern u1="a" u2="&#x27;" k="80" />
    <hkern u1="a" u2="&#x22;" k="80" />
    <hkern u1="b" u2="&#x2122;" k="100" />
    <hkern u1="b" u2="&#x201d;" k="100" />
    <hkern u1="b" u2="&#x201c;" k="100" />
    <hkern u1="b" u2="&#x2019;" k="100" />
    <hkern u1="b" u2="&#x2018;" k="100" />
    <hkern u1="b" u2="&#xba;" k="100" />
    <hkern u1="b" u2="&#xb0;" k="100" />
    <hkern u1="b" u2="&#xaa;" k="100" />
    <hkern u1="b" u2="&#x7d;" k="40" />
    <hkern u1="b" u2="y" k="40" />
    <hkern u1="b" u2="x" k="60" />
    <hkern u1="b" u2="v" k="40" />
    <hkern u1="b" u2="]" k="40" />
    <hkern u1="b" u2="\" k="130" />
    <hkern u1="b" u2="W" k="50" />
    <hkern u1="b" u2="V" k="130" />
    <hkern u1="b" u2="&#x2a;" k="100" />
    <hkern u1="b" u2="&#x29;" k="40" />
    <hkern u1="b" u2="&#x27;" k="100" />
    <hkern u1="b" u2="&#x22;" k="100" />
    <hkern u1="e" u2="&#x2122;" k="100" />
    <hkern u1="e" u2="&#x201d;" k="100" />
    <hkern u1="e" u2="&#x201c;" k="100" />
    <hkern u1="e" u2="&#x2019;" k="100" />
    <hkern u1="e" u2="&#x2018;" k="100" />
    <hkern u1="e" u2="&#xba;" k="100" />
    <hkern u1="e" u2="&#xb0;" k="100" />
    <hkern u1="e" u2="&#xaa;" k="100" />
    <hkern u1="e" u2="&#x7d;" k="40" />
    <hkern u1="e" u2="y" k="40" />
    <hkern u1="e" u2="x" k="60" />
    <hkern u1="e" u2="v" k="40" />
    <hkern u1="e" u2="]" k="40" />
    <hkern u1="e" u2="\" k="130" />
    <hkern u1="e" u2="W" k="50" />
    <hkern u1="e" u2="V" k="130" />
    <hkern u1="e" u2="&#x2a;" k="100" />
    <hkern u1="e" u2="&#x29;" k="40" />
    <hkern u1="e" u2="&#x27;" k="100" />
    <hkern u1="e" u2="&#x22;" k="100" />
    <hkern u1="f" u2="&#x2122;" k="-60" />
    <hkern u1="f" u2="&#x201e;" k="120" />
    <hkern u1="f" u2="&#x201d;" k="-60" />
    <hkern u1="f" u2="&#x201c;" k="-60" />
    <hkern u1="f" u2="&#x201a;" k="120" />
    <hkern u1="f" u2="&#x2019;" k="-60" />
    <hkern u1="f" u2="&#x2018;" k="-60" />
    <hkern u1="f" u2="&#xba;" k="-60" />
    <hkern u1="f" u2="&#xb9;" k="-100" />
    <hkern u1="f" u2="&#xb3;" k="-100" />
    <hkern u1="f" u2="&#xb2;" k="-100" />
    <hkern u1="f" u2="&#xb0;" k="-60" />
    <hkern u1="f" u2="&#xaa;" k="-60" />
    <hkern u1="f" u2="&#x2e;" k="120" />
    <hkern u1="f" u2="&#x2c;" k="120" />
    <hkern u1="f" u2="&#x2a;" k="-60" />
    <hkern u1="f" u2="&#x27;" k="-60" />
    <hkern u1="f" u2="&#x22;" k="-60" />
    <hkern u1="h" u2="&#x2122;" k="80" />
    <hkern u1="h" u2="&#x201d;" k="80" />
    <hkern u1="h" u2="&#x201c;" k="80" />
    <hkern u1="h" u2="&#x2019;" k="80" />
    <hkern u1="h" u2="&#x2018;" k="80" />
    <hkern u1="h" u2="&#xba;" k="80" />
    <hkern u1="h" u2="&#xb9;" k="80" />
    <hkern u1="h" u2="&#xb3;" k="80" />
    <hkern u1="h" u2="&#xb2;" k="80" />
    <hkern u1="h" u2="&#xb0;" k="80" />
    <hkern u1="h" u2="&#xaa;" k="80" />
    <hkern u1="h" u2="y" k="40" />
    <hkern u1="h" u2="w" k="20" />
    <hkern u1="h" u2="v" k="40" />
    <hkern u1="h" u2="&#x2a;" k="80" />
    <hkern u1="h" u2="&#x27;" k="80" />
    <hkern u1="h" u2="&#x22;" k="80" />
    <hkern u1="k" u2="&#x153;" k="60" />
    <hkern u1="k" u2="&#x119;" k="60" />
    <hkern u1="k" u2="&#x107;" k="60" />
    <hkern u1="k" u2="&#xf8;" k="60" />
    <hkern u1="k" u2="&#xf6;" k="60" />
    <hkern u1="k" u2="&#xf5;" k="60" />
    <hkern u1="k" u2="&#xf4;" k="60" />
    <hkern u1="k" u2="&#xf3;" k="60" />
    <hkern u1="k" u2="&#xf2;" k="60" />
    <hkern u1="k" u2="&#xf0;" k="60" />
    <hkern u1="k" u2="&#xeb;" k="60" />
    <hkern u1="k" u2="&#xea;" k="60" />
    <hkern u1="k" u2="&#xe9;" k="60" />
    <hkern u1="k" u2="&#xe8;" k="60" />
    <hkern u1="k" u2="&#xe7;" k="60" />
    <hkern u1="k" u2="q" k="60" />
    <hkern u1="k" u2="o" k="60" />
    <hkern u1="k" u2="e" k="60" />
    <hkern u1="k" u2="d" k="60" />
    <hkern u1="k" u2="c" k="60" />
    <hkern u1="m" u2="&#x2122;" k="80" />
    <hkern u1="m" u2="&#x201d;" k="80" />
    <hkern u1="m" u2="&#x201c;" k="80" />
    <hkern u1="m" u2="&#x2019;" k="80" />
    <hkern u1="m" u2="&#x2018;" k="80" />
    <hkern u1="m" u2="&#xba;" k="80" />
    <hkern u1="m" u2="&#xb9;" k="80" />
    <hkern u1="m" u2="&#xb3;" k="80" />
    <hkern u1="m" u2="&#xb2;" k="80" />
    <hkern u1="m" u2="&#xb0;" k="80" />
    <hkern u1="m" u2="&#xaa;" k="80" />
    <hkern u1="m" u2="y" k="40" />
    <hkern u1="m" u2="w" k="20" />
    <hkern u1="m" u2="v" k="40" />
    <hkern u1="m" u2="&#x2a;" k="80" />
    <hkern u1="m" u2="&#x27;" k="80" />
    <hkern u1="m" u2="&#x22;" k="80" />
    <hkern u1="n" u2="&#x2122;" k="80" />
    <hkern u1="n" u2="&#x201d;" k="80" />
    <hkern u1="n" u2="&#x201c;" k="80" />
    <hkern u1="n" u2="&#x2019;" k="80" />
    <hkern u1="n" u2="&#x2018;" k="80" />
    <hkern u1="n" u2="&#xba;" k="80" />
    <hkern u1="n" u2="&#xb9;" k="80" />
    <hkern u1="n" u2="&#xb3;" k="80" />
    <hkern u1="n" u2="&#xb2;" k="80" />
    <hkern u1="n" u2="&#xb0;" k="80" />
    <hkern u1="n" u2="&#xaa;" k="80" />
    <hkern u1="n" u2="y" k="40" />
    <hkern u1="n" u2="w" k="20" />
    <hkern u1="n" u2="v" k="40" />
    <hkern u1="n" u2="&#x2a;" k="80" />
    <hkern u1="n" u2="&#x27;" k="80" />
    <hkern u1="n" u2="&#x22;" k="80" />
    <hkern u1="o" u2="&#x2122;" k="100" />
    <hkern u1="o" u2="&#x201d;" k="100" />
    <hkern u1="o" u2="&#x201c;" k="100" />
    <hkern u1="o" u2="&#x2019;" k="100" />
    <hkern u1="o" u2="&#x2018;" k="100" />
    <hkern u1="o" u2="&#xba;" k="100" />
    <hkern u1="o" u2="&#xb0;" k="100" />
    <hkern u1="o" u2="&#xaa;" k="100" />
    <hkern u1="o" u2="&#x7d;" k="40" />
    <hkern u1="o" u2="y" k="40" />
    <hkern u1="o" u2="x" k="60" />
    <hkern u1="o" u2="v" k="40" />
    <hkern u1="o" u2="]" k="40" />
    <hkern u1="o" u2="\" k="130" />
    <hkern u1="o" u2="W" k="50" />
    <hkern u1="o" u2="V" k="130" />
    <hkern u1="o" u2="&#x2a;" k="100" />
    <hkern u1="o" u2="&#x29;" k="40" />
    <hkern u1="o" u2="&#x27;" k="100" />
    <hkern u1="o" u2="&#x22;" k="100" />
    <hkern u1="p" u2="&#x2122;" k="100" />
    <hkern u1="p" u2="&#x201d;" k="100" />
    <hkern u1="p" u2="&#x201c;" k="100" />
    <hkern u1="p" u2="&#x2019;" k="100" />
    <hkern u1="p" u2="&#x2018;" k="100" />
    <hkern u1="p" u2="&#xba;" k="100" />
    <hkern u1="p" u2="&#xb0;" k="100" />
    <hkern u1="p" u2="&#xaa;" k="100" />
    <hkern u1="p" u2="&#x7d;" k="40" />
    <hkern u1="p" u2="y" k="40" />
    <hkern u1="p" u2="x" k="60" />
    <hkern u1="p" u2="v" k="40" />
    <hkern u1="p" u2="]" k="40" />
    <hkern u1="p" u2="\" k="130" />
    <hkern u1="p" u2="W" k="50" />
    <hkern u1="p" u2="V" k="130" />
    <hkern u1="p" u2="&#x2a;" k="100" />
    <hkern u1="p" u2="&#x29;" k="40" />
    <hkern u1="p" u2="&#x27;" k="100" />
    <hkern u1="p" u2="&#x22;" k="100" />
    <hkern u1="r" u2="&#x201e;" k="140" />
    <hkern u1="r" u2="&#x201a;" k="140" />
    <hkern u1="r" u2="&#x105;" k="20" />
    <hkern u1="r" u2="&#xe6;" k="20" />
    <hkern u1="r" u2="&#xe5;" k="20" />
    <hkern u1="r" u2="&#xe4;" k="20" />
    <hkern u1="r" u2="&#xe3;" k="20" />
    <hkern u1="r" u2="&#xe2;" k="20" />
    <hkern u1="r" u2="&#xe1;" k="20" />
    <hkern u1="r" u2="&#xe0;" k="20" />
    <hkern u1="r" u2="a" k="20" />
    <hkern u1="r" u2="&#x2e;" k="140" />
    <hkern u1="r" u2="&#x2c;" k="140" />
    <hkern u1="v" u2="&#x2206;" k="100" />
    <hkern u1="v" u2="&#x201e;" k="140" />
    <hkern u1="v" u2="&#x201a;" k="140" />
    <hkern u1="v" u2="&#x153;" k="40" />
    <hkern u1="v" u2="&#x119;" k="40" />
    <hkern u1="v" u2="&#x107;" k="40" />
    <hkern u1="v" u2="&#x104;" k="100" />
    <hkern u1="v" u2="&#xf8;" k="40" />
    <hkern u1="v" u2="&#xf6;" k="40" />
    <hkern u1="v" u2="&#xf5;" k="40" />
    <hkern u1="v" u2="&#xf4;" k="40" />
    <hkern u1="v" u2="&#xf3;" k="40" />
    <hkern u1="v" u2="&#xf2;" k="40" />
    <hkern u1="v" u2="&#xf0;" k="40" />
    <hkern u1="v" u2="&#xeb;" k="40" />
    <hkern u1="v" u2="&#xea;" k="40" />
    <hkern u1="v" u2="&#xe9;" k="40" />
    <hkern u1="v" u2="&#xe8;" k="40" />
    <hkern u1="v" u2="&#xe7;" k="40" />
    <hkern u1="v" u2="&#xc6;" k="100" />
    <hkern u1="v" u2="&#xc5;" k="100" />
    <hkern u1="v" u2="&#xc4;" k="100" />
    <hkern u1="v" u2="&#xc3;" k="100" />
    <hkern u1="v" u2="&#xc2;" k="100" />
    <hkern u1="v" u2="&#xc1;" k="100" />
    <hkern u1="v" u2="&#xc0;" k="100" />
    <hkern u1="v" u2="q" k="40" />
    <hkern u1="v" u2="o" k="40" />
    <hkern u1="v" u2="e" k="40" />
    <hkern u1="v" u2="d" k="40" />
    <hkern u1="v" u2="c" k="40" />
    <hkern u1="v" u2="A" k="100" />
    <hkern u1="v" u2="&#x2f;" k="100" />
    <hkern u1="v" u2="&#x2e;" k="140" />
    <hkern u1="v" u2="&#x2c;" k="140" />
    <hkern u1="v" u2="&#x26;" k="100" />
    <hkern u1="w" u2="&#x201e;" k="80" />
    <hkern u1="w" u2="&#x201a;" k="80" />
    <hkern u1="w" u2="&#x2e;" k="80" />
    <hkern u1="w" u2="&#x2c;" k="80" />
    <hkern u1="x" u2="&#x153;" k="60" />
    <hkern u1="x" u2="&#x119;" k="60" />
    <hkern u1="x" u2="&#x107;" k="60" />
    <hkern u1="x" u2="&#xf8;" k="60" />
    <hkern u1="x" u2="&#xf6;" k="60" />
    <hkern u1="x" u2="&#xf5;" k="60" />
    <hkern u1="x" u2="&#xf4;" k="60" />
    <hkern u1="x" u2="&#xf3;" k="60" />
    <hkern u1="x" u2="&#xf2;" k="60" />
    <hkern u1="x" u2="&#xf0;" k="60" />
    <hkern u1="x" u2="&#xeb;" k="60" />
    <hkern u1="x" u2="&#xea;" k="60" />
    <hkern u1="x" u2="&#xe9;" k="60" />
    <hkern u1="x" u2="&#xe8;" k="60" />
    <hkern u1="x" u2="&#xe7;" k="60" />
    <hkern u1="x" u2="q" k="60" />
    <hkern u1="x" u2="o" k="60" />
    <hkern u1="x" u2="e" k="60" />
    <hkern u1="x" u2="d" k="60" />
    <hkern u1="x" u2="c" k="60" />
    <hkern u1="y" u2="&#x2206;" k="100" />
    <hkern u1="y" u2="&#x201e;" k="140" />
    <hkern u1="y" u2="&#x201a;" k="140" />
    <hkern u1="y" u2="&#x153;" k="40" />
    <hkern u1="y" u2="&#x119;" k="40" />
    <hkern u1="y" u2="&#x107;" k="40" />
    <hkern u1="y" u2="&#x104;" k="100" />
    <hkern u1="y" u2="&#xf8;" k="40" />
    <hkern u1="y" u2="&#xf6;" k="40" />
    <hkern u1="y" u2="&#xf5;" k="40" />
    <hkern u1="y" u2="&#xf4;" k="40" />
    <hkern u1="y" u2="&#xf3;" k="40" />
    <hkern u1="y" u2="&#xf2;" k="40" />
    <hkern u1="y" u2="&#xf0;" k="40" />
    <hkern u1="y" u2="&#xeb;" k="40" />
    <hkern u1="y" u2="&#xea;" k="40" />
    <hkern u1="y" u2="&#xe9;" k="40" />
    <hkern u1="y" u2="&#xe8;" k="40" />
    <hkern u1="y" u2="&#xe7;" k="40" />
    <hkern u1="y" u2="&#xc6;" k="100" />
    <hkern u1="y" u2="&#xc5;" k="100" />
    <hkern u1="y" u2="&#xc4;" k="100" />
    <hkern u1="y" u2="&#xc3;" k="100" />
    <hkern u1="y" u2="&#xc2;" k="100" />
    <hkern u1="y" u2="&#xc1;" k="100" />
    <hkern u1="y" u2="&#xc0;" k="100" />
    <hkern u1="y" u2="q" k="40" />
    <hkern u1="y" u2="o" k="40" />
    <hkern u1="y" u2="e" k="40" />
    <hkern u1="y" u2="d" k="40" />
    <hkern u1="y" u2="c" k="40" />
    <hkern u1="y" u2="A" k="100" />
    <hkern u1="y" u2="&#x2f;" k="100" />
    <hkern u1="y" u2="&#x2e;" k="140" />
    <hkern u1="y" u2="&#x2c;" k="140" />
    <hkern u1="y" u2="&#x26;" k="100" />
    <hkern u1="&#x7b;" u2="&#x153;" k="40" />
    <hkern u1="&#x7b;" u2="&#x152;" k="40" />
    <hkern u1="&#x7b;" u2="&#x119;" k="40" />
    <hkern u1="&#x7b;" u2="&#x107;" k="40" />
    <hkern u1="&#x7b;" u2="&#x106;" k="40" />
    <hkern u1="&#x7b;" u2="&#xf8;" k="40" />
    <hkern u1="&#x7b;" u2="&#xf6;" k="40" />
    <hkern u1="&#x7b;" u2="&#xf5;" k="40" />
    <hkern u1="&#x7b;" u2="&#xf4;" k="40" />
    <hkern u1="&#x7b;" u2="&#xf3;" k="40" />
    <hkern u1="&#x7b;" u2="&#xf2;" k="40" />
    <hkern u1="&#x7b;" u2="&#xf0;" k="40" />
    <hkern u1="&#x7b;" u2="&#xeb;" k="40" />
    <hkern u1="&#x7b;" u2="&#xea;" k="40" />
    <hkern u1="&#x7b;" u2="&#xe9;" k="40" />
    <hkern u1="&#x7b;" u2="&#xe8;" k="40" />
    <hkern u1="&#x7b;" u2="&#xe7;" k="40" />
    <hkern u1="&#x7b;" u2="&#xd8;" k="40" />
    <hkern u1="&#x7b;" u2="&#xd6;" k="40" />
    <hkern u1="&#x7b;" u2="&#xd5;" k="40" />
    <hkern u1="&#x7b;" u2="&#xd4;" k="40" />
    <hkern u1="&#x7b;" u2="&#xd3;" k="40" />
    <hkern u1="&#x7b;" u2="&#xd2;" k="40" />
    <hkern u1="&#x7b;" u2="&#xc7;" k="40" />
    <hkern u1="&#x7b;" u2="q" k="40" />
    <hkern u1="&#x7b;" u2="o" k="40" />
    <hkern u1="&#x7b;" u2="e" k="40" />
    <hkern u1="&#x7b;" u2="d" k="40" />
    <hkern u1="&#x7b;" u2="c" k="40" />
    <hkern u1="&#x7b;" u2="Q" k="40" />
    <hkern u1="&#x7b;" u2="O" k="40" />
    <hkern u1="&#x7b;" u2="G" k="40" />
    <hkern u1="&#x7b;" u2="C" k="40" />
    <hkern u1="&#x7b;" u2="&#x40;" k="40" />
    <hkern u1="&#xaa;" u2="&#x2206;" k="200" />
    <hkern u1="&#xaa;" u2="&#x203a;" k="160" />
    <hkern u1="&#xaa;" u2="&#x2039;" k="160" />
    <hkern u1="&#xaa;" u2="&#x2022;" k="160" />
    <hkern u1="&#xaa;" u2="&#x201e;" k="200" />
    <hkern u1="&#xaa;" u2="&#x201a;" k="200" />
    <hkern u1="&#xaa;" u2="&#x2014;" k="160" />
    <hkern u1="&#xaa;" u2="&#x2013;" k="160" />
    <hkern u1="&#xaa;" u2="&#x178;" k="-40" />
    <hkern u1="&#xaa;" u2="&#x153;" k="100" />
    <hkern u1="&#xaa;" u2="&#x152;" k="40" />
    <hkern u1="&#xaa;" u2="&#x119;" k="100" />
    <hkern u1="&#xaa;" u2="&#x107;" k="100" />
    <hkern u1="&#xaa;" u2="&#x106;" k="40" />
    <hkern u1="&#xaa;" u2="&#x105;" k="68" />
    <hkern u1="&#xaa;" u2="&#x104;" k="200" />
    <hkern u1="&#xaa;" u2="&#xf8;" k="100" />
    <hkern u1="&#xaa;" u2="&#xf6;" k="100" />
    <hkern u1="&#xaa;" u2="&#xf5;" k="100" />
    <hkern u1="&#xaa;" u2="&#xf4;" k="100" />
    <hkern u1="&#xaa;" u2="&#xf3;" k="100" />
    <hkern u1="&#xaa;" u2="&#xf2;" k="100" />
    <hkern u1="&#xaa;" u2="&#xf0;" k="100" />
    <hkern u1="&#xaa;" u2="&#xeb;" k="100" />
    <hkern u1="&#xaa;" u2="&#xea;" k="100" />
    <hkern u1="&#xaa;" u2="&#xe9;" k="100" />
    <hkern u1="&#xaa;" u2="&#xe8;" k="100" />
    <hkern u1="&#xaa;" u2="&#xe7;" k="100" />
    <hkern u1="&#xaa;" u2="&#xe6;" k="68" />
    <hkern u1="&#xaa;" u2="&#xe5;" k="68" />
    <hkern u1="&#xaa;" u2="&#xe4;" k="68" />
    <hkern u1="&#xaa;" u2="&#xe3;" k="68" />
    <hkern u1="&#xaa;" u2="&#xe2;" k="68" />
    <hkern u1="&#xaa;" u2="&#xe1;" k="68" />
    <hkern u1="&#xaa;" u2="&#xe0;" k="68" />
    <hkern u1="&#xaa;" u2="&#xdd;" k="-40" />
    <hkern u1="&#xaa;" u2="&#xd8;" k="40" />
    <hkern u1="&#xaa;" u2="&#xd6;" k="40" />
    <hkern u1="&#xaa;" u2="&#xd5;" k="40" />
    <hkern u1="&#xaa;" u2="&#xd4;" k="40" />
    <hkern u1="&#xaa;" u2="&#xd3;" k="40" />
    <hkern u1="&#xaa;" u2="&#xd2;" k="40" />
    <hkern u1="&#xaa;" u2="&#xc7;" k="40" />
    <hkern u1="&#xaa;" u2="&#xc6;" k="200" />
    <hkern u1="&#xaa;" u2="&#xc5;" k="200" />
    <hkern u1="&#xaa;" u2="&#xc4;" k="200" />
    <hkern u1="&#xaa;" u2="&#xc3;" k="200" />
    <hkern u1="&#xaa;" u2="&#xc2;" k="200" />
    <hkern u1="&#xaa;" u2="&#xc1;" k="200" />
    <hkern u1="&#xaa;" u2="&#xc0;" k="200" />
    <hkern u1="&#xaa;" u2="&#xbb;" k="160" />
    <hkern u1="&#xaa;" u2="&#xb7;" k="160" />
    <hkern u1="&#xaa;" u2="&#xad;" k="160" />
    <hkern u1="&#xaa;" u2="&#xab;" k="160" />
    <hkern u1="&#xaa;" u2="q" k="100" />
    <hkern u1="&#xaa;" u2="o" k="100" />
    <hkern u1="&#xaa;" u2="e" k="100" />
    <hkern u1="&#xaa;" u2="d" k="100" />
    <hkern u1="&#xaa;" u2="c" k="100" />
    <hkern u1="&#xaa;" u2="a" k="68" />
    <hkern u1="&#xaa;" u2="\" k="-40" />
    <hkern u1="&#xaa;" u2="Y" k="-40" />
    <hkern u1="&#xaa;" u2="W" k="-40" />
    <hkern u1="&#xaa;" u2="V" k="-40" />
    <hkern u1="&#xaa;" u2="Q" k="40" />
    <hkern u1="&#xaa;" u2="O" k="40" />
    <hkern u1="&#xaa;" u2="G" k="40" />
    <hkern u1="&#xaa;" u2="C" k="40" />
    <hkern u1="&#xaa;" u2="A" k="200" />
    <hkern u1="&#xaa;" u2="&#x40;" k="40" />
    <hkern u1="&#xaa;" u2="&#x2f;" k="200" />
    <hkern u1="&#xaa;" u2="&#x2e;" k="200" />
    <hkern u1="&#xaa;" u2="&#x2d;" k="160" />
    <hkern u1="&#xaa;" u2="&#x2c;" k="200" />
    <hkern u1="&#xaa;" u2="&#x26;" k="200" />
    <hkern u1="&#xab;" u2="&#x2206;" k="80" />
    <hkern u1="&#xab;" u2="&#x2122;" k="160" />
    <hkern u1="&#xab;" u2="&#x201e;" k="130" />
    <hkern u1="&#xab;" u2="&#x201d;" k="160" />
    <hkern u1="&#xab;" u2="&#x201c;" k="160" />
    <hkern u1="&#xab;" u2="&#x201a;" k="130" />
    <hkern u1="&#xab;" u2="&#x2019;" k="160" />
    <hkern u1="&#xab;" u2="&#x2018;" k="160" />
    <hkern u1="&#xab;" u2="&#x17d;" k="50" />
    <hkern u1="&#xab;" u2="&#x17b;" k="50" />
    <hkern u1="&#xab;" u2="&#x179;" k="50" />
    <hkern u1="&#xab;" u2="&#x178;" k="160" />
    <hkern u1="&#xab;" u2="&#x104;" k="80" />
    <hkern u1="&#xab;" u2="&#xdd;" k="160" />
    <hkern u1="&#xab;" u2="&#xc6;" k="80" />
    <hkern u1="&#xab;" u2="&#xc5;" k="80" />
    <hkern u1="&#xab;" u2="&#xc4;" k="80" />
    <hkern u1="&#xab;" u2="&#xc3;" k="80" />
    <hkern u1="&#xab;" u2="&#xc2;" k="80" />
    <hkern u1="&#xab;" u2="&#xc1;" k="80" />
    <hkern u1="&#xab;" u2="&#xc0;" k="80" />
    <hkern u1="&#xab;" u2="&#xba;" k="160" />
    <hkern u1="&#xab;" u2="&#xb0;" k="160" />
    <hkern u1="&#xab;" u2="&#xaa;" k="160" />
    <hkern u1="&#xab;" u2="\" k="120" />
    <hkern u1="&#xab;" u2="Z" k="50" />
    <hkern u1="&#xab;" u2="Y" k="160" />
    <hkern u1="&#xab;" u2="X" k="70" />
    <hkern u1="&#xab;" u2="W" k="40" />
    <hkern u1="&#xab;" u2="V" k="120" />
    <hkern u1="&#xab;" u2="T" k="180" />
    <hkern u1="&#xab;" u2="A" k="80" />
    <hkern u1="&#xab;" u2="&#x2f;" k="80" />
    <hkern u1="&#xab;" u2="&#x2e;" k="130" />
    <hkern u1="&#xab;" u2="&#x2c;" k="130" />
    <hkern u1="&#xab;" u2="&#x2a;" k="160" />
    <hkern u1="&#xab;" u2="&#x27;" k="160" />
    <hkern u1="&#xab;" u2="&#x26;" k="80" />
    <hkern u1="&#xab;" u2="&#x22;" k="160" />
    <hkern u1="&#xad;" u2="&#x2206;" k="80" />
    <hkern u1="&#xad;" u2="&#x2122;" k="160" />
    <hkern u1="&#xad;" u2="&#x201e;" k="130" />
    <hkern u1="&#xad;" u2="&#x201d;" k="160" />
    <hkern u1="&#xad;" u2="&#x201c;" k="160" />
    <hkern u1="&#xad;" u2="&#x201a;" k="130" />
    <hkern u1="&#xad;" u2="&#x2019;" k="160" />
    <hkern u1="&#xad;" u2="&#x2018;" k="160" />
    <hkern u1="&#xad;" u2="&#x17d;" k="50" />
    <hkern u1="&#xad;" u2="&#x17b;" k="50" />
    <hkern u1="&#xad;" u2="&#x179;" k="50" />
    <hkern u1="&#xad;" u2="&#x178;" k="160" />
    <hkern u1="&#xad;" u2="&#x104;" k="80" />
    <hkern u1="&#xad;" u2="&#xdd;" k="160" />
    <hkern u1="&#xad;" u2="&#xc6;" k="80" />
    <hkern u1="&#xad;" u2="&#xc5;" k="80" />
    <hkern u1="&#xad;" u2="&#xc4;" k="80" />
    <hkern u1="&#xad;" u2="&#xc3;" k="80" />
    <hkern u1="&#xad;" u2="&#xc2;" k="80" />
    <hkern u1="&#xad;" u2="&#xc1;" k="80" />
    <hkern u1="&#xad;" u2="&#xc0;" k="80" />
    <hkern u1="&#xad;" u2="&#xba;" k="160" />
    <hkern u1="&#xad;" u2="&#xb0;" k="160" />
    <hkern u1="&#xad;" u2="&#xaa;" k="160" />
    <hkern u1="&#xad;" u2="\" k="120" />
    <hkern u1="&#xad;" u2="Z" k="50" />
    <hkern u1="&#xad;" u2="Y" k="160" />
    <hkern u1="&#xad;" u2="X" k="70" />
    <hkern u1="&#xad;" u2="W" k="40" />
    <hkern u1="&#xad;" u2="V" k="120" />
    <hkern u1="&#xad;" u2="T" k="180" />
    <hkern u1="&#xad;" u2="A" k="80" />
    <hkern u1="&#xad;" u2="&#x2f;" k="80" />
    <hkern u1="&#xad;" u2="&#x2e;" k="130" />
    <hkern u1="&#xad;" u2="&#x2c;" k="130" />
    <hkern u1="&#xad;" u2="&#x2a;" k="160" />
    <hkern u1="&#xad;" u2="&#x27;" k="160" />
    <hkern u1="&#xad;" u2="&#x26;" k="80" />
    <hkern u1="&#xad;" u2="&#x22;" k="160" />
    <hkern u1="&#xae;" u2="&#x2206;" k="60" />
    <hkern u1="&#xae;" u2="&#x2122;" k="40" />
    <hkern u1="&#xae;" u2="&#x201e;" k="50" />
    <hkern u1="&#xae;" u2="&#x201d;" k="40" />
    <hkern u1="&#xae;" u2="&#x201c;" k="40" />
    <hkern u1="&#xae;" u2="&#x201a;" k="50" />
    <hkern u1="&#xae;" u2="&#x2019;" k="40" />
    <hkern u1="&#xae;" u2="&#x2018;" k="40" />
    <hkern u1="&#xae;" u2="&#x17d;" k="60" />
    <hkern u1="&#xae;" u2="&#x17b;" k="60" />
    <hkern u1="&#xae;" u2="&#x179;" k="60" />
    <hkern u1="&#xae;" u2="&#x178;" k="80" />
    <hkern u1="&#xae;" u2="&#x104;" k="60" />
    <hkern u1="&#xae;" u2="&#xdd;" k="80" />
    <hkern u1="&#xae;" u2="&#xc6;" k="60" />
    <hkern u1="&#xae;" u2="&#xc5;" k="60" />
    <hkern u1="&#xae;" u2="&#xc4;" k="60" />
    <hkern u1="&#xae;" u2="&#xc3;" k="60" />
    <hkern u1="&#xae;" u2="&#xc2;" k="60" />
    <hkern u1="&#xae;" u2="&#xc1;" k="60" />
    <hkern u1="&#xae;" u2="&#xc0;" k="60" />
    <hkern u1="&#xae;" u2="&#xba;" k="40" />
    <hkern u1="&#xae;" u2="&#xb0;" k="40" />
    <hkern u1="&#xae;" u2="&#xaa;" k="40" />
    <hkern u1="&#xae;" u2="&#x7d;" k="40" />
    <hkern u1="&#xae;" u2="]" k="40" />
    <hkern u1="&#xae;" u2="\" k="60" />
    <hkern u1="&#xae;" u2="Z" k="60" />
    <hkern u1="&#xae;" u2="Y" k="80" />
    <hkern u1="&#xae;" u2="X" k="30" />
    <hkern u1="&#xae;" u2="V" k="60" />
    <hkern u1="&#xae;" u2="T" k="60" />
    <hkern u1="&#xae;" u2="A" k="60" />
    <hkern u1="&#xae;" u2="&#x2f;" k="60" />
    <hkern u1="&#xae;" u2="&#x2e;" k="50" />
    <hkern u1="&#xae;" u2="&#x2c;" k="50" />
    <hkern u1="&#xae;" u2="&#x2a;" k="40" />
    <hkern u1="&#xae;" u2="&#x29;" k="40" />
    <hkern u1="&#xae;" u2="&#x27;" k="40" />
    <hkern u1="&#xae;" u2="&#x26;" k="60" />
    <hkern u1="&#xae;" u2="&#x22;" k="40" />
    <hkern u1="&#xb0;" u2="&#x2206;" k="200" />
    <hkern u1="&#xb0;" u2="&#x203a;" k="160" />
    <hkern u1="&#xb0;" u2="&#x2039;" k="160" />
    <hkern u1="&#xb0;" u2="&#x2022;" k="160" />
    <hkern u1="&#xb0;" u2="&#x201e;" k="200" />
    <hkern u1="&#xb0;" u2="&#x201a;" k="200" />
    <hkern u1="&#xb0;" u2="&#x2014;" k="160" />
    <hkern u1="&#xb0;" u2="&#x2013;" k="160" />
    <hkern u1="&#xb0;" u2="&#x178;" k="-40" />
    <hkern u1="&#xb0;" u2="&#x153;" k="100" />
    <hkern u1="&#xb0;" u2="&#x152;" k="40" />
    <hkern u1="&#xb0;" u2="&#x119;" k="100" />
    <hkern u1="&#xb0;" u2="&#x107;" k="100" />
    <hkern u1="&#xb0;" u2="&#x106;" k="40" />
    <hkern u1="&#xb0;" u2="&#x105;" k="68" />
    <hkern u1="&#xb0;" u2="&#x104;" k="200" />
    <hkern u1="&#xb0;" u2="&#xf8;" k="100" />
    <hkern u1="&#xb0;" u2="&#xf6;" k="100" />
    <hkern u1="&#xb0;" u2="&#xf5;" k="100" />
    <hkern u1="&#xb0;" u2="&#xf4;" k="100" />
    <hkern u1="&#xb0;" u2="&#xf3;" k="100" />
    <hkern u1="&#xb0;" u2="&#xf2;" k="100" />
    <hkern u1="&#xb0;" u2="&#xf0;" k="100" />
    <hkern u1="&#xb0;" u2="&#xeb;" k="100" />
    <hkern u1="&#xb0;" u2="&#xea;" k="100" />
    <hkern u1="&#xb0;" u2="&#xe9;" k="100" />
    <hkern u1="&#xb0;" u2="&#xe8;" k="100" />
    <hkern u1="&#xb0;" u2="&#xe7;" k="100" />
    <hkern u1="&#xb0;" u2="&#xe6;" k="68" />
    <hkern u1="&#xb0;" u2="&#xe5;" k="68" />
    <hkern u1="&#xb0;" u2="&#xe4;" k="68" />
    <hkern u1="&#xb0;" u2="&#xe3;" k="68" />
    <hkern u1="&#xb0;" u2="&#xe2;" k="68" />
    <hkern u1="&#xb0;" u2="&#xe1;" k="68" />
    <hkern u1="&#xb0;" u2="&#xe0;" k="68" />
    <hkern u1="&#xb0;" u2="&#xdd;" k="-40" />
    <hkern u1="&#xb0;" u2="&#xd8;" k="40" />
    <hkern u1="&#xb0;" u2="&#xd6;" k="40" />
    <hkern u1="&#xb0;" u2="&#xd5;" k="40" />
    <hkern u1="&#xb0;" u2="&#xd4;" k="40" />
    <hkern u1="&#xb0;" u2="&#xd3;" k="40" />
    <hkern u1="&#xb0;" u2="&#xd2;" k="40" />
    <hkern u1="&#xb0;" u2="&#xc7;" k="40" />
    <hkern u1="&#xb0;" u2="&#xc6;" k="200" />
    <hkern u1="&#xb0;" u2="&#xc5;" k="200" />
    <hkern u1="&#xb0;" u2="&#xc4;" k="200" />
    <hkern u1="&#xb0;" u2="&#xc3;" k="200" />
    <hkern u1="&#xb0;" u2="&#xc2;" k="200" />
    <hkern u1="&#xb0;" u2="&#xc1;" k="200" />
    <hkern u1="&#xb0;" u2="&#xc0;" k="200" />
    <hkern u1="&#xb0;" u2="&#xbb;" k="160" />
    <hkern u1="&#xb0;" u2="&#xb7;" k="160" />
    <hkern u1="&#xb0;" u2="&#xad;" k="160" />
    <hkern u1="&#xb0;" u2="&#xab;" k="160" />
    <hkern u1="&#xb0;" u2="q" k="100" />
    <hkern u1="&#xb0;" u2="o" k="100" />
    <hkern u1="&#xb0;" u2="e" k="100" />
    <hkern u1="&#xb0;" u2="d" k="100" />
    <hkern u1="&#xb0;" u2="c" k="100" />
    <hkern u1="&#xb0;" u2="a" k="68" />
    <hkern u1="&#xb0;" u2="\" k="-40" />
    <hkern u1="&#xb0;" u2="Y" k="-40" />
    <hkern u1="&#xb0;" u2="W" k="-40" />
    <hkern u1="&#xb0;" u2="V" k="-40" />
    <hkern u1="&#xb0;" u2="Q" k="40" />
    <hkern u1="&#xb0;" u2="O" k="40" />
    <hkern u1="&#xb0;" u2="G" k="40" />
    <hkern u1="&#xb0;" u2="C" k="40" />
    <hkern u1="&#xb0;" u2="A" k="200" />
    <hkern u1="&#xb0;" u2="&#x40;" k="40" />
    <hkern u1="&#xb0;" u2="&#x2f;" k="200" />
    <hkern u1="&#xb0;" u2="&#x2e;" k="200" />
    <hkern u1="&#xb0;" u2="&#x2d;" k="160" />
    <hkern u1="&#xb0;" u2="&#x2c;" k="200" />
    <hkern u1="&#xb0;" u2="&#x26;" k="200" />
    <hkern u1="&#xb2;" u2="&#x2206;" k="220" />
    <hkern u1="&#xb2;" u2="&#x178;" k="-40" />
    <hkern u1="&#xb2;" u2="&#x104;" k="220" />
    <hkern u1="&#xb2;" u2="&#xdd;" k="-40" />
    <hkern u1="&#xb2;" u2="&#xc6;" k="220" />
    <hkern u1="&#xb2;" u2="&#xc5;" k="220" />
    <hkern u1="&#xb2;" u2="&#xc4;" k="220" />
    <hkern u1="&#xb2;" u2="&#xc3;" k="220" />
    <hkern u1="&#xb2;" u2="&#xc2;" k="220" />
    <hkern u1="&#xb2;" u2="&#xc1;" k="220" />
    <hkern u1="&#xb2;" u2="&#xc0;" k="220" />
    <hkern u1="&#xb2;" u2="\" k="-40" />
    <hkern u1="&#xb2;" u2="Y" k="-40" />
    <hkern u1="&#xb2;" u2="W" k="-40" />
    <hkern u1="&#xb2;" u2="V" k="-40" />
    <hkern u1="&#xb2;" u2="A" k="220" />
    <hkern u1="&#xb2;" u2="&#x2f;" k="220" />
    <hkern u1="&#xb2;" u2="&#x26;" k="220" />
    <hkern u1="&#xb3;" u2="&#x2206;" k="220" />
    <hkern u1="&#xb3;" u2="&#x178;" k="-40" />
    <hkern u1="&#xb3;" u2="&#x104;" k="220" />
    <hkern u1="&#xb3;" u2="&#xdd;" k="-40" />
    <hkern u1="&#xb3;" u2="&#xc6;" k="220" />
    <hkern u1="&#xb3;" u2="&#xc5;" k="220" />
    <hkern u1="&#xb3;" u2="&#xc4;" k="220" />
    <hkern u1="&#xb3;" u2="&#xc3;" k="220" />
    <hkern u1="&#xb3;" u2="&#xc2;" k="220" />
    <hkern u1="&#xb3;" u2="&#xc1;" k="220" />
    <hkern u1="&#xb3;" u2="&#xc0;" k="220" />
    <hkern u1="&#xb3;" u2="\" k="-40" />
    <hkern u1="&#xb3;" u2="Y" k="-40" />
    <hkern u1="&#xb3;" u2="W" k="-40" />
    <hkern u1="&#xb3;" u2="V" k="-40" />
    <hkern u1="&#xb3;" u2="A" k="220" />
    <hkern u1="&#xb3;" u2="&#x2f;" k="220" />
    <hkern u1="&#xb3;" u2="&#x26;" k="220" />
    <hkern u1="&#xb7;" u2="&#x2206;" k="80" />
    <hkern u1="&#xb7;" u2="&#x2122;" k="160" />
    <hkern u1="&#xb7;" u2="&#x201e;" k="130" />
    <hkern u1="&#xb7;" u2="&#x201d;" k="160" />
    <hkern u1="&#xb7;" u2="&#x201c;" k="160" />
    <hkern u1="&#xb7;" u2="&#x201a;" k="130" />
    <hkern u1="&#xb7;" u2="&#x2019;" k="160" />
    <hkern u1="&#xb7;" u2="&#x2018;" k="160" />
    <hkern u1="&#xb7;" u2="&#x17d;" k="50" />
    <hkern u1="&#xb7;" u2="&#x17b;" k="50" />
    <hkern u1="&#xb7;" u2="&#x179;" k="50" />
    <hkern u1="&#xb7;" u2="&#x178;" k="160" />
    <hkern u1="&#xb7;" u2="&#x104;" k="80" />
    <hkern u1="&#xb7;" u2="&#xdd;" k="160" />
    <hkern u1="&#xb7;" u2="&#xc6;" k="80" />
    <hkern u1="&#xb7;" u2="&#xc5;" k="80" />
    <hkern u1="&#xb7;" u2="&#xc4;" k="80" />
    <hkern u1="&#xb7;" u2="&#xc3;" k="80" />
    <hkern u1="&#xb7;" u2="&#xc2;" k="80" />
    <hkern u1="&#xb7;" u2="&#xc1;" k="80" />
    <hkern u1="&#xb7;" u2="&#xc0;" k="80" />
    <hkern u1="&#xb7;" u2="&#xba;" k="160" />
    <hkern u1="&#xb7;" u2="&#xb0;" k="160" />
    <hkern u1="&#xb7;" u2="&#xaa;" k="160" />
    <hkern u1="&#xb7;" u2="\" k="120" />
    <hkern u1="&#xb7;" u2="Z" k="50" />
    <hkern u1="&#xb7;" u2="Y" k="160" />
    <hkern u1="&#xb7;" u2="X" k="70" />
    <hkern u1="&#xb7;" u2="W" k="40" />
    <hkern u1="&#xb7;" u2="V" k="120" />
    <hkern u1="&#xb7;" u2="T" k="180" />
    <hkern u1="&#xb7;" u2="A" k="80" />
    <hkern u1="&#xb7;" u2="&#x2f;" k="80" />
    <hkern u1="&#xb7;" u2="&#x2e;" k="130" />
    <hkern u1="&#xb7;" u2="&#x2c;" k="130" />
    <hkern u1="&#xb7;" u2="&#x2a;" k="160" />
    <hkern u1="&#xb7;" u2="&#x27;" k="160" />
    <hkern u1="&#xb7;" u2="&#x26;" k="80" />
    <hkern u1="&#xb7;" u2="&#x22;" k="160" />
    <hkern u1="&#xb9;" u2="&#x2206;" k="220" />
    <hkern u1="&#xb9;" u2="&#x178;" k="-40" />
    <hkern u1="&#xb9;" u2="&#x104;" k="220" />
    <hkern u1="&#xb9;" u2="&#xdd;" k="-40" />
    <hkern u1="&#xb9;" u2="&#xc6;" k="220" />
    <hkern u1="&#xb9;" u2="&#xc5;" k="220" />
    <hkern u1="&#xb9;" u2="&#xc4;" k="220" />
    <hkern u1="&#xb9;" u2="&#xc3;" k="220" />
    <hkern u1="&#xb9;" u2="&#xc2;" k="220" />
    <hkern u1="&#xb9;" u2="&#xc1;" k="220" />
    <hkern u1="&#xb9;" u2="&#xc0;" k="220" />
    <hkern u1="&#xb9;" u2="\" k="-40" />
    <hkern u1="&#xb9;" u2="Y" k="-40" />
    <hkern u1="&#xb9;" u2="W" k="-40" />
    <hkern u1="&#xb9;" u2="V" k="-40" />
    <hkern u1="&#xb9;" u2="A" k="220" />
    <hkern u1="&#xb9;" u2="&#x2f;" k="220" />
    <hkern u1="&#xb9;" u2="&#x26;" k="220" />
    <hkern u1="&#xba;" u2="&#x2206;" k="200" />
    <hkern u1="&#xba;" u2="&#x203a;" k="160" />
    <hkern u1="&#xba;" u2="&#x2039;" k="160" />
    <hkern u1="&#xba;" u2="&#x2022;" k="160" />
    <hkern u1="&#xba;" u2="&#x201e;" k="200" />
    <hkern u1="&#xba;" u2="&#x201a;" k="200" />
    <hkern u1="&#xba;" u2="&#x2014;" k="160" />
    <hkern u1="&#xba;" u2="&#x2013;" k="160" />
    <hkern u1="&#xba;" u2="&#x178;" k="-40" />
    <hkern u1="&#xba;" u2="&#x153;" k="100" />
    <hkern u1="&#xba;" u2="&#x152;" k="40" />
    <hkern u1="&#xba;" u2="&#x119;" k="100" />
    <hkern u1="&#xba;" u2="&#x107;" k="100" />
    <hkern u1="&#xba;" u2="&#x106;" k="40" />
    <hkern u1="&#xba;" u2="&#x105;" k="68" />
    <hkern u1="&#xba;" u2="&#x104;" k="200" />
    <hkern u1="&#xba;" u2="&#xf8;" k="100" />
    <hkern u1="&#xba;" u2="&#xf6;" k="100" />
    <hkern u1="&#xba;" u2="&#xf5;" k="100" />
    <hkern u1="&#xba;" u2="&#xf4;" k="100" />
    <hkern u1="&#xba;" u2="&#xf3;" k="100" />
    <hkern u1="&#xba;" u2="&#xf2;" k="100" />
    <hkern u1="&#xba;" u2="&#xf0;" k="100" />
    <hkern u1="&#xba;" u2="&#xeb;" k="100" />
    <hkern u1="&#xba;" u2="&#xea;" k="100" />
    <hkern u1="&#xba;" u2="&#xe9;" k="100" />
    <hkern u1="&#xba;" u2="&#xe8;" k="100" />
    <hkern u1="&#xba;" u2="&#xe7;" k="100" />
    <hkern u1="&#xba;" u2="&#xe6;" k="68" />
    <hkern u1="&#xba;" u2="&#xe5;" k="68" />
    <hkern u1="&#xba;" u2="&#xe4;" k="68" />
    <hkern u1="&#xba;" u2="&#xe3;" k="68" />
    <hkern u1="&#xba;" u2="&#xe2;" k="68" />
    <hkern u1="&#xba;" u2="&#xe1;" k="68" />
    <hkern u1="&#xba;" u2="&#xe0;" k="68" />
    <hkern u1="&#xba;" u2="&#xdd;" k="-40" />
    <hkern u1="&#xba;" u2="&#xd8;" k="40" />
    <hkern u1="&#xba;" u2="&#xd6;" k="40" />
    <hkern u1="&#xba;" u2="&#xd5;" k="40" />
    <hkern u1="&#xba;" u2="&#xd4;" k="40" />
    <hkern u1="&#xba;" u2="&#xd3;" k="40" />
    <hkern u1="&#xba;" u2="&#xd2;" k="40" />
    <hkern u1="&#xba;" u2="&#xc7;" k="40" />
    <hkern u1="&#xba;" u2="&#xc6;" k="200" />
    <hkern u1="&#xba;" u2="&#xc5;" k="200" />
    <hkern u1="&#xba;" u2="&#xc4;" k="200" />
    <hkern u1="&#xba;" u2="&#xc3;" k="200" />
    <hkern u1="&#xba;" u2="&#xc2;" k="200" />
    <hkern u1="&#xba;" u2="&#xc1;" k="200" />
    <hkern u1="&#xba;" u2="&#xc0;" k="200" />
    <hkern u1="&#xba;" u2="&#xbb;" k="160" />
    <hkern u1="&#xba;" u2="&#xb7;" k="160" />
    <hkern u1="&#xba;" u2="&#xad;" k="160" />
    <hkern u1="&#xba;" u2="&#xab;" k="160" />
    <hkern u1="&#xba;" u2="q" k="100" />
    <hkern u1="&#xba;" u2="o" k="100" />
    <hkern u1="&#xba;" u2="e" k="100" />
    <hkern u1="&#xba;" u2="d" k="100" />
    <hkern u1="&#xba;" u2="c" k="100" />
    <hkern u1="&#xba;" u2="a" k="68" />
    <hkern u1="&#xba;" u2="\" k="-40" />
    <hkern u1="&#xba;" u2="Y" k="-40" />
    <hkern u1="&#xba;" u2="W" k="-40" />
    <hkern u1="&#xba;" u2="V" k="-40" />
    <hkern u1="&#xba;" u2="Q" k="40" />
    <hkern u1="&#xba;" u2="O" k="40" />
    <hkern u1="&#xba;" u2="G" k="40" />
    <hkern u1="&#xba;" u2="C" k="40" />
    <hkern u1="&#xba;" u2="A" k="200" />
    <hkern u1="&#xba;" u2="&#x40;" k="40" />
    <hkern u1="&#xba;" u2="&#x2f;" k="200" />
    <hkern u1="&#xba;" u2="&#x2e;" k="200" />
    <hkern u1="&#xba;" u2="&#x2d;" k="160" />
    <hkern u1="&#xba;" u2="&#x2c;" k="200" />
    <hkern u1="&#xba;" u2="&#x26;" k="200" />
    <hkern u1="&#xbb;" u2="&#x2206;" k="80" />
    <hkern u1="&#xbb;" u2="&#x2122;" k="160" />
    <hkern u1="&#xbb;" u2="&#x201e;" k="130" />
    <hkern u1="&#xbb;" u2="&#x201d;" k="160" />
    <hkern u1="&#xbb;" u2="&#x201c;" k="160" />
    <hkern u1="&#xbb;" u2="&#x201a;" k="130" />
    <hkern u1="&#xbb;" u2="&#x2019;" k="160" />
    <hkern u1="&#xbb;" u2="&#x2018;" k="160" />
    <hkern u1="&#xbb;" u2="&#x17d;" k="50" />
    <hkern u1="&#xbb;" u2="&#x17b;" k="50" />
    <hkern u1="&#xbb;" u2="&#x179;" k="50" />
    <hkern u1="&#xbb;" u2="&#x178;" k="160" />
    <hkern u1="&#xbb;" u2="&#x104;" k="80" />
    <hkern u1="&#xbb;" u2="&#xdd;" k="160" />
    <hkern u1="&#xbb;" u2="&#xc6;" k="80" />
    <hkern u1="&#xbb;" u2="&#xc5;" k="80" />
    <hkern u1="&#xbb;" u2="&#xc4;" k="80" />
    <hkern u1="&#xbb;" u2="&#xc3;" k="80" />
    <hkern u1="&#xbb;" u2="&#xc2;" k="80" />
    <hkern u1="&#xbb;" u2="&#xc1;" k="80" />
    <hkern u1="&#xbb;" u2="&#xc0;" k="80" />
    <hkern u1="&#xbb;" u2="&#xba;" k="160" />
    <hkern u1="&#xbb;" u2="&#xb0;" k="160" />
    <hkern u1="&#xbb;" u2="&#xaa;" k="160" />
    <hkern u1="&#xbb;" u2="\" k="120" />
    <hkern u1="&#xbb;" u2="Z" k="50" />
    <hkern u1="&#xbb;" u2="Y" k="160" />
    <hkern u1="&#xbb;" u2="X" k="70" />
    <hkern u1="&#xbb;" u2="W" k="40" />
    <hkern u1="&#xbb;" u2="V" k="120" />
    <hkern u1="&#xbb;" u2="T" k="180" />
    <hkern u1="&#xbb;" u2="A" k="80" />
    <hkern u1="&#xbb;" u2="&#x2f;" k="80" />
    <hkern u1="&#xbb;" u2="&#x2e;" k="130" />
    <hkern u1="&#xbb;" u2="&#x2c;" k="130" />
    <hkern u1="&#xbb;" u2="&#x2a;" k="160" />
    <hkern u1="&#xbb;" u2="&#x27;" k="160" />
    <hkern u1="&#xbb;" u2="&#x26;" k="80" />
    <hkern u1="&#xbb;" u2="&#x22;" k="160" />
    <hkern u1="&#xc0;" u2="&#x2122;" k="200" />
    <hkern u1="&#xc0;" u2="&#x203a;" k="80" />
    <hkern u1="&#xc0;" u2="&#x2039;" k="80" />
    <hkern u1="&#xc0;" u2="&#x2022;" k="80" />
    <hkern u1="&#xc0;" u2="&#x201d;" k="200" />
    <hkern u1="&#xc0;" u2="&#x201c;" k="200" />
    <hkern u1="&#xc0;" u2="&#x2019;" k="200" />
    <hkern u1="&#xc0;" u2="&#x2018;" k="200" />
    <hkern u1="&#xc0;" u2="&#x2014;" k="80" />
    <hkern u1="&#xc0;" u2="&#x2013;" k="80" />
    <hkern u1="&#xc0;" u2="&#x178;" k="200" />
    <hkern u1="&#xc0;" u2="&#x152;" k="60" />
    <hkern u1="&#xc0;" u2="&#x106;" k="60" />
    <hkern u1="&#xc0;" u2="&#xdd;" k="200" />
    <hkern u1="&#xc0;" u2="&#xdc;" k="50" />
    <hkern u1="&#xc0;" u2="&#xdb;" k="50" />
    <hkern u1="&#xc0;" u2="&#xda;" k="50" />
    <hkern u1="&#xc0;" u2="&#xd9;" k="50" />
    <hkern u1="&#xc0;" u2="&#xd8;" k="60" />
    <hkern u1="&#xc0;" u2="&#xd6;" k="60" />
    <hkern u1="&#xc0;" u2="&#xd5;" k="60" />
    <hkern u1="&#xc0;" u2="&#xd4;" k="60" />
    <hkern u1="&#xc0;" u2="&#xd3;" k="60" />
    <hkern u1="&#xc0;" u2="&#xd2;" k="60" />
    <hkern u1="&#xc0;" u2="&#xc7;" k="60" />
    <hkern u1="&#xc0;" u2="&#xbb;" k="80" />
    <hkern u1="&#xc0;" u2="&#xba;" k="200" />
    <hkern u1="&#xc0;" u2="&#xb9;" k="220" />
    <hkern u1="&#xc0;" u2="&#xb7;" k="80" />
    <hkern u1="&#xc0;" u2="&#xb3;" k="220" />
    <hkern u1="&#xc0;" u2="&#xb2;" k="220" />
    <hkern u1="&#xc0;" u2="&#xb0;" k="200" />
    <hkern u1="&#xc0;" u2="&#xad;" k="80" />
    <hkern u1="&#xc0;" u2="&#xab;" k="80" />
    <hkern u1="&#xc0;" u2="&#xaa;" k="200" />
    <hkern u1="&#xc0;" u2="y" k="100" />
    <hkern u1="&#xc0;" u2="v" k="100" />
    <hkern u1="&#xc0;" u2="\" k="200" />
    <hkern u1="&#xc0;" u2="Y" k="200" />
    <hkern u1="&#xc0;" u2="W" k="120" />
    <hkern u1="&#xc0;" u2="V" k="200" />
    <hkern u1="&#xc0;" u2="U" k="50" />
    <hkern u1="&#xc0;" u2="T" k="160" />
    <hkern u1="&#xc0;" u2="Q" k="60" />
    <hkern u1="&#xc0;" u2="O" k="60" />
    <hkern u1="&#xc0;" u2="J" k="-60" />
    <hkern u1="&#xc0;" u2="G" k="60" />
    <hkern u1="&#xc0;" u2="C" k="60" />
    <hkern u1="&#xc0;" u2="&#x40;" k="60" />
    <hkern u1="&#xc0;" u2="&#x3f;" k="70" />
    <hkern u1="&#xc0;" u2="&#x2d;" k="80" />
    <hkern u1="&#xc0;" u2="&#x2a;" k="200" />
    <hkern u1="&#xc0;" u2="&#x27;" k="200" />
    <hkern u1="&#xc0;" u2="&#x22;" k="200" />
    <hkern u1="&#xc1;" u2="&#x2122;" k="200" />
    <hkern u1="&#xc1;" u2="&#x203a;" k="80" />
    <hkern u1="&#xc1;" u2="&#x2039;" k="80" />
    <hkern u1="&#xc1;" u2="&#x2022;" k="80" />
    <hkern u1="&#xc1;" u2="&#x201d;" k="200" />
    <hkern u1="&#xc1;" u2="&#x201c;" k="200" />
    <hkern u1="&#xc1;" u2="&#x2019;" k="200" />
    <hkern u1="&#xc1;" u2="&#x2018;" k="200" />
    <hkern u1="&#xc1;" u2="&#x2014;" k="80" />
    <hkern u1="&#xc1;" u2="&#x2013;" k="80" />
    <hkern u1="&#xc1;" u2="&#x178;" k="200" />
    <hkern u1="&#xc1;" u2="&#x152;" k="60" />
    <hkern u1="&#xc1;" u2="&#x106;" k="60" />
    <hkern u1="&#xc1;" u2="&#xdd;" k="200" />
    <hkern u1="&#xc1;" u2="&#xdc;" k="50" />
    <hkern u1="&#xc1;" u2="&#xdb;" k="50" />
    <hkern u1="&#xc1;" u2="&#xda;" k="50" />
    <hkern u1="&#xc1;" u2="&#xd9;" k="50" />
    <hkern u1="&#xc1;" u2="&#xd8;" k="60" />
    <hkern u1="&#xc1;" u2="&#xd6;" k="60" />
    <hkern u1="&#xc1;" u2="&#xd5;" k="60" />
    <hkern u1="&#xc1;" u2="&#xd4;" k="60" />
    <hkern u1="&#xc1;" u2="&#xd3;" k="60" />
    <hkern u1="&#xc1;" u2="&#xd2;" k="60" />
    <hkern u1="&#xc1;" u2="&#xc7;" k="60" />
    <hkern u1="&#xc1;" u2="&#xbb;" k="80" />
    <hkern u1="&#xc1;" u2="&#xba;" k="200" />
    <hkern u1="&#xc1;" u2="&#xb9;" k="220" />
    <hkern u1="&#xc1;" u2="&#xb7;" k="80" />
    <hkern u1="&#xc1;" u2="&#xb3;" k="220" />
    <hkern u1="&#xc1;" u2="&#xb2;" k="220" />
    <hkern u1="&#xc1;" u2="&#xb0;" k="200" />
    <hkern u1="&#xc1;" u2="&#xad;" k="80" />
    <hkern u1="&#xc1;" u2="&#xab;" k="80" />
    <hkern u1="&#xc1;" u2="&#xaa;" k="200" />
    <hkern u1="&#xc1;" u2="y" k="100" />
    <hkern u1="&#xc1;" u2="v" k="100" />
    <hkern u1="&#xc1;" u2="\" k="200" />
    <hkern u1="&#xc1;" u2="Y" k="200" />
    <hkern u1="&#xc1;" u2="W" k="120" />
    <hkern u1="&#xc1;" u2="V" k="200" />
    <hkern u1="&#xc1;" u2="U" k="50" />
    <hkern u1="&#xc1;" u2="T" k="160" />
    <hkern u1="&#xc1;" u2="Q" k="60" />
    <hkern u1="&#xc1;" u2="O" k="60" />
    <hkern u1="&#xc1;" u2="J" k="-60" />
    <hkern u1="&#xc1;" u2="G" k="60" />
    <hkern u1="&#xc1;" u2="C" k="60" />
    <hkern u1="&#xc1;" u2="&#x40;" k="60" />
    <hkern u1="&#xc1;" u2="&#x3f;" k="70" />
    <hkern u1="&#xc1;" u2="&#x2d;" k="80" />
    <hkern u1="&#xc1;" u2="&#x2a;" k="200" />
    <hkern u1="&#xc1;" u2="&#x27;" k="200" />
    <hkern u1="&#xc1;" u2="&#x22;" k="200" />
    <hkern u1="&#xc2;" u2="&#x2122;" k="200" />
    <hkern u1="&#xc2;" u2="&#x203a;" k="80" />
    <hkern u1="&#xc2;" u2="&#x2039;" k="80" />
    <hkern u1="&#xc2;" u2="&#x2022;" k="80" />
    <hkern u1="&#xc2;" u2="&#x201d;" k="200" />
    <hkern u1="&#xc2;" u2="&#x201c;" k="200" />
    <hkern u1="&#xc2;" u2="&#x2019;" k="200" />
    <hkern u1="&#xc2;" u2="&#x2018;" k="200" />
    <hkern u1="&#xc2;" u2="&#x2014;" k="80" />
    <hkern u1="&#xc2;" u2="&#x2013;" k="80" />
    <hkern u1="&#xc2;" u2="&#x178;" k="200" />
    <hkern u1="&#xc2;" u2="&#x152;" k="60" />
    <hkern u1="&#xc2;" u2="&#x106;" k="60" />
    <hkern u1="&#xc2;" u2="&#xdd;" k="200" />
    <hkern u1="&#xc2;" u2="&#xdc;" k="50" />
    <hkern u1="&#xc2;" u2="&#xdb;" k="50" />
    <hkern u1="&#xc2;" u2="&#xda;" k="50" />
    <hkern u1="&#xc2;" u2="&#xd9;" k="50" />
    <hkern u1="&#xc2;" u2="&#xd8;" k="60" />
    <hkern u1="&#xc2;" u2="&#xd6;" k="60" />
    <hkern u1="&#xc2;" u2="&#xd5;" k="60" />
    <hkern u1="&#xc2;" u2="&#xd4;" k="60" />
    <hkern u1="&#xc2;" u2="&#xd3;" k="60" />
    <hkern u1="&#xc2;" u2="&#xd2;" k="60" />
    <hkern u1="&#xc2;" u2="&#xc7;" k="60" />
    <hkern u1="&#xc2;" u2="&#xbb;" k="80" />
    <hkern u1="&#xc2;" u2="&#xba;" k="200" />
    <hkern u1="&#xc2;" u2="&#xb9;" k="220" />
    <hkern u1="&#xc2;" u2="&#xb7;" k="80" />
    <hkern u1="&#xc2;" u2="&#xb3;" k="220" />
    <hkern u1="&#xc2;" u2="&#xb2;" k="220" />
    <hkern u1="&#xc2;" u2="&#xb0;" k="200" />
    <hkern u1="&#xc2;" u2="&#xad;" k="80" />
    <hkern u1="&#xc2;" u2="&#xab;" k="80" />
    <hkern u1="&#xc2;" u2="&#xaa;" k="200" />
    <hkern u1="&#xc2;" u2="y" k="100" />
    <hkern u1="&#xc2;" u2="v" k="100" />
    <hkern u1="&#xc2;" u2="\" k="200" />
    <hkern u1="&#xc2;" u2="Y" k="200" />
    <hkern u1="&#xc2;" u2="W" k="120" />
    <hkern u1="&#xc2;" u2="V" k="200" />
    <hkern u1="&#xc2;" u2="U" k="50" />
    <hkern u1="&#xc2;" u2="T" k="160" />
    <hkern u1="&#xc2;" u2="Q" k="60" />
    <hkern u1="&#xc2;" u2="O" k="60" />
    <hkern u1="&#xc2;" u2="J" k="-60" />
    <hkern u1="&#xc2;" u2="G" k="60" />
    <hkern u1="&#xc2;" u2="C" k="60" />
    <hkern u1="&#xc2;" u2="&#x40;" k="60" />
    <hkern u1="&#xc2;" u2="&#x3f;" k="70" />
    <hkern u1="&#xc2;" u2="&#x2d;" k="80" />
    <hkern u1="&#xc2;" u2="&#x2a;" k="200" />
    <hkern u1="&#xc2;" u2="&#x27;" k="200" />
    <hkern u1="&#xc2;" u2="&#x22;" k="200" />
    <hkern u1="&#xc3;" u2="&#x2122;" k="200" />
    <hkern u1="&#xc3;" u2="&#x203a;" k="80" />
    <hkern u1="&#xc3;" u2="&#x2039;" k="80" />
    <hkern u1="&#xc3;" u2="&#x2022;" k="80" />
    <hkern u1="&#xc3;" u2="&#x201d;" k="200" />
    <hkern u1="&#xc3;" u2="&#x201c;" k="200" />
    <hkern u1="&#xc3;" u2="&#x2019;" k="200" />
    <hkern u1="&#xc3;" u2="&#x2018;" k="200" />
    <hkern u1="&#xc3;" u2="&#x2014;" k="80" />
    <hkern u1="&#xc3;" u2="&#x2013;" k="80" />
    <hkern u1="&#xc3;" u2="&#x178;" k="200" />
    <hkern u1="&#xc3;" u2="&#x152;" k="60" />
    <hkern u1="&#xc3;" u2="&#x106;" k="60" />
    <hkern u1="&#xc3;" u2="&#xdd;" k="200" />
    <hkern u1="&#xc3;" u2="&#xdc;" k="50" />
    <hkern u1="&#xc3;" u2="&#xdb;" k="50" />
    <hkern u1="&#xc3;" u2="&#xda;" k="50" />
    <hkern u1="&#xc3;" u2="&#xd9;" k="50" />
    <hkern u1="&#xc3;" u2="&#xd8;" k="60" />
    <hkern u1="&#xc3;" u2="&#xd6;" k="60" />
    <hkern u1="&#xc3;" u2="&#xd5;" k="60" />
    <hkern u1="&#xc3;" u2="&#xd4;" k="60" />
    <hkern u1="&#xc3;" u2="&#xd3;" k="60" />
    <hkern u1="&#xc3;" u2="&#xd2;" k="60" />
    <hkern u1="&#xc3;" u2="&#xc7;" k="60" />
    <hkern u1="&#xc3;" u2="&#xbb;" k="80" />
    <hkern u1="&#xc3;" u2="&#xba;" k="200" />
    <hkern u1="&#xc3;" u2="&#xb9;" k="220" />
    <hkern u1="&#xc3;" u2="&#xb7;" k="80" />
    <hkern u1="&#xc3;" u2="&#xb3;" k="220" />
    <hkern u1="&#xc3;" u2="&#xb2;" k="220" />
    <hkern u1="&#xc3;" u2="&#xb0;" k="200" />
    <hkern u1="&#xc3;" u2="&#xad;" k="80" />
    <hkern u1="&#xc3;" u2="&#xab;" k="80" />
    <hkern u1="&#xc3;" u2="&#xaa;" k="200" />
    <hkern u1="&#xc3;" u2="y" k="100" />
    <hkern u1="&#xc3;" u2="v" k="100" />
    <hkern u1="&#xc3;" u2="\" k="200" />
    <hkern u1="&#xc3;" u2="Y" k="200" />
    <hkern u1="&#xc3;" u2="W" k="120" />
    <hkern u1="&#xc3;" u2="V" k="200" />
    <hkern u1="&#xc3;" u2="U" k="50" />
    <hkern u1="&#xc3;" u2="T" k="160" />
    <hkern u1="&#xc3;" u2="Q" k="60" />
    <hkern u1="&#xc3;" u2="O" k="60" />
    <hkern u1="&#xc3;" u2="J" k="-60" />
    <hkern u1="&#xc3;" u2="G" k="60" />
    <hkern u1="&#xc3;" u2="C" k="60" />
    <hkern u1="&#xc3;" u2="&#x40;" k="60" />
    <hkern u1="&#xc3;" u2="&#x3f;" k="70" />
    <hkern u1="&#xc3;" u2="&#x2d;" k="80" />
    <hkern u1="&#xc3;" u2="&#x2a;" k="200" />
    <hkern u1="&#xc3;" u2="&#x27;" k="200" />
    <hkern u1="&#xc3;" u2="&#x22;" k="200" />
    <hkern u1="&#xc4;" u2="&#x2122;" k="200" />
    <hkern u1="&#xc4;" u2="&#x203a;" k="80" />
    <hkern u1="&#xc4;" u2="&#x2039;" k="80" />
    <hkern u1="&#xc4;" u2="&#x2022;" k="80" />
    <hkern u1="&#xc4;" u2="&#x201d;" k="200" />
    <hkern u1="&#xc4;" u2="&#x201c;" k="200" />
    <hkern u1="&#xc4;" u2="&#x2019;" k="200" />
    <hkern u1="&#xc4;" u2="&#x2018;" k="200" />
    <hkern u1="&#xc4;" u2="&#x2014;" k="80" />
    <hkern u1="&#xc4;" u2="&#x2013;" k="80" />
    <hkern u1="&#xc4;" u2="&#x178;" k="200" />
    <hkern u1="&#xc4;" u2="&#x152;" k="60" />
    <hkern u1="&#xc4;" u2="&#x106;" k="60" />
    <hkern u1="&#xc4;" u2="&#xdd;" k="200" />
    <hkern u1="&#xc4;" u2="&#xdc;" k="50" />
    <hkern u1="&#xc4;" u2="&#xdb;" k="50" />
    <hkern u1="&#xc4;" u2="&#xda;" k="50" />
    <hkern u1="&#xc4;" u2="&#xd9;" k="50" />
    <hkern u1="&#xc4;" u2="&#xd8;" k="60" />
    <hkern u1="&#xc4;" u2="&#xd6;" k="60" />
    <hkern u1="&#xc4;" u2="&#xd5;" k="60" />
    <hkern u1="&#xc4;" u2="&#xd4;" k="60" />
    <hkern u1="&#xc4;" u2="&#xd3;" k="60" />
    <hkern u1="&#xc4;" u2="&#xd2;" k="60" />
    <hkern u1="&#xc4;" u2="&#xc7;" k="60" />
    <hkern u1="&#xc4;" u2="&#xbb;" k="80" />
    <hkern u1="&#xc4;" u2="&#xba;" k="200" />
    <hkern u1="&#xc4;" u2="&#xb9;" k="220" />
    <hkern u1="&#xc4;" u2="&#xb7;" k="80" />
    <hkern u1="&#xc4;" u2="&#xb3;" k="220" />
    <hkern u1="&#xc4;" u2="&#xb2;" k="220" />
    <hkern u1="&#xc4;" u2="&#xb0;" k="200" />
    <hkern u1="&#xc4;" u2="&#xad;" k="80" />
    <hkern u1="&#xc4;" u2="&#xab;" k="80" />
    <hkern u1="&#xc4;" u2="&#xaa;" k="200" />
    <hkern u1="&#xc4;" u2="y" k="100" />
    <hkern u1="&#xc4;" u2="v" k="100" />
    <hkern u1="&#xc4;" u2="\" k="200" />
    <hkern u1="&#xc4;" u2="Y" k="200" />
    <hkern u1="&#xc4;" u2="W" k="120" />
    <hkern u1="&#xc4;" u2="V" k="200" />
    <hkern u1="&#xc4;" u2="U" k="50" />
    <hkern u1="&#xc4;" u2="T" k="160" />
    <hkern u1="&#xc4;" u2="Q" k="60" />
    <hkern u1="&#xc4;" u2="O" k="60" />
    <hkern u1="&#xc4;" u2="J" k="-60" />
    <hkern u1="&#xc4;" u2="G" k="60" />
    <hkern u1="&#xc4;" u2="C" k="60" />
    <hkern u1="&#xc4;" u2="&#x40;" k="60" />
    <hkern u1="&#xc4;" u2="&#x3f;" k="70" />
    <hkern u1="&#xc4;" u2="&#x2d;" k="80" />
    <hkern u1="&#xc4;" u2="&#x2a;" k="200" />
    <hkern u1="&#xc4;" u2="&#x27;" k="200" />
    <hkern u1="&#xc4;" u2="&#x22;" k="200" />
    <hkern u1="&#xc5;" u2="&#x2122;" k="200" />
    <hkern u1="&#xc5;" u2="&#x203a;" k="80" />
    <hkern u1="&#xc5;" u2="&#x2039;" k="80" />
    <hkern u1="&#xc5;" u2="&#x2022;" k="80" />
    <hkern u1="&#xc5;" u2="&#x201d;" k="200" />
    <hkern u1="&#xc5;" u2="&#x201c;" k="200" />
    <hkern u1="&#xc5;" u2="&#x2019;" k="200" />
    <hkern u1="&#xc5;" u2="&#x2018;" k="200" />
    <hkern u1="&#xc5;" u2="&#x2014;" k="80" />
    <hkern u1="&#xc5;" u2="&#x2013;" k="80" />
    <hkern u1="&#xc5;" u2="&#x178;" k="200" />
    <hkern u1="&#xc5;" u2="&#x152;" k="60" />
    <hkern u1="&#xc5;" u2="&#x106;" k="60" />
    <hkern u1="&#xc5;" u2="&#xdd;" k="200" />
    <hkern u1="&#xc5;" u2="&#xdc;" k="50" />
    <hkern u1="&#xc5;" u2="&#xdb;" k="50" />
    <hkern u1="&#xc5;" u2="&#xda;" k="50" />
    <hkern u1="&#xc5;" u2="&#xd9;" k="50" />
    <hkern u1="&#xc5;" u2="&#xd8;" k="60" />
    <hkern u1="&#xc5;" u2="&#xd6;" k="60" />
    <hkern u1="&#xc5;" u2="&#xd5;" k="60" />
    <hkern u1="&#xc5;" u2="&#xd4;" k="60" />
    <hkern u1="&#xc5;" u2="&#xd3;" k="60" />
    <hkern u1="&#xc5;" u2="&#xd2;" k="60" />
    <hkern u1="&#xc5;" u2="&#xc7;" k="60" />
    <hkern u1="&#xc5;" u2="&#xbb;" k="80" />
    <hkern u1="&#xc5;" u2="&#xba;" k="200" />
    <hkern u1="&#xc5;" u2="&#xb9;" k="220" />
    <hkern u1="&#xc5;" u2="&#xb7;" k="80" />
    <hkern u1="&#xc5;" u2="&#xb3;" k="220" />
    <hkern u1="&#xc5;" u2="&#xb2;" k="220" />
    <hkern u1="&#xc5;" u2="&#xb0;" k="200" />
    <hkern u1="&#xc5;" u2="&#xad;" k="80" />
    <hkern u1="&#xc5;" u2="&#xab;" k="80" />
    <hkern u1="&#xc5;" u2="&#xaa;" k="200" />
    <hkern u1="&#xc5;" u2="y" k="100" />
    <hkern u1="&#xc5;" u2="v" k="100" />
    <hkern u1="&#xc5;" u2="\" k="200" />
    <hkern u1="&#xc5;" u2="Y" k="200" />
    <hkern u1="&#xc5;" u2="W" k="120" />
    <hkern u1="&#xc5;" u2="V" k="200" />
    <hkern u1="&#xc5;" u2="U" k="50" />
    <hkern u1="&#xc5;" u2="T" k="160" />
    <hkern u1="&#xc5;" u2="Q" k="60" />
    <hkern u1="&#xc5;" u2="O" k="60" />
    <hkern u1="&#xc5;" u2="J" k="-60" />
    <hkern u1="&#xc5;" u2="G" k="60" />
    <hkern u1="&#xc5;" u2="C" k="60" />
    <hkern u1="&#xc5;" u2="&#x40;" k="60" />
    <hkern u1="&#xc5;" u2="&#x3f;" k="70" />
    <hkern u1="&#xc5;" u2="&#x2d;" k="80" />
    <hkern u1="&#xc5;" u2="&#x2a;" k="200" />
    <hkern u1="&#xc5;" u2="&#x27;" k="200" />
    <hkern u1="&#xc5;" u2="&#x22;" k="200" />
    <hkern u1="&#xc7;" u2="&#x203a;" k="140" />
    <hkern u1="&#xc7;" u2="&#x2039;" k="140" />
    <hkern u1="&#xc7;" u2="&#x2022;" k="140" />
    <hkern u1="&#xc7;" u2="&#x2014;" k="140" />
    <hkern u1="&#xc7;" u2="&#x2013;" k="140" />
    <hkern u1="&#xc7;" u2="&#xbb;" k="140" />
    <hkern u1="&#xc7;" u2="&#xb7;" k="140" />
    <hkern u1="&#xc7;" u2="&#xad;" k="140" />
    <hkern u1="&#xc7;" u2="&#xab;" k="140" />
    <hkern u1="&#xc7;" u2="&#x2d;" k="140" />
    <hkern u1="&#xd0;" u2="&#x2206;" k="60" />
    <hkern u1="&#xd0;" u2="&#x2122;" k="40" />
    <hkern u1="&#xd0;" u2="&#x201e;" k="50" />
    <hkern u1="&#xd0;" u2="&#x201d;" k="40" />
    <hkern u1="&#xd0;" u2="&#x201c;" k="40" />
    <hkern u1="&#xd0;" u2="&#x201a;" k="50" />
    <hkern u1="&#xd0;" u2="&#x2019;" k="40" />
    <hkern u1="&#xd0;" u2="&#x2018;" k="40" />
    <hkern u1="&#xd0;" u2="&#x17d;" k="60" />
    <hkern u1="&#xd0;" u2="&#x17b;" k="60" />
    <hkern u1="&#xd0;" u2="&#x179;" k="60" />
    <hkern u1="&#xd0;" u2="&#x178;" k="80" />
    <hkern u1="&#xd0;" u2="&#x104;" k="60" />
    <hkern u1="&#xd0;" u2="&#xdd;" k="80" />
    <hkern u1="&#xd0;" u2="&#xc6;" k="60" />
    <hkern u1="&#xd0;" u2="&#xc5;" k="60" />
    <hkern u1="&#xd0;" u2="&#xc4;" k="60" />
    <hkern u1="&#xd0;" u2="&#xc3;" k="60" />
    <hkern u1="&#xd0;" u2="&#xc2;" k="60" />
    <hkern u1="&#xd0;" u2="&#xc1;" k="60" />
    <hkern u1="&#xd0;" u2="&#xc0;" k="60" />
    <hkern u1="&#xd0;" u2="&#xba;" k="40" />
    <hkern u1="&#xd0;" u2="&#xb0;" k="40" />
    <hkern u1="&#xd0;" u2="&#xaa;" k="40" />
    <hkern u1="&#xd0;" u2="&#x7d;" k="40" />
    <hkern u1="&#xd0;" u2="]" k="40" />
    <hkern u1="&#xd0;" u2="\" k="60" />
    <hkern u1="&#xd0;" u2="Z" k="60" />
    <hkern u1="&#xd0;" u2="Y" k="80" />
    <hkern u1="&#xd0;" u2="X" k="30" />
    <hkern u1="&#xd0;" u2="V" k="60" />
    <hkern u1="&#xd0;" u2="T" k="60" />
    <hkern u1="&#xd0;" u2="A" k="60" />
    <hkern u1="&#xd0;" u2="&#x2f;" k="60" />
    <hkern u1="&#xd0;" u2="&#x2e;" k="50" />
    <hkern u1="&#xd0;" u2="&#x2c;" k="50" />
    <hkern u1="&#xd0;" u2="&#x2a;" k="40" />
    <hkern u1="&#xd0;" u2="&#x29;" k="40" />
    <hkern u1="&#xd0;" u2="&#x27;" k="40" />
    <hkern u1="&#xd0;" u2="&#x26;" k="60" />
    <hkern u1="&#xd0;" u2="&#x22;" k="40" />
    <hkern u1="&#xd2;" u2="&#x2206;" k="60" />
    <hkern u1="&#xd2;" u2="&#x2122;" k="40" />
    <hkern u1="&#xd2;" u2="&#x201e;" k="50" />
    <hkern u1="&#xd2;" u2="&#x201d;" k="40" />
    <hkern u1="&#xd2;" u2="&#x201c;" k="40" />
    <hkern u1="&#xd2;" u2="&#x201a;" k="50" />
    <hkern u1="&#xd2;" u2="&#x2019;" k="40" />
    <hkern u1="&#xd2;" u2="&#x2018;" k="40" />
    <hkern u1="&#xd2;" u2="&#x17d;" k="60" />
    <hkern u1="&#xd2;" u2="&#x17b;" k="60" />
    <hkern u1="&#xd2;" u2="&#x179;" k="60" />
    <hkern u1="&#xd2;" u2="&#x178;" k="80" />
    <hkern u1="&#xd2;" u2="&#x104;" k="60" />
    <hkern u1="&#xd2;" u2="&#xdd;" k="80" />
    <hkern u1="&#xd2;" u2="&#xc6;" k="60" />
    <hkern u1="&#xd2;" u2="&#xc5;" k="60" />
    <hkern u1="&#xd2;" u2="&#xc4;" k="60" />
    <hkern u1="&#xd2;" u2="&#xc3;" k="60" />
    <hkern u1="&#xd2;" u2="&#xc2;" k="60" />
    <hkern u1="&#xd2;" u2="&#xc1;" k="60" />
    <hkern u1="&#xd2;" u2="&#xc0;" k="60" />
    <hkern u1="&#xd2;" u2="&#xba;" k="40" />
    <hkern u1="&#xd2;" u2="&#xb0;" k="40" />
    <hkern u1="&#xd2;" u2="&#xaa;" k="40" />
    <hkern u1="&#xd2;" u2="&#x7d;" k="40" />
    <hkern u1="&#xd2;" u2="]" k="40" />
    <hkern u1="&#xd2;" u2="\" k="60" />
    <hkern u1="&#xd2;" u2="Z" k="60" />
    <hkern u1="&#xd2;" u2="Y" k="80" />
    <hkern u1="&#xd2;" u2="X" k="30" />
    <hkern u1="&#xd2;" u2="V" k="60" />
    <hkern u1="&#xd2;" u2="T" k="60" />
    <hkern u1="&#xd2;" u2="A" k="60" />
    <hkern u1="&#xd2;" u2="&#x2f;" k="60" />
    <hkern u1="&#xd2;" u2="&#x2e;" k="50" />
    <hkern u1="&#xd2;" u2="&#x2c;" k="50" />
    <hkern u1="&#xd2;" u2="&#x2a;" k="40" />
    <hkern u1="&#xd2;" u2="&#x29;" k="40" />
    <hkern u1="&#xd2;" u2="&#x27;" k="40" />
    <hkern u1="&#xd2;" u2="&#x26;" k="60" />
    <hkern u1="&#xd2;" u2="&#x22;" k="40" />
    <hkern u1="&#xd3;" u2="&#x2206;" k="60" />
    <hkern u1="&#xd3;" u2="&#x2122;" k="40" />
    <hkern u1="&#xd3;" u2="&#x201e;" k="50" />
    <hkern u1="&#xd3;" u2="&#x201d;" k="40" />
    <hkern u1="&#xd3;" u2="&#x201c;" k="40" />
    <hkern u1="&#xd3;" u2="&#x201a;" k="50" />
    <hkern u1="&#xd3;" u2="&#x2019;" k="40" />
    <hkern u1="&#xd3;" u2="&#x2018;" k="40" />
    <hkern u1="&#xd3;" u2="&#x17d;" k="60" />
    <hkern u1="&#xd3;" u2="&#x17b;" k="60" />
    <hkern u1="&#xd3;" u2="&#x179;" k="60" />
    <hkern u1="&#xd3;" u2="&#x178;" k="80" />
    <hkern u1="&#xd3;" u2="&#x104;" k="60" />
    <hkern u1="&#xd3;" u2="&#xdd;" k="80" />
    <hkern u1="&#xd3;" u2="&#xc6;" k="60" />
    <hkern u1="&#xd3;" u2="&#xc5;" k="60" />
    <hkern u1="&#xd3;" u2="&#xc4;" k="60" />
    <hkern u1="&#xd3;" u2="&#xc3;" k="60" />
    <hkern u1="&#xd3;" u2="&#xc2;" k="60" />
    <hkern u1="&#xd3;" u2="&#xc1;" k="60" />
    <hkern u1="&#xd3;" u2="&#xc0;" k="60" />
    <hkern u1="&#xd3;" u2="&#xba;" k="40" />
    <hkern u1="&#xd3;" u2="&#xb0;" k="40" />
    <hkern u1="&#xd3;" u2="&#xaa;" k="40" />
    <hkern u1="&#xd3;" u2="&#x7d;" k="40" />
    <hkern u1="&#xd3;" u2="]" k="40" />
    <hkern u1="&#xd3;" u2="\" k="60" />
    <hkern u1="&#xd3;" u2="Z" k="60" />
    <hkern u1="&#xd3;" u2="Y" k="80" />
    <hkern u1="&#xd3;" u2="X" k="30" />
    <hkern u1="&#xd3;" u2="V" k="60" />
    <hkern u1="&#xd3;" u2="T" k="60" />
    <hkern u1="&#xd3;" u2="A" k="60" />
    <hkern u1="&#xd3;" u2="&#x2f;" k="60" />
    <hkern u1="&#xd3;" u2="&#x2e;" k="50" />
    <hkern u1="&#xd3;" u2="&#x2c;" k="50" />
    <hkern u1="&#xd3;" u2="&#x2a;" k="40" />
    <hkern u1="&#xd3;" u2="&#x29;" k="40" />
    <hkern u1="&#xd3;" u2="&#x27;" k="40" />
    <hkern u1="&#xd3;" u2="&#x26;" k="60" />
    <hkern u1="&#xd3;" u2="&#x22;" k="40" />
    <hkern u1="&#xd4;" u2="&#x2206;" k="60" />
    <hkern u1="&#xd4;" u2="&#x2122;" k="40" />
    <hkern u1="&#xd4;" u2="&#x201e;" k="50" />
    <hkern u1="&#xd4;" u2="&#x201d;" k="40" />
    <hkern u1="&#xd4;" u2="&#x201c;" k="40" />
    <hkern u1="&#xd4;" u2="&#x201a;" k="50" />
    <hkern u1="&#xd4;" u2="&#x2019;" k="40" />
    <hkern u1="&#xd4;" u2="&#x2018;" k="40" />
    <hkern u1="&#xd4;" u2="&#x17d;" k="60" />
    <hkern u1="&#xd4;" u2="&#x17b;" k="60" />
    <hkern u1="&#xd4;" u2="&#x179;" k="60" />
    <hkern u1="&#xd4;" u2="&#x178;" k="80" />
    <hkern u1="&#xd4;" u2="&#x104;" k="60" />
    <hkern u1="&#xd4;" u2="&#xdd;" k="80" />
    <hkern u1="&#xd4;" u2="&#xc6;" k="60" />
    <hkern u1="&#xd4;" u2="&#xc5;" k="60" />
    <hkern u1="&#xd4;" u2="&#xc4;" k="60" />
    <hkern u1="&#xd4;" u2="&#xc3;" k="60" />
    <hkern u1="&#xd4;" u2="&#xc2;" k="60" />
    <hkern u1="&#xd4;" u2="&#xc1;" k="60" />
    <hkern u1="&#xd4;" u2="&#xc0;" k="60" />
    <hkern u1="&#xd4;" u2="&#xba;" k="40" />
    <hkern u1="&#xd4;" u2="&#xb0;" k="40" />
    <hkern u1="&#xd4;" u2="&#xaa;" k="40" />
    <hkern u1="&#xd4;" u2="&#x7d;" k="40" />
    <hkern u1="&#xd4;" u2="]" k="40" />
    <hkern u1="&#xd4;" u2="\" k="60" />
    <hkern u1="&#xd4;" u2="Z" k="60" />
    <hkern u1="&#xd4;" u2="Y" k="80" />
    <hkern u1="&#xd4;" u2="X" k="30" />
    <hkern u1="&#xd4;" u2="V" k="60" />
    <hkern u1="&#xd4;" u2="T" k="60" />
    <hkern u1="&#xd4;" u2="A" k="60" />
    <hkern u1="&#xd4;" u2="&#x2f;" k="60" />
    <hkern u1="&#xd4;" u2="&#x2e;" k="50" />
    <hkern u1="&#xd4;" u2="&#x2c;" k="50" />
    <hkern u1="&#xd4;" u2="&#x2a;" k="40" />
    <hkern u1="&#xd4;" u2="&#x29;" k="40" />
    <hkern u1="&#xd4;" u2="&#x27;" k="40" />
    <hkern u1="&#xd4;" u2="&#x26;" k="60" />
    <hkern u1="&#xd4;" u2="&#x22;" k="40" />
    <hkern u1="&#xd5;" u2="&#x2206;" k="60" />
    <hkern u1="&#xd5;" u2="&#x2122;" k="40" />
    <hkern u1="&#xd5;" u2="&#x201e;" k="50" />
    <hkern u1="&#xd5;" u2="&#x201d;" k="40" />
    <hkern u1="&#xd5;" u2="&#x201c;" k="40" />
    <hkern u1="&#xd5;" u2="&#x201a;" k="50" />
    <hkern u1="&#xd5;" u2="&#x2019;" k="40" />
    <hkern u1="&#xd5;" u2="&#x2018;" k="40" />
    <hkern u1="&#xd5;" u2="&#x17d;" k="60" />
    <hkern u1="&#xd5;" u2="&#x17b;" k="60" />
    <hkern u1="&#xd5;" u2="&#x179;" k="60" />
    <hkern u1="&#xd5;" u2="&#x178;" k="80" />
    <hkern u1="&#xd5;" u2="&#x104;" k="60" />
    <hkern u1="&#xd5;" u2="&#xdd;" k="80" />
    <hkern u1="&#xd5;" u2="&#xc6;" k="60" />
    <hkern u1="&#xd5;" u2="&#xc5;" k="60" />
    <hkern u1="&#xd5;" u2="&#xc4;" k="60" />
    <hkern u1="&#xd5;" u2="&#xc3;" k="60" />
    <hkern u1="&#xd5;" u2="&#xc2;" k="60" />
    <hkern u1="&#xd5;" u2="&#xc1;" k="60" />
    <hkern u1="&#xd5;" u2="&#xc0;" k="60" />
    <hkern u1="&#xd5;" u2="&#xba;" k="40" />
    <hkern u1="&#xd5;" u2="&#xb0;" k="40" />
    <hkern u1="&#xd5;" u2="&#xaa;" k="40" />
    <hkern u1="&#xd5;" u2="&#x7d;" k="40" />
    <hkern u1="&#xd5;" u2="]" k="40" />
    <hkern u1="&#xd5;" u2="\" k="60" />
    <hkern u1="&#xd5;" u2="Z" k="60" />
    <hkern u1="&#xd5;" u2="Y" k="80" />
    <hkern u1="&#xd5;" u2="X" k="30" />
    <hkern u1="&#xd5;" u2="V" k="60" />
    <hkern u1="&#xd5;" u2="T" k="60" />
    <hkern u1="&#xd5;" u2="A" k="60" />
    <hkern u1="&#xd5;" u2="&#x2f;" k="60" />
    <hkern u1="&#xd5;" u2="&#x2e;" k="50" />
    <hkern u1="&#xd5;" u2="&#x2c;" k="50" />
    <hkern u1="&#xd5;" u2="&#x2a;" k="40" />
    <hkern u1="&#xd5;" u2="&#x29;" k="40" />
    <hkern u1="&#xd5;" u2="&#x27;" k="40" />
    <hkern u1="&#xd5;" u2="&#x26;" k="60" />
    <hkern u1="&#xd5;" u2="&#x22;" k="40" />
    <hkern u1="&#xd6;" u2="&#x2206;" k="60" />
    <hkern u1="&#xd6;" u2="&#x2122;" k="40" />
    <hkern u1="&#xd6;" u2="&#x201e;" k="50" />
    <hkern u1="&#xd6;" u2="&#x201d;" k="40" />
    <hkern u1="&#xd6;" u2="&#x201c;" k="40" />
    <hkern u1="&#xd6;" u2="&#x201a;" k="50" />
    <hkern u1="&#xd6;" u2="&#x2019;" k="40" />
    <hkern u1="&#xd6;" u2="&#x2018;" k="40" />
    <hkern u1="&#xd6;" u2="&#x17d;" k="60" />
    <hkern u1="&#xd6;" u2="&#x17b;" k="60" />
    <hkern u1="&#xd6;" u2="&#x179;" k="60" />
    <hkern u1="&#xd6;" u2="&#x178;" k="80" />
    <hkern u1="&#xd6;" u2="&#x104;" k="60" />
    <hkern u1="&#xd6;" u2="&#xdd;" k="80" />
    <hkern u1="&#xd6;" u2="&#xc6;" k="60" />
    <hkern u1="&#xd6;" u2="&#xc5;" k="60" />
    <hkern u1="&#xd6;" u2="&#xc4;" k="60" />
    <hkern u1="&#xd6;" u2="&#xc3;" k="60" />
    <hkern u1="&#xd6;" u2="&#xc2;" k="60" />
    <hkern u1="&#xd6;" u2="&#xc1;" k="60" />
    <hkern u1="&#xd6;" u2="&#xc0;" k="60" />
    <hkern u1="&#xd6;" u2="&#xba;" k="40" />
    <hkern u1="&#xd6;" u2="&#xb0;" k="40" />
    <hkern u1="&#xd6;" u2="&#xaa;" k="40" />
    <hkern u1="&#xd6;" u2="&#x7d;" k="40" />
    <hkern u1="&#xd6;" u2="]" k="40" />
    <hkern u1="&#xd6;" u2="\" k="60" />
    <hkern u1="&#xd6;" u2="Z" k="60" />
    <hkern u1="&#xd6;" u2="Y" k="80" />
    <hkern u1="&#xd6;" u2="X" k="30" />
    <hkern u1="&#xd6;" u2="V" k="60" />
    <hkern u1="&#xd6;" u2="T" k="60" />
    <hkern u1="&#xd6;" u2="A" k="60" />
    <hkern u1="&#xd6;" u2="&#x2f;" k="60" />
    <hkern u1="&#xd6;" u2="&#x2e;" k="50" />
    <hkern u1="&#xd6;" u2="&#x2c;" k="50" />
    <hkern u1="&#xd6;" u2="&#x2a;" k="40" />
    <hkern u1="&#xd6;" u2="&#x29;" k="40" />
    <hkern u1="&#xd6;" u2="&#x27;" k="40" />
    <hkern u1="&#xd6;" u2="&#x26;" k="60" />
    <hkern u1="&#xd6;" u2="&#x22;" k="40" />
    <hkern u1="&#xd9;" u2="&#x2206;" k="50" />
    <hkern u1="&#xd9;" u2="&#x201e;" k="50" />
    <hkern u1="&#xd9;" u2="&#x201a;" k="50" />
    <hkern u1="&#xd9;" u2="&#x104;" k="50" />
    <hkern u1="&#xd9;" u2="&#xc6;" k="50" />
    <hkern u1="&#xd9;" u2="&#xc5;" k="50" />
    <hkern u1="&#xd9;" u2="&#xc4;" k="50" />
    <hkern u1="&#xd9;" u2="&#xc3;" k="50" />
    <hkern u1="&#xd9;" u2="&#xc2;" k="50" />
    <hkern u1="&#xd9;" u2="&#xc1;" k="50" />
    <hkern u1="&#xd9;" u2="&#xc0;" k="50" />
    <hkern u1="&#xd9;" u2="A" k="50" />
    <hkern u1="&#xd9;" u2="&#x2f;" k="50" />
    <hkern u1="&#xd9;" u2="&#x2e;" k="50" />
    <hkern u1="&#xd9;" u2="&#x2c;" k="50" />
    <hkern u1="&#xd9;" u2="&#x26;" k="50" />
    <hkern u1="&#xda;" u2="&#x2206;" k="50" />
    <hkern u1="&#xda;" u2="&#x201e;" k="50" />
    <hkern u1="&#xda;" u2="&#x201a;" k="50" />
    <hkern u1="&#xda;" u2="&#x104;" k="50" />
    <hkern u1="&#xda;" u2="&#xc6;" k="50" />
    <hkern u1="&#xda;" u2="&#xc5;" k="50" />
    <hkern u1="&#xda;" u2="&#xc4;" k="50" />
    <hkern u1="&#xda;" u2="&#xc3;" k="50" />
    <hkern u1="&#xda;" u2="&#xc2;" k="50" />
    <hkern u1="&#xda;" u2="&#xc1;" k="50" />
    <hkern u1="&#xda;" u2="&#xc0;" k="50" />
    <hkern u1="&#xda;" u2="A" k="50" />
    <hkern u1="&#xda;" u2="&#x2f;" k="50" />
    <hkern u1="&#xda;" u2="&#x2e;" k="50" />
    <hkern u1="&#xda;" u2="&#x2c;" k="50" />
    <hkern u1="&#xda;" u2="&#x26;" k="50" />
    <hkern u1="&#xdb;" u2="&#x2206;" k="50" />
    <hkern u1="&#xdb;" u2="&#x201e;" k="50" />
    <hkern u1="&#xdb;" u2="&#x201a;" k="50" />
    <hkern u1="&#xdb;" u2="&#x104;" k="50" />
    <hkern u1="&#xdb;" u2="&#xc6;" k="50" />
    <hkern u1="&#xdb;" u2="&#xc5;" k="50" />
    <hkern u1="&#xdb;" u2="&#xc4;" k="50" />
    <hkern u1="&#xdb;" u2="&#xc3;" k="50" />
    <hkern u1="&#xdb;" u2="&#xc2;" k="50" />
    <hkern u1="&#xdb;" u2="&#xc1;" k="50" />
    <hkern u1="&#xdb;" u2="&#xc0;" k="50" />
    <hkern u1="&#xdb;" u2="A" k="50" />
    <hkern u1="&#xdb;" u2="&#x2f;" k="50" />
    <hkern u1="&#xdb;" u2="&#x2e;" k="50" />
    <hkern u1="&#xdb;" u2="&#x2c;" k="50" />
    <hkern u1="&#xdb;" u2="&#x26;" k="50" />
    <hkern u1="&#xdc;" u2="&#x2206;" k="50" />
    <hkern u1="&#xdc;" u2="&#x201e;" k="50" />
    <hkern u1="&#xdc;" u2="&#x201a;" k="50" />
    <hkern u1="&#xdc;" u2="&#x104;" k="50" />
    <hkern u1="&#xdc;" u2="&#xc6;" k="50" />
    <hkern u1="&#xdc;" u2="&#xc5;" k="50" />
    <hkern u1="&#xdc;" u2="&#xc4;" k="50" />
    <hkern u1="&#xdc;" u2="&#xc3;" k="50" />
    <hkern u1="&#xdc;" u2="&#xc2;" k="50" />
    <hkern u1="&#xdc;" u2="&#xc1;" k="50" />
    <hkern u1="&#xdc;" u2="&#xc0;" k="50" />
    <hkern u1="&#xdc;" u2="A" k="50" />
    <hkern u1="&#xdc;" u2="&#x2f;" k="50" />
    <hkern u1="&#xdc;" u2="&#x2e;" k="50" />
    <hkern u1="&#xdc;" u2="&#x2c;" k="50" />
    <hkern u1="&#xdc;" u2="&#x26;" k="50" />
    <hkern u1="&#xdd;" u2="&#x2206;" k="200" />
    <hkern u1="&#xdd;" u2="&#x2122;" k="-40" />
    <hkern u1="&#xdd;" u2="&#x203a;" k="160" />
    <hkern u1="&#xdd;" u2="&#x2039;" k="160" />
    <hkern u1="&#xdd;" u2="&#x2022;" k="160" />
    <hkern u1="&#xdd;" u2="&#x201e;" k="180" />
    <hkern u1="&#xdd;" u2="&#x201d;" k="-40" />
    <hkern u1="&#xdd;" u2="&#x201c;" k="-40" />
    <hkern u1="&#xdd;" u2="&#x201a;" k="180" />
    <hkern u1="&#xdd;" u2="&#x2019;" k="-40" />
    <hkern u1="&#xdd;" u2="&#x2018;" k="-40" />
    <hkern u1="&#xdd;" u2="&#x2014;" k="160" />
    <hkern u1="&#xdd;" u2="&#x2013;" k="160" />
    <hkern u1="&#xdd;" u2="&#x153;" k="160" />
    <hkern u1="&#xdd;" u2="&#x152;" k="80" />
    <hkern u1="&#xdd;" u2="&#x144;" k="140" />
    <hkern u1="&#xdd;" u2="&#x119;" k="160" />
    <hkern u1="&#xdd;" u2="&#x107;" k="160" />
    <hkern u1="&#xdd;" u2="&#x106;" k="80" />
    <hkern u1="&#xdd;" u2="&#x105;" k="160" />
    <hkern u1="&#xdd;" u2="&#x104;" k="200" />
    <hkern u1="&#xdd;" u2="&#xfc;" k="140" />
    <hkern u1="&#xdd;" u2="&#xfb;" k="140" />
    <hkern u1="&#xdd;" u2="&#xfa;" k="140" />
    <hkern u1="&#xdd;" u2="&#xf9;" k="140" />
    <hkern u1="&#xdd;" u2="&#xf8;" k="160" />
    <hkern u1="&#xdd;" u2="&#xf6;" k="160" />
    <hkern u1="&#xdd;" u2="&#xf5;" k="160" />
    <hkern u1="&#xdd;" u2="&#xf4;" k="160" />
    <hkern u1="&#xdd;" u2="&#xf3;" k="160" />
    <hkern u1="&#xdd;" u2="&#xf2;" k="160" />
    <hkern u1="&#xdd;" u2="&#xf1;" k="140" />
    <hkern u1="&#xdd;" u2="&#xf0;" k="160" />
    <hkern u1="&#xdd;" u2="&#xeb;" k="160" />
    <hkern u1="&#xdd;" u2="&#xea;" k="160" />
    <hkern u1="&#xdd;" u2="&#xe9;" k="160" />
    <hkern u1="&#xdd;" u2="&#xe8;" k="160" />
    <hkern u1="&#xdd;" u2="&#xe7;" k="160" />
    <hkern u1="&#xdd;" u2="&#xe6;" k="160" />
    <hkern u1="&#xdd;" u2="&#xe5;" k="160" />
    <hkern u1="&#xdd;" u2="&#xe4;" k="160" />
    <hkern u1="&#xdd;" u2="&#xe3;" k="160" />
    <hkern u1="&#xdd;" u2="&#xe2;" k="160" />
    <hkern u1="&#xdd;" u2="&#xe1;" k="160" />
    <hkern u1="&#xdd;" u2="&#xe0;" k="160" />
    <hkern u1="&#xdd;" u2="&#xd8;" k="80" />
    <hkern u1="&#xdd;" u2="&#xd6;" k="80" />
    <hkern u1="&#xdd;" u2="&#xd5;" k="80" />
    <hkern u1="&#xdd;" u2="&#xd4;" k="80" />
    <hkern u1="&#xdd;" u2="&#xd3;" k="80" />
    <hkern u1="&#xdd;" u2="&#xd2;" k="80" />
    <hkern u1="&#xdd;" u2="&#xc7;" k="80" />
    <hkern u1="&#xdd;" u2="&#xc6;" k="200" />
    <hkern u1="&#xdd;" u2="&#xc5;" k="200" />
    <hkern u1="&#xdd;" u2="&#xc4;" k="200" />
    <hkern u1="&#xdd;" u2="&#xc3;" k="200" />
    <hkern u1="&#xdd;" u2="&#xc2;" k="200" />
    <hkern u1="&#xdd;" u2="&#xc1;" k="200" />
    <hkern u1="&#xdd;" u2="&#xc0;" k="200" />
    <hkern u1="&#xdd;" u2="&#xbb;" k="160" />
    <hkern u1="&#xdd;" u2="&#xba;" k="-40" />
    <hkern u1="&#xdd;" u2="&#xb9;" k="-60" />
    <hkern u1="&#xdd;" u2="&#xb7;" k="160" />
    <hkern u1="&#xdd;" u2="&#xb5;" k="140" />
    <hkern u1="&#xdd;" u2="&#xb3;" k="-60" />
    <hkern u1="&#xdd;" u2="&#xb2;" k="-60" />
    <hkern u1="&#xdd;" u2="&#xb0;" k="-40" />
    <hkern u1="&#xdd;" u2="&#xad;" k="160" />
    <hkern u1="&#xdd;" u2="&#xab;" k="160" />
    <hkern u1="&#xdd;" u2="&#xaa;" k="-40" />
    <hkern u1="&#xdd;" u2="y" k="100" />
    <hkern u1="&#xdd;" u2="x" k="140" />
    <hkern u1="&#xdd;" u2="w" k="100" />
    <hkern u1="&#xdd;" u2="v" k="100" />
    <hkern u1="&#xdd;" u2="u" k="140" />
    <hkern u1="&#xdd;" u2="s" k="150" />
    <hkern u1="&#xdd;" u2="r" k="140" />
    <hkern u1="&#xdd;" u2="q" k="160" />
    <hkern u1="&#xdd;" u2="p" k="140" />
    <hkern u1="&#xdd;" u2="o" k="160" />
    <hkern u1="&#xdd;" u2="n" k="140" />
    <hkern u1="&#xdd;" u2="m" k="140" />
    <hkern u1="&#xdd;" u2="g" k="180" />
    <hkern u1="&#xdd;" u2="e" k="160" />
    <hkern u1="&#xdd;" u2="d" k="160" />
    <hkern u1="&#xdd;" u2="c" k="160" />
    <hkern u1="&#xdd;" u2="a" k="160" />
    <hkern u1="&#xdd;" u2="Q" k="80" />
    <hkern u1="&#xdd;" u2="O" k="80" />
    <hkern u1="&#xdd;" u2="J" k="200" />
    <hkern u1="&#xdd;" u2="G" k="80" />
    <hkern u1="&#xdd;" u2="C" k="80" />
    <hkern u1="&#xdd;" u2="A" k="200" />
    <hkern u1="&#xdd;" u2="&#x40;" k="80" />
    <hkern u1="&#xdd;" u2="&#x3f;" k="-30" />
    <hkern u1="&#xdd;" u2="&#x3b;" k="140" />
    <hkern u1="&#xdd;" u2="&#x3a;" k="140" />
    <hkern u1="&#xdd;" u2="&#x2f;" k="200" />
    <hkern u1="&#xdd;" u2="&#x2e;" k="180" />
    <hkern u1="&#xdd;" u2="&#x2d;" k="160" />
    <hkern u1="&#xdd;" u2="&#x2c;" k="180" />
    <hkern u1="&#xdd;" u2="&#x2a;" k="-40" />
    <hkern u1="&#xdd;" u2="&#x27;" k="-40" />
    <hkern u1="&#xdd;" u2="&#x26;" k="200" />
    <hkern u1="&#xdd;" u2="&#x22;" k="-40" />
    <hkern u1="&#xde;" u2="&#x2206;" k="60" />
    <hkern u1="&#xde;" u2="&#x2122;" k="40" />
    <hkern u1="&#xde;" u2="&#x201e;" k="50" />
    <hkern u1="&#xde;" u2="&#x201d;" k="40" />
    <hkern u1="&#xde;" u2="&#x201c;" k="40" />
    <hkern u1="&#xde;" u2="&#x201a;" k="50" />
    <hkern u1="&#xde;" u2="&#x2019;" k="40" />
    <hkern u1="&#xde;" u2="&#x2018;" k="40" />
    <hkern u1="&#xde;" u2="&#x17d;" k="60" />
    <hkern u1="&#xde;" u2="&#x17b;" k="60" />
    <hkern u1="&#xde;" u2="&#x179;" k="60" />
    <hkern u1="&#xde;" u2="&#x178;" k="80" />
    <hkern u1="&#xde;" u2="&#x104;" k="60" />
    <hkern u1="&#xde;" u2="&#xdd;" k="80" />
    <hkern u1="&#xde;" u2="&#xc6;" k="60" />
    <hkern u1="&#xde;" u2="&#xc5;" k="60" />
    <hkern u1="&#xde;" u2="&#xc4;" k="60" />
    <hkern u1="&#xde;" u2="&#xc3;" k="60" />
    <hkern u1="&#xde;" u2="&#xc2;" k="60" />
    <hkern u1="&#xde;" u2="&#xc1;" k="60" />
    <hkern u1="&#xde;" u2="&#xc0;" k="60" />
    <hkern u1="&#xde;" u2="&#xba;" k="40" />
    <hkern u1="&#xde;" u2="&#xb0;" k="40" />
    <hkern u1="&#xde;" u2="&#xaa;" k="40" />
    <hkern u1="&#xde;" u2="&#x7d;" k="40" />
    <hkern u1="&#xde;" u2="]" k="40" />
    <hkern u1="&#xde;" u2="\" k="60" />
    <hkern u1="&#xde;" u2="Z" k="60" />
    <hkern u1="&#xde;" u2="Y" k="80" />
    <hkern u1="&#xde;" u2="X" k="30" />
    <hkern u1="&#xde;" u2="V" k="60" />
    <hkern u1="&#xde;" u2="T" k="60" />
    <hkern u1="&#xde;" u2="A" k="60" />
    <hkern u1="&#xde;" u2="&#x2f;" k="60" />
    <hkern u1="&#xde;" u2="&#x2e;" k="50" />
    <hkern u1="&#xde;" u2="&#x2c;" k="50" />
    <hkern u1="&#xde;" u2="&#x2a;" k="40" />
    <hkern u1="&#xde;" u2="&#x29;" k="40" />
    <hkern u1="&#xde;" u2="&#x27;" k="40" />
    <hkern u1="&#xde;" u2="&#x26;" k="60" />
    <hkern u1="&#xde;" u2="&#x22;" k="40" />
    <hkern u1="&#xe0;" u2="&#x2122;" k="80" />
    <hkern u1="&#xe0;" u2="&#x201d;" k="80" />
    <hkern u1="&#xe0;" u2="&#x201c;" k="80" />
    <hkern u1="&#xe0;" u2="&#x2019;" k="80" />
    <hkern u1="&#xe0;" u2="&#x2018;" k="80" />
    <hkern u1="&#xe0;" u2="&#xba;" k="80" />
    <hkern u1="&#xe0;" u2="&#xb9;" k="80" />
    <hkern u1="&#xe0;" u2="&#xb3;" k="80" />
    <hkern u1="&#xe0;" u2="&#xb2;" k="80" />
    <hkern u1="&#xe0;" u2="&#xb0;" k="80" />
    <hkern u1="&#xe0;" u2="&#xaa;" k="80" />
    <hkern u1="&#xe0;" u2="y" k="40" />
    <hkern u1="&#xe0;" u2="w" k="20" />
    <hkern u1="&#xe0;" u2="v" k="40" />
    <hkern u1="&#xe0;" u2="&#x2a;" k="80" />
    <hkern u1="&#xe0;" u2="&#x27;" k="80" />
    <hkern u1="&#xe0;" u2="&#x22;" k="80" />
    <hkern u1="&#xe1;" u2="&#x2122;" k="80" />
    <hkern u1="&#xe1;" u2="&#x201d;" k="80" />
    <hkern u1="&#xe1;" u2="&#x201c;" k="80" />
    <hkern u1="&#xe1;" u2="&#x2019;" k="80" />
    <hkern u1="&#xe1;" u2="&#x2018;" k="80" />
    <hkern u1="&#xe1;" u2="&#xba;" k="80" />
    <hkern u1="&#xe1;" u2="&#xb9;" k="80" />
    <hkern u1="&#xe1;" u2="&#xb3;" k="80" />
    <hkern u1="&#xe1;" u2="&#xb2;" k="80" />
    <hkern u1="&#xe1;" u2="&#xb0;" k="80" />
    <hkern u1="&#xe1;" u2="&#xaa;" k="80" />
    <hkern u1="&#xe1;" u2="y" k="40" />
    <hkern u1="&#xe1;" u2="w" k="20" />
    <hkern u1="&#xe1;" u2="v" k="40" />
    <hkern u1="&#xe1;" u2="&#x2a;" k="80" />
    <hkern u1="&#xe1;" u2="&#x27;" k="80" />
    <hkern u1="&#xe1;" u2="&#x22;" k="80" />
    <hkern u1="&#xe2;" u2="&#x2122;" k="80" />
    <hkern u1="&#xe2;" u2="&#x201d;" k="80" />
    <hkern u1="&#xe2;" u2="&#x201c;" k="80" />
    <hkern u1="&#xe2;" u2="&#x2019;" k="80" />
    <hkern u1="&#xe2;" u2="&#x2018;" k="80" />
    <hkern u1="&#xe2;" u2="&#xba;" k="80" />
    <hkern u1="&#xe2;" u2="&#xb9;" k="80" />
    <hkern u1="&#xe2;" u2="&#xb3;" k="80" />
    <hkern u1="&#xe2;" u2="&#xb2;" k="80" />
    <hkern u1="&#xe2;" u2="&#xb0;" k="80" />
    <hkern u1="&#xe2;" u2="&#xaa;" k="80" />
    <hkern u1="&#xe2;" u2="y" k="40" />
    <hkern u1="&#xe2;" u2="w" k="20" />
    <hkern u1="&#xe2;" u2="v" k="40" />
    <hkern u1="&#xe2;" u2="&#x2a;" k="80" />
    <hkern u1="&#xe2;" u2="&#x27;" k="80" />
    <hkern u1="&#xe2;" u2="&#x22;" k="80" />
    <hkern u1="&#xe3;" u2="&#x2122;" k="80" />
    <hkern u1="&#xe3;" u2="&#x201d;" k="80" />
    <hkern u1="&#xe3;" u2="&#x201c;" k="80" />
    <hkern u1="&#xe3;" u2="&#x2019;" k="80" />
    <hkern u1="&#xe3;" u2="&#x2018;" k="80" />
    <hkern u1="&#xe3;" u2="&#xba;" k="80" />
    <hkern u1="&#xe3;" u2="&#xb9;" k="80" />
    <hkern u1="&#xe3;" u2="&#xb3;" k="80" />
    <hkern u1="&#xe3;" u2="&#xb2;" k="80" />
    <hkern u1="&#xe3;" u2="&#xb0;" k="80" />
    <hkern u1="&#xe3;" u2="&#xaa;" k="80" />
    <hkern u1="&#xe3;" u2="y" k="40" />
    <hkern u1="&#xe3;" u2="w" k="20" />
    <hkern u1="&#xe3;" u2="v" k="40" />
    <hkern u1="&#xe3;" u2="&#x2a;" k="80" />
    <hkern u1="&#xe3;" u2="&#x27;" k="80" />
    <hkern u1="&#xe3;" u2="&#x22;" k="80" />
    <hkern u1="&#xe4;" u2="&#x2122;" k="80" />
    <hkern u1="&#xe4;" u2="&#x201d;" k="80" />
    <hkern u1="&#xe4;" u2="&#x201c;" k="80" />
    <hkern u1="&#xe4;" u2="&#x2019;" k="80" />
    <hkern u1="&#xe4;" u2="&#x2018;" k="80" />
    <hkern u1="&#xe4;" u2="&#xba;" k="80" />
    <hkern u1="&#xe4;" u2="&#xb9;" k="80" />
    <hkern u1="&#xe4;" u2="&#xb3;" k="80" />
    <hkern u1="&#xe4;" u2="&#xb2;" k="80" />
    <hkern u1="&#xe4;" u2="&#xb0;" k="80" />
    <hkern u1="&#xe4;" u2="&#xaa;" k="80" />
    <hkern u1="&#xe4;" u2="y" k="40" />
    <hkern u1="&#xe4;" u2="w" k="20" />
    <hkern u1="&#xe4;" u2="v" k="40" />
    <hkern u1="&#xe4;" u2="&#x2a;" k="80" />
    <hkern u1="&#xe4;" u2="&#x27;" k="80" />
    <hkern u1="&#xe4;" u2="&#x22;" k="80" />
    <hkern u1="&#xe5;" u2="&#x2122;" k="80" />
    <hkern u1="&#xe5;" u2="&#x201d;" k="80" />
    <hkern u1="&#xe5;" u2="&#x201c;" k="80" />
    <hkern u1="&#xe5;" u2="&#x2019;" k="80" />
    <hkern u1="&#xe5;" u2="&#x2018;" k="80" />
    <hkern u1="&#xe5;" u2="&#xba;" k="80" />
    <hkern u1="&#xe5;" u2="&#xb9;" k="80" />
    <hkern u1="&#xe5;" u2="&#xb3;" k="80" />
    <hkern u1="&#xe5;" u2="&#xb2;" k="80" />
    <hkern u1="&#xe5;" u2="&#xb0;" k="80" />
    <hkern u1="&#xe5;" u2="&#xaa;" k="80" />
    <hkern u1="&#xe5;" u2="y" k="40" />
    <hkern u1="&#xe5;" u2="w" k="20" />
    <hkern u1="&#xe5;" u2="v" k="40" />
    <hkern u1="&#xe5;" u2="&#x2a;" k="80" />
    <hkern u1="&#xe5;" u2="&#x27;" k="80" />
    <hkern u1="&#xe5;" u2="&#x22;" k="80" />
    <hkern u1="&#xe6;" u2="&#x2122;" k="100" />
    <hkern u1="&#xe6;" u2="&#x201d;" k="100" />
    <hkern u1="&#xe6;" u2="&#x201c;" k="100" />
    <hkern u1="&#xe6;" u2="&#x2019;" k="100" />
    <hkern u1="&#xe6;" u2="&#x2018;" k="100" />
    <hkern u1="&#xe6;" u2="&#xba;" k="100" />
    <hkern u1="&#xe6;" u2="&#xb0;" k="100" />
    <hkern u1="&#xe6;" u2="&#xaa;" k="100" />
    <hkern u1="&#xe6;" u2="&#x7d;" k="40" />
    <hkern u1="&#xe6;" u2="y" k="40" />
    <hkern u1="&#xe6;" u2="x" k="60" />
    <hkern u1="&#xe6;" u2="v" k="40" />
    <hkern u1="&#xe6;" u2="]" k="40" />
    <hkern u1="&#xe6;" u2="\" k="130" />
    <hkern u1="&#xe6;" u2="W" k="50" />
    <hkern u1="&#xe6;" u2="V" k="130" />
    <hkern u1="&#xe6;" u2="&#x2a;" k="100" />
    <hkern u1="&#xe6;" u2="&#x29;" k="40" />
    <hkern u1="&#xe6;" u2="&#x27;" k="100" />
    <hkern u1="&#xe6;" u2="&#x22;" k="100" />
    <hkern u1="&#xe8;" u2="&#x2122;" k="100" />
    <hkern u1="&#xe8;" u2="&#x201d;" k="100" />
    <hkern u1="&#xe8;" u2="&#x201c;" k="100" />
    <hkern u1="&#xe8;" u2="&#x2019;" k="100" />
    <hkern u1="&#xe8;" u2="&#x2018;" k="100" />
    <hkern u1="&#xe8;" u2="&#xba;" k="100" />
    <hkern u1="&#xe8;" u2="&#xb0;" k="100" />
    <hkern u1="&#xe8;" u2="&#xaa;" k="100" />
    <hkern u1="&#xe8;" u2="&#x7d;" k="40" />
    <hkern u1="&#xe8;" u2="y" k="40" />
    <hkern u1="&#xe8;" u2="x" k="60" />
    <hkern u1="&#xe8;" u2="v" k="40" />
    <hkern u1="&#xe8;" u2="]" k="40" />
    <hkern u1="&#xe8;" u2="\" k="130" />
    <hkern u1="&#xe8;" u2="W" k="50" />
    <hkern u1="&#xe8;" u2="V" k="130" />
    <hkern u1="&#xe8;" u2="&#x2a;" k="100" />
    <hkern u1="&#xe8;" u2="&#x29;" k="40" />
    <hkern u1="&#xe8;" u2="&#x27;" k="100" />
    <hkern u1="&#xe8;" u2="&#x22;" k="100" />
    <hkern u1="&#xe9;" u2="&#x2122;" k="100" />
    <hkern u1="&#xe9;" u2="&#x201d;" k="100" />
    <hkern u1="&#xe9;" u2="&#x201c;" k="100" />
    <hkern u1="&#xe9;" u2="&#x2019;" k="100" />
    <hkern u1="&#xe9;" u2="&#x2018;" k="100" />
    <hkern u1="&#xe9;" u2="&#xba;" k="100" />
    <hkern u1="&#xe9;" u2="&#xb0;" k="100" />
    <hkern u1="&#xe9;" u2="&#xaa;" k="100" />
    <hkern u1="&#xe9;" u2="&#x7d;" k="40" />
    <hkern u1="&#xe9;" u2="y" k="40" />
    <hkern u1="&#xe9;" u2="x" k="60" />
    <hkern u1="&#xe9;" u2="v" k="40" />
    <hkern u1="&#xe9;" u2="]" k="40" />
    <hkern u1="&#xe9;" u2="\" k="130" />
    <hkern u1="&#xe9;" u2="W" k="50" />
    <hkern u1="&#xe9;" u2="V" k="130" />
    <hkern u1="&#xe9;" u2="&#x2a;" k="100" />
    <hkern u1="&#xe9;" u2="&#x29;" k="40" />
    <hkern u1="&#xe9;" u2="&#x27;" k="100" />
    <hkern u1="&#xe9;" u2="&#x22;" k="100" />
    <hkern u1="&#xea;" u2="&#x2122;" k="100" />
    <hkern u1="&#xea;" u2="&#x201d;" k="100" />
    <hkern u1="&#xea;" u2="&#x201c;" k="100" />
    <hkern u1="&#xea;" u2="&#x2019;" k="100" />
    <hkern u1="&#xea;" u2="&#x2018;" k="100" />
    <hkern u1="&#xea;" u2="&#xba;" k="100" />
    <hkern u1="&#xea;" u2="&#xb0;" k="100" />
    <hkern u1="&#xea;" u2="&#xaa;" k="100" />
    <hkern u1="&#xea;" u2="&#x7d;" k="40" />
    <hkern u1="&#xea;" u2="y" k="40" />
    <hkern u1="&#xea;" u2="x" k="60" />
    <hkern u1="&#xea;" u2="v" k="40" />
    <hkern u1="&#xea;" u2="]" k="40" />
    <hkern u1="&#xea;" u2="\" k="130" />
    <hkern u1="&#xea;" u2="W" k="50" />
    <hkern u1="&#xea;" u2="V" k="130" />
    <hkern u1="&#xea;" u2="&#x2a;" k="100" />
    <hkern u1="&#xea;" u2="&#x29;" k="40" />
    <hkern u1="&#xea;" u2="&#x27;" k="100" />
    <hkern u1="&#xea;" u2="&#x22;" k="100" />
    <hkern u1="&#xeb;" u2="&#x2122;" k="100" />
    <hkern u1="&#xeb;" u2="&#x201d;" k="100" />
    <hkern u1="&#xeb;" u2="&#x201c;" k="100" />
    <hkern u1="&#xeb;" u2="&#x2019;" k="100" />
    <hkern u1="&#xeb;" u2="&#x2018;" k="100" />
    <hkern u1="&#xeb;" u2="&#xba;" k="100" />
    <hkern u1="&#xeb;" u2="&#xb0;" k="100" />
    <hkern u1="&#xeb;" u2="&#xaa;" k="100" />
    <hkern u1="&#xeb;" u2="&#x7d;" k="40" />
    <hkern u1="&#xeb;" u2="y" k="40" />
    <hkern u1="&#xeb;" u2="x" k="60" />
    <hkern u1="&#xeb;" u2="v" k="40" />
    <hkern u1="&#xeb;" u2="]" k="40" />
    <hkern u1="&#xeb;" u2="\" k="130" />
    <hkern u1="&#xeb;" u2="W" k="50" />
    <hkern u1="&#xeb;" u2="V" k="130" />
    <hkern u1="&#xeb;" u2="&#x2a;" k="100" />
    <hkern u1="&#xeb;" u2="&#x29;" k="40" />
    <hkern u1="&#xeb;" u2="&#x27;" k="100" />
    <hkern u1="&#xeb;" u2="&#x22;" k="100" />
    <hkern u1="&#xf1;" u2="&#x2122;" k="80" />
    <hkern u1="&#xf1;" u2="&#x201d;" k="80" />
    <hkern u1="&#xf1;" u2="&#x201c;" k="80" />
    <hkern u1="&#xf1;" u2="&#x2019;" k="80" />
    <hkern u1="&#xf1;" u2="&#x2018;" k="80" />
    <hkern u1="&#xf1;" u2="&#xba;" k="80" />
    <hkern u1="&#xf1;" u2="&#xb9;" k="80" />
    <hkern u1="&#xf1;" u2="&#xb3;" k="80" />
    <hkern u1="&#xf1;" u2="&#xb2;" k="80" />
    <hkern u1="&#xf1;" u2="&#xb0;" k="80" />
    <hkern u1="&#xf1;" u2="&#xaa;" k="80" />
    <hkern u1="&#xf1;" u2="y" k="40" />
    <hkern u1="&#xf1;" u2="w" k="20" />
    <hkern u1="&#xf1;" u2="v" k="40" />
    <hkern u1="&#xf1;" u2="&#x2a;" k="80" />
    <hkern u1="&#xf1;" u2="&#x27;" k="80" />
    <hkern u1="&#xf1;" u2="&#x22;" k="80" />
    <hkern u1="&#xf2;" u2="&#x2122;" k="100" />
    <hkern u1="&#xf2;" u2="&#x201d;" k="100" />
    <hkern u1="&#xf2;" u2="&#x201c;" k="100" />
    <hkern u1="&#xf2;" u2="&#x2019;" k="100" />
    <hkern u1="&#xf2;" u2="&#x2018;" k="100" />
    <hkern u1="&#xf2;" u2="&#xba;" k="100" />
    <hkern u1="&#xf2;" u2="&#xb0;" k="100" />
    <hkern u1="&#xf2;" u2="&#xaa;" k="100" />
    <hkern u1="&#xf2;" u2="&#x7d;" k="40" />
    <hkern u1="&#xf2;" u2="y" k="40" />
    <hkern u1="&#xf2;" u2="x" k="60" />
    <hkern u1="&#xf2;" u2="v" k="40" />
    <hkern u1="&#xf2;" u2="]" k="40" />
    <hkern u1="&#xf2;" u2="\" k="130" />
    <hkern u1="&#xf2;" u2="W" k="50" />
    <hkern u1="&#xf2;" u2="V" k="130" />
    <hkern u1="&#xf2;" u2="&#x2a;" k="100" />
    <hkern u1="&#xf2;" u2="&#x29;" k="40" />
    <hkern u1="&#xf2;" u2="&#x27;" k="100" />
    <hkern u1="&#xf2;" u2="&#x22;" k="100" />
    <hkern u1="&#xf3;" u2="&#x2122;" k="100" />
    <hkern u1="&#xf3;" u2="&#x201d;" k="100" />
    <hkern u1="&#xf3;" u2="&#x201c;" k="100" />
    <hkern u1="&#xf3;" u2="&#x2019;" k="100" />
    <hkern u1="&#xf3;" u2="&#x2018;" k="100" />
    <hkern u1="&#xf3;" u2="&#xba;" k="100" />
    <hkern u1="&#xf3;" u2="&#xb0;" k="100" />
    <hkern u1="&#xf3;" u2="&#xaa;" k="100" />
    <hkern u1="&#xf3;" u2="&#x7d;" k="40" />
    <hkern u1="&#xf3;" u2="y" k="40" />
    <hkern u1="&#xf3;" u2="x" k="60" />
    <hkern u1="&#xf3;" u2="v" k="40" />
    <hkern u1="&#xf3;" u2="]" k="40" />
    <hkern u1="&#xf3;" u2="\" k="130" />
    <hkern u1="&#xf3;" u2="W" k="50" />
    <hkern u1="&#xf3;" u2="V" k="130" />
    <hkern u1="&#xf3;" u2="&#x2a;" k="100" />
    <hkern u1="&#xf3;" u2="&#x29;" k="40" />
    <hkern u1="&#xf3;" u2="&#x27;" k="100" />
    <hkern u1="&#xf3;" u2="&#x22;" k="100" />
    <hkern u1="&#xf4;" u2="&#x2122;" k="100" />
    <hkern u1="&#xf4;" u2="&#x201d;" k="100" />
    <hkern u1="&#xf4;" u2="&#x201c;" k="100" />
    <hkern u1="&#xf4;" u2="&#x2019;" k="100" />
    <hkern u1="&#xf4;" u2="&#x2018;" k="100" />
    <hkern u1="&#xf4;" u2="&#xba;" k="100" />
    <hkern u1="&#xf4;" u2="&#xb0;" k="100" />
    <hkern u1="&#xf4;" u2="&#xaa;" k="100" />
    <hkern u1="&#xf4;" u2="&#x7d;" k="40" />
    <hkern u1="&#xf4;" u2="y" k="40" />
    <hkern u1="&#xf4;" u2="x" k="60" />
    <hkern u1="&#xf4;" u2="v" k="40" />
    <hkern u1="&#xf4;" u2="]" k="40" />
    <hkern u1="&#xf4;" u2="\" k="130" />
    <hkern u1="&#xf4;" u2="W" k="50" />
    <hkern u1="&#xf4;" u2="V" k="130" />
    <hkern u1="&#xf4;" u2="&#x2a;" k="100" />
    <hkern u1="&#xf4;" u2="&#x29;" k="40" />
    <hkern u1="&#xf4;" u2="&#x27;" k="100" />
    <hkern u1="&#xf4;" u2="&#x22;" k="100" />
    <hkern u1="&#xf5;" u2="&#x2122;" k="100" />
    <hkern u1="&#xf5;" u2="&#x201d;" k="100" />
    <hkern u1="&#xf5;" u2="&#x201c;" k="100" />
    <hkern u1="&#xf5;" u2="&#x2019;" k="100" />
    <hkern u1="&#xf5;" u2="&#x2018;" k="100" />
    <hkern u1="&#xf5;" u2="&#xba;" k="100" />
    <hkern u1="&#xf5;" u2="&#xb0;" k="100" />
    <hkern u1="&#xf5;" u2="&#xaa;" k="100" />
    <hkern u1="&#xf5;" u2="&#x7d;" k="40" />
    <hkern u1="&#xf5;" u2="y" k="40" />
    <hkern u1="&#xf5;" u2="x" k="60" />
    <hkern u1="&#xf5;" u2="v" k="40" />
    <hkern u1="&#xf5;" u2="]" k="40" />
    <hkern u1="&#xf5;" u2="\" k="130" />
    <hkern u1="&#xf5;" u2="W" k="50" />
    <hkern u1="&#xf5;" u2="V" k="130" />
    <hkern u1="&#xf5;" u2="&#x2a;" k="100" />
    <hkern u1="&#xf5;" u2="&#x29;" k="40" />
    <hkern u1="&#xf5;" u2="&#x27;" k="100" />
    <hkern u1="&#xf5;" u2="&#x22;" k="100" />
    <hkern u1="&#xf6;" u2="&#x2122;" k="100" />
    <hkern u1="&#xf6;" u2="&#x201d;" k="100" />
    <hkern u1="&#xf6;" u2="&#x201c;" k="100" />
    <hkern u1="&#xf6;" u2="&#x2019;" k="100" />
    <hkern u1="&#xf6;" u2="&#x2018;" k="100" />
    <hkern u1="&#xf6;" u2="&#xba;" k="100" />
    <hkern u1="&#xf6;" u2="&#xb0;" k="100" />
    <hkern u1="&#xf6;" u2="&#xaa;" k="100" />
    <hkern u1="&#xf6;" u2="&#x7d;" k="40" />
    <hkern u1="&#xf6;" u2="y" k="40" />
    <hkern u1="&#xf6;" u2="x" k="60" />
    <hkern u1="&#xf6;" u2="v" k="40" />
    <hkern u1="&#xf6;" u2="]" k="40" />
    <hkern u1="&#xf6;" u2="\" k="130" />
    <hkern u1="&#xf6;" u2="W" k="50" />
    <hkern u1="&#xf6;" u2="V" k="130" />
    <hkern u1="&#xf6;" u2="&#x2a;" k="100" />
    <hkern u1="&#xf6;" u2="&#x29;" k="40" />
    <hkern u1="&#xf6;" u2="&#x27;" k="100" />
    <hkern u1="&#xf6;" u2="&#x22;" k="100" />
    <hkern u1="&#xf8;" u2="&#x2122;" k="100" />
    <hkern u1="&#xf8;" u2="&#x201d;" k="100" />
    <hkern u1="&#xf8;" u2="&#x201c;" k="100" />
    <hkern u1="&#xf8;" u2="&#x2019;" k="100" />
    <hkern u1="&#xf8;" u2="&#x2018;" k="100" />
    <hkern u1="&#xf8;" u2="&#xba;" k="100" />
    <hkern u1="&#xf8;" u2="&#xb0;" k="100" />
    <hkern u1="&#xf8;" u2="&#xaa;" k="100" />
    <hkern u1="&#xf8;" u2="&#x7d;" k="40" />
    <hkern u1="&#xf8;" u2="y" k="40" />
    <hkern u1="&#xf8;" u2="x" k="60" />
    <hkern u1="&#xf8;" u2="v" k="40" />
    <hkern u1="&#xf8;" u2="]" k="40" />
    <hkern u1="&#xf8;" u2="\" k="130" />
    <hkern u1="&#xf8;" u2="W" k="50" />
    <hkern u1="&#xf8;" u2="V" k="130" />
    <hkern u1="&#xf8;" u2="&#x2a;" k="100" />
    <hkern u1="&#xf8;" u2="&#x29;" k="40" />
    <hkern u1="&#xf8;" u2="&#x27;" k="100" />
    <hkern u1="&#xf8;" u2="&#x22;" k="100" />
    <hkern u1="&#xfe;" u2="&#x2122;" k="100" />
    <hkern u1="&#xfe;" u2="&#x201d;" k="100" />
    <hkern u1="&#xfe;" u2="&#x201c;" k="100" />
    <hkern u1="&#xfe;" u2="&#x2019;" k="100" />
    <hkern u1="&#xfe;" u2="&#x2018;" k="100" />
    <hkern u1="&#xfe;" u2="&#xba;" k="100" />
    <hkern u1="&#xfe;" u2="&#xb0;" k="100" />
    <hkern u1="&#xfe;" u2="&#xaa;" k="100" />
    <hkern u1="&#xfe;" u2="&#x7d;" k="40" />
    <hkern u1="&#xfe;" u2="y" k="40" />
    <hkern u1="&#xfe;" u2="x" k="60" />
    <hkern u1="&#xfe;" u2="v" k="40" />
    <hkern u1="&#xfe;" u2="]" k="40" />
    <hkern u1="&#xfe;" u2="\" k="130" />
    <hkern u1="&#xfe;" u2="W" k="50" />
    <hkern u1="&#xfe;" u2="V" k="130" />
    <hkern u1="&#xfe;" u2="&#x2a;" k="100" />
    <hkern u1="&#xfe;" u2="&#x29;" k="40" />
    <hkern u1="&#xfe;" u2="&#x27;" k="100" />
    <hkern u1="&#xfe;" u2="&#x22;" k="100" />
    <hkern u1="&#x104;" u2="&#x2122;" k="200" />
    <hkern u1="&#x104;" u2="&#x203a;" k="80" />
    <hkern u1="&#x104;" u2="&#x2039;" k="80" />
    <hkern u1="&#x104;" u2="&#x2022;" k="80" />
    <hkern u1="&#x104;" u2="&#x201d;" k="200" />
    <hkern u1="&#x104;" u2="&#x201c;" k="200" />
    <hkern u1="&#x104;" u2="&#x2019;" k="200" />
    <hkern u1="&#x104;" u2="&#x2018;" k="200" />
    <hkern u1="&#x104;" u2="&#x2014;" k="80" />
    <hkern u1="&#x104;" u2="&#x2013;" k="80" />
    <hkern u1="&#x104;" u2="&#x178;" k="200" />
    <hkern u1="&#x104;" u2="&#x152;" k="60" />
    <hkern u1="&#x104;" u2="&#x106;" k="60" />
    <hkern u1="&#x104;" u2="&#xdd;" k="200" />
    <hkern u1="&#x104;" u2="&#xdc;" k="50" />
    <hkern u1="&#x104;" u2="&#xdb;" k="50" />
    <hkern u1="&#x104;" u2="&#xda;" k="50" />
    <hkern u1="&#x104;" u2="&#xd9;" k="50" />
    <hkern u1="&#x104;" u2="&#xd8;" k="60" />
    <hkern u1="&#x104;" u2="&#xd6;" k="60" />
    <hkern u1="&#x104;" u2="&#xd5;" k="60" />
    <hkern u1="&#x104;" u2="&#xd4;" k="60" />
    <hkern u1="&#x104;" u2="&#xd3;" k="60" />
    <hkern u1="&#x104;" u2="&#xd2;" k="60" />
    <hkern u1="&#x104;" u2="&#xc7;" k="60" />
    <hkern u1="&#x104;" u2="&#xbb;" k="80" />
    <hkern u1="&#x104;" u2="&#xba;" k="200" />
    <hkern u1="&#x104;" u2="&#xb9;" k="220" />
    <hkern u1="&#x104;" u2="&#xb7;" k="80" />
    <hkern u1="&#x104;" u2="&#xb3;" k="220" />
    <hkern u1="&#x104;" u2="&#xb2;" k="220" />
    <hkern u1="&#x104;" u2="&#xb0;" k="200" />
    <hkern u1="&#x104;" u2="&#xad;" k="80" />
    <hkern u1="&#x104;" u2="&#xab;" k="80" />
    <hkern u1="&#x104;" u2="&#xaa;" k="200" />
    <hkern u1="&#x104;" u2="y" k="100" />
    <hkern u1="&#x104;" u2="v" k="100" />
    <hkern u1="&#x104;" u2="\" k="200" />
    <hkern u1="&#x104;" u2="Y" k="200" />
    <hkern u1="&#x104;" u2="W" k="120" />
    <hkern u1="&#x104;" u2="V" k="200" />
    <hkern u1="&#x104;" u2="U" k="50" />
    <hkern u1="&#x104;" u2="T" k="160" />
    <hkern u1="&#x104;" u2="Q" k="60" />
    <hkern u1="&#x104;" u2="O" k="60" />
    <hkern u1="&#x104;" u2="J" k="-60" />
    <hkern u1="&#x104;" u2="G" k="60" />
    <hkern u1="&#x104;" u2="C" k="60" />
    <hkern u1="&#x104;" u2="&#x40;" k="60" />
    <hkern u1="&#x104;" u2="&#x3f;" k="70" />
    <hkern u1="&#x104;" u2="&#x2d;" k="80" />
    <hkern u1="&#x104;" u2="&#x2a;" k="200" />
    <hkern u1="&#x104;" u2="&#x27;" k="200" />
    <hkern u1="&#x104;" u2="&#x22;" k="200" />
    <hkern u1="&#x105;" u2="&#x2122;" k="80" />
    <hkern u1="&#x105;" u2="&#x201d;" k="80" />
    <hkern u1="&#x105;" u2="&#x201c;" k="80" />
    <hkern u1="&#x105;" u2="&#x2019;" k="80" />
    <hkern u1="&#x105;" u2="&#x2018;" k="80" />
    <hkern u1="&#x105;" u2="&#xba;" k="80" />
    <hkern u1="&#x105;" u2="&#xb9;" k="80" />
    <hkern u1="&#x105;" u2="&#xb3;" k="80" />
    <hkern u1="&#x105;" u2="&#xb2;" k="80" />
    <hkern u1="&#x105;" u2="&#xb0;" k="80" />
    <hkern u1="&#x105;" u2="&#xaa;" k="80" />
    <hkern u1="&#x105;" u2="y" k="40" />
    <hkern u1="&#x105;" u2="w" k="20" />
    <hkern u1="&#x105;" u2="v" k="40" />
    <hkern u1="&#x105;" u2="&#x2a;" k="80" />
    <hkern u1="&#x105;" u2="&#x27;" k="80" />
    <hkern u1="&#x105;" u2="&#x22;" k="80" />
    <hkern u1="&#x106;" u2="&#x203a;" k="140" />
    <hkern u1="&#x106;" u2="&#x2039;" k="140" />
    <hkern u1="&#x106;" u2="&#x2022;" k="140" />
    <hkern u1="&#x106;" u2="&#x2014;" k="140" />
    <hkern u1="&#x106;" u2="&#x2013;" k="140" />
    <hkern u1="&#x106;" u2="&#xbb;" k="140" />
    <hkern u1="&#x106;" u2="&#xb7;" k="140" />
    <hkern u1="&#x106;" u2="&#xad;" k="140" />
    <hkern u1="&#x106;" u2="&#xab;" k="140" />
    <hkern u1="&#x106;" u2="&#x2d;" k="140" />
    <hkern u1="&#x119;" u2="&#x2122;" k="100" />
    <hkern u1="&#x119;" u2="&#x201d;" k="100" />
    <hkern u1="&#x119;" u2="&#x201c;" k="100" />
    <hkern u1="&#x119;" u2="&#x2019;" k="100" />
    <hkern u1="&#x119;" u2="&#x2018;" k="100" />
    <hkern u1="&#x119;" u2="&#xba;" k="100" />
    <hkern u1="&#x119;" u2="&#xb0;" k="100" />
    <hkern u1="&#x119;" u2="&#xaa;" k="100" />
    <hkern u1="&#x119;" u2="&#x7d;" k="40" />
    <hkern u1="&#x119;" u2="y" k="40" />
    <hkern u1="&#x119;" u2="x" k="60" />
    <hkern u1="&#x119;" u2="v" k="40" />
    <hkern u1="&#x119;" u2="]" k="40" />
    <hkern u1="&#x119;" u2="\" k="130" />
    <hkern u1="&#x119;" u2="W" k="50" />
    <hkern u1="&#x119;" u2="V" k="130" />
    <hkern u1="&#x119;" u2="&#x2a;" k="100" />
    <hkern u1="&#x119;" u2="&#x29;" k="40" />
    <hkern u1="&#x119;" u2="&#x27;" k="100" />
    <hkern u1="&#x119;" u2="&#x22;" k="100" />
    <hkern u1="&#x141;" u2="&#x2122;" k="140" />
    <hkern u1="&#x141;" u2="&#x203a;" k="100" />
    <hkern u1="&#x141;" u2="&#x2039;" k="100" />
    <hkern u1="&#x141;" u2="&#x2022;" k="100" />
    <hkern u1="&#x141;" u2="&#x201d;" k="140" />
    <hkern u1="&#x141;" u2="&#x201c;" k="140" />
    <hkern u1="&#x141;" u2="&#x2019;" k="140" />
    <hkern u1="&#x141;" u2="&#x2018;" k="140" />
    <hkern u1="&#x141;" u2="&#x2014;" k="100" />
    <hkern u1="&#x141;" u2="&#x2013;" k="100" />
    <hkern u1="&#x141;" u2="&#x178;" k="180" />
    <hkern u1="&#x141;" u2="&#xdd;" k="180" />
    <hkern u1="&#x141;" u2="&#xbb;" k="100" />
    <hkern u1="&#x141;" u2="&#xba;" k="140" />
    <hkern u1="&#x141;" u2="&#xb9;" k="140" />
    <hkern u1="&#x141;" u2="&#xb7;" k="100" />
    <hkern u1="&#x141;" u2="&#xb3;" k="140" />
    <hkern u1="&#x141;" u2="&#xb2;" k="140" />
    <hkern u1="&#x141;" u2="&#xb0;" k="140" />
    <hkern u1="&#x141;" u2="&#xad;" k="100" />
    <hkern u1="&#x141;" u2="&#xab;" k="100" />
    <hkern u1="&#x141;" u2="&#xaa;" k="140" />
    <hkern u1="&#x141;" u2="y" k="60" />
    <hkern u1="&#x141;" u2="v" k="60" />
    <hkern u1="&#x141;" u2="\" k="180" />
    <hkern u1="&#x141;" u2="Y" k="180" />
    <hkern u1="&#x141;" u2="W" k="140" />
    <hkern u1="&#x141;" u2="V" k="180" />
    <hkern u1="&#x141;" u2="&#x2d;" k="100" />
    <hkern u1="&#x141;" u2="&#x2a;" k="140" />
    <hkern u1="&#x141;" u2="&#x27;" k="140" />
    <hkern u1="&#x141;" u2="&#x22;" k="140" />
    <hkern u1="&#x144;" u2="&#x2122;" k="80" />
    <hkern u1="&#x144;" u2="&#x201d;" k="80" />
    <hkern u1="&#x144;" u2="&#x201c;" k="80" />
    <hkern u1="&#x144;" u2="&#x2019;" k="80" />
    <hkern u1="&#x144;" u2="&#x2018;" k="80" />
    <hkern u1="&#x144;" u2="&#xba;" k="80" />
    <hkern u1="&#x144;" u2="&#xb9;" k="80" />
    <hkern u1="&#x144;" u2="&#xb3;" k="80" />
    <hkern u1="&#x144;" u2="&#xb2;" k="80" />
    <hkern u1="&#x144;" u2="&#xb0;" k="80" />
    <hkern u1="&#x144;" u2="&#xaa;" k="80" />
    <hkern u1="&#x144;" u2="y" k="40" />
    <hkern u1="&#x144;" u2="w" k="20" />
    <hkern u1="&#x144;" u2="v" k="40" />
    <hkern u1="&#x144;" u2="&#x2a;" k="80" />
    <hkern u1="&#x144;" u2="&#x27;" k="80" />
    <hkern u1="&#x144;" u2="&#x22;" k="80" />
    <hkern u1="&#x153;" u2="&#x2122;" k="100" />
    <hkern u1="&#x153;" u2="&#x201d;" k="100" />
    <hkern u1="&#x153;" u2="&#x201c;" k="100" />
    <hkern u1="&#x153;" u2="&#x2019;" k="100" />
    <hkern u1="&#x153;" u2="&#x2018;" k="100" />
    <hkern u1="&#x153;" u2="&#xba;" k="100" />
    <hkern u1="&#x153;" u2="&#xb0;" k="100" />
    <hkern u1="&#x153;" u2="&#xaa;" k="100" />
    <hkern u1="&#x153;" u2="&#x7d;" k="40" />
    <hkern u1="&#x153;" u2="y" k="40" />
    <hkern u1="&#x153;" u2="x" k="60" />
    <hkern u1="&#x153;" u2="v" k="40" />
    <hkern u1="&#x153;" u2="]" k="40" />
    <hkern u1="&#x153;" u2="\" k="130" />
    <hkern u1="&#x153;" u2="W" k="50" />
    <hkern u1="&#x153;" u2="V" k="130" />
    <hkern u1="&#x153;" u2="&#x2a;" k="100" />
    <hkern u1="&#x153;" u2="&#x29;" k="40" />
    <hkern u1="&#x153;" u2="&#x27;" k="100" />
    <hkern u1="&#x153;" u2="&#x22;" k="100" />
    <hkern u1="&#x178;" u2="&#x2206;" k="200" />
    <hkern u1="&#x178;" u2="&#x2122;" k="-40" />
    <hkern u1="&#x178;" u2="&#x203a;" k="160" />
    <hkern u1="&#x178;" u2="&#x2039;" k="160" />
    <hkern u1="&#x178;" u2="&#x2022;" k="160" />
    <hkern u1="&#x178;" u2="&#x201e;" k="180" />
    <hkern u1="&#x178;" u2="&#x201d;" k="-40" />
    <hkern u1="&#x178;" u2="&#x201c;" k="-40" />
    <hkern u1="&#x178;" u2="&#x201a;" k="180" />
    <hkern u1="&#x178;" u2="&#x2019;" k="-40" />
    <hkern u1="&#x178;" u2="&#x2018;" k="-40" />
    <hkern u1="&#x178;" u2="&#x2014;" k="160" />
    <hkern u1="&#x178;" u2="&#x2013;" k="160" />
    <hkern u1="&#x178;" u2="&#x153;" k="160" />
    <hkern u1="&#x178;" u2="&#x152;" k="80" />
    <hkern u1="&#x178;" u2="&#x144;" k="140" />
    <hkern u1="&#x178;" u2="&#x119;" k="160" />
    <hkern u1="&#x178;" u2="&#x107;" k="160" />
    <hkern u1="&#x178;" u2="&#x106;" k="80" />
    <hkern u1="&#x178;" u2="&#x105;" k="160" />
    <hkern u1="&#x178;" u2="&#x104;" k="200" />
    <hkern u1="&#x178;" u2="&#xfc;" k="140" />
    <hkern u1="&#x178;" u2="&#xfb;" k="140" />
    <hkern u1="&#x178;" u2="&#xfa;" k="140" />
    <hkern u1="&#x178;" u2="&#xf9;" k="140" />
    <hkern u1="&#x178;" u2="&#xf8;" k="160" />
    <hkern u1="&#x178;" u2="&#xf6;" k="160" />
    <hkern u1="&#x178;" u2="&#xf5;" k="160" />
    <hkern u1="&#x178;" u2="&#xf4;" k="160" />
    <hkern u1="&#x178;" u2="&#xf3;" k="160" />
    <hkern u1="&#x178;" u2="&#xf2;" k="160" />
    <hkern u1="&#x178;" u2="&#xf1;" k="140" />
    <hkern u1="&#x178;" u2="&#xf0;" k="160" />
    <hkern u1="&#x178;" u2="&#xeb;" k="160" />
    <hkern u1="&#x178;" u2="&#xea;" k="160" />
    <hkern u1="&#x178;" u2="&#xe9;" k="160" />
    <hkern u1="&#x178;" u2="&#xe8;" k="160" />
    <hkern u1="&#x178;" u2="&#xe7;" k="160" />
    <hkern u1="&#x178;" u2="&#xe6;" k="160" />
    <hkern u1="&#x178;" u2="&#xe5;" k="160" />
    <hkern u1="&#x178;" u2="&#xe4;" k="160" />
    <hkern u1="&#x178;" u2="&#xe3;" k="160" />
    <hkern u1="&#x178;" u2="&#xe2;" k="160" />
    <hkern u1="&#x178;" u2="&#xe1;" k="160" />
    <hkern u1="&#x178;" u2="&#xe0;" k="160" />
    <hkern u1="&#x178;" u2="&#xd8;" k="80" />
    <hkern u1="&#x178;" u2="&#xd6;" k="80" />
    <hkern u1="&#x178;" u2="&#xd5;" k="80" />
    <hkern u1="&#x178;" u2="&#xd4;" k="80" />
    <hkern u1="&#x178;" u2="&#xd3;" k="80" />
    <hkern u1="&#x178;" u2="&#xd2;" k="80" />
    <hkern u1="&#x178;" u2="&#xc7;" k="80" />
    <hkern u1="&#x178;" u2="&#xc6;" k="200" />
    <hkern u1="&#x178;" u2="&#xc5;" k="200" />
    <hkern u1="&#x178;" u2="&#xc4;" k="200" />
    <hkern u1="&#x178;" u2="&#xc3;" k="200" />
    <hkern u1="&#x178;" u2="&#xc2;" k="200" />
    <hkern u1="&#x178;" u2="&#xc1;" k="200" />
    <hkern u1="&#x178;" u2="&#xc0;" k="200" />
    <hkern u1="&#x178;" u2="&#xbb;" k="160" />
    <hkern u1="&#x178;" u2="&#xba;" k="-40" />
    <hkern u1="&#x178;" u2="&#xb9;" k="-60" />
    <hkern u1="&#x178;" u2="&#xb7;" k="160" />
    <hkern u1="&#x178;" u2="&#xb5;" k="140" />
    <hkern u1="&#x178;" u2="&#xb3;" k="-60" />
    <hkern u1="&#x178;" u2="&#xb2;" k="-60" />
    <hkern u1="&#x178;" u2="&#xb0;" k="-40" />
    <hkern u1="&#x178;" u2="&#xad;" k="160" />
    <hkern u1="&#x178;" u2="&#xab;" k="160" />
    <hkern u1="&#x178;" u2="&#xaa;" k="-40" />
    <hkern u1="&#x178;" u2="y" k="100" />
    <hkern u1="&#x178;" u2="x" k="140" />
    <hkern u1="&#x178;" u2="w" k="100" />
    <hkern u1="&#x178;" u2="v" k="100" />
    <hkern u1="&#x178;" u2="u" k="140" />
    <hkern u1="&#x178;" u2="s" k="150" />
    <hkern u1="&#x178;" u2="r" k="140" />
    <hkern u1="&#x178;" u2="q" k="160" />
    <hkern u1="&#x178;" u2="p" k="140" />
    <hkern u1="&#x178;" u2="o" k="160" />
    <hkern u1="&#x178;" u2="n" k="140" />
    <hkern u1="&#x178;" u2="m" k="140" />
    <hkern u1="&#x178;" u2="g" k="180" />
    <hkern u1="&#x178;" u2="e" k="160" />
    <hkern u1="&#x178;" u2="d" k="160" />
    <hkern u1="&#x178;" u2="c" k="160" />
    <hkern u1="&#x178;" u2="a" k="160" />
    <hkern u1="&#x178;" u2="Q" k="80" />
    <hkern u1="&#x178;" u2="O" k="80" />
    <hkern u1="&#x178;" u2="J" k="200" />
    <hkern u1="&#x178;" u2="G" k="80" />
    <hkern u1="&#x178;" u2="C" k="80" />
    <hkern u1="&#x178;" u2="A" k="200" />
    <hkern u1="&#x178;" u2="&#x40;" k="80" />
    <hkern u1="&#x178;" u2="&#x3f;" k="-30" />
    <hkern u1="&#x178;" u2="&#x3b;" k="140" />
    <hkern u1="&#x178;" u2="&#x3a;" k="140" />
    <hkern u1="&#x178;" u2="&#x2f;" k="200" />
    <hkern u1="&#x178;" u2="&#x2e;" k="180" />
    <hkern u1="&#x178;" u2="&#x2d;" k="160" />
    <hkern u1="&#x178;" u2="&#x2c;" k="180" />
    <hkern u1="&#x178;" u2="&#x2a;" k="-40" />
    <hkern u1="&#x178;" u2="&#x27;" k="-40" />
    <hkern u1="&#x178;" u2="&#x26;" k="200" />
    <hkern u1="&#x178;" u2="&#x22;" k="-40" />
    <hkern u1="&#x179;" u2="&#x203a;" k="60" />
    <hkern u1="&#x179;" u2="&#x2039;" k="60" />
    <hkern u1="&#x179;" u2="&#x2022;" k="60" />
    <hkern u1="&#x179;" u2="&#x2014;" k="60" />
    <hkern u1="&#x179;" u2="&#x2013;" k="60" />
    <hkern u1="&#x179;" u2="&#x153;" k="20" />
    <hkern u1="&#x179;" u2="&#x152;" k="40" />
    <hkern u1="&#x179;" u2="&#x119;" k="20" />
    <hkern u1="&#x179;" u2="&#x107;" k="20" />
    <hkern u1="&#x179;" u2="&#x106;" k="40" />
    <hkern u1="&#x179;" u2="&#xf8;" k="20" />
    <hkern u1="&#x179;" u2="&#xf6;" k="20" />
    <hkern u1="&#x179;" u2="&#xf5;" k="20" />
    <hkern u1="&#x179;" u2="&#xf4;" k="20" />
    <hkern u1="&#x179;" u2="&#xf3;" k="20" />
    <hkern u1="&#x179;" u2="&#xf2;" k="20" />
    <hkern u1="&#x179;" u2="&#xf0;" k="20" />
    <hkern u1="&#x179;" u2="&#xeb;" k="20" />
    <hkern u1="&#x179;" u2="&#xea;" k="20" />
    <hkern u1="&#x179;" u2="&#xe9;" k="20" />
    <hkern u1="&#x179;" u2="&#xe8;" k="20" />
    <hkern u1="&#x179;" u2="&#xe7;" k="20" />
    <hkern u1="&#x179;" u2="&#xd8;" k="40" />
    <hkern u1="&#x179;" u2="&#xd6;" k="40" />
    <hkern u1="&#x179;" u2="&#xd5;" k="40" />
    <hkern u1="&#x179;" u2="&#xd4;" k="40" />
    <hkern u1="&#x179;" u2="&#xd3;" k="40" />
    <hkern u1="&#x179;" u2="&#xd2;" k="40" />
    <hkern u1="&#x179;" u2="&#xc7;" k="40" />
    <hkern u1="&#x179;" u2="&#xbb;" k="60" />
    <hkern u1="&#x179;" u2="&#xb7;" k="60" />
    <hkern u1="&#x179;" u2="&#xad;" k="60" />
    <hkern u1="&#x179;" u2="&#xab;" k="60" />
    <hkern u1="&#x179;" u2="y" k="30" />
    <hkern u1="&#x179;" u2="v" k="30" />
    <hkern u1="&#x179;" u2="s" k="10" />
    <hkern u1="&#x179;" u2="q" k="20" />
    <hkern u1="&#x179;" u2="o" k="20" />
    <hkern u1="&#x179;" u2="e" k="20" />
    <hkern u1="&#x179;" u2="d" k="20" />
    <hkern u1="&#x179;" u2="c" k="20" />
    <hkern u1="&#x179;" u2="Q" k="40" />
    <hkern u1="&#x179;" u2="O" k="40" />
    <hkern u1="&#x179;" u2="G" k="40" />
    <hkern u1="&#x179;" u2="C" k="40" />
    <hkern u1="&#x179;" u2="&#x40;" k="40" />
    <hkern u1="&#x179;" u2="&#x3f;" k="-30" />
    <hkern u1="&#x179;" u2="&#x2d;" k="60" />
    <hkern u1="&#x17b;" u2="&#x203a;" k="60" />
    <hkern u1="&#x17b;" u2="&#x2039;" k="60" />
    <hkern u1="&#x17b;" u2="&#x2022;" k="60" />
    <hkern u1="&#x17b;" u2="&#x2014;" k="60" />
    <hkern u1="&#x17b;" u2="&#x2013;" k="60" />
    <hkern u1="&#x17b;" u2="&#x153;" k="20" />
    <hkern u1="&#x17b;" u2="&#x152;" k="40" />
    <hkern u1="&#x17b;" u2="&#x119;" k="20" />
    <hkern u1="&#x17b;" u2="&#x107;" k="20" />
    <hkern u1="&#x17b;" u2="&#x106;" k="40" />
    <hkern u1="&#x17b;" u2="&#xf8;" k="20" />
    <hkern u1="&#x17b;" u2="&#xf6;" k="20" />
    <hkern u1="&#x17b;" u2="&#xf5;" k="20" />
    <hkern u1="&#x17b;" u2="&#xf4;" k="20" />
    <hkern u1="&#x17b;" u2="&#xf3;" k="20" />
    <hkern u1="&#x17b;" u2="&#xf2;" k="20" />
    <hkern u1="&#x17b;" u2="&#xf0;" k="20" />
    <hkern u1="&#x17b;" u2="&#xeb;" k="20" />
    <hkern u1="&#x17b;" u2="&#xea;" k="20" />
    <hkern u1="&#x17b;" u2="&#xe9;" k="20" />
    <hkern u1="&#x17b;" u2="&#xe8;" k="20" />
    <hkern u1="&#x17b;" u2="&#xe7;" k="20" />
    <hkern u1="&#x17b;" u2="&#xd8;" k="40" />
    <hkern u1="&#x17b;" u2="&#xd6;" k="40" />
    <hkern u1="&#x17b;" u2="&#xd5;" k="40" />
    <hkern u1="&#x17b;" u2="&#xd4;" k="40" />
    <hkern u1="&#x17b;" u2="&#xd3;" k="40" />
    <hkern u1="&#x17b;" u2="&#xd2;" k="40" />
    <hkern u1="&#x17b;" u2="&#xc7;" k="40" />
    <hkern u1="&#x17b;" u2="&#xbb;" k="60" />
    <hkern u1="&#x17b;" u2="&#xb7;" k="60" />
    <hkern u1="&#x17b;" u2="&#xad;" k="60" />
    <hkern u1="&#x17b;" u2="&#xab;" k="60" />
    <hkern u1="&#x17b;" u2="y" k="30" />
    <hkern u1="&#x17b;" u2="v" k="30" />
    <hkern u1="&#x17b;" u2="s" k="10" />
    <hkern u1="&#x17b;" u2="q" k="20" />
    <hkern u1="&#x17b;" u2="o" k="20" />
    <hkern u1="&#x17b;" u2="e" k="20" />
    <hkern u1="&#x17b;" u2="d" k="20" />
    <hkern u1="&#x17b;" u2="c" k="20" />
    <hkern u1="&#x17b;" u2="Q" k="40" />
    <hkern u1="&#x17b;" u2="O" k="40" />
    <hkern u1="&#x17b;" u2="G" k="40" />
    <hkern u1="&#x17b;" u2="C" k="40" />
    <hkern u1="&#x17b;" u2="&#x40;" k="40" />
    <hkern u1="&#x17b;" u2="&#x3f;" k="-30" />
    <hkern u1="&#x17b;" u2="&#x2d;" k="60" />
    <hkern u1="&#x17d;" u2="&#x203a;" k="60" />
    <hkern u1="&#x17d;" u2="&#x2039;" k="60" />
    <hkern u1="&#x17d;" u2="&#x2022;" k="60" />
    <hkern u1="&#x17d;" u2="&#x2014;" k="60" />
    <hkern u1="&#x17d;" u2="&#x2013;" k="60" />
    <hkern u1="&#x17d;" u2="&#x153;" k="20" />
    <hkern u1="&#x17d;" u2="&#x152;" k="40" />
    <hkern u1="&#x17d;" u2="&#x119;" k="20" />
    <hkern u1="&#x17d;" u2="&#x107;" k="20" />
    <hkern u1="&#x17d;" u2="&#x106;" k="40" />
    <hkern u1="&#x17d;" u2="&#xf8;" k="20" />
    <hkern u1="&#x17d;" u2="&#xf6;" k="20" />
    <hkern u1="&#x17d;" u2="&#xf5;" k="20" />
    <hkern u1="&#x17d;" u2="&#xf4;" k="20" />
    <hkern u1="&#x17d;" u2="&#xf3;" k="20" />
    <hkern u1="&#x17d;" u2="&#xf2;" k="20" />
    <hkern u1="&#x17d;" u2="&#xf0;" k="20" />
    <hkern u1="&#x17d;" u2="&#xeb;" k="20" />
    <hkern u1="&#x17d;" u2="&#xea;" k="20" />
    <hkern u1="&#x17d;" u2="&#xe9;" k="20" />
    <hkern u1="&#x17d;" u2="&#xe8;" k="20" />
    <hkern u1="&#x17d;" u2="&#xe7;" k="20" />
    <hkern u1="&#x17d;" u2="&#xd8;" k="40" />
    <hkern u1="&#x17d;" u2="&#xd6;" k="40" />
    <hkern u1="&#x17d;" u2="&#xd5;" k="40" />
    <hkern u1="&#x17d;" u2="&#xd4;" k="40" />
    <hkern u1="&#x17d;" u2="&#xd3;" k="40" />
    <hkern u1="&#x17d;" u2="&#xd2;" k="40" />
    <hkern u1="&#x17d;" u2="&#xc7;" k="40" />
    <hkern u1="&#x17d;" u2="&#xbb;" k="60" />
    <hkern u1="&#x17d;" u2="&#xb7;" k="60" />
    <hkern u1="&#x17d;" u2="&#xad;" k="60" />
    <hkern u1="&#x17d;" u2="&#xab;" k="60" />
    <hkern u1="&#x17d;" u2="y" k="30" />
    <hkern u1="&#x17d;" u2="v" k="30" />
    <hkern u1="&#x17d;" u2="s" k="10" />
    <hkern u1="&#x17d;" u2="q" k="20" />
    <hkern u1="&#x17d;" u2="o" k="20" />
    <hkern u1="&#x17d;" u2="e" k="20" />
    <hkern u1="&#x17d;" u2="d" k="20" />
    <hkern u1="&#x17d;" u2="c" k="20" />
    <hkern u1="&#x17d;" u2="Q" k="40" />
    <hkern u1="&#x17d;" u2="O" k="40" />
    <hkern u1="&#x17d;" u2="G" k="40" />
    <hkern u1="&#x17d;" u2="C" k="40" />
    <hkern u1="&#x17d;" u2="&#x40;" k="40" />
    <hkern u1="&#x17d;" u2="&#x3f;" k="-30" />
    <hkern u1="&#x17d;" u2="&#x2d;" k="60" />
    <hkern u1="&#x2013;" u2="&#x2206;" k="80" />
    <hkern u1="&#x2013;" u2="&#x2122;" k="160" />
    <hkern u1="&#x2013;" u2="&#x201e;" k="130" />
    <hkern u1="&#x2013;" u2="&#x201d;" k="160" />
    <hkern u1="&#x2013;" u2="&#x201c;" k="160" />
    <hkern u1="&#x2013;" u2="&#x201a;" k="130" />
    <hkern u1="&#x2013;" u2="&#x2019;" k="160" />
    <hkern u1="&#x2013;" u2="&#x2018;" k="160" />
    <hkern u1="&#x2013;" u2="&#x17d;" k="50" />
    <hkern u1="&#x2013;" u2="&#x17b;" k="50" />
    <hkern u1="&#x2013;" u2="&#x179;" k="50" />
    <hkern u1="&#x2013;" u2="&#x178;" k="160" />
    <hkern u1="&#x2013;" u2="&#x104;" k="80" />
    <hkern u1="&#x2013;" u2="&#xdd;" k="160" />
    <hkern u1="&#x2013;" u2="&#xc6;" k="80" />
    <hkern u1="&#x2013;" u2="&#xc5;" k="80" />
    <hkern u1="&#x2013;" u2="&#xc4;" k="80" />
    <hkern u1="&#x2013;" u2="&#xc3;" k="80" />
    <hkern u1="&#x2013;" u2="&#xc2;" k="80" />
    <hkern u1="&#x2013;" u2="&#xc1;" k="80" />
    <hkern u1="&#x2013;" u2="&#xc0;" k="80" />
    <hkern u1="&#x2013;" u2="&#xba;" k="160" />
    <hkern u1="&#x2013;" u2="&#xb0;" k="160" />
    <hkern u1="&#x2013;" u2="&#xaa;" k="160" />
    <hkern u1="&#x2013;" u2="\" k="120" />
    <hkern u1="&#x2013;" u2="Z" k="50" />
    <hkern u1="&#x2013;" u2="Y" k="160" />
    <hkern u1="&#x2013;" u2="X" k="70" />
    <hkern u1="&#x2013;" u2="W" k="40" />
    <hkern u1="&#x2013;" u2="V" k="120" />
    <hkern u1="&#x2013;" u2="T" k="180" />
    <hkern u1="&#x2013;" u2="A" k="80" />
    <hkern u1="&#x2013;" u2="&#x2f;" k="80" />
    <hkern u1="&#x2013;" u2="&#x2e;" k="130" />
    <hkern u1="&#x2013;" u2="&#x2c;" k="130" />
    <hkern u1="&#x2013;" u2="&#x2a;" k="160" />
    <hkern u1="&#x2013;" u2="&#x27;" k="160" />
    <hkern u1="&#x2013;" u2="&#x26;" k="80" />
    <hkern u1="&#x2013;" u2="&#x22;" k="160" />
    <hkern u1="&#x2014;" u2="&#x2206;" k="80" />
    <hkern u1="&#x2014;" u2="&#x2122;" k="160" />
    <hkern u1="&#x2014;" u2="&#x201e;" k="130" />
    <hkern u1="&#x2014;" u2="&#x201d;" k="160" />
    <hkern u1="&#x2014;" u2="&#x201c;" k="160" />
    <hkern u1="&#x2014;" u2="&#x201a;" k="130" />
    <hkern u1="&#x2014;" u2="&#x2019;" k="160" />
    <hkern u1="&#x2014;" u2="&#x2018;" k="160" />
    <hkern u1="&#x2014;" u2="&#x17d;" k="50" />
    <hkern u1="&#x2014;" u2="&#x17b;" k="50" />
    <hkern u1="&#x2014;" u2="&#x179;" k="50" />
    <hkern u1="&#x2014;" u2="&#x178;" k="160" />
    <hkern u1="&#x2014;" u2="&#x104;" k="80" />
    <hkern u1="&#x2014;" u2="&#xdd;" k="160" />
    <hkern u1="&#x2014;" u2="&#xc6;" k="80" />
    <hkern u1="&#x2014;" u2="&#xc5;" k="80" />
    <hkern u1="&#x2014;" u2="&#xc4;" k="80" />
    <hkern u1="&#x2014;" u2="&#xc3;" k="80" />
    <hkern u1="&#x2014;" u2="&#xc2;" k="80" />
    <hkern u1="&#x2014;" u2="&#xc1;" k="80" />
    <hkern u1="&#x2014;" u2="&#xc0;" k="80" />
    <hkern u1="&#x2014;" u2="&#xba;" k="160" />
    <hkern u1="&#x2014;" u2="&#xb0;" k="160" />
    <hkern u1="&#x2014;" u2="&#xaa;" k="160" />
    <hkern u1="&#x2014;" u2="\" k="120" />
    <hkern u1="&#x2014;" u2="Z" k="50" />
    <hkern u1="&#x2014;" u2="Y" k="160" />
    <hkern u1="&#x2014;" u2="X" k="70" />
    <hkern u1="&#x2014;" u2="W" k="40" />
    <hkern u1="&#x2014;" u2="V" k="120" />
    <hkern u1="&#x2014;" u2="T" k="180" />
    <hkern u1="&#x2014;" u2="A" k="80" />
    <hkern u1="&#x2014;" u2="&#x2f;" k="80" />
    <hkern u1="&#x2014;" u2="&#x2e;" k="130" />
    <hkern u1="&#x2014;" u2="&#x2c;" k="130" />
    <hkern u1="&#x2014;" u2="&#x2a;" k="160" />
    <hkern u1="&#x2014;" u2="&#x27;" k="160" />
    <hkern u1="&#x2014;" u2="&#x26;" k="80" />
    <hkern u1="&#x2014;" u2="&#x22;" k="160" />
    <hkern u1="&#x2018;" u2="&#x2206;" k="200" />
    <hkern u1="&#x2018;" u2="&#x203a;" k="160" />
    <hkern u1="&#x2018;" u2="&#x2039;" k="160" />
    <hkern u1="&#x2018;" u2="&#x2022;" k="160" />
    <hkern u1="&#x2018;" u2="&#x201e;" k="200" />
    <hkern u1="&#x2018;" u2="&#x201a;" k="200" />
    <hkern u1="&#x2018;" u2="&#x2014;" k="160" />
    <hkern u1="&#x2018;" u2="&#x2013;" k="160" />
    <hkern u1="&#x2018;" u2="&#x178;" k="-40" />
    <hkern u1="&#x2018;" u2="&#x153;" k="100" />
    <hkern u1="&#x2018;" u2="&#x152;" k="40" />
    <hkern u1="&#x2018;" u2="&#x119;" k="100" />
    <hkern u1="&#x2018;" u2="&#x107;" k="100" />
    <hkern u1="&#x2018;" u2="&#x106;" k="40" />
    <hkern u1="&#x2018;" u2="&#x105;" k="68" />
    <hkern u1="&#x2018;" u2="&#x104;" k="200" />
    <hkern u1="&#x2018;" u2="&#xf8;" k="100" />
    <hkern u1="&#x2018;" u2="&#xf6;" k="100" />
    <hkern u1="&#x2018;" u2="&#xf5;" k="100" />
    <hkern u1="&#x2018;" u2="&#xf4;" k="100" />
    <hkern u1="&#x2018;" u2="&#xf3;" k="100" />
    <hkern u1="&#x2018;" u2="&#xf2;" k="100" />
    <hkern u1="&#x2018;" u2="&#xf0;" k="100" />
    <hkern u1="&#x2018;" u2="&#xeb;" k="100" />
    <hkern u1="&#x2018;" u2="&#xea;" k="100" />
    <hkern u1="&#x2018;" u2="&#xe9;" k="100" />
    <hkern u1="&#x2018;" u2="&#xe8;" k="100" />
    <hkern u1="&#x2018;" u2="&#xe7;" k="100" />
    <hkern u1="&#x2018;" u2="&#xe6;" k="68" />
    <hkern u1="&#x2018;" u2="&#xe5;" k="68" />
    <hkern u1="&#x2018;" u2="&#xe4;" k="68" />
    <hkern u1="&#x2018;" u2="&#xe3;" k="68" />
    <hkern u1="&#x2018;" u2="&#xe2;" k="68" />
    <hkern u1="&#x2018;" u2="&#xe1;" k="68" />
    <hkern u1="&#x2018;" u2="&#xe0;" k="68" />
    <hkern u1="&#x2018;" u2="&#xdd;" k="-40" />
    <hkern u1="&#x2018;" u2="&#xd8;" k="40" />
    <hkern u1="&#x2018;" u2="&#xd6;" k="40" />
    <hkern u1="&#x2018;" u2="&#xd5;" k="40" />
    <hkern u1="&#x2018;" u2="&#xd4;" k="40" />
    <hkern u1="&#x2018;" u2="&#xd3;" k="40" />
    <hkern u1="&#x2018;" u2="&#xd2;" k="40" />
    <hkern u1="&#x2018;" u2="&#xc7;" k="40" />
    <hkern u1="&#x2018;" u2="&#xc6;" k="200" />
    <hkern u1="&#x2018;" u2="&#xc5;" k="200" />
    <hkern u1="&#x2018;" u2="&#xc4;" k="200" />
    <hkern u1="&#x2018;" u2="&#xc3;" k="200" />
    <hkern u1="&#x2018;" u2="&#xc2;" k="200" />
    <hkern u1="&#x2018;" u2="&#xc1;" k="200" />
    <hkern u1="&#x2018;" u2="&#xc0;" k="200" />
    <hkern u1="&#x2018;" u2="&#xbb;" k="160" />
    <hkern u1="&#x2018;" u2="&#xb7;" k="160" />
    <hkern u1="&#x2018;" u2="&#xad;" k="160" />
    <hkern u1="&#x2018;" u2="&#xab;" k="160" />
    <hkern u1="&#x2018;" u2="q" k="100" />
    <hkern u1="&#x2018;" u2="o" k="100" />
    <hkern u1="&#x2018;" u2="e" k="100" />
    <hkern u1="&#x2018;" u2="d" k="100" />
    <hkern u1="&#x2018;" u2="c" k="100" />
    <hkern u1="&#x2018;" u2="a" k="68" />
    <hkern u1="&#x2018;" u2="\" k="-40" />
    <hkern u1="&#x2018;" u2="Y" k="-40" />
    <hkern u1="&#x2018;" u2="W" k="-40" />
    <hkern u1="&#x2018;" u2="V" k="-40" />
    <hkern u1="&#x2018;" u2="Q" k="40" />
    <hkern u1="&#x2018;" u2="O" k="40" />
    <hkern u1="&#x2018;" u2="G" k="40" />
    <hkern u1="&#x2018;" u2="C" k="40" />
    <hkern u1="&#x2018;" u2="A" k="200" />
    <hkern u1="&#x2018;" u2="&#x40;" k="40" />
    <hkern u1="&#x2018;" u2="&#x2f;" k="200" />
    <hkern u1="&#x2018;" u2="&#x2e;" k="200" />
    <hkern u1="&#x2018;" u2="&#x2d;" k="160" />
    <hkern u1="&#x2018;" u2="&#x2c;" k="200" />
    <hkern u1="&#x2018;" u2="&#x26;" k="200" />
    <hkern u1="&#x2019;" u2="&#x2206;" k="200" />
    <hkern u1="&#x2019;" u2="&#x203a;" k="160" />
    <hkern u1="&#x2019;" u2="&#x2039;" k="160" />
    <hkern u1="&#x2019;" u2="&#x2022;" k="160" />
    <hkern u1="&#x2019;" u2="&#x201e;" k="200" />
    <hkern u1="&#x2019;" u2="&#x201a;" k="200" />
    <hkern u1="&#x2019;" u2="&#x2014;" k="160" />
    <hkern u1="&#x2019;" u2="&#x2013;" k="160" />
    <hkern u1="&#x2019;" u2="&#x178;" k="-40" />
    <hkern u1="&#x2019;" u2="&#x153;" k="100" />
    <hkern u1="&#x2019;" u2="&#x152;" k="40" />
    <hkern u1="&#x2019;" u2="&#x119;" k="100" />
    <hkern u1="&#x2019;" u2="&#x107;" k="100" />
    <hkern u1="&#x2019;" u2="&#x106;" k="40" />
    <hkern u1="&#x2019;" u2="&#x105;" k="68" />
    <hkern u1="&#x2019;" u2="&#x104;" k="200" />
    <hkern u1="&#x2019;" u2="&#xf8;" k="100" />
    <hkern u1="&#x2019;" u2="&#xf6;" k="100" />
    <hkern u1="&#x2019;" u2="&#xf5;" k="100" />
    <hkern u1="&#x2019;" u2="&#xf4;" k="100" />
    <hkern u1="&#x2019;" u2="&#xf3;" k="100" />
    <hkern u1="&#x2019;" u2="&#xf2;" k="100" />
    <hkern u1="&#x2019;" u2="&#xf0;" k="100" />
    <hkern u1="&#x2019;" u2="&#xeb;" k="100" />
    <hkern u1="&#x2019;" u2="&#xea;" k="100" />
    <hkern u1="&#x2019;" u2="&#xe9;" k="100" />
    <hkern u1="&#x2019;" u2="&#xe8;" k="100" />
    <hkern u1="&#x2019;" u2="&#xe7;" k="100" />
    <hkern u1="&#x2019;" u2="&#xe6;" k="68" />
    <hkern u1="&#x2019;" u2="&#xe5;" k="68" />
    <hkern u1="&#x2019;" u2="&#xe4;" k="68" />
    <hkern u1="&#x2019;" u2="&#xe3;" k="68" />
    <hkern u1="&#x2019;" u2="&#xe2;" k="68" />
    <hkern u1="&#x2019;" u2="&#xe1;" k="68" />
    <hkern u1="&#x2019;" u2="&#xe0;" k="68" />
    <hkern u1="&#x2019;" u2="&#xdd;" k="-40" />
    <hkern u1="&#x2019;" u2="&#xd8;" k="40" />
    <hkern u1="&#x2019;" u2="&#xd6;" k="40" />
    <hkern u1="&#x2019;" u2="&#xd5;" k="40" />
    <hkern u1="&#x2019;" u2="&#xd4;" k="40" />
    <hkern u1="&#x2019;" u2="&#xd3;" k="40" />
    <hkern u1="&#x2019;" u2="&#xd2;" k="40" />
    <hkern u1="&#x2019;" u2="&#xc7;" k="40" />
    <hkern u1="&#x2019;" u2="&#xc6;" k="200" />
    <hkern u1="&#x2019;" u2="&#xc5;" k="200" />
    <hkern u1="&#x2019;" u2="&#xc4;" k="200" />
    <hkern u1="&#x2019;" u2="&#xc3;" k="200" />
    <hkern u1="&#x2019;" u2="&#xc2;" k="200" />
    <hkern u1="&#x2019;" u2="&#xc1;" k="200" />
    <hkern u1="&#x2019;" u2="&#xc0;" k="200" />
    <hkern u1="&#x2019;" u2="&#xbb;" k="160" />
    <hkern u1="&#x2019;" u2="&#xb7;" k="160" />
    <hkern u1="&#x2019;" u2="&#xad;" k="160" />
    <hkern u1="&#x2019;" u2="&#xab;" k="160" />
    <hkern u1="&#x2019;" u2="q" k="100" />
    <hkern u1="&#x2019;" u2="o" k="100" />
    <hkern u1="&#x2019;" u2="e" k="100" />
    <hkern u1="&#x2019;" u2="d" k="100" />
    <hkern u1="&#x2019;" u2="c" k="100" />
    <hkern u1="&#x2019;" u2="a" k="68" />
    <hkern u1="&#x2019;" u2="\" k="-40" />
    <hkern u1="&#x2019;" u2="Y" k="-40" />
    <hkern u1="&#x2019;" u2="W" k="-40" />
    <hkern u1="&#x2019;" u2="V" k="-40" />
    <hkern u1="&#x2019;" u2="Q" k="40" />
    <hkern u1="&#x2019;" u2="O" k="40" />
    <hkern u1="&#x2019;" u2="G" k="40" />
    <hkern u1="&#x2019;" u2="C" k="40" />
    <hkern u1="&#x2019;" u2="A" k="200" />
    <hkern u1="&#x2019;" u2="&#x40;" k="40" />
    <hkern u1="&#x2019;" u2="&#x2f;" k="200" />
    <hkern u1="&#x2019;" u2="&#x2e;" k="200" />
    <hkern u1="&#x2019;" u2="&#x2d;" k="160" />
    <hkern u1="&#x2019;" u2="&#x2c;" k="200" />
    <hkern u1="&#x2019;" u2="&#x26;" k="200" />
    <hkern u1="&#x201a;" u2="&#x2122;" k="200" />
    <hkern u1="&#x201a;" u2="&#x203a;" k="130" />
    <hkern u1="&#x201a;" u2="&#x2039;" k="130" />
    <hkern u1="&#x201a;" u2="&#x2022;" k="130" />
    <hkern u1="&#x201a;" u2="&#x201d;" k="200" />
    <hkern u1="&#x201a;" u2="&#x201c;" k="200" />
    <hkern u1="&#x201a;" u2="&#x2019;" k="200" />
    <hkern u1="&#x201a;" u2="&#x2018;" k="200" />
    <hkern u1="&#x201a;" u2="&#x2014;" k="130" />
    <hkern u1="&#x201a;" u2="&#x2013;" k="130" />
    <hkern u1="&#x201a;" u2="&#x178;" k="180" />
    <hkern u1="&#x201a;" u2="&#x152;" k="50" />
    <hkern u1="&#x201a;" u2="&#x106;" k="50" />
    <hkern u1="&#x201a;" u2="&#xdd;" k="180" />
    <hkern u1="&#x201a;" u2="&#xd8;" k="50" />
    <hkern u1="&#x201a;" u2="&#xd6;" k="50" />
    <hkern u1="&#x201a;" u2="&#xd5;" k="50" />
    <hkern u1="&#x201a;" u2="&#xd4;" k="50" />
    <hkern u1="&#x201a;" u2="&#xd3;" k="50" />
    <hkern u1="&#x201a;" u2="&#xd2;" k="50" />
    <hkern u1="&#x201a;" u2="&#xc7;" k="50" />
    <hkern u1="&#x201a;" u2="&#xbb;" k="130" />
    <hkern u1="&#x201a;" u2="&#xba;" k="200" />
    <hkern u1="&#x201a;" u2="&#xb7;" k="130" />
    <hkern u1="&#x201a;" u2="&#xb0;" k="200" />
    <hkern u1="&#x201a;" u2="&#xad;" k="130" />
    <hkern u1="&#x201a;" u2="&#xab;" k="130" />
    <hkern u1="&#x201a;" u2="&#xaa;" k="200" />
    <hkern u1="&#x201a;" u2="y" k="140" />
    <hkern u1="&#x201a;" u2="w" k="80" />
    <hkern u1="&#x201a;" u2="v" k="140" />
    <hkern u1="&#x201a;" u2="\" k="180" />
    <hkern u1="&#x201a;" u2="Y" k="180" />
    <hkern u1="&#x201a;" u2="W" k="140" />
    <hkern u1="&#x201a;" u2="V" k="180" />
    <hkern u1="&#x201a;" u2="T" k="180" />
    <hkern u1="&#x201a;" u2="Q" k="50" />
    <hkern u1="&#x201a;" u2="O" k="50" />
    <hkern u1="&#x201a;" u2="G" k="50" />
    <hkern u1="&#x201a;" u2="C" k="50" />
    <hkern u1="&#x201a;" u2="&#x40;" k="50" />
    <hkern u1="&#x201a;" u2="&#x2d;" k="130" />
    <hkern u1="&#x201a;" u2="&#x2a;" k="200" />
    <hkern u1="&#x201a;" u2="&#x27;" k="200" />
    <hkern u1="&#x201a;" u2="&#x22;" k="200" />
    <hkern u1="&#x201c;" u2="&#x2206;" k="200" />
    <hkern u1="&#x201c;" u2="&#x203a;" k="160" />
    <hkern u1="&#x201c;" u2="&#x2039;" k="160" />
    <hkern u1="&#x201c;" u2="&#x2022;" k="160" />
    <hkern u1="&#x201c;" u2="&#x201e;" k="200" />
    <hkern u1="&#x201c;" u2="&#x201a;" k="200" />
    <hkern u1="&#x201c;" u2="&#x2014;" k="160" />
    <hkern u1="&#x201c;" u2="&#x2013;" k="160" />
    <hkern u1="&#x201c;" u2="&#x178;" k="-40" />
    <hkern u1="&#x201c;" u2="&#x153;" k="100" />
    <hkern u1="&#x201c;" u2="&#x152;" k="40" />
    <hkern u1="&#x201c;" u2="&#x119;" k="100" />
    <hkern u1="&#x201c;" u2="&#x107;" k="100" />
    <hkern u1="&#x201c;" u2="&#x106;" k="40" />
    <hkern u1="&#x201c;" u2="&#x105;" k="68" />
    <hkern u1="&#x201c;" u2="&#x104;" k="200" />
    <hkern u1="&#x201c;" u2="&#xf8;" k="100" />
    <hkern u1="&#x201c;" u2="&#xf6;" k="100" />
    <hkern u1="&#x201c;" u2="&#xf5;" k="100" />
    <hkern u1="&#x201c;" u2="&#xf4;" k="100" />
    <hkern u1="&#x201c;" u2="&#xf3;" k="100" />
    <hkern u1="&#x201c;" u2="&#xf2;" k="100" />
    <hkern u1="&#x201c;" u2="&#xf0;" k="100" />
    <hkern u1="&#x201c;" u2="&#xeb;" k="100" />
    <hkern u1="&#x201c;" u2="&#xea;" k="100" />
    <hkern u1="&#x201c;" u2="&#xe9;" k="100" />
    <hkern u1="&#x201c;" u2="&#xe8;" k="100" />
    <hkern u1="&#x201c;" u2="&#xe7;" k="100" />
    <hkern u1="&#x201c;" u2="&#xe6;" k="68" />
    <hkern u1="&#x201c;" u2="&#xe5;" k="68" />
    <hkern u1="&#x201c;" u2="&#xe4;" k="68" />
    <hkern u1="&#x201c;" u2="&#xe3;" k="68" />
    <hkern u1="&#x201c;" u2="&#xe2;" k="68" />
    <hkern u1="&#x201c;" u2="&#xe1;" k="68" />
    <hkern u1="&#x201c;" u2="&#xe0;" k="68" />
    <hkern u1="&#x201c;" u2="&#xdd;" k="-40" />
    <hkern u1="&#x201c;" u2="&#xd8;" k="40" />
    <hkern u1="&#x201c;" u2="&#xd6;" k="40" />
    <hkern u1="&#x201c;" u2="&#xd5;" k="40" />
    <hkern u1="&#x201c;" u2="&#xd4;" k="40" />
    <hkern u1="&#x201c;" u2="&#xd3;" k="40" />
    <hkern u1="&#x201c;" u2="&#xd2;" k="40" />
    <hkern u1="&#x201c;" u2="&#xc7;" k="40" />
    <hkern u1="&#x201c;" u2="&#xc6;" k="200" />
    <hkern u1="&#x201c;" u2="&#xc5;" k="200" />
    <hkern u1="&#x201c;" u2="&#xc4;" k="200" />
    <hkern u1="&#x201c;" u2="&#xc3;" k="200" />
    <hkern u1="&#x201c;" u2="&#xc2;" k="200" />
    <hkern u1="&#x201c;" u2="&#xc1;" k="200" />
    <hkern u1="&#x201c;" u2="&#xc0;" k="200" />
    <hkern u1="&#x201c;" u2="&#xbb;" k="160" />
    <hkern u1="&#x201c;" u2="&#xb7;" k="160" />
    <hkern u1="&#x201c;" u2="&#xad;" k="160" />
    <hkern u1="&#x201c;" u2="&#xab;" k="160" />
    <hkern u1="&#x201c;" u2="q" k="100" />
    <hkern u1="&#x201c;" u2="o" k="100" />
    <hkern u1="&#x201c;" u2="e" k="100" />
    <hkern u1="&#x201c;" u2="d" k="100" />
    <hkern u1="&#x201c;" u2="c" k="100" />
    <hkern u1="&#x201c;" u2="a" k="68" />
    <hkern u1="&#x201c;" u2="\" k="-40" />
    <hkern u1="&#x201c;" u2="Y" k="-40" />
    <hkern u1="&#x201c;" u2="W" k="-40" />
    <hkern u1="&#x201c;" u2="V" k="-40" />
    <hkern u1="&#x201c;" u2="Q" k="40" />
    <hkern u1="&#x201c;" u2="O" k="40" />
    <hkern u1="&#x201c;" u2="G" k="40" />
    <hkern u1="&#x201c;" u2="C" k="40" />
    <hkern u1="&#x201c;" u2="A" k="200" />
    <hkern u1="&#x201c;" u2="&#x40;" k="40" />
    <hkern u1="&#x201c;" u2="&#x2f;" k="200" />
    <hkern u1="&#x201c;" u2="&#x2e;" k="200" />
    <hkern u1="&#x201c;" u2="&#x2d;" k="160" />
    <hkern u1="&#x201c;" u2="&#x2c;" k="200" />
    <hkern u1="&#x201c;" u2="&#x26;" k="200" />
    <hkern u1="&#x201d;" u2="&#x2206;" k="200" />
    <hkern u1="&#x201d;" u2="&#x203a;" k="160" />
    <hkern u1="&#x201d;" u2="&#x2039;" k="160" />
    <hkern u1="&#x201d;" u2="&#x2022;" k="160" />
    <hkern u1="&#x201d;" u2="&#x201e;" k="200" />
    <hkern u1="&#x201d;" u2="&#x201a;" k="200" />
    <hkern u1="&#x201d;" u2="&#x2014;" k="160" />
    <hkern u1="&#x201d;" u2="&#x2013;" k="160" />
    <hkern u1="&#x201d;" u2="&#x178;" k="-40" />
    <hkern u1="&#x201d;" u2="&#x153;" k="100" />
    <hkern u1="&#x201d;" u2="&#x152;" k="40" />
    <hkern u1="&#x201d;" u2="&#x119;" k="100" />
    <hkern u1="&#x201d;" u2="&#x107;" k="100" />
    <hkern u1="&#x201d;" u2="&#x106;" k="40" />
    <hkern u1="&#x201d;" u2="&#x105;" k="68" />
    <hkern u1="&#x201d;" u2="&#x104;" k="200" />
    <hkern u1="&#x201d;" u2="&#xf8;" k="100" />
    <hkern u1="&#x201d;" u2="&#xf6;" k="100" />
    <hkern u1="&#x201d;" u2="&#xf5;" k="100" />
    <hkern u1="&#x201d;" u2="&#xf4;" k="100" />
    <hkern u1="&#x201d;" u2="&#xf3;" k="100" />
    <hkern u1="&#x201d;" u2="&#xf2;" k="100" />
    <hkern u1="&#x201d;" u2="&#xf0;" k="100" />
    <hkern u1="&#x201d;" u2="&#xeb;" k="100" />
    <hkern u1="&#x201d;" u2="&#xea;" k="100" />
    <hkern u1="&#x201d;" u2="&#xe9;" k="100" />
    <hkern u1="&#x201d;" u2="&#xe8;" k="100" />
    <hkern u1="&#x201d;" u2="&#xe7;" k="100" />
    <hkern u1="&#x201d;" u2="&#xe6;" k="68" />
    <hkern u1="&#x201d;" u2="&#xe5;" k="68" />
    <hkern u1="&#x201d;" u2="&#xe4;" k="68" />
    <hkern u1="&#x201d;" u2="&#xe3;" k="68" />
    <hkern u1="&#x201d;" u2="&#xe2;" k="68" />
    <hkern u1="&#x201d;" u2="&#xe1;" k="68" />
    <hkern u1="&#x201d;" u2="&#xe0;" k="68" />
    <hkern u1="&#x201d;" u2="&#xdd;" k="-40" />
    <hkern u1="&#x201d;" u2="&#xd8;" k="40" />
    <hkern u1="&#x201d;" u2="&#xd6;" k="40" />
    <hkern u1="&#x201d;" u2="&#xd5;" k="40" />
    <hkern u1="&#x201d;" u2="&#xd4;" k="40" />
    <hkern u1="&#x201d;" u2="&#xd3;" k="40" />
    <hkern u1="&#x201d;" u2="&#xd2;" k="40" />
    <hkern u1="&#x201d;" u2="&#xc7;" k="40" />
    <hkern u1="&#x201d;" u2="&#xc6;" k="200" />
    <hkern u1="&#x201d;" u2="&#xc5;" k="200" />
    <hkern u1="&#x201d;" u2="&#xc4;" k="200" />
    <hkern u1="&#x201d;" u2="&#xc3;" k="200" />
    <hkern u1="&#x201d;" u2="&#xc2;" k="200" />
    <hkern u1="&#x201d;" u2="&#xc1;" k="200" />
    <hkern u1="&#x201d;" u2="&#xc0;" k="200" />
    <hkern u1="&#x201d;" u2="&#xbb;" k="160" />
    <hkern u1="&#x201d;" u2="&#xb7;" k="160" />
    <hkern u1="&#x201d;" u2="&#xad;" k="160" />
    <hkern u1="&#x201d;" u2="&#xab;" k="160" />
    <hkern u1="&#x201d;" u2="q" k="100" />
    <hkern u1="&#x201d;" u2="o" k="100" />
    <hkern u1="&#x201d;" u2="e" k="100" />
    <hkern u1="&#x201d;" u2="d" k="100" />
    <hkern u1="&#x201d;" u2="c" k="100" />
    <hkern u1="&#x201d;" u2="a" k="68" />
    <hkern u1="&#x201d;" u2="\" k="-40" />
    <hkern u1="&#x201d;" u2="Y" k="-40" />
    <hkern u1="&#x201d;" u2="W" k="-40" />
    <hkern u1="&#x201d;" u2="V" k="-40" />
    <hkern u1="&#x201d;" u2="Q" k="40" />
    <hkern u1="&#x201d;" u2="O" k="40" />
    <hkern u1="&#x201d;" u2="G" k="40" />
    <hkern u1="&#x201d;" u2="C" k="40" />
    <hkern u1="&#x201d;" u2="A" k="200" />
    <hkern u1="&#x201d;" u2="&#x40;" k="40" />
    <hkern u1="&#x201d;" u2="&#x2f;" k="200" />
    <hkern u1="&#x201d;" u2="&#x2e;" k="200" />
    <hkern u1="&#x201d;" u2="&#x2d;" k="160" />
    <hkern u1="&#x201d;" u2="&#x2c;" k="200" />
    <hkern u1="&#x201d;" u2="&#x26;" k="200" />
    <hkern u1="&#x201e;" u2="&#x2122;" k="200" />
    <hkern u1="&#x201e;" u2="&#x203a;" k="130" />
    <hkern u1="&#x201e;" u2="&#x2039;" k="130" />
    <hkern u1="&#x201e;" u2="&#x2022;" k="130" />
    <hkern u1="&#x201e;" u2="&#x201d;" k="200" />
    <hkern u1="&#x201e;" u2="&#x201c;" k="200" />
    <hkern u1="&#x201e;" u2="&#x2019;" k="200" />
    <hkern u1="&#x201e;" u2="&#x2018;" k="200" />
    <hkern u1="&#x201e;" u2="&#x2014;" k="130" />
    <hkern u1="&#x201e;" u2="&#x2013;" k="130" />
    <hkern u1="&#x201e;" u2="&#x178;" k="180" />
    <hkern u1="&#x201e;" u2="&#x152;" k="50" />
    <hkern u1="&#x201e;" u2="&#x106;" k="50" />
    <hkern u1="&#x201e;" u2="&#xdd;" k="180" />
    <hkern u1="&#x201e;" u2="&#xd8;" k="50" />
    <hkern u1="&#x201e;" u2="&#xd6;" k="50" />
    <hkern u1="&#x201e;" u2="&#xd5;" k="50" />
    <hkern u1="&#x201e;" u2="&#xd4;" k="50" />
    <hkern u1="&#x201e;" u2="&#xd3;" k="50" />
    <hkern u1="&#x201e;" u2="&#xd2;" k="50" />
    <hkern u1="&#x201e;" u2="&#xc7;" k="50" />
    <hkern u1="&#x201e;" u2="&#xbb;" k="130" />
    <hkern u1="&#x201e;" u2="&#xba;" k="200" />
    <hkern u1="&#x201e;" u2="&#xb7;" k="130" />
    <hkern u1="&#x201e;" u2="&#xb0;" k="200" />
    <hkern u1="&#x201e;" u2="&#xad;" k="130" />
    <hkern u1="&#x201e;" u2="&#xab;" k="130" />
    <hkern u1="&#x201e;" u2="&#xaa;" k="200" />
    <hkern u1="&#x201e;" u2="y" k="140" />
    <hkern u1="&#x201e;" u2="w" k="80" />
    <hkern u1="&#x201e;" u2="v" k="140" />
    <hkern u1="&#x201e;" u2="\" k="180" />
    <hkern u1="&#x201e;" u2="Y" k="180" />
    <hkern u1="&#x201e;" u2="W" k="140" />
    <hkern u1="&#x201e;" u2="V" k="180" />
    <hkern u1="&#x201e;" u2="T" k="180" />
    <hkern u1="&#x201e;" u2="Q" k="50" />
    <hkern u1="&#x201e;" u2="O" k="50" />
    <hkern u1="&#x201e;" u2="G" k="50" />
    <hkern u1="&#x201e;" u2="C" k="50" />
    <hkern u1="&#x201e;" u2="&#x40;" k="50" />
    <hkern u1="&#x201e;" u2="&#x2d;" k="130" />
    <hkern u1="&#x201e;" u2="&#x2a;" k="200" />
    <hkern u1="&#x201e;" u2="&#x27;" k="200" />
    <hkern u1="&#x201e;" u2="&#x22;" k="200" />
    <hkern u1="&#x2022;" u2="&#x2206;" k="80" />
    <hkern u1="&#x2022;" u2="&#x2122;" k="160" />
    <hkern u1="&#x2022;" u2="&#x201e;" k="130" />
    <hkern u1="&#x2022;" u2="&#x201d;" k="160" />
    <hkern u1="&#x2022;" u2="&#x201c;" k="160" />
    <hkern u1="&#x2022;" u2="&#x201a;" k="130" />
    <hkern u1="&#x2022;" u2="&#x2019;" k="160" />
    <hkern u1="&#x2022;" u2="&#x2018;" k="160" />
    <hkern u1="&#x2022;" u2="&#x17d;" k="50" />
    <hkern u1="&#x2022;" u2="&#x17b;" k="50" />
    <hkern u1="&#x2022;" u2="&#x179;" k="50" />
    <hkern u1="&#x2022;" u2="&#x178;" k="160" />
    <hkern u1="&#x2022;" u2="&#x104;" k="80" />
    <hkern u1="&#x2022;" u2="&#xdd;" k="160" />
    <hkern u1="&#x2022;" u2="&#xc6;" k="80" />
    <hkern u1="&#x2022;" u2="&#xc5;" k="80" />
    <hkern u1="&#x2022;" u2="&#xc4;" k="80" />
    <hkern u1="&#x2022;" u2="&#xc3;" k="80" />
    <hkern u1="&#x2022;" u2="&#xc2;" k="80" />
    <hkern u1="&#x2022;" u2="&#xc1;" k="80" />
    <hkern u1="&#x2022;" u2="&#xc0;" k="80" />
    <hkern u1="&#x2022;" u2="&#xba;" k="160" />
    <hkern u1="&#x2022;" u2="&#xb0;" k="160" />
    <hkern u1="&#x2022;" u2="&#xaa;" k="160" />
    <hkern u1="&#x2022;" u2="\" k="120" />
    <hkern u1="&#x2022;" u2="Z" k="50" />
    <hkern u1="&#x2022;" u2="Y" k="160" />
    <hkern u1="&#x2022;" u2="X" k="70" />
    <hkern u1="&#x2022;" u2="W" k="40" />
    <hkern u1="&#x2022;" u2="V" k="120" />
    <hkern u1="&#x2022;" u2="T" k="180" />
    <hkern u1="&#x2022;" u2="A" k="80" />
    <hkern u1="&#x2022;" u2="&#x2f;" k="80" />
    <hkern u1="&#x2022;" u2="&#x2e;" k="130" />
    <hkern u1="&#x2022;" u2="&#x2c;" k="130" />
    <hkern u1="&#x2022;" u2="&#x2a;" k="160" />
    <hkern u1="&#x2022;" u2="&#x27;" k="160" />
    <hkern u1="&#x2022;" u2="&#x26;" k="80" />
    <hkern u1="&#x2022;" u2="&#x22;" k="160" />
    <hkern u1="&#x2039;" u2="&#x2206;" k="80" />
    <hkern u1="&#x2039;" u2="&#x2122;" k="160" />
    <hkern u1="&#x2039;" u2="&#x201e;" k="130" />
    <hkern u1="&#x2039;" u2="&#x201d;" k="160" />
    <hkern u1="&#x2039;" u2="&#x201c;" k="160" />
    <hkern u1="&#x2039;" u2="&#x201a;" k="130" />
    <hkern u1="&#x2039;" u2="&#x2019;" k="160" />
    <hkern u1="&#x2039;" u2="&#x2018;" k="160" />
    <hkern u1="&#x2039;" u2="&#x17d;" k="50" />
    <hkern u1="&#x2039;" u2="&#x17b;" k="50" />
    <hkern u1="&#x2039;" u2="&#x179;" k="50" />
    <hkern u1="&#x2039;" u2="&#x178;" k="160" />
    <hkern u1="&#x2039;" u2="&#x104;" k="80" />
    <hkern u1="&#x2039;" u2="&#xdd;" k="160" />
    <hkern u1="&#x2039;" u2="&#xc6;" k="80" />
    <hkern u1="&#x2039;" u2="&#xc5;" k="80" />
    <hkern u1="&#x2039;" u2="&#xc4;" k="80" />
    <hkern u1="&#x2039;" u2="&#xc3;" k="80" />
    <hkern u1="&#x2039;" u2="&#xc2;" k="80" />
    <hkern u1="&#x2039;" u2="&#xc1;" k="80" />
    <hkern u1="&#x2039;" u2="&#xc0;" k="80" />
    <hkern u1="&#x2039;" u2="&#xba;" k="160" />
    <hkern u1="&#x2039;" u2="&#xb0;" k="160" />
    <hkern u1="&#x2039;" u2="&#xaa;" k="160" />
    <hkern u1="&#x2039;" u2="\" k="120" />
    <hkern u1="&#x2039;" u2="Z" k="50" />
    <hkern u1="&#x2039;" u2="Y" k="160" />
    <hkern u1="&#x2039;" u2="X" k="70" />
    <hkern u1="&#x2039;" u2="W" k="40" />
    <hkern u1="&#x2039;" u2="V" k="120" />
    <hkern u1="&#x2039;" u2="T" k="180" />
    <hkern u1="&#x2039;" u2="A" k="80" />
    <hkern u1="&#x2039;" u2="&#x2f;" k="80" />
    <hkern u1="&#x2039;" u2="&#x2e;" k="130" />
    <hkern u1="&#x2039;" u2="&#x2c;" k="130" />
    <hkern u1="&#x2039;" u2="&#x2a;" k="160" />
    <hkern u1="&#x2039;" u2="&#x27;" k="160" />
    <hkern u1="&#x2039;" u2="&#x26;" k="80" />
    <hkern u1="&#x2039;" u2="&#x22;" k="160" />
    <hkern u1="&#x203a;" u2="&#x2206;" k="80" />
    <hkern u1="&#x203a;" u2="&#x2122;" k="160" />
    <hkern u1="&#x203a;" u2="&#x201e;" k="130" />
    <hkern u1="&#x203a;" u2="&#x201d;" k="160" />
    <hkern u1="&#x203a;" u2="&#x201c;" k="160" />
    <hkern u1="&#x203a;" u2="&#x201a;" k="130" />
    <hkern u1="&#x203a;" u2="&#x2019;" k="160" />
    <hkern u1="&#x203a;" u2="&#x2018;" k="160" />
    <hkern u1="&#x203a;" u2="&#x17d;" k="50" />
    <hkern u1="&#x203a;" u2="&#x17b;" k="50" />
    <hkern u1="&#x203a;" u2="&#x179;" k="50" />
    <hkern u1="&#x203a;" u2="&#x178;" k="160" />
    <hkern u1="&#x203a;" u2="&#x104;" k="80" />
    <hkern u1="&#x203a;" u2="&#xdd;" k="160" />
    <hkern u1="&#x203a;" u2="&#xc6;" k="80" />
    <hkern u1="&#x203a;" u2="&#xc5;" k="80" />
    <hkern u1="&#x203a;" u2="&#xc4;" k="80" />
    <hkern u1="&#x203a;" u2="&#xc3;" k="80" />
    <hkern u1="&#x203a;" u2="&#xc2;" k="80" />
    <hkern u1="&#x203a;" u2="&#xc1;" k="80" />
    <hkern u1="&#x203a;" u2="&#xc0;" k="80" />
    <hkern u1="&#x203a;" u2="&#xba;" k="160" />
    <hkern u1="&#x203a;" u2="&#xb0;" k="160" />
    <hkern u1="&#x203a;" u2="&#xaa;" k="160" />
    <hkern u1="&#x203a;" u2="\" k="120" />
    <hkern u1="&#x203a;" u2="Z" k="50" />
    <hkern u1="&#x203a;" u2="Y" k="160" />
    <hkern u1="&#x203a;" u2="X" k="70" />
    <hkern u1="&#x203a;" u2="W" k="40" />
    <hkern u1="&#x203a;" u2="V" k="120" />
    <hkern u1="&#x203a;" u2="T" k="180" />
    <hkern u1="&#x203a;" u2="A" k="80" />
    <hkern u1="&#x203a;" u2="&#x2f;" k="80" />
    <hkern u1="&#x203a;" u2="&#x2e;" k="130" />
    <hkern u1="&#x203a;" u2="&#x2c;" k="130" />
    <hkern u1="&#x203a;" u2="&#x2a;" k="160" />
    <hkern u1="&#x203a;" u2="&#x27;" k="160" />
    <hkern u1="&#x203a;" u2="&#x26;" k="80" />
    <hkern u1="&#x203a;" u2="&#x22;" k="160" />
    <hkern u1="&#x2122;" u2="&#x2206;" k="200" />
    <hkern u1="&#x2122;" u2="&#x203a;" k="160" />
    <hkern u1="&#x2122;" u2="&#x2039;" k="160" />
    <hkern u1="&#x2122;" u2="&#x2022;" k="160" />
    <hkern u1="&#x2122;" u2="&#x201e;" k="200" />
    <hkern u1="&#x2122;" u2="&#x201a;" k="200" />
    <hkern u1="&#x2122;" u2="&#x2014;" k="160" />
    <hkern u1="&#x2122;" u2="&#x2013;" k="160" />
    <hkern u1="&#x2122;" u2="&#x178;" k="-40" />
    <hkern u1="&#x2122;" u2="&#x153;" k="100" />
    <hkern u1="&#x2122;" u2="&#x152;" k="40" />
    <hkern u1="&#x2122;" u2="&#x119;" k="100" />
    <hkern u1="&#x2122;" u2="&#x107;" k="100" />
    <hkern u1="&#x2122;" u2="&#x106;" k="40" />
    <hkern u1="&#x2122;" u2="&#x105;" k="68" />
    <hkern u1="&#x2122;" u2="&#x104;" k="200" />
    <hkern u1="&#x2122;" u2="&#xf8;" k="100" />
    <hkern u1="&#x2122;" u2="&#xf6;" k="100" />
    <hkern u1="&#x2122;" u2="&#xf5;" k="100" />
    <hkern u1="&#x2122;" u2="&#xf4;" k="100" />
    <hkern u1="&#x2122;" u2="&#xf3;" k="100" />
    <hkern u1="&#x2122;" u2="&#xf2;" k="100" />
    <hkern u1="&#x2122;" u2="&#xf0;" k="100" />
    <hkern u1="&#x2122;" u2="&#xeb;" k="100" />
    <hkern u1="&#x2122;" u2="&#xea;" k="100" />
    <hkern u1="&#x2122;" u2="&#xe9;" k="100" />
    <hkern u1="&#x2122;" u2="&#xe8;" k="100" />
    <hkern u1="&#x2122;" u2="&#xe7;" k="100" />
    <hkern u1="&#x2122;" u2="&#xe6;" k="68" />
    <hkern u1="&#x2122;" u2="&#xe5;" k="68" />
    <hkern u1="&#x2122;" u2="&#xe4;" k="68" />
    <hkern u1="&#x2122;" u2="&#xe3;" k="68" />
    <hkern u1="&#x2122;" u2="&#xe2;" k="68" />
    <hkern u1="&#x2122;" u2="&#xe1;" k="68" />
    <hkern u1="&#x2122;" u2="&#xe0;" k="68" />
    <hkern u1="&#x2122;" u2="&#xdd;" k="-40" />
    <hkern u1="&#x2122;" u2="&#xd8;" k="40" />
    <hkern u1="&#x2122;" u2="&#xd6;" k="40" />
    <hkern u1="&#x2122;" u2="&#xd5;" k="40" />
    <hkern u1="&#x2122;" u2="&#xd4;" k="40" />
    <hkern u1="&#x2122;" u2="&#xd3;" k="40" />
    <hkern u1="&#x2122;" u2="&#xd2;" k="40" />
    <hkern u1="&#x2122;" u2="&#xc7;" k="40" />
    <hkern u1="&#x2122;" u2="&#xc6;" k="200" />
    <hkern u1="&#x2122;" u2="&#xc5;" k="200" />
    <hkern u1="&#x2122;" u2="&#xc4;" k="200" />
    <hkern u1="&#x2122;" u2="&#xc3;" k="200" />
    <hkern u1="&#x2122;" u2="&#xc2;" k="200" />
    <hkern u1="&#x2122;" u2="&#xc1;" k="200" />
    <hkern u1="&#x2122;" u2="&#xc0;" k="200" />
    <hkern u1="&#x2122;" u2="&#xbb;" k="160" />
    <hkern u1="&#x2122;" u2="&#xb7;" k="160" />
    <hkern u1="&#x2122;" u2="&#xad;" k="160" />
    <hkern u1="&#x2122;" u2="&#xab;" k="160" />
    <hkern u1="&#x2122;" u2="q" k="100" />
    <hkern u1="&#x2122;" u2="o" k="100" />
    <hkern u1="&#x2122;" u2="e" k="100" />
    <hkern u1="&#x2122;" u2="d" k="100" />
    <hkern u1="&#x2122;" u2="c" k="100" />
    <hkern u1="&#x2122;" u2="a" k="68" />
    <hkern u1="&#x2122;" u2="\" k="-40" />
    <hkern u1="&#x2122;" u2="Y" k="-40" />
    <hkern u1="&#x2122;" u2="W" k="-40" />
    <hkern u1="&#x2122;" u2="V" k="-40" />
    <hkern u1="&#x2122;" u2="Q" k="40" />
    <hkern u1="&#x2122;" u2="O" k="40" />
    <hkern u1="&#x2122;" u2="G" k="40" />
    <hkern u1="&#x2122;" u2="C" k="40" />
    <hkern u1="&#x2122;" u2="A" k="200" />
    <hkern u1="&#x2122;" u2="&#x40;" k="40" />
    <hkern u1="&#x2122;" u2="&#x2f;" k="200" />
    <hkern u1="&#x2122;" u2="&#x2e;" k="200" />
    <hkern u1="&#x2122;" u2="&#x2d;" k="160" />
    <hkern u1="&#x2122;" u2="&#x2c;" k="200" />
    <hkern u1="&#x2122;" u2="&#x26;" k="200" />
    <hkern u1="&#x2206;" u2="&#x2122;" k="200" />
    <hkern u1="&#x2206;" u2="&#x203a;" k="80" />
    <hkern u1="&#x2206;" u2="&#x2039;" k="80" />
    <hkern u1="&#x2206;" u2="&#x2022;" k="80" />
    <hkern u1="&#x2206;" u2="&#x201d;" k="200" />
    <hkern u1="&#x2206;" u2="&#x201c;" k="200" />
    <hkern u1="&#x2206;" u2="&#x2019;" k="200" />
    <hkern u1="&#x2206;" u2="&#x2018;" k="200" />
    <hkern u1="&#x2206;" u2="&#x2014;" k="80" />
    <hkern u1="&#x2206;" u2="&#x2013;" k="80" />
    <hkern u1="&#x2206;" u2="&#x178;" k="200" />
    <hkern u1="&#x2206;" u2="&#x152;" k="60" />
    <hkern u1="&#x2206;" u2="&#x106;" k="60" />
    <hkern u1="&#x2206;" u2="&#xdd;" k="200" />
    <hkern u1="&#x2206;" u2="&#xdc;" k="50" />
    <hkern u1="&#x2206;" u2="&#xdb;" k="50" />
    <hkern u1="&#x2206;" u2="&#xda;" k="50" />
    <hkern u1="&#x2206;" u2="&#xd9;" k="50" />
    <hkern u1="&#x2206;" u2="&#xd8;" k="60" />
    <hkern u1="&#x2206;" u2="&#xd6;" k="60" />
    <hkern u1="&#x2206;" u2="&#xd5;" k="60" />
    <hkern u1="&#x2206;" u2="&#xd4;" k="60" />
    <hkern u1="&#x2206;" u2="&#xd3;" k="60" />
    <hkern u1="&#x2206;" u2="&#xd2;" k="60" />
    <hkern u1="&#x2206;" u2="&#xc7;" k="60" />
    <hkern u1="&#x2206;" u2="&#xbb;" k="80" />
    <hkern u1="&#x2206;" u2="&#xba;" k="200" />
    <hkern u1="&#x2206;" u2="&#xb9;" k="220" />
    <hkern u1="&#x2206;" u2="&#xb7;" k="80" />
    <hkern u1="&#x2206;" u2="&#xb3;" k="220" />
    <hkern u1="&#x2206;" u2="&#xb2;" k="220" />
    <hkern u1="&#x2206;" u2="&#xb0;" k="200" />
    <hkern u1="&#x2206;" u2="&#xad;" k="80" />
    <hkern u1="&#x2206;" u2="&#xab;" k="80" />
    <hkern u1="&#x2206;" u2="&#xaa;" k="200" />
    <hkern u1="&#x2206;" u2="y" k="100" />
    <hkern u1="&#x2206;" u2="v" k="100" />
    <hkern u1="&#x2206;" u2="\" k="200" />
    <hkern u1="&#x2206;" u2="Y" k="200" />
    <hkern u1="&#x2206;" u2="W" k="120" />
    <hkern u1="&#x2206;" u2="V" k="200" />
    <hkern u1="&#x2206;" u2="U" k="50" />
    <hkern u1="&#x2206;" u2="T" k="160" />
    <hkern u1="&#x2206;" u2="Q" k="60" />
    <hkern u1="&#x2206;" u2="O" k="60" />
    <hkern u1="&#x2206;" u2="J" k="-60" />
    <hkern u1="&#x2206;" u2="G" k="60" />
    <hkern u1="&#x2206;" u2="C" k="60" />
    <hkern u1="&#x2206;" u2="&#x40;" k="60" />
    <hkern u1="&#x2206;" u2="&#x3f;" k="70" />
    <hkern u1="&#x2206;" u2="&#x2d;" k="80" />
    <hkern u1="&#x2206;" u2="&#x2a;" k="200" />
    <hkern u1="&#x2206;" u2="&#x27;" k="200" />
    <hkern u1="&#x2206;" u2="&#x22;" k="200" />
  </font>
</defs></svg>
