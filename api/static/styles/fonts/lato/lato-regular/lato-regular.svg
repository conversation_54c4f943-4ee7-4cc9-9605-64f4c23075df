<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg>
<metadata>
Created by FontForge 20120731 at Thu Sep  1 13:59:47 2016
 By www-data
Copyright (c) 2010-2011 by tyPoland Lukasz Dziedzic with Reserved Font Name "Lato". Licensed under the SIL Open Font License, Version 1.1.
</metadata>
<defs>
<font id="Lato-Regular" horiz-adv-x="1160" >
  <font-face 
    font-family="Lato"
    font-weight="400"
    font-stretch="normal"
    units-per-em="2000"
    panose-1="2 15 5 2 2 2 4 3 2 3"
    ascent="1610"
    descent="-390"
    x-height="1013"
    cap-height="1433"
    bbox="-188 -365 2233 1837"
    underline-thickness="120"
    underline-position="-80"
    unicode-range="U+000D-FB02"
  />
<missing-glyph horiz-adv-x="1063" 
d="M250 1141q25 22 53.5 41t62.5 33.5t73.5 23t85.5 8.5q63 0 114.5 -17.5t88 -49.5t57 -77.5t20.5 -101.5q0 -55 -15 -95t-37.5 -69.5t-49.5 -51t-51 -40.5t-41.5 -37.5t-21.5 -41.5l-17 -105h-122l-12 117q-4 34 9 59.5t35.5 47t50 41.5t51.5 43t40 52t16 69
q0 51 -36.5 81t-93.5 30q-41 0 -69 -9t-48 -20t-34 -20t-25 -9q-25 0 -37 22zM396 292q0 47 31 79t79 32q22 0 42 -8.5t34.5 -23.5t23 -35.5t8.5 -43.5t-8.5 -43t-23 -35t-34.5 -23.5t-42 -8.5q-48 0 -79 31.5t-31 78.5zM45 1433h974v-1433h-974v1433zM95 54h867v1324h-867
v-1324z" />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="1140" 
d="M186 0v861l-112 13q-21 5 -34.5 15.5t-13.5 30.5v73h160v56q0 93 29 170.5t87 133.5t144.5 87t201.5 31q38 0 77.5 -5t68.5 -15l-6 -93q-2 -13 -12 -16.5t-29 -3.5q-11 0 -23 0.5t-27 0.5q-177 0 -257.5 -73.5t-80.5 -221.5v-51h612v-993h-178v864h-428v-864h-179z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="1188" 
d="M186 0v861l-112 13q-21 5 -34.5 15.5t-13.5 30.5v73h160v54q0 84 26 159.5t78 131.5t130 89t182 33q83 0 159.5 -6.5t148.5 -6.5h100v-1447h-178v1322q-54 2 -108.5 5t-94.5 3q-130 0 -200 -74.5t-70 -208.5v-54h264v-129h-258v-864h-179z" />
    <glyph glyph-name=".notdef" horiz-adv-x="1063" 
d="M250 1141q25 22 53.5 41t62.5 33.5t73.5 23t85.5 8.5q63 0 114.5 -17.5t88 -49.5t57 -77.5t20.5 -101.5q0 -55 -15 -95t-37.5 -69.5t-49.5 -51t-51 -40.5t-41.5 -37.5t-21.5 -41.5l-17 -105h-122l-12 117q-4 34 9 59.5t35.5 47t50 41.5t51.5 43t40 52t16 69
q0 51 -36.5 81t-93.5 30q-41 0 -69 -9t-48 -20t-34 -20t-25 -9q-25 0 -37 22zM396 292q0 47 31 79t79 32q22 0 42 -8.5t34.5 -23.5t23 -35.5t8.5 -43.5t-8.5 -43t-23 -35t-34.5 -23.5t-42 -8.5q-48 0 -79 31.5t-31 78.5zM45 1433h974v-1433h-974v1433zM95 54h867v1324h-867
v-1324z" />
    <glyph glyph-name="NULL" horiz-adv-x="0" 
 />
    <glyph glyph-name="NULL" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" unicode="&#xd;" horiz-adv-x="0" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="386" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="686" 
d="M430 1433v-572q0 -45 -1.5 -88t-4.5 -86.5t-7.5 -89t-10.5 -97.5h-121q-6 52 -10.5 97.5t-7.5 89t-4.5 86.5t-1.5 88v572h169zM218 110q0 26 9.5 49t26 40t39.5 27t49 10t49 -10t40 -27t26.5 -40t9.5 -49q0 -27 -9.5 -49.5t-26.5 -39.5t-40 -26.5t-49 -9.5t-49 9.5
t-39.5 26.5t-26 39.5t-9.5 49.5z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="794" 
d="M307 1433v-290l-16 -155q-3 -32 -17 -49.5t-45 -17.5q-26 0 -40.5 17.5t-20.5 49.5l-16 155v290h155zM640 1433v-290l-16 -155q-3 -32 -17 -49.5t-45 -17.5q-26 0 -40.5 17.5t-20.5 49.5l-16 155v290h155z" />
    <glyph glyph-name="numbersign" unicode="#" 
d="M790 423l-84 -423h-81q-23 0 -39 17t-16 44q0 4 0.5 7.5t1.5 8.5l71 346h-247l-71 -355q-8 -37 -30.5 -52.5t-51.5 -15.5h-79l85 423h-146q-23 0 -36 11.5t-13 39.5q0 5 0.5 11t1.5 12l8 57h204l65 326h-232l13 74q5 29 23 43t57 14h158l72 358q6 30 27.5 47t51.5 17h80
l-84 -422h247l84 422h79q25 0 41.5 -15t16.5 -39q0 -8 -1 -13l-73 -355h212l-13 -75q-5 -29 -23.5 -42.5t-56.5 -13.5h-138l-65 -326h179q24 0 37 -11.5t13 -40.5q0 -5 -0.5 -10.5t-1.5 -11.5l-9 -57h-237zM415 554h247l65 326h-247z" />
    <glyph glyph-name="dollar" unicode="$" 
d="M498 -12q-121 11 -220.5 59.5t-171.5 123.5l53 82q7 11 20 18t27 7q19 0 43 -19t59 -43.5t83.5 -47.5t116.5 -31l37 531q-70 21 -137.5 47.5t-121 69t-86 107t-32.5 161.5q0 73 28.5 142.5t83 123.5t134.5 88.5t184 38.5l10 144q2 19 15 34t35 15h66l-14 -198
q105 -13 181 -54t136 -99l-43 -66q-20 -30 -46 -30q-14 0 -34.5 12.5t-49.5 29t-67 33t-87 23.5l-33 -484q72 -22 142 -48t126 -67t90.5 -102.5t34.5 -153.5q0 -90 -30 -169t-87.5 -139t-141 -97.5t-190.5 -43.5l-12 -176q-2 -19 -15.5 -33.5t-34.5 -14.5h-66zM891 407
q0 50 -18.5 86t-50.5 62t-75 45t-92 35l-34 -498q65 6 115.5 28.5t85 57.5t52 81.5t17.5 102.5zM336 1071q0 -48 17 -83.5t47 -62.5t70 -47t87 -36l30 451q-65 -6 -112.5 -26t-78 -50t-45.5 -67.5t-15 -78.5z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="1572" 
d="M707 1087q0 -84 -26 -150.5t-69.5 -112t-101.5 -69.5t-121 -24q-68 0 -125.5 24t-100.5 69.5t-67 112t-24 150.5q0 86 24 153t67 113t100.5 70t125.5 24q67 0 125.5 -24t101 -70t67 -113t24.5 -153zM568 1087q0 66 -14 112t-38.5 75.5t-57 42.5t-69.5 13t-69.5 -13
t-56.5 -42.5t-37.5 -75.5t-13.5 -112q0 -65 13.5 -110.5t37.5 -74t56.5 -41t69.5 -12.5t69.5 12.5t57 41t38.5 74t14 110.5zM1208 1397q13 17 27.5 26.5t38.5 9.5h128l-1047 -1404q-10 -13 -24 -21t-33 -8h-132zM1499 338q0 -84 -26 -150t-69.5 -111.5t-101 -69.5
t-120.5 -24q-68 0 -125.5 24t-100.5 69.5t-67 111.5t-24 150q0 86 24 153.5t67 113.5t100.5 70t125.5 24q67 0 125 -24t101 -70t67 -113.5t24 -153.5zM1361 338q0 66 -14 112.5t-38.5 75.5t-57 42t-69.5 13t-69.5 -13t-56.5 -42t-37.5 -75.5t-13.5 -112.5q0 -65 13.5 -110
t37.5 -73.5t56.5 -41t69.5 -12.5t69.5 12.5t57 41t38.5 73.5t14 110z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="1406" 
d="M660 1449q79 0 144 -25.5t112.5 -67.5t75 -97.5t31.5 -115.5l-111 -22q-5 -1 -9 -1q-13 0 -24.5 7t-16.5 25q-7 26 -22.5 54t-40 51t-59 38t-80.5 15q-50 0 -90 -16t-68.5 -44.5t-44 -67t-15.5 -83.5q0 -35 8.5 -67t25.5 -64.5t44 -67t65 -72.5l412 -419
q38 67 60.5 140.5t30.5 147.5q2 19 12 30t28 11h110q-2 -115 -37 -226t-101 -208l300 -304h-172q-29 0 -47 7t-40 29l-144 145q-94 -91 -216.5 -144t-269.5 -53q-80 0 -157 27t-137.5 78.5t-97.5 125.5t-37 167q0 70 23.5 132.5t65 115.5t98.5 95t125 72q-61 77 -90 150
t-29 151q0 73 26.5 137t76.5 111.5t121.5 75t160.5 27.5zM263 396q0 -65 24 -114.5t63 -83.5t89 -51.5t102 -17.5q112 0 201 41t157 109l-423 427q-106 -57 -159.5 -136.5t-53.5 -173.5z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="460" 
d="M307 1433v-290l-16 -155q-3 -32 -17 -49.5t-45 -17.5q-26 0 -40.5 17.5t-20.5 49.5l-16 155v290h155z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="600" 
d="M289 629q0 -214 55 -415.5t159 -384.5q6 -11 8 -19t2 -16q0 -14 -7 -23t-18 -16l-79 -48q-75 115 -127.5 228t-85 227t-47.5 229.5t-15 237.5q0 121 15 237t47.5 229.5t85 226.5t127.5 229l79 -49q11 -7 18 -16t7 -23q0 -15 -10 -34q-105 -182 -159.5 -384t-54.5 -416z
" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="600" 
d="M298 629q0 214 -54.5 416t-159.5 384q-10 19 -10 34q0 14 7 23t18 16l79 49q75 -116 127.5 -229t85 -226.5t47.5 -229.5t15 -237q0 -122 -15 -237.5t-47.5 -229.5t-85 -227t-127.5 -228l-79 48q-11 7 -18 16t-7 23q0 8 2 16t8 19q104 183 159 384.5t55 415.5z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="800" 
d="M354 863v197q0 19 2.5 36t9.5 33q-20 -25 -53 -45l-172 -99l-44 75l172 100q36 21 73 24q-20 2 -37.5 7.5t-35.5 17.5l-173 101l44 75l173 -100q35 -20 57 -52q-9 18 -12.5 36.5t-3.5 38.5v198h88v-197q0 -41 -14 -72q11 15 24.5 26t30.5 22l172 99l44 -75l-172 -100
q-17 -11 -33.5 -17t-34.5 -8q18 -2 34.5 -7.5t33.5 -16.5l173 -101l-44 -75l-173 100q-18 11 -32 22t-25 27q16 -33 16 -72v-198h-88z" />
    <glyph glyph-name="plus" unicode="+" 
d="M651 1166v-427h407v-135h-407v-430h-146v430h-405v135h405v427h146z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="424" 
d="M94 123q0 23 8.5 43.5t24 36t37.5 24.5t48 9q30 0 53.5 -11t39 -30.5t23.5 -45t8 -55.5q0 -45 -13 -93.5t-37 -96t-59.5 -92.5t-80.5 -83l-30 29q-13 12 -13 28q0 13 14 27q10 11 25.5 29.5t31.5 42.5t29.5 53t19.5 62h-13q-26 0 -47 9t-36.5 25.5t-24 39t-8.5 49.5z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="694" 
d="M100 675h494v-151h-494v151z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="424" 
d="M88 110q0 26 9.5 49t26 40t39.5 27t49 10t49 -10t40 -27t26.5 -40t9.5 -49q0 -27 -9.5 -49.5t-26.5 -39.5t-40 -26.5t-49 -9.5t-49 9.5t-39.5 26.5t-26 39.5t-9.5 49.5z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="746" 
d="M161 -21q-14 -35 -41.5 -52t-56.5 -17h-75l601 1497q13 32 37 49t57 17h75z" />
    <glyph glyph-name="zero" unicode="0" 
d="M1100 716q0 -188 -40.5 -325.5t-110.5 -227.5t-165.5 -134t-204.5 -44q-110 0 -204.5 44t-164.5 134t-110 227.5t-40 325.5t40 326t110 228.5t164.5 134.5t204.5 44q109 0 204.5 -44t165.5 -134.5t110.5 -228.5t40.5 -326zM915 716q0 164 -27.5 275.5t-74 179.5
t-107.5 97.5t-127 29.5t-127 -29.5t-107 -97.5t-73.5 -179.5t-27.5 -275.5t27.5 -275t73.5 -179t107 -97.5t127 -29.5t127 29.5t107.5 97.5t74 179t27.5 275z" />
    <glyph glyph-name="one" unicode="1" 
d="M287 136h308v977q0 44 3 89l-256 -219q-10 -8 -20 -11.5t-19 -3.5q-15 0 -27 6.5t-18 15.5l-56 77l426 369h145v-1300h282v-136h-768v136z" />
    <glyph glyph-name="two" unicode="2" 
d="M601 1449q91 0 170 -27t136.5 -78.5t90.5 -125.5t33 -168q0 -80 -24 -148t-65 -130.5t-94.5 -121t-113.5 -119.5l-377 -386q40 11 81 17.5t79 6.5h480q29 0 46 -17t17 -44v-108h-956v61q0 19 7.5 39t24.5 37l459 461q57 58 104 111.5t80.5 107.5t51.5 109.5t18 118.5
t-20 110.5t-55 78.5t-82 46.5t-101 15.5t-100 -16t-81.5 -44.5t-60 -67.5t-34.5 -86q-8 -29 -24 -42t-42 -13q-5 0 -10.5 0.5t-12.5 1.5l-93 16q14 98 54 173.5t101.5 126.5t141 77.5t171.5 26.5z" />
    <glyph glyph-name="three" unicode="3" 
d="M620 1449q91 0 168 -26t132.5 -74t86.5 -116t31 -151q0 -68 -17.5 -121.5t-50 -94t-78.5 -68.5t-103 -45q140 -37 210.5 -124t70.5 -218q0 -99 -37.5 -178t-102.5 -134.5t-151.5 -85t-185.5 -29.5q-114 0 -195 28.5t-137 78.5t-92 118.5t-61 148.5l76 32q21 9 42 9
q20 0 35.5 -8.5t23.5 -26.5q2 -4 4 -8.5t4 -9.5q14 -29 34 -65.5t54 -68.5t84.5 -54t125.5 -22t131.5 24.5t94 63.5t56.5 87t19 95q0 58 -15.5 106t-56 83t-112 55t-183.5 20v129q91 1 155.5 20t105.5 52t59.5 79t18.5 102q0 62 -19.5 108t-53.5 76t-80.5 45t-100.5 15
t-100 -16t-81.5 -44.5t-59.5 -68t-36 -85.5q-8 -29 -24 -42t-41 -13q-5 0 -10.5 0.5t-12.5 1.5l-93 16q14 98 54 173.5t101.5 126.5t141 77.5t171.5 26.5z" />
    <glyph glyph-name="four" unicode="4" 
d="M903 517h217v-102q0 -16 -9.5 -27t-29.5 -11h-178v-377h-157v377h-635q-20 0 -34.5 11.5t-18.5 28.5l-18 91l697 925h166v-916zM746 1108q0 26 1.5 56t6.5 62l-521 -709h513v591z" />
    <glyph glyph-name="five" unicode="5" 
d="M978 1355q0 -38 -24 -62.5t-81 -24.5h-450l-66 -376q112 24 207 24q112 0 197.5 -33t143.5 -91t87.5 -137t29.5 -172q0 -114 -40 -206t-109.5 -157.5t-163.5 -100.5t-203 -35q-63 0 -121 12.5t-108 33.5t-93 48t-76 57l54 76q18 26 48 26q19 0 44.5 -15.5t61.5 -34.5
t84.5 -34.5t115.5 -15.5q75 0 135 24t102.5 68.5t65.5 106.5t23 139q0 67 -19.5 121t-59 92t-98.5 59t-138 21q-54 0 -112 -9t-120 -29l-112 33l116 670h679v-78z" />
    <glyph glyph-name="six" unicode="6" 
d="M650 878q86 0 163 -28.5t135 -83t92 -134t34 -181.5q0 -99 -36 -184t-100.5 -148t-155.5 -99t-200 -36q-108 0 -195.5 34.5t-149.5 98t-95.5 154t-33.5 202.5q0 94 42 200t133 228l363 489q14 18 39 30.5t57 12.5h158l-497 -629q51 35 113 54.5t134 19.5zM280 442
q0 -69 20 -126t58.5 -98t94 -63.5t126.5 -22.5q72 0 130 23t99.5 64t64 97t22.5 122q0 70 -22 126.5t-62 96t-96 60.5t-123 21q-72 0 -130 -24.5t-98.5 -66t-62 -96t-21.5 -113.5z" />
    <glyph glyph-name="seven" unicode="7" 
d="M1084 1433v-80q0 -34 -7.5 -56t-15.5 -37l-593 -1197q-13 -26 -36 -44.5t-62 -18.5h-127l602 1182q13 25 26.5 46t30.5 40h-748q-17 0 -30.5 13.5t-13.5 30.5v121h974z" />
    <glyph glyph-name="eight" unicode="8" 
d="M579 -16q-107 0 -196 28.5t-153 81.5t-99 128.5t-35 169.5q0 138 72 227.5t206 127.5q-113 42 -170.5 125.5t-57.5 199.5q0 79 31 148t88 120t136.5 80t177.5 29q97 0 177 -29t137 -80t88 -120t31 -148q0 -116 -58 -199.5t-170 -125.5q134 -38 206 -127.5t72 -227.5
q0 -94 -35.5 -169.5t-99 -128.5t-152.5 -81.5t-196 -28.5zM579 126q70 0 125.5 19.5t94 55t59 85t20.5 109.5q0 74 -24.5 126.5t-66 85.5t-95.5 48.5t-113 15.5t-113 -15.5t-95.5 -48.5t-66 -85.5t-24.5 -126.5q0 -60 20.5 -109.5t59 -85t94 -55t125.5 -19.5zM579 814
q70 0 119.5 21.5t80.5 57t45 81.5t14 95q0 50 -16.5 94t-49 77t-81 52t-112.5 19t-112.5 -19t-81 -52t-49 -77t-16.5 -94q0 -49 14 -95t45 -81.5t80.5 -57t119.5 -21.5z" />
    <glyph glyph-name="nine" unicode="9" 
d="M549 588q-81 0 -153.5 27t-127.5 79.5t-87.5 129t-32.5 175.5q0 94 35 176t98 143t150 96t191 35q103 0 187 -34t144 -95t92.5 -146t32.5 -188q0 -62 -11.5 -117.5t-33.5 -108.5t-52.5 -105.5t-69.5 -108.5l-349 -504q-13 -19 -37 -30.5t-55 -11.5h-164l436 571
q22 29 41 55t36 52q-55 -44 -124 -67t-146 -23zM907 1007q0 67 -21.5 121.5t-59.5 92.5t-90.5 58.5t-115.5 20.5q-66 0 -120.5 -21.5t-93 -59.5t-59.5 -91t-21 -116q0 -68 19.5 -121.5t56 -90.5t89 -56t117.5 -19q72 0 127.5 23.5t93.5 62.5t58 90t20 106z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="504" 
d="M128 110q0 26 9.5 49t26 40t39.5 27t49 10t49 -10t40 -27t26.5 -40t9.5 -49q0 -27 -9.5 -49.5t-26.5 -39.5t-40 -26.5t-49 -9.5t-49 9.5t-39.5 26.5t-26 39.5t-9.5 49.5zM128 860q0 26 9.5 49t26 40t39.5 27t49 10t49 -10t40 -27t26.5 -40t9.5 -49q0 -27 -9.5 -49.5
t-26.5 -39.5t-40 -26.5t-49 -9.5t-49 9.5t-39.5 26.5t-26 39.5t-9.5 49.5z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="504" 
d="M134 123q0 23 8.5 43.5t24 36t37.5 24.5t48 9q30 0 53.5 -11t39 -30.5t23.5 -45t8 -55.5q0 -45 -13 -93.5t-37 -96t-59.5 -92.5t-80.5 -83l-30 29q-13 12 -13 28q0 13 14 27q10 11 25.5 29.5t31.5 42.5t29.5 53t19.5 62h-13q-26 0 -47 9t-36.5 25.5t-24 39t-8.5 49.5z
M128 860q0 26 9.5 49t26 40t39.5 27t49 10t49 -10t40 -27t26.5 -40t9.5 -49q0 -27 -9.5 -49.5t-26.5 -39.5t-40 -26.5t-49 -9.5t-49 9.5t-39.5 26.5t-26 39.5t-9.5 49.5z" />
    <glyph glyph-name="less" unicode="&#x3c;" 
d="M148 710l774 401v-127q0 -17 -8 -29.5t-28 -22.5l-449 -228q-20 -11 -42.5 -18.5t-47.5 -13.5q25 -5 47.5 -13t42.5 -18l449 -227q20 -10 28 -23t8 -29v-128l-774 402v74z" />
    <glyph glyph-name="equal" unicode="=" 
d="M150 574h859v-135h-859v135zM150 909h859v-135h-859v135z" />
    <glyph glyph-name="greater" unicode="&#x3e;" 
d="M238 234v128q0 16 8 29t28 23l449 227q20 10 41.5 18t46.5 13q-25 6 -46.5 13.5t-41.5 18.5l-449 228q-20 10 -28 22.5t-8 29.5v127l773 -401v-74z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="796" 
d="M34 1305q31 29 68.5 55t82 46t96 31.5t111.5 11.5q79 0 146.5 -23t116.5 -65t77 -102t28 -135q0 -76 -22.5 -131t-57 -96.5t-75.5 -72t-76.5 -57.5t-61 -54t-29.5 -60l-18 -153h-122l-12 166v11q0 42 22.5 74.5t57 61t74 57t74 63t57 78.5t22.5 104q0 43 -17 78t-46 59.5
t-68.5 37.5t-84.5 13q-61 0 -104.5 -15t-73.5 -33t-48.5 -33t-30.5 -15q-25 0 -39 23zM230 110q0 26 9.5 49t26 40t39.5 27t49 10t49 -10t40 -27t26.5 -40t9.5 -49q0 -27 -9.5 -49.5t-26.5 -39.5t-40 -26.5t-49 -9.5t-49 9.5t-39.5 26.5t-26 39.5t-9.5 49.5z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="1644" 
d="M1167 186q-78 0 -127 37.5t-62 115.5q-58 -81 -126 -116t-146 -35q-60 0 -104 20.5t-73.5 57t-44 86.5t-14.5 108q0 85 32.5 171.5t96.5 156t159.5 113.5t220.5 44q67 0 117.5 -10.5t95.5 -30.5l-93 -361q-19 -75 -19 -124q0 -36 9 -59.5t24.5 -37t36 -18.5t43.5 -5
q49 0 93 28t77.5 79t53 122.5t19.5 158.5q0 138 -44.5 242t-122 173.5t-183 104t-227.5 34.5q-134 0 -251 -51t-204 -141t-137 -213t-50 -268q0 -170 53.5 -298.5t146 -215.5t217 -131t267.5 -44q152 0 268.5 33t201.5 84q15 9 27 9q21 0 31 -24l25 -66
q-107 -72 -243.5 -113t-309.5 -41t-322 55t-258.5 158.5t-172 253.5t-62.5 340q0 109 27.5 210t77.5 188.5t120 160t156 124t185 80t207 28.5q92 0 180 -20t166.5 -59t144.5 -96.5t113.5 -132t74 -166t26.5 -198.5q0 -108 -30.5 -199.5t-84 -158.5t-126 -105t-156.5 -38z
M741 306q31 0 62.5 10t61 35t54 68t41.5 108l76 295q-39 9 -85 9q-75 0 -137.5 -31.5t-107.5 -82.5t-70.5 -117t-25.5 -135q0 -72 33 -115.5t98 -43.5z" />
    <glyph glyph-name="A" unicode="A" horiz-adv-x="1360" 
d="M1353 0h-150q-26 0 -42 13t-24 33l-134 346h-643l-134 -346q-7 -18 -24 -32t-42 -14h-150l573 1433h197zM414 532h535l-225 583q-22 54 -43 135q-11 -41 -21.5 -75.5t-20.5 -60.5z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="1294" 
d="M174 0v1433h457q132 0 227.5 -26t157 -74t91 -117.5t29.5 -156.5q0 -53 -16.5 -102t-50 -91t-84 -75t-118.5 -54q157 -31 237 -113t80 -216q0 -91 -33.5 -166t-98 -129t-158 -83.5t-213.5 -29.5h-507zM368 653v-499h310q83 0 142.5 19t98 53.5t56.5 82t18 104.5
q0 111 -78.5 175.5t-237.5 64.5h-309zM368 791h256q82 0 142 18t99.5 50t58.5 77.5t19 99.5q0 126 -76 185t-236 59h-263v-489z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="1370" 
d="M1184 296q16 0 29 -13l76 -83q-88 -102 -213.5 -159t-302.5 -57q-155 0 -281 53.5t-215 150t-138 231.5t-49 297t52.5 297t147.5 232t227.5 150.5t292.5 53.5q158 0 272.5 -49t203.5 -133l-63 -89q-7 -10 -16 -16.5t-26 -6.5q-13 0 -27.5 9.5t-34.5 23.5t-47 30t-64 30
t-86 23.5t-113 9.5q-115 0 -210.5 -39.5t-164.5 -112.5t-107.5 -178t-38.5 -235q0 -134 38.5 -239t105 -177.5t157.5 -110.5t196 -38q64 0 115 7.5t94.5 23.5t81 40.5t75.5 58.5q17 15 33 15z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="1506" 
d="M1416 716q0 -161 -51 -293t-144 -226t-223 -145.5t-288 -51.5h-536v1433h536q158 0 288 -51.5t223 -146t144 -226.5t51 -293zM1217 716q0 132 -36 236t-102 176t-160 110t-209 38h-341v-1119h341q115 0 209 38t160 109.5t102 175.5t36 236z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="1162" 
d="M1057 1433v-158h-688v-476h557v-152h-557v-489h688v-158h-883v1433h883z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="1132" 
d="M1057 1433v-158h-688v-501h588v-158h-588v-616h-195v1433h883z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="1468" 
d="M813 141q58 0 106.5 5.5t91.5 16.5t81 26.5t76 35.5v316h-222q-19 0 -30.5 11t-11.5 27v110h440v-550q-54 -39 -112.5 -68t-125 -48.5t-142.5 -29t-165 -9.5q-156 0 -286 53.5t-224 150t-146.5 231.5t-52.5 297q0 164 51.5 299t147 231.5t231 149.5t303.5 53
q85 0 158 -12.5t135.5 -36t115.5 -57t99 -75.5l-55 -88q-17 -27 -44 -27q-16 0 -35 11q-25 14 -56 34t-75.5 38.5t-105 31.5t-143.5 13q-121 0 -219 -39.5t-167 -113t-106 -178t-37 -234.5q0 -136 38.5 -242.5t108.5 -180.5t166 -113t212 -39z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="1512" 
d="M1336 0h-195v652h-772v-652h-195v1433h195v-639h772v639h195v-1433z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="614" 
d="M404 0h-194v1433h194v-1433z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="888" 
d="M713 495q0 -120 -29.5 -215t-87 -160.5t-141.5 -100.5t-193 -35q-97 0 -202 28q2 29 5 57.5t6 56.5q2 17 12.5 27.5t31.5 10.5q18 0 48 -9t80 -9q66 0 117.5 20t87 62t54 107.5t18.5 155.5v942h193v-938z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="1362" 
d="M387 805h73q38 0 60.5 9.5t42.5 32.5l477 540q22 25 42.5 35.5t52.5 10.5h165l-546 -617q-21 -23 -39.5 -39t-39.5 -26q28 -9 49 -27t44 -45l570 -679h-168q-19 0 -32 3t-22.5 8t-17.5 13t-16 17l-495 569q-11 12 -20.5 20.5t-22 14.5t-29 8.5t-40.5 2.5h-88v-656h-193
v1433h193v-628z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="1028" 
d="M368 163h620v-163h-814v1433h194v-1270z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="1840" 
d="M879 518q14 -24 24.5 -50.5t20.5 -53.5q10 28 21 53.5t25 51.5l485 881q13 23 27 28t40 5h143v-1433h-170v1053q0 21 1 45t3 49l-491 -896q-25 -45 -70 -45h-28q-45 0 -70 45l-502 899q3 -26 4.5 -51t1.5 -46v-1053h-170v1433h143q26 0 40 -5t27 -28l495 -882v0z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="1512" 
d="M274 1433q26 0 38.5 -6.5t28.5 -26.5l830 -1080q-3 26 -4 50.5t-1 47.5v1015h170v-1433h-98q-23 0 -38.5 8t-30.5 27l-829 1079q2 -25 3 -49t1 -44v-1021h-170v1433h100v0z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="1596" 
d="M1505 716q0 -161 -51 -295.5t-144 -231.5t-223.5 -150.5t-288.5 -53.5t-288 53.5t-223 150.5t-144 231.5t-51 295.5t51 295.5t144 232t223 151.5t288 54t288.5 -54t223.5 -151.5t144 -232t51 -295.5zM1306 716q0 132 -36 237t-102 177.5t-160 111.5t-210 39
q-115 0 -209 -39t-160.5 -111.5t-102.5 -177.5t-36 -237t36 -236.5t102.5 -177t160.5 -111t209 -38.5q116 0 210 38.5t160 111t102 177t36 236.5z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="1222" 
d="M387 536v-536h-193v1433h423q136 0 236.5 -31.5t166.5 -89.5t98.5 -140t32.5 -183q0 -100 -35 -183t-102.5 -143t-167.5 -93.5t-229 -33.5h-230zM387 690h230q83 0 146.5 22t106.5 61.5t65 94.5t22 121q0 137 -84.5 214t-255.5 77h-230v-590z" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="1596" 
d="M1505 716q0 -101 -20.5 -192t-59.5 -169.5t-95 -142.5t-126 -111l368 -397h-160q-36 0 -64 10t-51 35l-252 274q-57 -18 -118.5 -28t-128.5 -10q-158 0 -288 53.5t-223 150.5t-144 231.5t-51 295.5t51 295.5t144 232t223 151.5t288 54t288.5 -54t223.5 -151.5t144 -232
t51 -295.5zM1306 716q0 132 -36 237t-102 177.5t-160 111.5t-210 39q-115 0 -209 -39t-160.5 -111.5t-102.5 -177.5t-36 -237t36 -236.5t102.5 -177t160.5 -111t209 -38.5q116 0 210 38.5t160 111t102 177t36 236.5z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="1288" 
d="M387 598v-598h-193v1433h405q136 0 235 -27.5t163.5 -79.5t95.5 -125.5t31 -164.5q0 -76 -24 -142t-69.5 -118.5t-111 -89.5t-148.5 -56q36 -21 64 -61l418 -569h-172q-53 0 -78 41l-372 512q-17 24 -37 34.5t-60 10.5h-147zM387 739h203q85 0 149.5 20.5t108 58
t65.5 89.5t22 115q0 128 -84.5 193t-251.5 65h-212v-541z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="1060" 
d="M908 1209q-9 -15 -19 -22.5t-26 -7.5q-17 0 -39.5 17t-57 37.5t-83 37.5t-117.5 17q-65 0 -115 -17.5t-83.5 -47.5t-50.5 -70.5t-17 -87.5q0 -60 29.5 -99.5t78 -67.5t110 -48.5t126 -42.5t126 -49.5t110 -69.5t78 -103t29.5 -150q0 -94 -32 -176.5t-93.5 -143.5
t-151 -96t-203.5 -35q-139 0 -253.5 50.5t-195.5 136.5l56 92q8 11 19.5 18.5t25.5 7.5q21 0 48 -22.5t67.5 -49.5t98 -49.5t140.5 -22.5q69 0 123 19t91.5 53.5t57.5 82.5t20 107q0 65 -29.5 106.5t-77.5 69.5t-109.5 47.5t-126 40t-126 47.5t-109.5 70t-77.5 107.5
t-29.5 159.5q0 76 29.5 147t85.5 126t138.5 88t189.5 33q120 0 219 -38t173 -110z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="1180" 
d="M1150 1433v-163h-463v-1270h-194v1270h-465v163h1122z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="1460" 
d="M731 154q89 0 159 30t118.5 84t74 129t25.5 165v871h193v-871q0 -124 -39.5 -230t-113 -183.5t-179.5 -121.5t-238 -44t-238 44t-180 121.5t-113.5 183.5t-39.5 230v871h193v-870q0 -90 25.5 -165t74 -129t119 -84.5t159.5 -30.5z" />
    <glyph glyph-name="V" unicode="V" horiz-adv-x="1360" 
d="M8 1433h155q26 0 42 -13t24 -33l405 -1011q14 -34 25.5 -74t22.5 -83q9 43 19.5 83t24.5 74l403 1011q7 17 24 31.5t42 14.5h156l-584 -1433h-175z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="2038" 
d="M14 1433h161q26 0 43 -13t23 -33l296 -996q8 -27 14.5 -58t12.5 -65q7 34 14 65.5t16 57.5l337 996q6 17 23.5 31.5t42.5 14.5h56q26 0 42.5 -13t23.5 -33l335 -996q18 -52 32 -119q6 33 11 63t13 56l297 996q5 18 22.5 32t42.5 14h151l-447 -1433h-174l-363 1093
q-11 31 -20 72q-5 -20 -9.5 -38.5t-9.5 -33.5l-365 -1093h-174z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="1286" 
d="M507 736l-473 697h193q21 0 31 -7t18 -20l374 -574q7 21 21 46l353 524q9 14 19.5 22.5t25.5 8.5h185l-475 -688l491 -745h-192q-22 0 -34.5 11.5t-20.5 25.5l-384 601q-7 -21 -18 -40l-374 -561q-9 -14 -20.5 -25.5t-32.5 -11.5h-180z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="1258" 
d="M726 570v-570h-193v570l-525 863h170q26 0 41 -13t26 -32l328 -557q20 -35 33.5 -66t24.5 -61q11 31 24 62t33 65l327 557q9 16 24.5 30.5t40.5 14.5h172z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="1248" 
d="M1172 1433v-72q0 -34 -21 -64l-811 -1139h818v-158h-1072v76q0 30 19 57l812 1142h-793v158h1048z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="600" 
d="M142 -289v1822h368v-70q0 -22 -13.5 -34.5t-35.5 -12.5h-169v-1587h169q22 0 35.5 -12.5t13.5 -35.5v-70h-368z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="750" 
d="M-20 1473h76q33 0 57 -17t37 -49l601 -1497h-75q-29 0 -57 17t-41 52z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="600" 
d="M90 -219q0 20 13.5 34t35.5 14h169v1587h-169q-22 0 -35.5 13.5t-13.5 33.5v70h368v-1822h-368v70z" />
    <glyph glyph-name="asciicircum" unicode="^" 
d="M516 1433h115l358 -646h-129q-17 0 -29 10t-20 24l-196 352q-13 23 -22.5 44.5t-16.5 42.5q-14 -44 -37 -87l-194 -352q-8 -14 -19.5 -24t-31.5 -10h-136z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="788" 
d="M788 -165v-120h-788v120h788z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="614" 
d="M207 1449q33 0 49 -10.5t30 -33.5l149 -242h-102q-21 0 -34 6.5t-27 21.5l-234 258h169z" />
    <glyph glyph-name="a" unicode="a" horiz-adv-x="1014" 
d="M890 0h-79q-26 0 -42 8t-21 34l-20 94q-40 -36 -78 -64.5t-80 -48t-89.5 -29.5t-105.5 -10q-59 0 -110.5 16.5t-89.5 49.5t-60.5 83.5t-22.5 119.5q0 60 33 115.5t106.5 98.5t192.5 70.5t291 31.5v79q0 118 -50.5 178.5t-149.5 60.5q-65 0 -109.5 -16.5t-77 -37t-56 -37
t-46.5 -16.5q-18 0 -31.5 9.5t-21.5 23.5l-32 57q84 81 181 121t215 40q85 0 151 -28t111 -78t68 -121t23 -156v-648zM428 109q47 0 86 9.5t73.5 27t66 42.5t61.5 57v211q-123 -4 -209 -19.5t-140 -40.5t-78.5 -59t-24.5 -76q0 -40 13 -69t35 -47.5t52 -27t65 -8.5z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="1118" 
d="M152 0v1473h179v-606q63 73 144.5 117.5t186.5 44.5q88 0 159 -33t121 -98.5t77 -162t27 -222.5q0 -112 -30 -208.5t-86.5 -167t-138 -111t-183.5 -40.5q-98 0 -166.5 38t-119.5 106l-9 -92q-8 -38 -46 -38h-115zM604 887q-87 0 -152.5 -40t-120.5 -113v-490
q48 -66 106.5 -93t130.5 -27q142 0 218 101t76 288q0 99 -17.5 170t-50.5 116.5t-81 66.5t-109 21z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="934" 
d="M837 833q-8 -11 -16 -17t-23 -6t-32.5 12.5t-44.5 27.5t-65.5 27.5t-94.5 12.5q-74 0 -131 -26.5t-95.5 -76.5t-58 -121t-19.5 -159q0 -92 21 -163.5t59 -120t92.5 -74t122.5 -25.5q65 0 107 15.5t70 34.5t46 34.5t36 15.5q23 0 34 -17l50 -65q-66 -81 -165 -118.5
t-209 -37.5q-95 0 -176.5 35t-141.5 101.5t-94.5 163.5t-34.5 221q0 113 31.5 209t92 165.5t149.5 108.5t204 39q106 0 188 -34.5t145 -97.5z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="1118" 
d="M859 0q-38 0 -48 37l-16 123q-65 -79 -148.5 -126.5t-191.5 -47.5q-87 0 -158 33.5t-121 98.5t-77 162t-27 223q0 112 30 208.5t86.5 167.5t137.5 111.5t184 40.5q93 0 159 -31.5t118 -88.5v562h178v-1473h-106zM514 130q87 0 152.5 40t120.5 113v490q-49 66 -107.5 92.5
t-129.5 26.5q-142 0 -218 -101t-76 -288q0 -99 17 -169.5t50 -116t81 -66.5t110 -21z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="1048" 
d="M547 1029q91 0 168 -30.5t133 -88t87.5 -142t31.5 -192.5q0 -42 -9 -56t-34 -14h-674q2 -96 26 -167t66 -118.5t100 -71t130 -23.5q67 0 115.5 15.5t83.5 33.5t58.5 33.5t40.5 15.5q22 0 34 -17l50 -65q-33 -40 -79 -69.5t-98.5 -48.5t-108.5 -28.5t-111 -9.5
q-105 0 -193.5 35.5t-153 104t-100.5 169.5t-36 232q0 106 32.5 198t93.5 159.5t149 106t198 38.5zM551 898q-129 0 -203 -74.5t-92 -206.5h551q0 62 -17 113.5t-50 89t-80.5 58t-108.5 20.5z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="674" 
d="M186 0v861l-112 13q-21 5 -34.5 15.5t-13.5 30.5v73h160v98q0 87 24.5 154.5t70 114t109.5 70.5t144 24q68 0 126 -20l-4 -89q-1 -20 -17 -24t-45 -4h-31q-46 0 -83.5 -12t-64.5 -39t-41.5 -71t-14.5 -109v-93h293v-129h-287v-864h-179z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="1022" 
d="M487 1030q66 0 123.5 -14.5t104.5 -42.5h275v-66q0 -33 -42 -42l-115 -16q34 -65 34 -145q0 -74 -28.5 -134.5t-79 -103.5t-120 -66t-152.5 -23q-71 0 -134 17q-32 -20 -48.5 -43t-16.5 -45q0 -36 29 -54.5t77 -26.5t109 -10t124.5 -6.5t124.5 -15.5t109 -36t77 -69
t29 -114q0 -65 -32.5 -126t-93.5 -108.5t-149 -76t-199 -28.5t-194.5 22t-138.5 59t-82.5 85.5t-27.5 101.5q0 75 47.5 127.5t130.5 83.5q-43 20 -68.5 53.5t-25.5 89.5q0 22 8 45.5t24.5 46.5t40.5 44t56 37q-75 42 -117.5 111.5t-42.5 162.5q0 74 28.5 134.5t79.5 103
t121.5 65.5t154.5 23zM803 -55q0 38 -21 61t-57 35.5t-83 18.5t-99 8.5t-106 5.5t-103 11q-57 -27 -92.5 -66t-35.5 -93q0 -34 17.5 -63.5t53.5 -51t90.5 -34t128.5 -12.5q72 0 129 13t96.5 37t60.5 57t21 73zM487 495q54 0 95.5 15t69.5 42t42 64.5t14 82.5q0 93 -56.5 148
t-164.5 55q-107 0 -163.5 -55t-56.5 -148q0 -45 14.5 -82.5t42.5 -64.5t69 -42t94 -15z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="1112" 
d="M146 0v1473h178v-596q65 69 144 110.5t182 41.5q83 0 146.5 -27.5t106 -78t64.5 -121.5t22 -157v-645h-178v645q0 115 -52.5 178.5t-160.5 63.5q-79 0 -147.5 -38t-126.5 -103v-746h-178z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="512" 
d="M344 1013v-1013h-178v1013h178zM384 1331q0 -26 -10.5 -48.5t-28 -40t-40.5 -27.5t-49 -10t-48.5 10t-40 27.5t-27.5 40t-10 48.5t10 49.5t27.5 41t40 27.5t48.5 10t49 -10t40.5 -27.5t28 -41t10.5 -49.5z" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="508" 
d="M344 1013v-1088q0 -61 -16 -113.5t-50.5 -91.5t-89 -61.5t-130.5 -22.5q-33 0 -60 5t-54 15l8 96q2 13 9 16.5t22 3.5q8 0 17 -0.5t22 -0.5q78 0 111 36.5t33 117.5v1088h178zM384 1331q0 -26 -10.5 -48.5t-28 -40t-40.5 -27.5t-49 -10t-48.5 10t-40 27.5t-27.5 40
t-10 48.5t10 49.5t27.5 41t40 27.5t48.5 10t49 -10t40.5 -27.5t28 -41t10.5 -49.5z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="1048" 
d="M331 1473v-867h46q20 0 33 5.5t29 22.5l320 343q15 16 30 26t40 10h162l-373 -397q-14 -17 -27.5 -30t-30.5 -23q18 -12 32.5 -27.5t27.5 -35.5l396 -500h-160q-22 0 -37.5 8.5t-29.5 26.5l-333 415q-15 21 -30 27.5t-45 6.5h-50v-484h-179v1473h179z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="512" 
d="M344 1473v-1473h-178v1473h178z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="1642" 
d="M146 0v1013h106q38 0 48 -37l13 -104q56 69 125.5 113t161.5 44q103 0 166.5 -57t91.5 -154q21 55 55.5 95t77.5 66t91.5 38t98.5 12q80 0 142.5 -25.5t106 -74.5t66.5 -120.5t23 -163.5v-645h-178v645q0 119 -52 180.5t-151 61.5q-44 0 -83.5 -15.5t-69.5 -45.5
t-47.5 -75.5t-17.5 -105.5v-645h-178v645q0 122 -49 182t-143 60q-66 0 -122.5 -35.5t-103.5 -96.5v-755h-178z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="1112" 
d="M146 0v1013h106q38 0 48 -37l14 -110q66 73 147.5 118t188.5 45q83 0 146.5 -27.5t106 -78t64.5 -121.5t22 -157v-645h-178v645q0 115 -52.5 178.5t-160.5 63.5q-79 0 -147.5 -38t-126.5 -103v-746h-178z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="1112" 
d="M556 1029q111 0 200.5 -37t152 -105t96 -164.5t33.5 -215.5q0 -120 -33.5 -216t-96 -164t-152 -104.5t-200.5 -36.5t-200.5 36.5t-152.5 104.5t-97 164t-34 216q0 119 34 215.5t97 164.5t152.5 105t200.5 37zM556 125q150 0 224 100.5t74 280.5q0 181 -74 282t-224 101
q-76 0 -132 -26t-93.5 -75t-56 -120.5t-18.5 -161.5t18.5 -161t56 -119.5t93.5 -74.5t132 -26z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="1104" 
d="M146 -343v1356h106q38 0 48 -37l15 -120q65 79 148.5 127t192.5 48q87 0 158 -33.5t121 -99t77 -162.5t27 -223q0 -112 -30 -208.5t-86 -167t-137.5 -111t-183.5 -40.5q-94 0 -160.5 31t-117.5 88v-448h-178zM597 887q-87 0 -152.5 -40t-120.5 -113v-490
q49 -66 107.5 -93t130.5 -27q141 0 217 101t76 288q0 99 -17.5 170t-50.5 116.5t-81 66.5t-109 21z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="1118" 
d="M965 1013v-1356h-178v493q-64 -74 -145.5 -119t-186.5 -45q-87 0 -158 33.5t-121 98.5t-77 162t-27 223q0 112 30 208.5t86.5 167.5t137.5 111.5t184 40.5q98 0 166.5 -35t122.5 -99l12 79q10 37 48 37h106zM514 130q87 0 152.5 40t120.5 113v490q-48 64 -107 91.5
t-130 27.5q-142 0 -218 -101t-76 -288q0 -99 17 -169.5t50 -116t81 -66.5t110 -21z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="806" 
d="M146 0v1013h102q29 0 40 -11t15 -38l12 -158q52 106 128.5 165.5t179.5 59.5q42 0 76 -9.5t63 -26.5l-23 -133q-7 -25 -31 -25q-14 0 -43 9.5t-81 9.5q-93 0 -155.5 -54t-104.5 -157v-645h-178z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="868" 
d="M726 846q-12 -22 -37 -22q-15 0 -34 11t-46.5 24.5t-65.5 25t-90 11.5q-45 0 -81 -11.5t-61.5 -31.5t-39 -46.5t-13.5 -57.5q0 -39 22.5 -65t59.5 -45t84 -33.5t96.5 -31t96.5 -36.5t84 -50t59.5 -73.5t22.5 -104.5q0 -70 -25 -129.5t-74 -103t-120 -68.5t-164 -25
q-106 0 -192 34.5t-146 88.5l42 68q8 13 19 20t29 7t38 -14t48.5 -31t69 -31t101.5 -14q52 0 91 13.5t65 36.5t38.5 53t12.5 64q0 42 -22.5 69.5t-59.5 47t-84.5 34t-97 30.5t-97 36.5t-84.5 51.5t-59.5 76.5t-22.5 110.5q0 58 24 111.5t70 94t113 64.5t153 24
q100 0 179.5 -31.5t137.5 -86.5z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="746" 
d="M453 -16q-120 0 -184.5 67t-64.5 193v620h-122q-16 0 -27 9.5t-11 29.5v71l166 21l41 313q2 15 13 24.5t28 9.5h90v-349h290v-129h-290v-608q0 -64 31 -95t80 -31q28 0 48.5 7.5t35.5 16.5t25.5 16.5t18.5 7.5q14 0 25 -17l52 -85q-46 -43 -111 -67.5t-134 -24.5z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="1112" 
d="M300 1013v-646q0 -115 53 -178t160 -63q78 0 147 37t127 103v747h178v-1013h-106q-38 0 -48 37l-14 109q-66 -73 -148 -117.5t-188 -44.5q-83 0 -146.5 27.5t-106.5 77.5t-64.5 121t-21.5 157v646h178z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="1024" 
d="M18 1013h146q21 0 35 -11t20 -26l257 -652q14 -36 22 -72t15 -71q8 35 17 71t23 72l260 652q6 16 19.5 26.5t33.5 10.5h139l-413 -1013h-161z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="1532" 
d="M14 1013h140q22 0 36 -11t19 -26l194 -652q8 -36 15 -69.5t12 -67.5q8 34 18 67.5t21 69.5l214 656q5 15 17.5 25t31.5 10h77q20 0 33 -10t18 -25l209 -656q11 -35 19.5 -69t16.5 -67q5 33 13 69t17 67l198 652q5 16 19 26.5t33 10.5h134l-328 -1013h-141q-26 0 -36 34
l-224 687q-8 23 -13 46.5t-10 46.5q-5 -23 -10 -47t-13 -47l-227 -686q-11 -34 -41 -34h-134z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="1008" 
d="M383 519l-341 494h171q22 0 32 -7t18 -20l248 -380q9 28 26 56l218 320q10 14 20 22.5t25 8.5h164l-341 -484l355 -529h-171q-22 0 -34.5 11.5t-20.5 25.5l-255 397q-7 -29 -21 -52l-236 -345q-10 -14 -21.5 -25.5t-31.5 -11.5h-159z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="1024" 
d="M443 -299q-9 -20 -22.5 -32t-41.5 -12h-132l185 402l-418 954h154q23 0 36 -11.5t19 -25.5l271 -638q9 -22 15.5 -44t11.5 -45q7 23 14 45t16 45l263 637q6 16 20.5 26.5t31.5 10.5h142z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="924" 
d="M853 937q0 -19 -7 -36.5t-18 -31.5l-548 -730h553v-139h-763v74q0 13 6.5 30.5t18.5 33.5l551 735h-545v140h752v-76z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="600" 
d="M181 425q0 63 -35 103.5t-102 40.5v107q67 0 102 40t35 104q0 50 -8 99t-17.5 98t-17.5 99t-8 102q0 69 20.5 127t62 100t103 65t143.5 23h53v-79q0 -20 -14 -29t-26 -9h-20q-77 0 -121.5 -50.5t-44.5 -136.5q0 -56 7 -108t16 -101.5t16 -98.5t7 -99q0 -38 -11 -70.5
t-31.5 -58t-48 -44t-60.5 -27.5q33 -9 60.5 -27.5t48 -44.5t31.5 -58t11 -69q0 -50 -7 -99t-16 -98.5t-16 -101.5t-7 -108q0 -87 44.5 -137t121.5 -50h20q12 0 26 -9t14 -29v-80h-53q-82 0 -143.5 23.5t-103 65.5t-62 100t-20.5 127q0 52 8 101.5t17.5 99t17.5 98.5t8 99z
" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="600" 
d="M230 1533h138v-1876h-138v1876z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="600" 
d="M419 425q0 -50 8 -99t17.5 -98.5t17.5 -99t8 -101.5q0 -69 -21 -127t-62 -100t-102.5 -65.5t-143.5 -23.5h-53v80q0 20 14 29t26 9h20q77 0 121.5 50t44.5 137q0 56 -7 108t-16 101.5t-16 98.5t-7 99q0 37 11 69t31.5 58t48 44.5t60.5 27.5q-33 9 -60.5 27.5t-48 44
t-31.5 58t-11 70.5q0 50 7 99t16 98.5t16 101.5t7 108q0 86 -44.5 136.5t-121.5 50.5h-20q-12 0 -26 9t-14 29v79h53q82 0 143.5 -23t102.5 -65t62 -100t21 -127q0 -52 -8 -102t-17.5 -99t-17.5 -98t-8 -99q0 -64 35 -104t102 -40v-107q-67 0 -102 -40.5t-35 -103.5z" />
    <glyph glyph-name="asciitilde" unicode="~" 
d="M759 613q65 0 101.5 42.5t37.5 112.5h144q0 -67 -18.5 -123t-53 -96t-85.5 -62t-115 -22q-52 0 -103 16t-98.5 35.5t-90.5 36t-79 16.5q-65 0 -101.5 -42t-37.5 -113h-144q0 67 18.5 123t53 96t85 62.5t115.5 22.5q52 0 103 -16.5t98.5 -36t90.5 -36t79 -16.5z" />
    <glyph glyph-name="uni00A0" unicode="&#xa0;" horiz-adv-x="386" 
 />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="686" 
d="M262 -343v541q0 45 1.5 87.5t4.5 86t7.5 89.5t10.5 98h121q6 -52 10.5 -98t7.5 -89.5t4.5 -86t1.5 -87.5v-541h-169zM218 904q0 27 9.5 49.5t26.5 39.5t39.5 26.5t49.5 9.5q26 0 48.5 -9.5t39.5 -26.5t27 -39.5t10 -49.5q0 -26 -10 -49t-27 -40t-39.5 -27t-48.5 -10
q-27 0 -49.5 10t-39.5 27t-26.5 40t-9.5 49z" />
    <glyph glyph-name="cent" unicode="&#xa2;" 
d="M561 -11q-92 10 -169.5 49.5t-134 105.5t-88 157t-31.5 205q0 111 33 204.5t96 162.5t155 109.5t211 43.5l12 179q2 20 15.5 34.5t34.5 14.5h66l-16 -233q82 -12 148 -43.5t120 -80.5l-46 -62q-8 -11 -15.5 -16.5t-21.5 -5.5q-12 0 -28.5 8.5t-39 20.5t-54 24t-73.5 19
l-52 -762q63 4 105.5 19.5t72 32.5t48.5 31t35 14q11 0 20 -4.5t14 -11.5l48 -63q-60 -72 -152.5 -109t-199.5 -44l-12 -175q-2 -19 -15.5 -33.5t-34.5 -14.5h-66zM315 506q0 -162 67.5 -258t188.5 -119l52 760q-76 -6 -133.5 -34.5t-96.5 -78t-58.5 -117.5t-19.5 -153z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" 
d="M52 672q0 26 16 44t45 18h134v261q0 94 27 176.5t82 144t137 97t192 35.5q78 0 138.5 -19.5t107.5 -53.5t81.5 -79t58.5 -97l-72 -46q-10 -6 -20.5 -8.5t-20.5 -2.5q-14 0 -26.5 5.5t-23.5 19.5q-20 25 -40.5 48.5t-46 41t-58.5 28t-78 10.5q-63 0 -111 -21t-80 -60
t-48 -94t-16 -123v-263h441v-72q0 -18 -15 -33t-37 -15h-389v-243q0 -75 -28.5 -129.5t-78.5 -99.5q29 5 57.5 8.5t58.5 3.5h676v-76q0 -14 -5.5 -27.5t-15.5 -25t-24 -18.5t-32 -7h-964v115q34 10 65 27t55 42.5t38.5 60t14.5 80.5v289h-195v58z" />
    <glyph glyph-name="currency" unicode="&#xa4;" 
d="M223 672q0 57 16.5 107.5t45.5 94.5l-153 153l91 90l151 -152q44 31 96 48t110 17q57 0 108 -16.5t94 -46.5l153 153l89 -91l-151 -152q31 -44 48 -95.5t17 -109.5q0 -57 -16.5 -108t-45.5 -94l152 -151l-91 -92l-152 152q-44 -30 -96 -47t-109 -17t-107.5 16.5
t-94.5 45.5l-153 -153l-89 91l151 152q-30 44 -47 95.5t-17 109.5zM355 672q0 -46 17.5 -86.5t48.5 -71t71.5 -48.5t87.5 -18t88.5 18t72 48.5t48.5 71t18 86.5q0 47 -18 88t-48.5 72t-72 48.5t-88.5 17.5t-87.5 -17.5t-71.5 -48.5t-48.5 -72t-17.5 -88z" />
    <glyph glyph-name="yen" unicode="&#xa5;" 
d="M146 625h306l-408 808h149q26 0 41.5 -12.5t25.5 -32.5l276 -566q14 -35 24 -64t17 -58q7 29 16 58.5t23 63.5l275 566q8 17 24.5 31t41.5 14h150l-409 -808h307v-102h-340v-105h340v-103h-340v-315h-179v315h-340v103h340v105h-340v102z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="600" 
d="M230 1533h138v-794h-138v794zM230 452h138v-795h-138v795z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="1006" 
d="M817 1265q-12 -22 -37 -22q-15 0 -34 11t-46.5 24.5t-65.5 25t-90 11.5q-48 0 -86.5 -12.5t-65 -33.5t-40.5 -49t-14 -59q0 -38 24.5 -66.5t64 -52t90.5 -45t103.5 -44.5t103.5 -50.5t90.5 -63t64 -81.5t24.5 -107q0 -81 -39 -144.5t-123 -101.5q49 -37 80 -86t31 -118
q0 -70 -25 -129.5t-73.5 -103t-120 -68.5t-163.5 -25q-106 0 -192 34.5t-146 88.5l41 68q8 13 19.5 20t28.5 7q18 0 38 -14t49 -31.5t71.5 -31.5t105.5 -14q50 0 89.5 12.5t66.5 35t41 53.5t14 69q0 45 -25 78t-66 58.5t-93 46.5t-107 43t-107 47.5t-93 60.5t-66 81.5
t-25 110.5q0 78 43 140.5t136 97.5q-50 38 -81.5 90.5t-31.5 127.5q0 58 24 111.5t70 93.5t113 64t153 24q100 0 179.5 -31t137.5 -86zM272 726q0 -51 35 -86.5t89.5 -64t120.5 -55t128 -57.5q54 26 78 63.5t24 84.5q0 36 -15 64t-41 51t-61 42t-74.5 36.5t-81.5 35
t-82 37.5q-66 -30 -93 -66.5t-27 -84.5z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="614" 
d="M239 1289q0 -23 -9 -43t-25 -35t-36.5 -24t-43.5 -9q-22 0 -42 9t-35.5 24t-24.5 35t-9 43t9 44t24.5 36.5t35.5 24.5t42 9q23 0 43.5 -9t36.5 -24.5t25 -36.5t9 -44zM598 1289q0 -23 -9 -43t-24.5 -35t-36 -24t-43.5 -9t-43.5 9t-35.5 24t-24 35t-9 43t9 44t24 36.5
t35.5 24.5t43.5 9t43.5 -9t36 -24.5t24.5 -36.5t9 -44z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="1596" 
d="M1030 463q8 5 13.5 8.5t11.5 3.5q11 0 15 -3t10 -9l61 -64q-57 -66 -140 -102.5t-199 -36.5q-98 0 -178.5 34t-138 95t-89 145t-31.5 184q0 101 34.5 185.5t95.5 145t144.5 94t181.5 33.5q108 0 184 -34t133 -89l-46 -65q-5 -6 -13 -12t-20 -6q-14 0 -29.5 11t-40.5 24.5
t-63 25t-97 11.5q-70 0 -126.5 -22.5t-96 -64.5t-61 -102t-21.5 -135q0 -77 21.5 -137.5t59.5 -101.5t91 -62.5t115 -21.5q48 0 81 6t57 16t42.5 22t38.5 24zM68 716q0 101 26 194.5t73.5 175t114.5 148t148 114.5t174 74t194 26t194.5 -26t174.5 -74t148 -114.5t114.5 -148
t73.5 -175t26 -194.5q0 -100 -26 -193.5t-73.5 -174.5t-114.5 -147.5t-148 -114.5t-174.5 -74t-194.5 -26t-194 26t-174 74t-148 114.5t-114.5 147.5t-73.5 174t-26 194zM168 716q0 -89 22 -171t63 -153.5t98 -129.5t127 -99.5t151 -64t169 -22.5q132 0 247.5 50t201 136.5
t135 203t49.5 250.5q0 89 -22.5 172t-63.5 154.5t-98.5 130.5t-127.5 100.5t-151.5 64.5t-169.5 23q-132 0 -247 -50.5t-200 -138t-134 -205t-49 -251.5z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="684" 
d="M596 840h-60q-18 0 -27 5.5t-17 23.5l-12 49q-24 -21 -47 -37t-48 -27.5t-53 -17t-62 -5.5q-38 0 -70.5 10t-56.5 30.5t-37.5 50.5t-13.5 71q0 34 19 67.5t63 60t116.5 44t179.5 19.5v37q0 63 -29 93t-86 30q-38 0 -63 -9t-43.5 -19.5t-33 -19t-30.5 -8.5q-14 0 -24 7.5
t-15 17.5l-22 42q52 49 112.5 72t133.5 23q54 0 96 -17t71 -47t44 -72t15 -93v-381zM309 923q51 0 88 19t73 54v105q-70 -2 -118.5 -10.5t-78.5 -22t-43 -31t-13 -38.5q0 -42 26 -59t66 -17z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="926" 
d="M138 518v23l249 389l58 -28q14 -7 21 -18t7 -24q0 -17 -10 -33l-159 -261q-14 -24 -28 -37q15 -14 28 -36l159 -261q5 -8 7.5 -17t2.5 -17q0 -28 -28 -41l-58 -28zM434 518v23l249 389l58 -28q14 -7 21 -18t7 -24q0 -17 -10 -33l-159 -261q-14 -24 -28 -37q15 -14 28 -36
l159 -261q5 -8 7.5 -17t2.5 -17q0 -28 -28 -41l-58 -28z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" 
d="M148 739h860v-424h-151v289h-709v135z" />
    <glyph glyph-name="uni00AD" unicode="&#xad;" horiz-adv-x="694" 
d="M100 675h494v-151h-494v151z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="1596" 
d="M68 716q0 101 26 194.5t73.5 175t114.5 148t148 114.5t174 74t194 26t194.5 -26t174.5 -74t148 -114.5t114.5 -148t73.5 -175t26 -194.5q0 -100 -26 -193.5t-73.5 -174.5t-114.5 -147.5t-148 -114.5t-174.5 -74t-194.5 -26t-194 26t-174 74t-148 114.5t-114.5 147.5
t-73.5 174t-26 194zM168 716q0 -89 22 -171t63 -153.5t98 -129.5t127 -99.5t151 -64t169 -22.5q132 0 247.5 50t201 136.5t135 203t49.5 250.5q0 89 -22.5 172t-63.5 154.5t-98.5 130.5t-127.5 100.5t-151.5 64.5t-169.5 23q-132 0 -247 -50.5t-200 -138t-134 -205
t-49 -251.5zM654 626v-354h-156v892h288q172 0 255 -62.5t83 -184.5q0 -94 -53.5 -160t-159.5 -91q17 -10 29.5 -25t23.5 -35l228 -334h-148q-33 0 -49 25l-201 302q-9 13 -21.5 20t-38.5 7h-80zM654 740h116q55 0 93.5 10.5t62 30.5t34 49t10.5 67q0 37 -9.5 65t-31 46
t-56.5 27t-87 9h-132v-304z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="614" 
d="M20 1348h574v-117h-574v117z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="794" 
d="M70 1128q0 67 25 126t69 102.5t103.5 68.5t128.5 25t128.5 -25t103.5 -68.5t69 -102.5t25 -126q0 -66 -25 -124.5t-69 -102t-103.5 -69t-128.5 -25.5t-128.5 25.5t-103.5 69t-69 102t-25 124.5zM197 1127q0 -42 15 -78.5t42 -63.5t63.5 -42.5t78.5 -15.5t78 15.5t63 42.5
t42 63.5t15 78.5t-15 79t-42 64.5t-63 43t-78 15.5t-78.5 -15.5t-63.5 -43t-42 -64.5t-15 -79z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" 
d="M651 1202v-376h407v-136h-407v-368h-146v368h-405v136h405v376h146zM100 215h958v-135h-958v135z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="664" 
d="M346 1637q52 0 94.5 -15t72.5 -42t46.5 -65.5t16.5 -85.5q0 -40 -12.5 -74.5t-33.5 -65.5t-48 -60t-57 -59l-162 -165q23 6 46.5 10t44.5 4h195q21 0 32.5 -11t11.5 -31v-77h-511v43q0 13 5 27t17 26l221 219q25 25 47 51t38 52.5t25.5 53t9.5 53.5q0 51 -30 78.5
t-75 27.5q-46 0 -74.5 -24t-42.5 -66q-8 -14 -17.5 -22t-26.5 -8q-4 0 -8.5 0.5t-9.5 1.5l-71 12q15 106 84 159t172 53z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="664" 
d="M354 1637q51 0 92 -14.5t70.5 -40t45.5 -59.5t16 -74q0 -128 -119 -173q66 -19 100.5 -58t34.5 -101q0 -55 -21 -97t-55.5 -70.5t-80 -43t-93.5 -14.5q-57 0 -99 12.5t-72.5 37t-52 60.5t-36.5 83l55 24q15 6 29 6q29 0 40 -23q6 -13 15 -29t24 -30t36.5 -23.5t53.5 -9.5
q31 0 54.5 10t39.5 25.5t24 35.5t8 41q0 30 -8.5 51.5t-28 35.5t-52 21t-79.5 7v87q87 1 122.5 31t35.5 82q0 50 -29 76t-77 26t-76.5 -23.5t-40.5 -63.5q-8 -16 -16.5 -23.5t-23.5 -7.5q-4 0 -8.5 0.5t-9.5 1.5l-67 12q7 53 29 92.5t54.5 66t74.5 40t91 13.5z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="614" 
d="M597 1449l-233 -258q-14 -15 -27.5 -21.5t-34.5 -6.5h-106l148 242q14 23 30.5 33.5t48.5 10.5h174z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="1112" 
d="M300 1013v-658q0 -109 54 -169t159 -60q78 0 147 37t127 103v747h178v-1013h-106q-38 0 -48 37l-14 109q-67 -72 -137.5 -106t-157.5 -34q-74 0 -130 25.5t-95 71.5q7 -42 10 -85.5t3 -81.5v-279h-89q-38 0 -58.5 20t-20.5 56v1280h178z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="1338" 
d="M1302 1433v-153h-219v-1481h-157v1481h-277v-1481h-157v861q-104 0 -187 30.5t-141.5 83t-90 123.5t-31.5 152q0 86 31.5 156.5t90 121t141.5 78.5t187 28h810z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="546" 
d="M124 593q0 31 11.5 59t32 48t47.5 32t57 12q31 0 59 -12t48 -32t32 -48t12 -59q0 -30 -12 -57t-32 -47.5t-48 -32t-59 -11.5q-30 0 -57 11.5t-47.5 32t-32 47.5t-11.5 57z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="614" 
d="M172 -247q6 0 14 -3.5t19 -8t27 -8t37 -3.5q42 0 63.5 16.5t21.5 42.5q0 19 -11 32t-31.5 22t-50.5 15t-68 11l43 141h112l-24 -80q90 -20 130.5 -54.5t40.5 -88.5q0 -32 -16 -57.5t-44.5 -43.5t-68.5 -27.5t-88 -9.5q-41 0 -78 8.5t-68 22.5l17 55q6 18 23 18z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="664" 
d="M173 985h147v440l4 43l-107 -88q-12 -9 -26 -9q-23 0 -32 14l-39 56l222 190h108v-646h130v-85h-407v85z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="762" 
d="M382 1449q70 0 126.5 -21.5t96.5 -61.5t62 -97.5t22 -128.5q0 -72 -22 -130t-62 -98.5t-96.5 -62t-126.5 -21.5q-71 0 -128 21.5t-97.5 62t-62.5 98.5t-22 130q0 71 22 128.5t62.5 97.5t97.5 61.5t128 21.5zM382 934q84 0 125.5 52.5t41.5 152.5t-41.5 152t-125.5 52
q-87 0 -128.5 -52t-41.5 -152t41.5 -152.5t128.5 -52.5z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="926" 
d="M236 129l-58 28q-28 13 -28 41q0 17 10 34l159 261q13 24 27 36q-12 11 -27 37l-159 261q-10 17 -10 34q0 28 28 41l58 28l249 -389v-23zM781 541v-23l-249 -389l-58 28q-28 13 -28 41q0 17 10 34l159 261q13 24 27 36q-12 11 -27 37l-159 261q-10 17 -10 34q0 28 28 41
l58 28z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="1424" 
d="M1295 267h109v-65q0 -11 -7 -18.5t-20 -7.5h-82v-176h-109v176h-306q-18 0 -28.5 8t-12.5 20l-10 57l342 468h124v-462zM155 788h147v440l4 43l-107 -88q-12 -9 -26 -9q-23 0 -32 14l-39 56l222 190h108v-646h130v-85h-407v85zM1186 508q0 19 1 41t4 45l-241 -327h236
v241zM434 53q-19 -31 -41 -42t-51 -11h-76l818 1372q18 29 41 45t55 16h77z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="1424" 
d="M1126 737q52 0 94.5 -15t72.5 -42t46.5 -65.5t16.5 -85.5q0 -40 -12.5 -74.5t-33.5 -65.5t-48 -60t-57 -59l-162 -165q23 6 46.5 10t44.5 4h195q21 0 32.5 -11t11.5 -31v-77h-511v43q0 13 5 27t17 26l221 219q25 25 47 51t38 52.5t25.5 53t9.5 53.5q0 51 -30 78.5
t-75 27.5q-46 0 -74.5 -24t-42.5 -66q-8 -14 -17.5 -22t-26.5 -8q-4 0 -8.5 0.5t-9.5 1.5l-71 12q15 106 84 159t172 53zM155 788h147v440l4 43l-107 -88q-12 -9 -26 -9q-23 0 -32 14l-39 56l222 190h108v-646h130v-85h-407v85zM390 53q-19 -31 -41 -42t-51 -11h-76
l818 1372q18 29 41 45t55 16h77z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="1426" 
d="M1296 267h109v-65q0 -11 -7 -18.5t-20 -7.5h-82v-176h-109v176h-306q-18 0 -28.5 8t-12.5 20l-10 57l342 468h124v-462zM338 1440q51 0 92 -14.5t70.5 -40t45.5 -59.5t16 -74q0 -128 -119 -173q66 -19 100.5 -58t34.5 -101q0 -55 -21 -97t-55.5 -70.5t-80 -43
t-93.5 -14.5q-57 0 -99 12.5t-72.5 37t-52 60.5t-36.5 83l55 24q15 6 29 6q29 0 40 -23q6 -13 15 -29t24 -30t36.5 -23.5t53.5 -9.5q31 0 54.5 10t39.5 25.5t24 35.5t8 41q0 30 -8.5 51.5t-28 35.5t-52 21t-79.5 7v87q87 1 122.5 31t35.5 82q0 50 -29 76t-77 26t-76.5 -23.5
t-40.5 -63.5q-8 -16 -16.5 -23.5t-23.5 -7.5q-4 0 -8.5 0.5t-9.5 1.5l-67 12q7 53 29 92.5t54.5 66t74.5 40t91 13.5zM1187 508q0 19 1 41t4 45l-241 -327h236v241zM439 53q-19 -31 -41 -42t-51 -11h-76l818 1372q18 29 41 45t55 16h77z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="796" 
d="M770 -212q-31 -29 -68.5 -55t-81.5 -46t-96 -31.5t-112 -11.5q-79 0 -146.5 22t-116.5 63t-77 100t-28 134q0 76 22.5 129t57 91t75.5 65t76.5 51t61 48.5t29.5 57.5l18 154h122l12 -167v-12q0 -44 -22.5 -75t-57 -56t-74 -48.5t-74 -53.5t-57 -70t-22.5 -99
q0 -44 17 -78.5t46 -59t68.5 -37.5t84.5 -13q61 0 104.5 15t73.5 33t49 33t31 15q14 0 22.5 -6t15.5 -17zM324 903q0 26 9.5 49t26 40t39.5 27t49 10t49 -10t40 -27t26.5 -40t9.5 -49q0 -27 -9.5 -49.5t-26.5 -39.5t-40 -26.5t-49 -9.5t-49 9.5t-39.5 26.5t-26 39.5
t-9.5 49.5z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" horiz-adv-x="1360" 
d="M1353 0h-150q-26 0 -42 13t-24 33l-134 346h-643l-134 -346q-7 -18 -24 -32t-42 -14h-150l573 1433h197zM414 532h535l-225 583q-22 54 -43 135q-11 -41 -21.5 -75.5t-20.5 -60.5zM520 1782q32 0 48 -6.5t36 -26.5l211 -203h-139q-21 0 -33 3.5t-29 15.5l-295 217h201z
" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" horiz-adv-x="1360" 
d="M1353 0h-150q-26 0 -42 13t-24 33l-134 346h-643l-134 -346q-7 -18 -24 -32t-42 -14h-150l573 1433h197zM414 532h535l-225 583q-22 54 -43 135q-11 -41 -21.5 -75.5t-20.5 -60.5zM1021 1782l-294 -216q-17 -12 -30 -16t-34 -4h-138l211 203q10 10 18.5 16t17.5 10
t20 5.5t28 1.5h201z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" horiz-adv-x="1360" 
d="M1353 0h-150q-26 0 -42 13t-24 33l-134 346h-643l-134 -346q-7 -18 -24 -32t-42 -14h-150l573 1433h197zM414 532h535l-225 583q-22 54 -43 135q-11 -41 -21.5 -75.5t-20.5 -60.5zM1006 1546h-135q-12 0 -26 3.5t-23 9.5l-130 95q-8 4 -12 8q-8 -6 -12 -8l-130 -95
q-9 -6 -23 -9.5t-26 -3.5h-135l238 210h176z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" horiz-adv-x="1360" 
d="M1353 0h-150q-26 0 -42 13t-24 33l-134 346h-643l-134 -346q-7 -18 -24 -32t-42 -14h-150l573 1433h197zM414 532h535l-225 583q-22 54 -43 135q-11 -41 -21.5 -75.5t-20.5 -60.5zM801 1663q35 0 53.5 20.5t19.5 57.5h98q0 -43 -11 -79t-32 -62.5t-52 -41t-71 -14.5
q-35 0 -67 13t-61.5 28.5t-55.5 28.5t-50 13q-34 0 -52.5 -21.5t-19.5 -57.5h-100q0 43 11.5 79.5t33 62.5t53 41t70.5 15q35 0 67 -13t61 -28.5t55 -28.5t50 -13z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" horiz-adv-x="1360" 
d="M1353 0h-150q-26 0 -42 13t-24 33l-134 346h-643l-134 -346q-7 -18 -24 -32t-42 -14h-150l573 1433h197zM414 532h535l-225 583q-22 54 -43 135q-11 -41 -21.5 -75.5t-20.5 -60.5zM578 1666q0 -22 -9 -41.5t-24 -34.5t-35.5 -23.5t-43.5 -8.5q-21 0 -40.5 8.5t-34.5 23.5
t-24 34.5t-9 41.5q0 23 9 43.5t24 35.5t34.5 24t40.5 9q23 0 43.5 -9t35.5 -24t24 -35.5t9 -43.5zM1004 1666q0 -22 -9 -41.5t-24 -34.5t-35 -23.5t-42 -8.5q-23 0 -43 8.5t-35 23.5t-23.5 34.5t-8.5 41.5q0 23 8.5 43.5t23.5 35.5t35 24t43 9q22 0 42 -9t35 -24t24 -35.5
t9 -43.5z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" horiz-adv-x="1360" 
d="M1353 0h-150q-26 0 -42 13t-24 33l-134 346h-643l-134 -346q-7 -18 -24 -32t-42 -14h-150l573 1433h197zM414 532h535l-225 583q-22 54 -43 135q-11 -41 -21.5 -75.5t-20.5 -60.5zM489 1659q0 39 15.5 72t41 56.5t59.5 36.5t72 13q39 0 73.5 -13t61 -36.5t41.5 -56.5
t15 -72q0 -38 -15 -70t-41.5 -55t-61 -36t-73.5 -13q-38 0 -72 13t-59.5 36t-41 55t-15.5 70zM578 1659q0 -43 27 -71.5t74 -28.5q45 0 72.5 28.5t27.5 71.5q0 45 -27.5 73t-72.5 28q-47 0 -74 -28t-27 -73z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="1858" 
d="M733 1433h1021v-158h-749l60 -476h559v-152h-540l61 -489h609v-158h-772l-49 392h-556l-179 -347q-11 -20 -29.5 -32.5t-44.5 -12.5h-148zM450 532h465l-94 753q-12 -41 -26.5 -75.5t-28.5 -65.5z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="1370" 
d="M643 -247q6 0 14 -3.5t19 -8t27 -8t37 -3.5q42 0 63.5 16.5t21.5 42.5q0 19 -11 32t-31.5 22t-50.5 15t-68 11l36 118q-139 12 -252 70.5t-192.5 154t-122.5 223.5t-43 281q0 162 52.5 297t147.5 232t227.5 150.5t292.5 53.5q158 0 272.5 -49t203.5 -133l-63 -89
q-7 -10 -16 -16.5t-26 -6.5q-13 0 -27.5 9.5t-34.5 23.5t-47 30t-64 30t-86 23.5t-113 9.5q-115 0 -210.5 -39.5t-164.5 -112.5t-107.5 -178t-38.5 -235q0 -134 38.5 -239t105 -177.5t157.5 -110.5t196 -38q64 0 115 7.5t94.5 23.5t81 40.5t75.5 58.5q17 15 33 15t29 -13
l76 -83q-83 -97 -199.5 -153t-278.5 -62l-16 -55q90 -20 130.5 -54.5t40.5 -88.5q0 -32 -16 -57.5t-44.5 -43.5t-68.5 -27.5t-88 -9.5q-41 0 -78 8.5t-68 22.5l17 55q6 18 23 18z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="1162" 
d="M1057 1433v-158h-688v-476h557v-152h-557v-489h688v-158h-883v1433h883zM468 1782q32 0 48 -6.5t36 -26.5l211 -203h-139q-21 0 -33 3.5t-29 15.5l-295 217h201z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="1162" 
d="M1057 1433v-158h-688v-476h557v-152h-557v-489h688v-158h-883v1433h883zM969 1782l-294 -216q-17 -12 -30 -16t-34 -4h-138l211 203q10 10 18.5 16t17.5 10t20 5.5t28 1.5h201z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="1162" 
d="M1057 1433v-158h-688v-476h557v-152h-557v-489h688v-158h-883v1433h883zM954 1546h-135q-12 0 -26 3.5t-23 9.5l-130 95q-8 4 -12 8q-8 -6 -12 -8l-130 -95q-9 -6 -23 -9.5t-26 -3.5h-135l238 210h176z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="1162" 
d="M1057 1433v-158h-688v-476h557v-152h-557v-489h688v-158h-883v1433h883zM526 1666q0 -22 -9 -41.5t-24 -34.5t-35.5 -23.5t-43.5 -8.5q-21 0 -40.5 8.5t-34.5 23.5t-24 34.5t-9 41.5q0 23 9 43.5t24 35.5t34.5 24t40.5 9q23 0 43.5 -9t35.5 -24t24 -35.5t9 -43.5z
M952 1666q0 -22 -9 -41.5t-24 -34.5t-35 -23.5t-42 -8.5q-23 0 -43 8.5t-35 23.5t-23.5 34.5t-8.5 41.5q0 23 8.5 43.5t23.5 35.5t35 24t43 9q22 0 42 -9t35 -24t24 -35.5t9 -43.5z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="614" 
d="M404 0h-194v1433h194v-1433zM149 1782q32 0 48 -6.5t36 -26.5l211 -203h-139q-21 0 -33 3.5t-29 15.5l-295 217h201z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="614" 
d="M404 0h-194v1433h194v-1433zM650 1782l-294 -216q-17 -12 -30 -16t-34 -4h-138l211 203q10 10 18.5 16t17.5 10t20 5.5t28 1.5h201z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="614" 
d="M404 0h-194v1433h194v-1433zM635 1546h-135q-12 0 -26 3.5t-23 9.5l-130 95q-8 4 -12 8q-8 -6 -12 -8l-130 -95q-9 -6 -23 -9.5t-26 -3.5h-135l238 210h176z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="614" 
d="M404 0h-194v1433h194v-1433zM206 1666q0 -22 -9 -41.5t-24 -34.5t-35.5 -23.5t-43.5 -8.5q-21 0 -40.5 8.5t-34.5 23.5t-24 34.5t-9 41.5q0 23 9 43.5t24 35.5t34.5 24t40.5 9q23 0 43.5 -9t35.5 -24t24 -35.5t9 -43.5zM632 1666q0 -22 -9 -41.5t-24 -34.5t-35 -23.5
t-42 -8.5q-23 0 -43 8.5t-35 23.5t-23.5 34.5t-8.5 41.5q0 23 8.5 43.5t23.5 35.5t35 24t43 9q22 0 42 -9t35 -24t24 -35.5t9 -43.5z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="1578" 
d="M50 780h197v653h535q158 0 288.5 -51.5t223.5 -146t144 -226.5t51 -293t-51 -293t-144 -226t-223.5 -145.5t-288.5 -51.5h-535v666h-197v114zM1290 716q0 132 -36 236t-102 176t-160 110t-210 38h-341v-496h381v-114h-381v-509h341q116 0 210 38t160 109.5t102 175.5
t36 236z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="1512" 
d="M274 1433q26 0 38.5 -6.5t28.5 -26.5l830 -1080q-3 26 -4 50.5t-1 47.5v1015h170v-1433h-98q-23 0 -38.5 8t-30.5 27l-829 1079q2 -25 3 -49t1 -44v-1021h-170v1433h100v0zM901 1663q35 0 53.5 20.5t19.5 57.5h98q0 -43 -11 -79t-32 -62.5t-52 -41t-71 -14.5
q-35 0 -67 13t-61.5 28.5t-55.5 28.5t-50 13q-34 0 -52.5 -21.5t-19.5 -57.5h-100q0 43 11.5 79.5t33 62.5t53 41t70.5 15q35 0 67 -13t61 -28.5t55 -28.5t50 -13z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="1596" 
d="M1505 716q0 -161 -51 -295.5t-144 -231.5t-223.5 -150.5t-288.5 -53.5t-288 53.5t-223 150.5t-144 231.5t-51 295.5t51 295.5t144 232t223 151.5t288 54t288.5 -54t223.5 -151.5t144 -232t51 -295.5zM1306 716q0 132 -36 237t-102 177.5t-160 111.5t-210 39
q-115 0 -209 -39t-160.5 -111.5t-102.5 -177.5t-36 -237t36 -236.5t102.5 -177t160.5 -111t209 -38.5q116 0 210 38.5t160 111t102 177t36 236.5zM640 1782q32 0 48 -6.5t36 -26.5l211 -203h-139q-21 0 -33 3.5t-29 15.5l-295 217h201z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="1596" 
d="M1505 716q0 -161 -51 -295.5t-144 -231.5t-223.5 -150.5t-288.5 -53.5t-288 53.5t-223 150.5t-144 231.5t-51 295.5t51 295.5t144 232t223 151.5t288 54t288.5 -54t223.5 -151.5t144 -232t51 -295.5zM1306 716q0 132 -36 237t-102 177.5t-160 111.5t-210 39
q-115 0 -209 -39t-160.5 -111.5t-102.5 -177.5t-36 -237t36 -236.5t102.5 -177t160.5 -111t209 -38.5q116 0 210 38.5t160 111t102 177t36 236.5zM1141 1782l-294 -216q-17 -12 -30 -16t-34 -4h-138l211 203q10 10 18.5 16t17.5 10t20 5.5t28 1.5h201z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="1596" 
d="M1505 716q0 -161 -51 -295.5t-144 -231.5t-223.5 -150.5t-288.5 -53.5t-288 53.5t-223 150.5t-144 231.5t-51 295.5t51 295.5t144 232t223 151.5t288 54t288.5 -54t223.5 -151.5t144 -232t51 -295.5zM1306 716q0 132 -36 237t-102 177.5t-160 111.5t-210 39
q-115 0 -209 -39t-160.5 -111.5t-102.5 -177.5t-36 -237t36 -236.5t102.5 -177t160.5 -111t209 -38.5q116 0 210 38.5t160 111t102 177t36 236.5zM1126 1546h-135q-12 0 -26 3.5t-23 9.5l-130 95q-8 4 -12 8q-8 -6 -12 -8l-130 -95q-9 -6 -23 -9.5t-26 -3.5h-135l238 210
h176z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="1596" 
d="M1505 716q0 -161 -51 -295.5t-144 -231.5t-223.5 -150.5t-288.5 -53.5t-288 53.5t-223 150.5t-144 231.5t-51 295.5t51 295.5t144 232t223 151.5t288 54t288.5 -54t223.5 -151.5t144 -232t51 -295.5zM1306 716q0 132 -36 237t-102 177.5t-160 111.5t-210 39
q-115 0 -209 -39t-160.5 -111.5t-102.5 -177.5t-36 -237t36 -236.5t102.5 -177t160.5 -111t209 -38.5q116 0 210 38.5t160 111t102 177t36 236.5zM921 1663q35 0 53.5 20.5t19.5 57.5h98q0 -43 -11 -79t-32 -62.5t-52 -41t-71 -14.5q-35 0 -67 13t-61.5 28.5t-55.5 28.5
t-50 13q-34 0 -52.5 -21.5t-19.5 -57.5h-100q0 43 11.5 79.5t33 62.5t53 41t70.5 15q35 0 67 -13t61 -28.5t55 -28.5t50 -13z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="1596" 
d="M1505 716q0 -161 -51 -295.5t-144 -231.5t-223.5 -150.5t-288.5 -53.5t-288 53.5t-223 150.5t-144 231.5t-51 295.5t51 295.5t144 232t223 151.5t288 54t288.5 -54t223.5 -151.5t144 -232t51 -295.5zM1306 716q0 132 -36 237t-102 177.5t-160 111.5t-210 39
q-115 0 -209 -39t-160.5 -111.5t-102.5 -177.5t-36 -237t36 -236.5t102.5 -177t160.5 -111t209 -38.5q116 0 210 38.5t160 111t102 177t36 236.5zM698 1666q0 -22 -9 -41.5t-24 -34.5t-35.5 -23.5t-43.5 -8.5q-21 0 -40.5 8.5t-34.5 23.5t-24 34.5t-9 41.5q0 23 9 43.5
t24 35.5t34.5 24t40.5 9q23 0 43.5 -9t35.5 -24t24 -35.5t9 -43.5zM1124 1666q0 -22 -9 -41.5t-24 -34.5t-35 -23.5t-42 -8.5q-23 0 -43 8.5t-35 23.5t-23.5 34.5t-8.5 41.5q0 23 8.5 43.5t23.5 35.5t35 24t43 9q22 0 42 -9t35 -24t24 -35.5t9 -43.5z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" 
d="M1017 1014l-344 -344l354 -353l-95 -96l-354 354l-357 -356l-95 96l356 356l-345 345l95 96l345 -346l344 344z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="1596" 
d="M1505 716q0 -161 -51 -295.5t-144 -231.5t-223.5 -150.5t-288.5 -53.5q-108 0 -202 24.5t-173 72.5l-100 -136q-22 -29 -51 -42t-58 -13h-78l191 260q-112 98 -173.5 242t-61.5 323q0 161 51 295.5t144 232t223 151.5t288 54q115 0 215 -29t183 -83l82 111q20 27 36 38.5
t48 11.5h100l-172 -235q103 -98 159 -237t56 -310zM290 716q0 -135 37.5 -240.5t106.5 -178.5l659 899q-60 42 -134 63.5t-161 21.5q-115 0 -209 -39t-160.5 -111.5t-102.5 -177.5t-36 -237zM1306 716q0 126 -32.5 226.5t-92.5 172.5l-655 -892q116 -70 272 -70
q116 0 210 38.5t160 111t102 177t36 236.5z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="1460" 
d="M731 154q89 0 159 30t118.5 84t74 129t25.5 165v871h193v-871q0 -124 -39.5 -230t-113 -183.5t-179.5 -121.5t-238 -44t-238 44t-180 121.5t-113.5 183.5t-39.5 230v871h193v-870q0 -90 25.5 -165t74 -129t119 -84.5t159.5 -30.5zM570 1782q32 0 48 -6.5t36 -26.5
l211 -203h-139q-21 0 -33 3.5t-29 15.5l-295 217h201z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="1460" 
d="M731 154q89 0 159 30t118.5 84t74 129t25.5 165v871h193v-871q0 -124 -39.5 -230t-113 -183.5t-179.5 -121.5t-238 -44t-238 44t-180 121.5t-113.5 183.5t-39.5 230v871h193v-870q0 -90 25.5 -165t74 -129t119 -84.5t159.5 -30.5zM1071 1782l-294 -216q-17 -12 -30 -16
t-34 -4h-138l211 203q10 10 18.5 16t17.5 10t20 5.5t28 1.5h201z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="1460" 
d="M731 154q89 0 159 30t118.5 84t74 129t25.5 165v871h193v-871q0 -124 -39.5 -230t-113 -183.5t-179.5 -121.5t-238 -44t-238 44t-180 121.5t-113.5 183.5t-39.5 230v871h193v-870q0 -90 25.5 -165t74 -129t119 -84.5t159.5 -30.5zM1056 1546h-135q-12 0 -26 3.5t-23 9.5
l-130 95q-8 4 -12 8q-8 -6 -12 -8l-130 -95q-9 -6 -23 -9.5t-26 -3.5h-135l238 210h176z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="1460" 
d="M731 154q89 0 159 30t118.5 84t74 129t25.5 165v871h193v-871q0 -124 -39.5 -230t-113 -183.5t-179.5 -121.5t-238 -44t-238 44t-180 121.5t-113.5 183.5t-39.5 230v871h193v-870q0 -90 25.5 -165t74 -129t119 -84.5t159.5 -30.5zM628 1666q0 -22 -9 -41.5t-24 -34.5
t-35.5 -23.5t-43.5 -8.5q-21 0 -40.5 8.5t-34.5 23.5t-24 34.5t-9 41.5q0 23 9 43.5t24 35.5t34.5 24t40.5 9q23 0 43.5 -9t35.5 -24t24 -35.5t9 -43.5zM1054 1666q0 -22 -9 -41.5t-24 -34.5t-35 -23.5t-42 -8.5q-23 0 -43 8.5t-35 23.5t-23.5 34.5t-8.5 41.5q0 23 8.5 43.5
t23.5 35.5t35 24t43 9q22 0 42 -9t35 -24t24 -35.5t9 -43.5z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="1258" 
d="M726 570v-570h-193v570l-525 863h170q26 0 41 -13t26 -32l328 -557q20 -35 33.5 -66t24.5 -61q11 31 24 62t33 65l327 557q9 16 24.5 30.5t40.5 14.5h172zM971 1782l-294 -216q-17 -12 -30 -16t-34 -4h-138l211 203q10 10 18.5 16t17.5 10t20 5.5t28 1.5h201z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="1222" 
d="M387 272v-272h-193v1433h193v-264h230q136 0 236.5 -31.5t166.5 -89.5t98.5 -140t32.5 -183q0 -100 -35 -183t-102.5 -143t-167.5 -93.5t-229 -33.5h-230zM387 426h230q83 0 146.5 22t106.5 61.5t65 94.5t22 121q0 137 -84.5 214t-255.5 77h-230v-590z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="1218" 
d="M673 1454q103 0 178.5 -30t124.5 -76.5t72.5 -101.5t23.5 -106q0 -60 -21.5 -103t-53.5 -76t-69.5 -58t-69.5 -49t-53.5 -49.5t-21.5 -57.5q0 -39 26.5 -65t66.5 -48.5t86.5 -46t86.5 -58.5t66.5 -86t26.5 -129t-28.5 -139t-78.5 -103.5t-117.5 -65t-146.5 -22.5
q-97 0 -176 34.5t-139 88.5l41 68q8 13 19.5 20t28.5 7q18 0 38 -14t47.5 -31t65 -31t90.5 -14q44 0 79 13.5t59.5 36.5t37.5 55t13 69q0 56 -28 91t-70 60.5t-91 46.5t-91 50t-70 70.5t-28 107.5q0 53 22.5 92.5t56 71t72.5 58.5t72.5 57t56 65.5t22.5 83.5
q0 32 -12.5 64.5t-40.5 58.5t-72.5 42.5t-107.5 16.5q-68 0 -123.5 -21t-95 -63t-61 -105t-21.5 -147v-986h-179v992q0 104 34.5 189t98.5 146t154 94t200 33z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" horiz-adv-x="1014" 
d="M890 0h-79q-26 0 -42 8t-21 34l-20 94q-40 -36 -78 -64.5t-80 -48t-89.5 -29.5t-105.5 -10q-59 0 -110.5 16.5t-89.5 49.5t-60.5 83.5t-22.5 119.5q0 60 33 115.5t106.5 98.5t192.5 70.5t291 31.5v79q0 118 -50.5 178.5t-149.5 60.5q-65 0 -109.5 -16.5t-77 -37t-56 -37
t-46.5 -16.5q-18 0 -31.5 9.5t-21.5 23.5l-32 57q84 81 181 121t215 40q85 0 151 -28t111 -78t68 -121t23 -156v-648zM428 109q47 0 86 9.5t73.5 27t66 42.5t61.5 57v211q-123 -4 -209 -19.5t-140 -40.5t-78.5 -59t-24.5 -76q0 -40 13 -69t35 -47.5t52 -27t65 -8.5z
M428 1449q33 0 49 -10.5t30 -33.5l149 -242h-102q-21 0 -34 6.5t-27 21.5l-234 258h169z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" horiz-adv-x="1014" 
d="M890 0h-79q-26 0 -42 8t-21 34l-20 94q-40 -36 -78 -64.5t-80 -48t-89.5 -29.5t-105.5 -10q-59 0 -110.5 16.5t-89.5 49.5t-60.5 83.5t-22.5 119.5q0 60 33 115.5t106.5 98.5t192.5 70.5t291 31.5v79q0 118 -50.5 178.5t-149.5 60.5q-65 0 -109.5 -16.5t-77 -37t-56 -37
t-46.5 -16.5q-18 0 -31.5 9.5t-21.5 23.5l-32 57q84 81 181 121t215 40q85 0 151 -28t111 -78t68 -121t23 -156v-648zM428 109q47 0 86 9.5t73.5 27t66 42.5t61.5 57v211q-123 -4 -209 -19.5t-140 -40.5t-78.5 -59t-24.5 -76q0 -40 13 -69t35 -47.5t52 -27t65 -8.5z
M818 1449l-233 -258q-14 -15 -27.5 -21.5t-34.5 -6.5h-106l148 242q14 23 30.5 33.5t48.5 10.5h174z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" horiz-adv-x="1014" 
d="M890 0h-79q-26 0 -42 8t-21 34l-20 94q-40 -36 -78 -64.5t-80 -48t-89.5 -29.5t-105.5 -10q-59 0 -110.5 16.5t-89.5 49.5t-60.5 83.5t-22.5 119.5q0 60 33 115.5t106.5 98.5t192.5 70.5t291 31.5v79q0 118 -50.5 178.5t-149.5 60.5q-65 0 -109.5 -16.5t-77 -37t-56 -37
t-46.5 -16.5q-18 0 -31.5 9.5t-21.5 23.5l-32 57q84 81 181 121t215 40q85 0 151 -28t111 -78t68 -121t23 -156v-648zM428 109q47 0 86 9.5t73.5 27t66 42.5t61.5 57v211q-123 -4 -209 -19.5t-140 -40.5t-78.5 -59t-24.5 -76q0 -40 13 -69t35 -47.5t52 -27t65 -8.5z
M833 1169h-119q-21 0 -40 14l-128 126l-17 17l-16 -17l-129 -126q-6 -5 -17 -9.5t-23 -4.5h-123l223 264h166z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" horiz-adv-x="1014" 
d="M890 0h-79q-26 0 -42 8t-21 34l-20 94q-40 -36 -78 -64.5t-80 -48t-89.5 -29.5t-105.5 -10q-59 0 -110.5 16.5t-89.5 49.5t-60.5 83.5t-22.5 119.5q0 60 33 115.5t106.5 98.5t192.5 70.5t291 31.5v79q0 118 -50.5 178.5t-149.5 60.5q-65 0 -109.5 -16.5t-77 -37t-56 -37
t-46.5 -16.5q-18 0 -31.5 9.5t-21.5 23.5l-32 57q84 81 181 121t215 40q85 0 151 -28t111 -78t68 -121t23 -156v-648zM428 109q47 0 86 9.5t73.5 27t66 42.5t61.5 57v211q-123 -4 -209 -19.5t-140 -40.5t-78.5 -59t-24.5 -76q0 -40 13 -69t35 -47.5t52 -27t65 -8.5z
M638 1325q36 0 55.5 21t20.5 65h108q0 -47 -12.5 -86.5t-36 -67.5t-56 -43.5t-72.5 -15.5q-35 0 -65.5 14.5t-57.5 31.5t-51 31.5t-47 14.5q-72 0 -74 -88h-111q0 48 13 87.5t37 68t57 44t72 15.5q35 0 65.5 -14.5t57.5 -31.5t50.5 -31.5t46.5 -14.5z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" horiz-adv-x="1014" 
d="M890 0h-79q-26 0 -42 8t-21 34l-20 94q-40 -36 -78 -64.5t-80 -48t-89.5 -29.5t-105.5 -10q-59 0 -110.5 16.5t-89.5 49.5t-60.5 83.5t-22.5 119.5q0 60 33 115.5t106.5 98.5t192.5 70.5t291 31.5v79q0 118 -50.5 178.5t-149.5 60.5q-65 0 -109.5 -16.5t-77 -37t-56 -37
t-46.5 -16.5q-18 0 -31.5 9.5t-21.5 23.5l-32 57q84 81 181 121t215 40q85 0 151 -28t111 -78t68 -121t23 -156v-648zM428 109q47 0 86 9.5t73.5 27t66 42.5t61.5 57v211q-123 -4 -209 -19.5t-140 -40.5t-78.5 -59t-24.5 -76q0 -40 13 -69t35 -47.5t52 -27t65 -8.5z
M460 1289q0 -23 -9 -43t-25 -35t-36.5 -24t-43.5 -9q-22 0 -42 9t-35.5 24t-24.5 35t-9 43t9 44t24.5 36.5t35.5 24.5t42 9q23 0 43.5 -9t36.5 -24.5t25 -36.5t9 -44zM819 1289q0 -23 -9 -43t-24.5 -35t-36 -24t-43.5 -9t-43.5 9t-35.5 24t-24 35t-9 43t9 44t24 36.5
t35.5 24.5t43.5 9t43.5 -9t36 -24.5t24.5 -36.5t9 -44z" />
    <glyph glyph-name="aring" unicode="&#xe5;" horiz-adv-x="1014" 
d="M890 0h-79q-26 0 -42 8t-21 34l-20 94q-40 -36 -78 -64.5t-80 -48t-89.5 -29.5t-105.5 -10q-59 0 -110.5 16.5t-89.5 49.5t-60.5 83.5t-22.5 119.5q0 60 33 115.5t106.5 98.5t192.5 70.5t291 31.5v79q0 118 -50.5 178.5t-149.5 60.5q-65 0 -109.5 -16.5t-77 -37t-56 -37
t-46.5 -16.5q-18 0 -31.5 9.5t-21.5 23.5l-32 57q84 81 181 121t215 40q85 0 151 -28t111 -78t68 -121t23 -156v-648zM428 109q47 0 86 9.5t73.5 27t66 42.5t61.5 57v211q-123 -4 -209 -19.5t-140 -40.5t-78.5 -59t-24.5 -76q0 -40 13 -69t35 -47.5t52 -27t65 -8.5z
M328 1315q0 42 16 76t43.5 59t63.5 38.5t76 13.5q41 0 77.5 -13.5t64.5 -38.5t44 -59t16 -76q0 -41 -16 -75t-44 -58t-64.5 -37.5t-77.5 -13.5q-40 0 -76 13.5t-63.5 37.5t-43.5 58t-16 75zM428 1315q0 -44 27 -72t74 -28q45 0 72.5 28t27.5 72q0 45 -27.5 73t-72.5 28
q-47 0 -74 -28t-27 -73z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="1632" 
d="M1166 1029q82 0 152.5 -32t122 -93t81 -148.5t29.5 -199.5q0 -41 -8 -55.5t-33 -14.5h-627q4 -91 27 -158.5t61.5 -112.5t91 -67t117.5 -22q69 0 115 14.5t76.5 32.5t49.5 32.5t35 14.5q14 0 23 -4.5t15 -12.5l47 -61q-33 -40 -76.5 -69.5t-93 -48.5t-102.5 -28.5
t-105 -9.5q-117 0 -212.5 56.5t-150.5 172.5q-27 -62 -70.5 -106t-96.5 -72t-112.5 -40.5t-118.5 -12.5q-69 0 -126 17.5t-98.5 52.5t-64 88t-22.5 125q0 60 33 118t106.5 104t192.5 75.5t291 33.5v50q0 118 -50.5 181t-149.5 63q-65 0 -109.5 -17.5t-77 -38.5t-56 -38.5
t-46.5 -17.5q-18 0 -31.5 9.5t-21.5 23.5l-32 57q84 81 174.5 121t203.5 40q120 0 193 -51t106 -142q54 88 140.5 139.5t207.5 51.5zM715 486q-123 -5 -209 -22.5t-140 -45.5t-78.5 -64t-24.5 -78q0 -87 50 -127t131 -40q57 0 106.5 18t86 55t57.5 92t21 129v83zM1159 898
q-61 0 -109 -20.5t-82.5 -59.5t-55 -95t-27.5 -126h508q0 65 -15.5 120.5t-45.5 95.5t-73.5 62.5t-99.5 22.5z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="934" 
d="M400 -247q6 0 14 -3.5t19 -8t27 -8t37 -3.5q42 0 63.5 16.5t21.5 42.5q0 19 -11 32t-31.5 22t-50.5 15t-68 11l37 121q-83 11 -153.5 50.5t-121.5 105.5t-80 157t-29 204t31.5 209t92 165.5t149.5 108.5t204 39q106 0 188 -34.5t145 -97.5l-47 -64q-8 -11 -16 -17t-23 -6
t-32.5 12.5t-44.5 27.5t-65.5 27.5t-94.5 12.5q-74 0 -131 -26.5t-95.5 -76.5t-58 -121t-19.5 -159q0 -92 21 -163.5t59 -120t92.5 -74t122.5 -25.5q65 0 107 15.5t70 34.5t46 34.5t36 15.5q11 0 19.5 -4.5t14.5 -12.5l50 -65q-59 -72 -144 -109t-182 -45l-17 -58
q90 -20 130.5 -54.5t40.5 -88.5q0 -32 -16 -57.5t-44.5 -43.5t-68.5 -27.5t-88 -9.5q-41 0 -78 8.5t-68 22.5l17 55q6 18 23 18z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="1048" 
d="M547 1029q91 0 168 -30.5t133 -88t87.5 -142t31.5 -192.5q0 -42 -9 -56t-34 -14h-674q2 -96 26 -167t66 -118.5t100 -71t130 -23.5q67 0 115.5 15.5t83.5 33.5t58.5 33.5t40.5 15.5q22 0 34 -17l50 -65q-33 -40 -79 -69.5t-98.5 -48.5t-108.5 -28.5t-111 -9.5
q-105 0 -193.5 35.5t-153 104t-100.5 169.5t-36 232q0 106 32.5 198t93.5 159.5t149 106t198 38.5zM551 898q-129 0 -203 -74.5t-92 -206.5h551q0 62 -17 113.5t-50 89t-80.5 58t-108.5 20.5zM451 1449q33 0 49 -10.5t30 -33.5l149 -242h-102q-21 0 -34 6.5t-27 21.5
l-234 258h169z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="1048" 
d="M547 1029q91 0 168 -30.5t133 -88t87.5 -142t31.5 -192.5q0 -42 -9 -56t-34 -14h-674q2 -96 26 -167t66 -118.5t100 -71t130 -23.5q67 0 115.5 15.5t83.5 33.5t58.5 33.5t40.5 15.5q22 0 34 -17l50 -65q-33 -40 -79 -69.5t-98.5 -48.5t-108.5 -28.5t-111 -9.5
q-105 0 -193.5 35.5t-153 104t-100.5 169.5t-36 232q0 106 32.5 198t93.5 159.5t149 106t198 38.5zM551 898q-129 0 -203 -74.5t-92 -206.5h551q0 62 -17 113.5t-50 89t-80.5 58t-108.5 20.5zM841 1449l-233 -258q-14 -15 -27.5 -21.5t-34.5 -6.5h-106l148 242
q14 23 30.5 33.5t48.5 10.5h174z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="1048" 
d="M547 1029q91 0 168 -30.5t133 -88t87.5 -142t31.5 -192.5q0 -42 -9 -56t-34 -14h-674q2 -96 26 -167t66 -118.5t100 -71t130 -23.5q67 0 115.5 15.5t83.5 33.5t58.5 33.5t40.5 15.5q22 0 34 -17l50 -65q-33 -40 -79 -69.5t-98.5 -48.5t-108.5 -28.5t-111 -9.5
q-105 0 -193.5 35.5t-153 104t-100.5 169.5t-36 232q0 106 32.5 198t93.5 159.5t149 106t198 38.5zM551 898q-129 0 -203 -74.5t-92 -206.5h551q0 62 -17 113.5t-50 89t-80.5 58t-108.5 20.5zM856 1169h-119q-21 0 -40 14l-128 126l-17 17l-16 -17l-129 -126q-6 -5 -17 -9.5
t-23 -4.5h-123l223 264h166z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="1048" 
d="M547 1029q91 0 168 -30.5t133 -88t87.5 -142t31.5 -192.5q0 -42 -9 -56t-34 -14h-674q2 -96 26 -167t66 -118.5t100 -71t130 -23.5q67 0 115.5 15.5t83.5 33.5t58.5 33.5t40.5 15.5q22 0 34 -17l50 -65q-33 -40 -79 -69.5t-98.5 -48.5t-108.5 -28.5t-111 -9.5
q-105 0 -193.5 35.5t-153 104t-100.5 169.5t-36 232q0 106 32.5 198t93.5 159.5t149 106t198 38.5zM551 898q-129 0 -203 -74.5t-92 -206.5h551q0 62 -17 113.5t-50 89t-80.5 58t-108.5 20.5zM483 1289q0 -23 -9 -43t-25 -35t-36.5 -24t-43.5 -9q-22 0 -42 9t-35.5 24
t-24.5 35t-9 43t9 44t24.5 36.5t35.5 24.5t42 9q23 0 43.5 -9t36.5 -24.5t25 -36.5t9 -44zM842 1289q0 -23 -9 -43t-24.5 -35t-36 -24t-43.5 -9t-43.5 9t-35.5 24t-24 35t-9 43t9 44t24 36.5t35.5 24.5t43.5 9t43.5 -9t36 -24.5t24.5 -36.5t9 -44z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="512" 
d="M344 1013v-1013h-178v1013h178zM162 1449q33 0 49 -10.5t30 -33.5l149 -242h-102q-21 0 -34 6.5t-27 21.5l-234 258h169z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="512" 
d="M344 1013v-1013h-178v1013h178zM552 1449l-233 -258q-14 -15 -27.5 -21.5t-34.5 -6.5h-106l148 242q14 23 30.5 33.5t48.5 10.5h174z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="512" 
d="M344 1013v-1013h-178v1013h178zM566 1169h-119q-21 0 -40 14l-128 126l-17 17l-16 -17l-129 -126q-6 -5 -17 -9.5t-23 -4.5h-123l223 264h166z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="512" 
d="M344 1013v-1013h-178v1013h178zM194 1289q0 -23 -9 -43t-25 -35t-36.5 -24t-43.5 -9q-22 0 -42 9t-35.5 24t-24.5 35t-9 43t9 44t24.5 36.5t35.5 24.5t42 9q23 0 43.5 -9t36.5 -24.5t25 -36.5t9 -44zM553 1289q0 -23 -9 -43t-24.5 -35t-36 -24t-43.5 -9t-43.5 9t-35.5 24
t-24 35t-9 43t9 44t24 36.5t35.5 24.5t43.5 9t43.5 -9t36 -24.5t24.5 -36.5t9 -44z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="1106" 
d="M417 1065q-4 7 -6.5 13.5t-2.5 12.5q0 22 23 37l103 72q-45 20 -95.5 37t-107.5 31q-18 5 -30.5 18.5t-12.5 36.5q0 15 5 29l20 62q96 -16 186 -46t171 -78l167 122l35 -57q8 -13 8 -24q0 -21 -22 -37l-97 -67q60 -49 109.5 -111t84.5 -139.5t54.5 -170t19.5 -202.5
q0 -143 -31 -257t-92.5 -194t-153 -123t-211.5 -43q-98 0 -183 33t-147.5 94.5t-98.5 150.5t-36 201q0 94 31 177.5t89 146.5t140.5 100t186.5 37q100 0 188.5 -43t153.5 -130q-20 136 -78.5 231t-152.5 159l-184 -135zM545 125q71 0 128.5 27t98 81.5t63.5 137t26 193.5
q-16 43 -42 83.5t-63.5 72t-87 50t-111.5 18.5q-75 0 -131.5 -25t-94.5 -68.5t-57.5 -103t-19.5 -127.5q0 -81 23 -144.5t63 -106.5t92.5 -65.5t112.5 -22.5z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="1112" 
d="M146 0v1013h106q38 0 48 -37l14 -110q66 73 147.5 118t188.5 45q83 0 146.5 -27.5t106 -78t64.5 -121.5t22 -157v-645h-178v645q0 115 -52.5 178.5t-160.5 63.5q-79 0 -147.5 -38t-126.5 -103v-746h-178zM675 1325q36 0 55.5 21t20.5 65h108q0 -47 -12.5 -86.5t-36 -67.5
t-56 -43.5t-72.5 -15.5q-35 0 -65.5 14.5t-57.5 31.5t-51 31.5t-47 14.5q-72 0 -74 -88h-111q0 48 13 87.5t37 68t57 44t72 15.5q35 0 65.5 -14.5t57.5 -31.5t50.5 -31.5t46.5 -14.5z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="1112" 
d="M556 1029q111 0 200.5 -37t152 -105t96 -164.5t33.5 -215.5q0 -120 -33.5 -216t-96 -164t-152 -104.5t-200.5 -36.5t-200.5 36.5t-152.5 104.5t-97 164t-34 216q0 119 34 215.5t97 164.5t152.5 105t200.5 37zM556 125q150 0 224 100.5t74 280.5q0 181 -74 282t-224 101
q-76 0 -132 -26t-93.5 -75t-56 -120.5t-18.5 -161.5t18.5 -161t56 -119.5t93.5 -74.5t132 -26zM458 1449q33 0 49 -10.5t30 -33.5l149 -242h-102q-21 0 -34 6.5t-27 21.5l-234 258h169z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="1112" 
d="M556 1029q111 0 200.5 -37t152 -105t96 -164.5t33.5 -215.5q0 -120 -33.5 -216t-96 -164t-152 -104.5t-200.5 -36.5t-200.5 36.5t-152.5 104.5t-97 164t-34 216q0 119 34 215.5t97 164.5t152.5 105t200.5 37zM556 125q150 0 224 100.5t74 280.5q0 181 -74 282t-224 101
q-76 0 -132 -26t-93.5 -75t-56 -120.5t-18.5 -161.5t18.5 -161t56 -119.5t93.5 -74.5t132 -26zM848 1449l-233 -258q-14 -15 -27.5 -21.5t-34.5 -6.5h-106l148 242q14 23 30.5 33.5t48.5 10.5h174z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="1112" 
d="M556 1029q111 0 200.5 -37t152 -105t96 -164.5t33.5 -215.5q0 -120 -33.5 -216t-96 -164t-152 -104.5t-200.5 -36.5t-200.5 36.5t-152.5 104.5t-97 164t-34 216q0 119 34 215.5t97 164.5t152.5 105t200.5 37zM556 125q150 0 224 100.5t74 280.5q0 181 -74 282t-224 101
q-76 0 -132 -26t-93.5 -75t-56 -120.5t-18.5 -161.5t18.5 -161t56 -119.5t93.5 -74.5t132 -26zM863 1169h-119q-21 0 -40 14l-128 126l-17 17l-16 -17l-129 -126q-6 -5 -17 -9.5t-23 -4.5h-123l223 264h166z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="1112" 
d="M556 1029q111 0 200.5 -37t152 -105t96 -164.5t33.5 -215.5q0 -120 -33.5 -216t-96 -164t-152 -104.5t-200.5 -36.5t-200.5 36.5t-152.5 104.5t-97 164t-34 216q0 119 34 215.5t97 164.5t152.5 105t200.5 37zM556 125q150 0 224 100.5t74 280.5q0 181 -74 282t-224 101
q-76 0 -132 -26t-93.5 -75t-56 -120.5t-18.5 -161.5t18.5 -161t56 -119.5t93.5 -74.5t132 -26zM668 1325q36 0 55.5 21t20.5 65h108q0 -47 -12.5 -86.5t-36 -67.5t-56 -43.5t-72.5 -15.5q-35 0 -65.5 14.5t-57.5 31.5t-51 31.5t-47 14.5q-72 0 -74 -88h-111q0 48 13 87.5
t37 68t57 44t72 15.5q35 0 65.5 -14.5t57.5 -31.5t50.5 -31.5t46.5 -14.5z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="1112" 
d="M556 1029q111 0 200.5 -37t152 -105t96 -164.5t33.5 -215.5q0 -120 -33.5 -216t-96 -164t-152 -104.5t-200.5 -36.5t-200.5 36.5t-152.5 104.5t-97 164t-34 216q0 119 34 215.5t97 164.5t152.5 105t200.5 37zM556 125q150 0 224 100.5t74 280.5q0 181 -74 282t-224 101
q-76 0 -132 -26t-93.5 -75t-56 -120.5t-18.5 -161.5t18.5 -161t56 -119.5t93.5 -74.5t132 -26zM490 1289q0 -23 -9 -43t-25 -35t-36.5 -24t-43.5 -9q-22 0 -42 9t-35.5 24t-24.5 35t-9 43t9 44t24.5 36.5t35.5 24.5t42 9q23 0 43.5 -9t36.5 -24.5t25 -36.5t9 -44zM849 1289
q0 -23 -9 -43t-24.5 -35t-36 -24t-43.5 -9t-43.5 9t-35.5 24t-24 35t-9 43t9 44t24 36.5t35.5 24.5t43.5 9t43.5 -9t36 -24.5t24.5 -36.5t9 -44z" />
    <glyph glyph-name="divide" unicode="&#xf7;" 
d="M100 739h958v-135h-958v135zM454 1026q0 26 9.5 49t26 40t39 27t49.5 10q26 0 48.5 -10t39.5 -27t27 -40t10 -49q0 -27 -10 -49.5t-27 -39.5t-39.5 -26.5t-48.5 -9.5q-27 0 -49.5 9.5t-39 26.5t-26 39.5t-9.5 49.5zM454 314q0 26 9.5 49t26 40t39 27t49.5 10
q26 0 48.5 -10t39.5 -27t27 -40t10 -49q0 -27 -10 -49.5t-27 -39.5t-39.5 -26.5t-48.5 -9.5q-27 0 -49.5 9.5t-39 26.5t-26 39.5t-9.5 49.5z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="1112" 
d="M912 884q61 -68 94 -163.5t33 -213.5q0 -120 -33.5 -216t-96 -164t-152 -104.5t-200.5 -36.5q-76 0 -141.5 17t-119.5 49l-55 -74q-22 -29 -51.5 -41.5t-58.5 -12.5h-67l145 196q-66 69 -101 166t-35 221q0 119 34 215.5t97 164.5t152.5 105t200.5 37q79 0 146.5 -19
t123.5 -54l68 91q20 27 36 38.5t48 11.5h90zM246 506q0 -160 59 -257l436 590q-73 56 -184 56q-76 0 -134 -27t-97.5 -77t-59.5 -122.5t-20 -162.5zM557 120q75 0 132.5 26.5t97 76.5t59.5 121.5t20 161.5q0 151 -52 247l-433 -585q70 -48 176 -48z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="1112" 
d="M300 1013v-646q0 -115 53 -178t160 -63q78 0 147 37t127 103v747h178v-1013h-106q-38 0 -48 37l-14 109q-66 -73 -148 -117.5t-188 -44.5q-83 0 -146.5 27.5t-106.5 77.5t-64.5 121t-21.5 157v646h178zM452 1449q33 0 49 -10.5t30 -33.5l149 -242h-102q-21 0 -34 6.5
t-27 21.5l-234 258h169z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="1112" 
d="M300 1013v-646q0 -115 53 -178t160 -63q78 0 147 37t127 103v747h178v-1013h-106q-38 0 -48 37l-14 109q-66 -73 -148 -117.5t-188 -44.5q-83 0 -146.5 27.5t-106.5 77.5t-64.5 121t-21.5 157v646h178zM842 1449l-233 -258q-14 -15 -27.5 -21.5t-34.5 -6.5h-106l148 242
q14 23 30.5 33.5t48.5 10.5h174z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="1112" 
d="M300 1013v-646q0 -115 53 -178t160 -63q78 0 147 37t127 103v747h178v-1013h-106q-38 0 -48 37l-14 109q-66 -73 -148 -117.5t-188 -44.5q-83 0 -146.5 27.5t-106.5 77.5t-64.5 121t-21.5 157v646h178zM857 1169h-119q-21 0 -40 14l-128 126l-17 17l-16 -17l-129 -126
q-6 -5 -17 -9.5t-23 -4.5h-123l223 264h166z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="1112" 
d="M300 1013v-646q0 -115 53 -178t160 -63q78 0 147 37t127 103v747h178v-1013h-106q-38 0 -48 37l-14 109q-66 -73 -148 -117.5t-188 -44.5q-83 0 -146.5 27.5t-106.5 77.5t-64.5 121t-21.5 157v646h178zM484 1289q0 -23 -9 -43t-25 -35t-36.5 -24t-43.5 -9q-22 0 -42 9
t-35.5 24t-24.5 35t-9 43t9 44t24.5 36.5t35.5 24.5t42 9q23 0 43.5 -9t36.5 -24.5t25 -36.5t9 -44zM843 1289q0 -23 -9 -43t-24.5 -35t-36 -24t-43.5 -9t-43.5 9t-35.5 24t-24 35t-9 43t9 44t24 36.5t35.5 24.5t43.5 9t43.5 -9t36 -24.5t24.5 -36.5t9 -44z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="1024" 
d="M443 -299q-9 -20 -22.5 -32t-41.5 -12h-132l185 402l-418 954h154q23 0 36 -11.5t19 -25.5l271 -638q9 -22 15.5 -44t11.5 -45q7 23 14 45t16 45l263 637q6 16 20.5 26.5t31.5 10.5h142zM825 1449l-233 -258q-14 -15 -27.5 -21.5t-34.5 -6.5h-106l148 242
q14 23 30.5 33.5t48.5 10.5h174z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="1104" 
d="M146 -343v1816h178v-607q63 74 145 118.5t187 44.5q87 0 158 -33t121 -98.5t77 -162t27 -222.5q0 -112 -30 -208.5t-86 -167t-137.5 -111t-183.5 -40.5q-95 0 -161 34.5t-117 97.5v-461h-178zM597 887q-87 0 -152.5 -40t-120.5 -113v-490q49 -66 107.5 -93t130.5 -27
q141 0 217 101t76 288q0 99 -17.5 170t-50.5 116.5t-81 66.5t-109 21z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="1024" 
d="M443 -299q-9 -20 -22.5 -32t-41.5 -12h-132l185 402l-418 954h154q23 0 36 -11.5t19 -25.5l271 -638q9 -22 15.5 -44t11.5 -45q7 23 14 45t16 45l263 637q6 16 20.5 26.5t31.5 10.5h142zM467 1289q0 -23 -9 -43t-25 -35t-36.5 -24t-43.5 -9q-22 0 -42 9t-35.5 24
t-24.5 35t-9 43t9 44t24.5 36.5t35.5 24.5t42 9q23 0 43.5 -9t36.5 -24.5t25 -36.5t9 -44zM826 1289q0 -23 -9 -43t-24.5 -35t-36 -24t-43.5 -9t-43.5 9t-35.5 24t-24 35t-9 43t9 44t24 36.5t35.5 24.5t43.5 9t43.5 -9t36 -24.5t24.5 -36.5t9 -44z" />
    <glyph glyph-name="Aogonek" unicode="&#x104;" horiz-adv-x="1360" 
d="M1380 -211q7 0 11.5 -4t6.5 -9l28 -66q-30 -23 -76.5 -37.5t-97.5 -14.5q-88 0 -138.5 39.5t-50.5 105.5q0 58 39.5 108.5t99.5 88.5q-25 0 -41 13t-24 33l-134 346h-643l-134 -346q-7 -18 -24 -32t-42 -14h-150l573 1433h197l573 -1433h-37q-20 -11 -41 -26.5t-38 -35
t-28 -43t-11 -49.5q0 -38 24.5 -59.5t66.5 -21.5q23 0 38 3.5t25 8t16.5 8.5t11.5 4zM414 532h535l-225 583q-22 54 -43 135q-11 -41 -21.5 -75.5t-20.5 -60.5z" />
    <glyph glyph-name="aogonek" unicode="&#x105;" horiz-adv-x="1014" 
d="M890 0q-20 -11 -41 -26.5t-38 -35t-28 -43t-11 -49.5q0 -38 24.5 -59.5t66.5 -21.5q23 0 38 3.5t25 8t16.5 8.5t11.5 4q7 0 11.5 -4t6.5 -9l28 -66q-30 -23 -76.5 -37.5t-97.5 -14.5q-88 0 -138.5 39.5t-50.5 105.5q0 29 11 57t30.5 53.5t46 48t57.5 41.5q-24 7 -34 39
l-20 94q-40 -36 -78 -64.5t-80 -48t-89.5 -29.5t-105.5 -10q-59 0 -110.5 16.5t-89.5 49.5t-60.5 83.5t-22.5 119.5q0 60 33 115.5t106.5 98.5t192.5 70.5t291 31.5v79q0 118 -50.5 178.5t-149.5 60.5q-65 0 -109.5 -16.5t-77 -37t-56 -37t-46.5 -16.5q-18 0 -31 9.5
t-22 23.5l-32 57q84 81 181 121t215 40q85 0 151 -28t111 -78t68 -121t23 -156v-648zM428 109q47 0 86 9.5t73.5 27t66 42.5t61.5 57v211q-123 -4 -209 -19.5t-140 -40.5t-78.5 -59t-24.5 -76q0 -40 13 -69t35 -47.5t52 -27t65 -8.5z" />
    <glyph glyph-name="Cacute" unicode="&#x106;" horiz-adv-x="1370" 
d="M1184 296q16 0 29 -13l76 -83q-88 -102 -213.5 -159t-302.5 -57q-155 0 -281 53.5t-215 150t-138 231.5t-49 297t52.5 297t147.5 232t227.5 150.5t292.5 53.5q158 0 272.5 -49t203.5 -133l-63 -89q-7 -10 -16 -16.5t-26 -6.5q-13 0 -27.5 9.5t-34.5 23.5t-47 30t-64 30
t-86 23.5t-113 9.5q-115 0 -210.5 -39.5t-164.5 -112.5t-107.5 -178t-38.5 -235q0 -134 38.5 -239t105 -177.5t157.5 -110.5t196 -38q64 0 115 7.5t94.5 23.5t81 40.5t75.5 58.5q17 15 33 15zM1147 1782l-294 -216q-17 -12 -30 -16t-34 -4h-138l211 203q10 10 18.5 16
t17.5 10t20 5.5t28 1.5h201z" />
    <glyph glyph-name="cacute" unicode="&#x107;" horiz-adv-x="934" 
d="M837 833q-8 -11 -16 -17t-23 -6t-32.5 12.5t-44.5 27.5t-65.5 27.5t-94.5 12.5q-74 0 -131 -26.5t-95.5 -76.5t-58 -121t-19.5 -159q0 -92 21 -163.5t59 -120t92.5 -74t122.5 -25.5q65 0 107 15.5t70 34.5t46 34.5t36 15.5q23 0 34 -17l50 -65q-66 -81 -165 -118.5
t-209 -37.5q-95 0 -176.5 35t-141.5 101.5t-94.5 163.5t-34.5 221q0 113 31.5 209t92 165.5t149.5 108.5t204 39q106 0 188 -34.5t145 -97.5zM840 1449l-233 -258q-14 -15 -27.5 -21.5t-34.5 -6.5h-106l148 242q14 23 30.5 33.5t48.5 10.5h174z" />
    <glyph glyph-name="Eogonek" unicode="&#x118;" horiz-adv-x="1188" 
d="M1030 -211q7 0 11.5 -4t6.5 -9l28 -66q-30 -23 -76.5 -37.5t-97.5 -14.5q-88 0 -138.5 39.5t-50.5 105.5q0 58 39.5 108.5t99.5 88.5h-678v1433h883v-158h-688v-476h557v-152h-557v-489h688v-158h-91q-20 -11 -41 -26.5t-38 -35t-28 -43t-11 -49.5q0 -38 24.5 -59.5
t66.5 -21.5q23 0 38 3.5t25 8t16.5 8.5t11.5 4z" />
    <glyph glyph-name="eogonek" unicode="&#x119;" horiz-adv-x="1048" 
d="M745 -211q7 0 11.5 -4t6.5 -9l28 -66q-30 -23 -76.5 -37.5t-97.5 -14.5q-88 0 -138.5 39.5t-50.5 105.5q0 53 33 99.5t85 83.5q-103 2 -189.5 38t-149.5 105t-98 169t-35 229q0 106 32.5 198t93.5 159.5t149 106t198 38.5q91 0 168 -30.5t133 -88t87.5 -142t31.5 -192.5
q0 -42 -9 -56t-34 -14h-674q2 -96 26 -167t66 -118.5t100 -71t130 -23.5q67 0 115.5 15.5t83.5 33.5t58.5 33.5t40.5 15.5q22 0 34 -17l50 -65q-50 -60 -123.5 -95.5t-155.5 -49.5q-20 -11 -39.5 -26.5t-36 -34.5t-26.5 -42t-10 -48q0 -38 24.5 -59.5t66.5 -21.5
q23 0 38 3.5t25 8t16.5 8.5t11.5 4zM551 898q-129 0 -203 -74.5t-92 -206.5h551q0 62 -17 113.5t-50 89t-80.5 58t-108.5 20.5z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="512" 
d="M344 1013v-1013h-178v1013h178z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="1086" 
d="M425 805l384 196v-129q0 -29 -27 -43l-357 -188v-478h620v-163h-814v554l-187 -95v133q0 26 25 39l162 86v716h194v-628z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="642" 
d="M409 1473v-574l178 77v-102q0 -16 -6 -26.5t-21 -17.5l-151 -67v-763h-178v697l-177 -76v105q0 29 25 40l152 68v639h178z" />
    <glyph glyph-name="Nacute" unicode="&#x143;" horiz-adv-x="1512" 
d="M274 1433q26 0 38.5 -6.5t28.5 -26.5l830 -1080q-3 26 -4 50.5t-1 47.5v1015h170v-1433h-98q-23 0 -38.5 8t-30.5 27l-829 1079q2 -25 3 -49t1 -44v-1021h-170v1433h100v0zM1122 1782l-294 -216q-17 -12 -30 -16t-34 -4h-138l211 203q10 10 18.5 16t17.5 10t20 5.5
t28 1.5h201z" />
    <glyph glyph-name="nacute" unicode="&#x144;" horiz-adv-x="1112" 
d="M146 0v1013h106q38 0 48 -37l14 -110q66 73 147.5 118t188.5 45q83 0 146.5 -27.5t106 -78t64.5 -121.5t22 -157v-645h-178v645q0 115 -52.5 178.5t-160.5 63.5q-79 0 -147.5 -38t-126.5 -103v-746h-178zM859 1449l-233 -258q-14 -15 -27.5 -21.5t-34.5 -6.5h-106
l148 242q14 23 30.5 33.5t48.5 10.5h174z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="2192" 
d="M2087 1433v-158h-688v-476h557v-152h-557v-489h688v-158h-860v240q-84 -120 -211 -187t-288 -67q-142 0 -259 53.5t-201 150t-130 231t-46 295.5t46 295.5t130 232t201 151.5t259 54q161 0 288 -67.5t211 -188.5v240h860zM1204 716q0 132 -32 237.5t-91.5 179.5
t-144 113.5t-188.5 39.5t-188.5 -39.5t-144.5 -113.5t-92.5 -179.5t-32.5 -237.5t32.5 -237.5t92.5 -179t144.5 -112.5t188.5 -39t188.5 39t144 112.5t91.5 179t32 237.5z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="1734" 
d="M1268 1029q82 0 152.5 -32t122 -93t81 -148.5t29.5 -199.5q0 -41 -8 -55.5t-33 -14.5h-627q4 -91 27 -158.5t61.5 -112.5t91 -67t117.5 -22q61 0 105.5 15.5t76.5 33.5t54.5 33.5t40.5 15.5q21 0 33 -17l51 -65q-33 -40 -76.5 -69.5t-93 -48.5t-102.5 -28.5t-105 -9.5
q-119 0 -214.5 57.5t-150.5 176.5q-54 -112 -150.5 -173t-233.5 -61q-100 0 -182 36.5t-140.5 104.5t-90.5 164t-32 216q0 119 32 215.5t91.5 164.5t143 105t185.5 37q131 0 226 -60.5t148 -170.5q50 105 143.5 168t226.5 63zM534 125q139 0 207 100.5t68 280.5
q0 90 -17 161.5t-51 120.5t-86 75t-121 26q-71 0 -123 -26t-86.5 -75t-51.5 -120.5t-17 -161.5q0 -180 68.5 -280.5t209.5 -100.5zM1262 898q-61 0 -109 -20.5t-82.5 -59.5t-55.5 -95t-28 -126h508q0 65 -15.5 120.5t-45.5 95.5t-73.5 62.5t-98.5 22.5z" />
    <glyph glyph-name="Sacute" unicode="&#x15a;" horiz-adv-x="1096" 
d="M908 1209q-9 -15 -19 -22.5t-26 -7.5q-17 0 -39.5 17t-57 37.5t-83 37.5t-117.5 17q-65 0 -115 -17.5t-83.5 -47.5t-50.5 -70.5t-17 -87.5q0 -60 29.5 -99.5t78 -67.5t110 -48.5t126 -42.5t126 -49.5t110 -69.5t78 -103t29.5 -150q0 -94 -32 -176.5t-93.5 -143.5
t-151 -96t-203.5 -35q-139 0 -253.5 50.5t-195.5 136.5l56 92q8 11 19.5 18.5t25.5 7.5q21 0 48 -22.5t67.5 -49.5t98 -49.5t140.5 -22.5q69 0 123 19t91.5 53.5t57.5 82.5t20 107q0 65 -29.5 106.5t-77.5 69.5t-109.5 47.5t-126 40t-126 47.5t-109.5 70t-77.5 107.5
t-29.5 159.5q0 76 29.5 147t85.5 126t138.5 88t189.5 33q120 0 219 -38t173 -110zM920 1782l-294 -216q-17 -12 -30 -16t-34 -4h-138l211 203q10 10 18.5 16t17.5 10t20 5.5t28 1.5h201z" />
    <glyph glyph-name="sacute" unicode="&#x15b;" horiz-adv-x="868" 
d="M726 846q-12 -22 -37 -22q-15 0 -34 11t-46.5 24.5t-65.5 25t-90 11.5q-45 0 -81 -11.5t-61.5 -31.5t-39 -46.5t-13.5 -57.5q0 -39 22.5 -65t59.5 -45t84 -33.5t96.5 -31t96.5 -36.5t84 -50t59.5 -73.5t22.5 -104.5q0 -70 -25 -129.5t-74 -103t-120 -68.5t-164 -25
q-106 0 -192 34.5t-146 88.5l42 68q8 13 19 20t29 7t38 -14t48.5 -31t69 -31t101.5 -14q52 0 91 13.5t65 36.5t38.5 53t12.5 64q0 42 -22.5 69.5t-59.5 47t-84.5 34t-97 30.5t-97 36.5t-84.5 51.5t-59.5 76.5t-22.5 110.5q0 58 24 111.5t70 94t113 64.5t153 24
q100 0 179.5 -31.5t137.5 -86.5zM764 1449l-233 -258q-14 -15 -27.5 -21.5t-34.5 -6.5h-106l148 242q14 23 30.5 33.5t48.5 10.5h174z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="1060" 
d="M908 1209q-9 -15 -19 -22.5t-26 -7.5q-17 0 -39.5 17t-57 37.5t-83 37.5t-117.5 17q-65 0 -115 -17.5t-83.5 -47.5t-50.5 -70.5t-17 -87.5q0 -60 29.5 -99.5t78 -67.5t110 -48.5t126 -42.5t126 -49.5t110 -69.5t78 -103t29.5 -150q0 -94 -32 -176.5t-93.5 -143.5
t-151 -96t-203.5 -35q-139 0 -253.5 50.5t-195.5 136.5l56 92q8 11 19.5 18.5t25.5 7.5q21 0 48 -22.5t67.5 -49.5t98 -49.5t140.5 -22.5q69 0 123 19t91.5 53.5t57.5 82.5t20 107q0 65 -29.5 106.5t-77.5 69.5t-109.5 47.5t-126 40t-126 47.5t-109.5 70t-77.5 107.5
t-29.5 159.5q0 76 29.5 147t85.5 126t138.5 88t189.5 33q120 0 219 -38t173 -110zM666 1546h-176l-238 210h135q12 0 26 -3.5t23 -9.5l130 -94q3 -2 6.5 -5t5.5 -6q2 3 5.5 6t6.5 5l130 94q9 6 23 9.5t26 3.5h135z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="868" 
d="M726 846q-12 -22 -37 -22q-15 0 -34 11t-46.5 24.5t-65.5 25t-90 11.5q-45 0 -81 -11.5t-61.5 -31.5t-39 -46.5t-13.5 -57.5q0 -39 22.5 -65t59.5 -45t84 -33.5t96.5 -31t96.5 -36.5t84 -50t59.5 -73.5t22.5 -104.5q0 -70 -25 -129.5t-74 -103t-120 -68.5t-164 -25
q-106 0 -192 34.5t-146 88.5l42 68q8 13 19 20t29 7t38 -14t48.5 -31t69 -31t101.5 -14q52 0 91 13.5t65 36.5t38.5 53t12.5 64q0 42 -22.5 69.5t-59.5 47t-84.5 34t-97 30.5t-97 36.5t-84.5 51.5t-59.5 76.5t-22.5 110.5q0 58 24 111.5t70 94t113 64.5t153 24
q100 0 179.5 -31.5t137.5 -86.5zM151 1433h123q12 0 23 -5t17 -10l128 -125q11 -9 17 -19q5 5 8.5 10t8.5 9l128 125q6 6 17.5 10.5t22.5 4.5h119l-223 -264h-166z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="1258" 
d="M726 570v-570h-193v570l-525 863h170q26 0 41 -13t26 -32l328 -557q20 -35 33.5 -66t24.5 -61q11 31 24 62t33 65l327 557q9 16 24.5 30.5t40.5 14.5h172zM528 1666q0 -22 -9 -41.5t-24 -34.5t-35.5 -23.5t-43.5 -8.5q-21 0 -40.5 8.5t-34.5 23.5t-24 34.5t-9 41.5
q0 23 9 43.5t24 35.5t34.5 24t40.5 9q23 0 43.5 -9t35.5 -24t24 -35.5t9 -43.5zM954 1666q0 -22 -9 -41.5t-24 -34.5t-35 -23.5t-42 -8.5q-23 0 -43 8.5t-35 23.5t-23.5 34.5t-8.5 41.5q0 23 8.5 43.5t23.5 35.5t35 24t43 9q22 0 42 -9t35 -24t24 -35.5t9 -43.5z" />
    <glyph glyph-name="Zacute" unicode="&#x179;" horiz-adv-x="1248" 
d="M1172 1433v-72q0 -34 -21 -64l-811 -1139h818v-158h-1072v76q0 30 19 57l812 1142h-793v158h1048zM992 1782l-294 -216q-17 -12 -30 -16t-34 -4h-138l211 203q10 10 18.5 16t17.5 10t20 5.5t28 1.5h201z" />
    <glyph glyph-name="zacute" unicode="&#x17a;" horiz-adv-x="924" 
d="M853 937q0 -19 -7 -36.5t-18 -31.5l-548 -730h553v-139h-763v74q0 13 6.5 30.5t18.5 33.5l551 735h-545v140h752v-76zM777 1449l-233 -258q-14 -15 -27.5 -21.5t-34.5 -6.5h-106l148 242q14 23 30.5 33.5t48.5 10.5h174z" />
    <glyph glyph-name="Zdotaccent" unicode="&#x17b;" horiz-adv-x="1248" 
d="M1172 1433v-72q0 -34 -21 -64l-811 -1139h818v-158h-1072v76q0 30 19 57l812 1142h-793v158h1048zM772 1688q0 -24 -10 -46t-27 -38.5t-39.5 -26.5t-47.5 -10q-24 0 -46 10t-38.5 26.5t-26 38.5t-9.5 46q0 25 9.5 47t26 39t38.5 27t46 10q25 0 47.5 -10t39.5 -27t27 -39
t10 -47z" />
    <glyph glyph-name="zdotaccent" unicode="&#x17c;" horiz-adv-x="924" 
d="M853 937q0 -19 -7 -36.5t-18 -31.5l-548 -730h553v-139h-763v74q0 13 6.5 30.5t18.5 33.5l551 735h-545v140h752v-76zM614 1338q0 -26 -10.5 -48.5t-28 -39.5t-40.5 -27t-49 -10t-48.5 10t-39.5 27t-27 39.5t-10 48.5t10 49.5t27 41t39.5 27.5t48.5 10t49 -10t40.5 -27.5
t28 -41t10.5 -49.5z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="1248" 
d="M1172 1433v-72q0 -34 -21 -64l-811 -1139h818v-158h-1072v76q0 30 19 57l812 1142h-793v158h1048zM738 1546h-176l-238 210h135q12 0 26 -3.5t23 -9.5l130 -94q3 -2 6.5 -5t5.5 -6q2 3 5.5 6t6.5 5l130 94q9 6 23 9.5t26 3.5h135z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="924" 
d="M853 937q0 -19 -7 -36.5t-18 -31.5l-548 -730h553v-139h-763v74q0 13 6.5 30.5t18.5 33.5l551 735h-545v140h752v-76zM180 1433h123q12 0 23 -5t17 -10l128 -125q11 -9 17 -19q5 5 8.5 10t8.5 9l128 125q6 6 17.5 10.5t22.5 4.5h119l-223 -264h-166z" />
    <glyph glyph-name="florin" unicode="&#x192;" 
d="M673 768l-89 -730q-25 -205 -144.5 -297.5t-333.5 -92.5v94q0 57 60 57q45 0 85.5 13t72 42t53.5 75t30 114l89 720l-155 15q-21 2 -31.5 14.5t-10.5 31.5v73h213l21 168q24 198 141.5 293.5t335.5 95.5v-98q0 -29 -14 -40.5t-45 -11.5q-46 0 -86.5 -13t-72.5 -42
t-54.5 -75.5t-31.5 -114.5l-22 -162h322v-129h-333z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="614" 
d="M612 1169h-119q-21 0 -40 14l-128 126l-17 17l-16 -17l-129 -126q-6 -5 -17 -9.5t-23 -4.5h-123l223 264h166z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="614" 
d="M0 1433h123q12 0 23 -5t17 -10l128 -125q11 -9 17 -19q5 5 8.5 10t8.5 9l128 125q6 6 17.5 10.5t22.5 4.5h119l-223 -264h-166z" />
    <glyph glyph-name="uni02C9" unicode="&#x2c9;" horiz-adv-x="614" 
d="M20 1348h574v-117h-574v117z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="614" 
d="M306 1164q-77 0 -129 21.5t-84.5 58t-46.5 85.5t-14 104h126q0 -33 7 -61.5t24 -48.5t45.5 -31.5t71.5 -11.5t71.5 11.5t45.5 31.5t24 48.5t7 61.5h126q0 -55 -14 -104t-46.5 -85.5t-85 -58t-128.5 -21.5z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="614" 
d="M433 1338q0 -26 -10.5 -48.5t-28 -39.5t-40.5 -27t-49 -10t-48.5 10t-39.5 27t-27 39.5t-10 48.5t10 49.5t27 41t39.5 27.5t48.5 10t49 -10t40.5 -27.5t28 -41t10.5 -49.5z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="614" 
d="M106 1315q0 42 16 76t43.5 59t63.5 38.5t76 13.5q41 0 77.5 -13.5t64.5 -38.5t44 -59t16 -76q0 -41 -16 -75t-44 -58t-64.5 -37.5t-77.5 -13.5q-40 0 -76 13.5t-63.5 37.5t-43.5 58t-16 75zM206 1315q0 -44 27 -72t74 -28q45 0 72.5 28t27.5 72q0 45 -27.5 73t-72.5 28
q-47 0 -74 -28t-27 -73z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="614" 
d="M451 -211q7 0 11.5 -4t6.5 -9l28 -66q-30 -23 -76.5 -37.5t-97.5 -14.5q-88 0 -138.5 39.5t-50.5 105.5q0 31 12.5 60.5t34 56.5t51 50.5t63.5 42.5l92 -13q-20 -11 -41 -26.5t-38 -35t-28 -43t-11 -49.5q0 -38 24.5 -59.5t66.5 -21.5q23 0 38 3.5t25 8t16.5 8.5t11.5 4z
" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="614" 
d="M417 1325q36 0 55.5 21t20.5 65h108q0 -47 -12.5 -86.5t-36 -67.5t-56 -43.5t-72.5 -15.5q-35 0 -65.5 14.5t-57.5 31.5t-51 31.5t-47 14.5q-72 0 -74 -88h-111q0 48 13 87.5t37 68t57 44t72 15.5q35 0 65.5 -14.5t57.5 -31.5t50.5 -31.5t46.5 -14.5z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="614" 
d="M429 1449l-199 -258q-12 -16 -26.5 -22t-35.5 -6h-74l138 242q13 23 29.5 33.5t49.5 10.5h118zM743 1449l-243 -258q-14 -15 -27.5 -21.5t-33.5 -6.5h-86l180 242q16 22 31 33t48 11h131z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="1236" 
d="M1199 1013v-72q0 -23 -15 -41t-44 -18h-136v-882h-178v882h-397v-638q0 -119 -60.5 -187.5t-189.5 -68.5q-34 0 -67 6t-64 23l7 75q2 9 6 14.5t12 8t21 3t33 0.5q66 0 94.5 31t28.5 97v636h-189v64q0 11 4.5 23t13 21.5t21.5 16t30 6.5h1069z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="1112" 
d="M156 655h800v-130h-800v130z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="1642" 
d="M156 655h1330v-130h-1330v130z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="424" 
d="M114 1012q-29 48 -42.5 96.5t-13.5 96.5q0 90 44.5 172.5t124.5 151.5l55 -34q8 -5 10.5 -11t2.5 -12q0 -14 -10 -24q-16 -20 -31 -42t-27 -46.5t-19 -51.5t-7 -57q0 -32 9.5 -66t32.5 -72q7 -11 7 -23q0 -24 -27 -34z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="424" 
d="M271 1508q29 -48 42 -96t13 -96q0 -91 -44.5 -173.5t-123.5 -151.5l-55 34q-8 5 -10.5 11t-2.5 12q0 14 10 24q16 19 31 41.5t27 47t19 51.5t7 57q0 32 -9.5 66t-32.5 72q-7 11 -7 22q0 24 27 35z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="424" 
d="M271 241q29 -48 42 -96t13 -96q0 -91 -44.5 -173.5t-123.5 -151.5l-55 34q-8 5 -10.5 11t-2.5 12q0 14 10 24q16 19 31 41.5t27 47t19 51.5t7 57q0 32 -9.5 66t-32.5 72q-7 11 -7 22q0 24 27 35z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="728" 
d="M114 1012q-29 48 -42.5 96.5t-13.5 96.5q0 90 44.5 172.5t124.5 151.5l55 -34q8 -5 10.5 -11t2.5 -12q0 -14 -10 -24q-16 -20 -31 -42t-27 -46.5t-19 -51.5t-7 -57q0 -32 9.5 -66t32.5 -72q7 -11 7 -23q0 -24 -27 -34zM418 1012q-29 48 -42.5 96.5t-13.5 96.5
q0 90 44.5 172.5t124.5 151.5l55 -34q8 -5 10.5 -11t2.5 -12q0 -14 -10 -24q-16 -20 -31 -42t-27 -46.5t-19 -51.5t-7 -57q0 -32 9.5 -66t32.5 -72q7 -11 7 -23q0 -24 -27 -34z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="728" 
d="M271 1508q29 -48 42 -96t13 -96q0 -91 -44.5 -173.5t-123.5 -151.5l-55 34q-8 5 -10.5 11t-2.5 12q0 14 10 24q16 19 31 41.5t27 47t19 51.5t7 57q0 32 -9.5 66t-32.5 72q-7 11 -7 22q0 24 27 35zM575 1508q29 -48 42 -96t13 -96q0 -91 -44.5 -173.5t-123.5 -151.5
l-55 34q-8 5 -10.5 11t-2.5 12q0 14 10 24q16 19 31 41.5t27 47t19 51.5t7 57q0 32 -9.5 66t-32.5 72q-7 11 -7 22q0 24 27 35z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="728" 
d="M271 241q29 -48 42 -96t13 -96q0 -91 -44.5 -173.5t-123.5 -151.5l-55 34q-8 5 -10.5 11t-2.5 12q0 14 10 24q16 19 31 41.5t27 47t19 51.5t7 57q0 32 -9.5 66t-32.5 72q-7 11 -7 22q0 24 27 35zM575 241q29 -48 42 -96t13 -96q0 -91 -44.5 -173.5t-123.5 -151.5l-55 34
q-8 5 -10.5 11t-2.5 12q0 14 10 24q16 19 31 41.5t27 47t19 51.5t7 57q0 32 -9.5 66t-32.5 72q-7 11 -7 22q0 24 27 35z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" 
d="M118 918q0 31 20.5 55t63.5 24q34 0 72 -4.5t78 -9.5t80 -11t79 -9l-24 488q40 23 93 23q55 0 93 -23l-23 -488q78 7 158.5 20t149.5 14q44 0 64 -24t20 -55v-60h-392v-416l23 -777q-38 -23 -93 -23q-53 0 -93 23l24 777v416h-393v60z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" 
d="M118 918q0 31 20.5 55t63.5 24q34 0 72 -4.5t78 -9.5t80 -11t79 -9l-24 488q40 23 93 23q55 0 93 -23l-23 -488q78 7 158.5 20t149.5 14q44 0 64 -24t20 -55v-60h-392v-600h392v-60q0 -31 -20 -55t-64 -24q-69 1 -149.5 13.5t-158.5 19.5l23 -487q-38 -23 -93 -23
q-53 0 -93 23l24 487q-78 -7 -159 -19.5t-150 -13.5q-43 0 -63.5 24t-20.5 55v60h393v600h-393v60z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" 
d="M208 595q0 77 29.5 145t79.5 118.5t117.5 80t143.5 29.5q77 0 145 -29.5t118.5 -80t80 -118.5t29.5 -145t-29.5 -144.5t-80 -117.5t-118.5 -79.5t-145 -29.5q-76 0 -143.5 29.5t-117.5 79.5t-79.5 117.5t-29.5 144.5z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="1454" 
d="M88 110q0 26 9.5 49t26 40t39.5 27t49 10t49 -10t40 -27t26.5 -40t9.5 -49q0 -27 -9.5 -49.5t-26.5 -39.5t-40 -26.5t-49 -9.5t-49 9.5t-39.5 26.5t-26 39.5t-9.5 49.5zM1117 110q0 26 9.5 49t26 40t39.5 27t49 10t49 -10t40 -27t26.5 -40t9.5 -49q0 -27 -9.5 -49.5
t-26.5 -39.5t-40 -26.5t-49 -9.5t-49 9.5t-39.5 26.5t-26 39.5t-9.5 49.5zM602 110q0 26 9.5 49t26 40t39.5 27t49 10t49 -10t40 -27t26.5 -40t9.5 -49q0 -27 -9.5 -49.5t-26.5 -39.5t-40 -26.5t-49 -9.5t-49 9.5t-39.5 26.5t-26 39.5t-9.5 49.5z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="2306" 
d="M707 1087q0 -84 -26 -150.5t-69.5 -112t-101.5 -69.5t-121 -24q-68 0 -125.5 24t-100.5 69.5t-67 112t-24 150.5q0 86 24 153t67 113t100.5 70t125.5 24q67 0 125.5 -24t101 -70t67 -113t24.5 -153zM568 1087q0 66 -14 112t-38.5 75.5t-57 42.5t-69.5 13t-69.5 -13
t-56.5 -42.5t-37.5 -75.5t-13.5 -112q0 -65 13.5 -110.5t37.5 -74t56.5 -41t69.5 -12.5t69.5 12.5t57 41t38.5 74t14 110.5zM1213 1403q9 13 23 21.5t38 8.5h128l-1047 -1404q-10 -13 -24 -21t-33 -8h-132zM1499 338q0 -84 -26 -150t-69.5 -111.5t-101 -69.5t-120.5 -24
q-68 0 -125.5 24t-100.5 69.5t-67 111.5t-24 150q0 86 24 153.5t67 113.5t100.5 70t125.5 24q67 0 125 -24t101 -70t67 -113.5t24 -153.5zM1361 338q0 66 -14 112.5t-38.5 75.5t-57 42t-69.5 13t-69.5 -13t-56.5 -42t-37.5 -75.5t-13.5 -112.5q0 -65 13.5 -110t37.5 -73.5
t56.5 -41t69.5 -12.5t69.5 12.5t57 41t38.5 73.5t14 110zM2233 338q0 -84 -26 -150t-69.5 -111.5t-101.5 -69.5t-121 -24q-68 0 -125.5 24t-100.5 69.5t-67 111.5t-24 150q0 86 24 153.5t67 113.5t100.5 70t125.5 24q67 0 125.5 -24t101 -70t67 -113.5t24.5 -153.5z
M2094 338q0 66 -14 112.5t-38.5 75.5t-57 42t-69.5 13t-69.5 -13t-56.5 -42t-37.5 -75.5t-13.5 -112.5q0 -65 13.5 -110t37.5 -73.5t56.5 -41t69.5 -12.5t69.5 12.5t57 41t38.5 73.5t14 110z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="632" 
d="M138 518v23l249 389l58 -28q14 -7 21 -18t7 -24q0 -17 -10 -33l-159 -261q-14 -24 -28 -37q15 -14 28 -36l159 -261q5 -8 7.5 -17t2.5 -17q0 -28 -28 -41l-58 -28z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="632" 
d="M485 541v-23l-249 -389l-58 28q-28 13 -28 41q0 17 10 34l159 261q13 24 27 36q-12 11 -27 37l-159 261q-10 17 -10 34q0 28 28 41l58 28z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="614" 
d="M-20 53q-19 -31 -41 -42t-51 -11h-76l818 1372q18 29 41 45t55 16h77z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" 
d="M34 899h147q20 126 67.5 227.5t118 173t162.5 109.5t200 38q134 0 229 -50t165 -138l-61 -68q-8 -9 -16 -15.5t-22 -6.5q-17 0 -35.5 19t-49.5 42t-79.5 42t-123.5 19q-146 0 -242.5 -100t-128.5 -292h547v-55q0 -18 -13.5 -32.5t-37.5 -14.5h-508q-1 -20 -1.5 -40
t-0.5 -41v-31t1 -30h467v-56q0 -17 -14 -31.5t-38 -14.5h-406q28 -206 124 -309t242 -103q54 0 95 10.5t71 26.5t51.5 34.5t37.5 34.5t28.5 26.5t24.5 10.5q8 0 14 -3.5t14 -10.5l75 -70q-70 -102 -174 -158.5t-247 -56.5q-116 0 -209 39.5t-161 113t-111.5 179t-59.5 236.5
h-142v102h134q-1 15 -1 30v31q0 20 0.5 40.5t1.5 40.5h-135v102z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="1440" 
d="M963 1109q6 -14 11.5 -26.5t9.5 -26.5q5 14 9 26.5t12 26.5l169 303q9 13 17 17t24 4h106v-592h-110v365l9 75l-183 -336q-13 -28 -44 -28h-18q-31 0 -43 28l-184 333l8 -72v-365h-110v592h106q17 0 24 -4t18 -17zM550 1433v-105h-180v-487h-126v487h-180v105h486z" />
    <glyph glyph-name="Omega" unicode="&#x2126;" horiz-adv-x="1494" 
d="M836 0v438q81 15 149 47.5t117.5 84t77 123t27.5 164.5q0 107 -34.5 188.5t-96 136t-146 82.5t-184.5 28t-184.5 -28t-146 -82.5t-96 -136t-34.5 -188.5q0 -93 27.5 -164.5t76.5 -123t117.5 -84t149.5 -47.5v-438h-504q-30 0 -48 17.5t-18 45.5v100h419v174
q-96 23 -173.5 71t-131.5 116t-83.5 155t-29.5 188q0 128 49.5 235t137 184t208.5 120t264 43t264 -43t208.5 -120t137 -184t49.5 -235q0 -101 -29.5 -188t-84 -155t-132 -116t-173.5 -71v-174h420v-100q0 -28 -18 -45.5t-48 -17.5h-504z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" 
d="M348 1337q39 27 75.5 48t75.5 35t82 21t94 7q90 0 164 -37t126 -107t80.5 -171t28.5 -229q0 -202 -36.5 -370.5t-110 -290t-184 -189t-258.5 -67.5q-86 0 -158.5 28.5t-124.5 82t-81 129.5t-29 171q0 115 38 216t106 175t162 117t206 43q104 0 181 -44.5t125 -130.5
q2 37 2.5 70.5t0.5 59.5q0 198 -69 299.5t-193 101.5q-42 0 -77.5 -10t-63.5 -22.5t-49 -22.5t-34 -10q-11 0 -20.5 6t-20.5 24zM506 127q66 0 125.5 27.5t107.5 83.5t83.5 141t55.5 200q-10 44 -29 85.5t-50 73.5t-74.5 51t-101.5 19q-84 0 -149 -31t-109 -85.5t-66.5 -129
t-22.5 -161.5q0 -65 16 -116t46 -86t72.5 -53.5t95.5 -18.5z" />
    <glyph glyph-name="Delta" unicode="&#x2206;" horiz-adv-x="1392" 
d="M608 1433h175l595 -1433h-1364zM273 156h845l-384 959q-9 23 -19 52t-19 63q-9 -34 -18.5 -63.5t-18.5 -52.5z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="1368" 
d="M1305 1433v-153h-187v-1623h-184v1623h-501v-1623h-184v1623h-187v153h1243z" />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="1368" 
d="M84 1433h1199v-153h-923l573 -709v-52l-573 -709h923v-153h-1199v65q0 14 4.5 28.5t14.5 26.5l629 770l-629 765q-11 13 -15 27.5t-4 28.5v65z" />
    <glyph glyph-name="minus" unicode="&#x2212;" 
d="M148 739h860v-135h-860v135z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="1146" 
d="M286 663h-171q-28 0 -48.5 16.5t-20.5 57.5v57h335q22 0 36 -11t19 -26l150 -411q12 -32 18 -66t10 -68q5 27 10.5 55t14.5 57l432 1351q5 16 19 26.5t34 10.5h115l-554 -1712h-149z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="1368" 
d="M1002 254q-56 0 -101.5 17t-84 46.5t-71 67.5t-60.5 80q-29 -42 -61.5 -80t-70.5 -67.5t-84 -46.5t-101 -17q-62 0 -118.5 24t-99 68.5t-67.5 106.5t-25 138t25 138t67.5 106t99 68.5t118.5 24.5q55 0 101 -17t84 -46.5t70.5 -67.5t61.5 -80q28 42 60.5 80t71 67.5
t84 46.5t101.5 17q62 0 118 -24.5t99 -68.5t68 -106t25 -138t-25 -138t-68 -106.5t-99 -68.5t-118 -24zM375 400q36 0 67 15t58.5 41.5t52.5 61t50 73.5q-25 39 -50 73.5t-52.5 60.5t-58.5 41.5t-67 15.5t-67.5 -12t-55.5 -35.5t-38 -59.5t-14 -84t14 -84t38 -59.5
t55.5 -35.5t67.5 -12zM995 400q36 0 67.5 12t55 35.5t37.5 59.5t14 84t-14 84t-37.5 59.5t-55 35.5t-67.5 12t-67 -15.5t-59 -41.5t-53 -60.5t-50 -73.5q25 -39 50 -73.5t53 -61t59 -41.5t67 -15z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="800" 
d="M374 1091q21 174 111 266t236 92q38 0 72.5 -7t66.5 -23l-8 -86q-2 -9 -6 -16t-13 -12t-24 -7.5t-39 -2.5q-100 0 -155.5 -54.5t-70.5 -172.5l-130 -1030q-13 -102 -45.5 -175t-82 -120.5t-114 -70t-139.5 -22.5q-35 0 -72 6.5t-67 22.5l10 76q3 12 7.5 18.5t14.5 9.5
t26 3.5t41 0.5q58 0 101.5 13.5t74 43t49 76t26.5 112.5z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" 
d="M787 858q29 0 56 7t50 17.5t40 22.5t26 23l25 -114q-35 -47 -91 -70t-117 -23q-52 0 -103.5 16.5t-102 36.5t-98 36.5t-92.5 16.5q-30 0 -58 -6.5t-51 -17t-40 -23t-26 -24.5l-29 109q35 51 91.5 75.5t122.5 24.5q53 0 105 -16.5t102 -37t97.5 -37t92.5 -16.5zM787 520
q29 0 56 6.5t50 17t40 22.5t26 23l25 -113q-35 -48 -91 -71t-117 -23q-52 0 -103.5 16.5t-102 37t-98 37t-92.5 16.5q-30 0 -58 -7t-51 -17.5t-40 -23t-26 -24.5l-29 109q35 51 91.5 75.5t122.5 24.5q53 0 105 -16.5t102 -36.5t97.5 -36.5t92.5 -16.5z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" 
d="M150 909h489l114 241h129l-114 -241h241v-135h-304l-95 -200h399v-135h-462l-119 -252h-129l119 252h-268v135h331l95 200h-426v135z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" 
d="M148 791l774 377v-122q0 -16 -9 -29.5t-35 -25.5l-441 -207q-46 -18 -97 -30q26 -5 51 -12.5t46 -17.5l441 -209q25 -12 34.5 -25.5t9.5 -29.5v-122l-774 379v74zM148 215h774v-135h-774v135z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" 
d="M1012 791v-74l-774 -379v122q0 16 9.5 29.5t34.5 25.5l441 209q41 19 97 30q-27 6 -51.5 13t-45.5 17l-441 207q-26 11 -35 25t-9 30v122zM1012 80h-774v135h774v-135z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" 
d="M128 701l390 821h124l390 -821l-390 -820h-124zM276 701l276 -581q8 -23 15.5 -42t12.5 -37q5 18 12 37t16 42l280 581l-280 582q-18 43 -28 78q-5 -18 -12.5 -37t-15.5 -41z" />
    <glyph glyph-name="uni2669" unicode="&#x2669;" horiz-adv-x="0" 
d="M-2 1473h4v-1816h-4v1816z" />
    <glyph glyph-name="undercommaaccent" horiz-adv-x="614" 
d="M360 -80q23 0 28.5 -10.5t5.5 -22.5q0 -10 -2.5 -25t-10 -40t-20 -61t-31.5 -88q-10 -22 -24.5 -29t-39.5 -7h-62l49 283h107z" />
    <glyph glyph-name="grave.case" horiz-adv-x="614" 
d="M157 1782q32 0 48 -6.5t36 -26.5l211 -203h-139q-21 0 -33 3.5t-29 15.5l-295 217h201z" />
    <glyph glyph-name="dieresis.case" horiz-adv-x="614" 
d="M204 1666q0 -22 -9 -41.5t-24 -34.5t-35.5 -23.5t-43.5 -8.5q-21 0 -40.5 8.5t-34.5 23.5t-24 34.5t-9 41.5q0 23 9 43.5t24 35.5t34.5 24t40.5 9q23 0 43.5 -9t35.5 -24t24 -35.5t9 -43.5zM630 1666q0 -22 -9 -41.5t-24 -34.5t-35 -23.5t-42 -8.5q-23 0 -43 8.5
t-35 23.5t-23.5 34.5t-8.5 41.5q0 23 8.5 43.5t23.5 35.5t35 24t43 9q22 0 42 -9t35 -24t24 -35.5t9 -43.5z" />
    <glyph glyph-name="macron.case" horiz-adv-x="614" 
d="M68 1681h478v-106h-478v106z" />
    <glyph glyph-name="acute.case" horiz-adv-x="614" 
d="M658 1782l-294 -216q-17 -12 -30 -16t-34 -4h-138l211 203q10 10 18.5 16t17.5 10t20 5.5t28 1.5h201z" />
    <glyph glyph-name="circumflex.case" horiz-adv-x="614" 
d="M632 1546h-135q-12 0 -26 3.5t-23 9.5l-130 95q-8 4 -12 8q-8 -6 -12 -8l-130 -95q-9 -6 -23 -9.5t-26 -3.5h-135l238 210h176z" />
    <glyph glyph-name="caron.case" horiz-adv-x="614" 
d="M394 1546h-176l-238 210h135q12 0 26 -3.5t23 -9.5l130 -94q3 -2 6.5 -5t5.5 -6q2 3 5.5 6t6.5 5l130 94q9 6 23 9.5t26 3.5h135z" />
    <glyph glyph-name="breve.case" horiz-adv-x="614" 
d="M307 1527q-137 0 -208 57.5t-71 171.5h115q0 -59 39 -89.5t125 -30.5t125 30.5t39 89.5h115q0 -105 -71.5 -167t-207.5 -62z" />
    <glyph glyph-name="dotaccent.case" horiz-adv-x="614" 
d="M428 1688q0 -24 -10 -46t-27 -38.5t-39.5 -26.5t-47.5 -10q-24 0 -46 10t-38.5 26.5t-26 38.5t-9.5 46q0 25 9.5 47t26 39t38.5 27t46 10q25 0 47.5 -10t39.5 -27t27 -39t10 -47z" />
    <glyph glyph-name="ring.case" horiz-adv-x="614" 
d="M118 1659q0 39 15.5 72t41 56.5t59.5 36.5t72 13q39 0 73.5 -13t61 -36.5t41.5 -56.5t15 -72q0 -38 -15 -70t-41.5 -55t-61 -36t-73.5 -13q-38 0 -72 13t-59.5 36t-41 55t-15.5 70zM207 1659q0 -43 27 -71.5t74 -28.5q45 0 72.5 28.5t27.5 71.5q0 45 -27.5 73t-72.5 28
q-47 0 -74 -28t-27 -73z" />
    <glyph glyph-name="tilde.case" horiz-adv-x="614" 
d="M427 1663q35 0 53.5 20.5t19.5 57.5h98q0 -43 -11 -79t-32 -62.5t-52 -41t-71 -14.5q-35 0 -67 13t-61.5 28.5t-55.5 28.5t-50 13q-34 0 -52.5 -21.5t-19.5 -57.5h-100q0 43 11.5 79.5t33 62.5t53 41t70.5 15q35 0 67 -13t61 -28.5t55 -28.5t50 -13z" />
    <glyph glyph-name="hungarumlaut.case" horiz-adv-x="614" 
d="M424 1771l-199 -198q-14 -14 -27.5 -20.5t-33.5 -6.5h-86l142 181q16 20 35 32t52 12h117zM762 1771l-243 -198q-17 -13 -31.5 -20t-35.5 -7h-96l179 181q19 20 39 32t52 12h136z" />
    <glyph glyph-name="caron.salt" horiz-adv-x="614" 
d="M385 1475q22 0 27.5 -10.5t5.5 -22.5q0 -10 -3.5 -26.5t-12.5 -46t-25 -74.5t-41 -111q-10 -22 -24.5 -29t-39.5 -7h-62l57 327h118z" />
    <hkern u1="&#x22;" u2="&#x2206;" k="182" />
    <hkern u1="&#x22;" u2="&#x203a;" k="178" />
    <hkern u1="&#x22;" u2="&#x2039;" k="178" />
    <hkern u1="&#x22;" u2="&#x2022;" k="178" />
    <hkern u1="&#x22;" u2="&#x201e;" k="228" />
    <hkern u1="&#x22;" u2="&#x201a;" k="228" />
    <hkern u1="&#x22;" u2="&#x2014;" k="178" />
    <hkern u1="&#x22;" u2="&#x2013;" k="178" />
    <hkern u1="&#x22;" u2="&#x178;" k="-30" />
    <hkern u1="&#x22;" u2="&#x153;" k="92" />
    <hkern u1="&#x22;" u2="&#x152;" k="46" />
    <hkern u1="&#x22;" u2="&#x119;" k="92" />
    <hkern u1="&#x22;" u2="&#x107;" k="92" />
    <hkern u1="&#x22;" u2="&#x106;" k="46" />
    <hkern u1="&#x22;" u2="&#x105;" k="64" />
    <hkern u1="&#x22;" u2="&#x104;" k="182" />
    <hkern u1="&#x22;" u2="&#xf8;" k="92" />
    <hkern u1="&#x22;" u2="&#xf6;" k="92" />
    <hkern u1="&#x22;" u2="&#xf5;" k="92" />
    <hkern u1="&#x22;" u2="&#xf4;" k="92" />
    <hkern u1="&#x22;" u2="&#xf3;" k="92" />
    <hkern u1="&#x22;" u2="&#xf2;" k="92" />
    <hkern u1="&#x22;" u2="&#xf0;" k="92" />
    <hkern u1="&#x22;" u2="&#xeb;" k="92" />
    <hkern u1="&#x22;" u2="&#xea;" k="92" />
    <hkern u1="&#x22;" u2="&#xe9;" k="92" />
    <hkern u1="&#x22;" u2="&#xe8;" k="92" />
    <hkern u1="&#x22;" u2="&#xe7;" k="92" />
    <hkern u1="&#x22;" u2="&#xe6;" k="64" />
    <hkern u1="&#x22;" u2="&#xe5;" k="64" />
    <hkern u1="&#x22;" u2="&#xe4;" k="64" />
    <hkern u1="&#x22;" u2="&#xe3;" k="64" />
    <hkern u1="&#x22;" u2="&#xe2;" k="64" />
    <hkern u1="&#x22;" u2="&#xe1;" k="64" />
    <hkern u1="&#x22;" u2="&#xe0;" k="64" />
    <hkern u1="&#x22;" u2="&#xdd;" k="-30" />
    <hkern u1="&#x22;" u2="&#xd8;" k="46" />
    <hkern u1="&#x22;" u2="&#xd6;" k="46" />
    <hkern u1="&#x22;" u2="&#xd5;" k="46" />
    <hkern u1="&#x22;" u2="&#xd4;" k="46" />
    <hkern u1="&#x22;" u2="&#xd3;" k="46" />
    <hkern u1="&#x22;" u2="&#xd2;" k="46" />
    <hkern u1="&#x22;" u2="&#xc7;" k="46" />
    <hkern u1="&#x22;" u2="&#xc6;" k="182" />
    <hkern u1="&#x22;" u2="&#xc5;" k="182" />
    <hkern u1="&#x22;" u2="&#xc4;" k="182" />
    <hkern u1="&#x22;" u2="&#xc3;" k="182" />
    <hkern u1="&#x22;" u2="&#xc2;" k="182" />
    <hkern u1="&#x22;" u2="&#xc1;" k="182" />
    <hkern u1="&#x22;" u2="&#xc0;" k="182" />
    <hkern u1="&#x22;" u2="&#xbb;" k="178" />
    <hkern u1="&#x22;" u2="&#xb7;" k="178" />
    <hkern u1="&#x22;" u2="&#xad;" k="178" />
    <hkern u1="&#x22;" u2="&#xab;" k="178" />
    <hkern u1="&#x22;" u2="q" k="92" />
    <hkern u1="&#x22;" u2="o" k="92" />
    <hkern u1="&#x22;" u2="e" k="92" />
    <hkern u1="&#x22;" u2="d" k="92" />
    <hkern u1="&#x22;" u2="c" k="92" />
    <hkern u1="&#x22;" u2="a" k="64" />
    <hkern u1="&#x22;" u2="\" k="-48" />
    <hkern u1="&#x22;" u2="Y" k="-30" />
    <hkern u1="&#x22;" u2="W" k="-48" />
    <hkern u1="&#x22;" u2="V" k="-48" />
    <hkern u1="&#x22;" u2="Q" k="46" />
    <hkern u1="&#x22;" u2="O" k="46" />
    <hkern u1="&#x22;" u2="G" k="46" />
    <hkern u1="&#x22;" u2="C" k="46" />
    <hkern u1="&#x22;" u2="A" k="182" />
    <hkern u1="&#x22;" u2="&#x40;" k="46" />
    <hkern u1="&#x22;" u2="&#x2f;" k="182" />
    <hkern u1="&#x22;" u2="&#x2e;" k="228" />
    <hkern u1="&#x22;" u2="&#x2d;" k="178" />
    <hkern u1="&#x22;" u2="&#x2c;" k="228" />
    <hkern u1="&#x22;" u2="&#x26;" k="182" />
    <hkern u1="&#x27;" u2="&#x2206;" k="182" />
    <hkern u1="&#x27;" u2="&#x203a;" k="178" />
    <hkern u1="&#x27;" u2="&#x2039;" k="178" />
    <hkern u1="&#x27;" u2="&#x2022;" k="178" />
    <hkern u1="&#x27;" u2="&#x201e;" k="228" />
    <hkern u1="&#x27;" u2="&#x201a;" k="228" />
    <hkern u1="&#x27;" u2="&#x2014;" k="178" />
    <hkern u1="&#x27;" u2="&#x2013;" k="178" />
    <hkern u1="&#x27;" u2="&#x178;" k="-30" />
    <hkern u1="&#x27;" u2="&#x153;" k="92" />
    <hkern u1="&#x27;" u2="&#x152;" k="46" />
    <hkern u1="&#x27;" u2="&#x119;" k="92" />
    <hkern u1="&#x27;" u2="&#x107;" k="92" />
    <hkern u1="&#x27;" u2="&#x106;" k="46" />
    <hkern u1="&#x27;" u2="&#x105;" k="64" />
    <hkern u1="&#x27;" u2="&#x104;" k="182" />
    <hkern u1="&#x27;" u2="&#xf8;" k="92" />
    <hkern u1="&#x27;" u2="&#xf6;" k="92" />
    <hkern u1="&#x27;" u2="&#xf5;" k="92" />
    <hkern u1="&#x27;" u2="&#xf4;" k="92" />
    <hkern u1="&#x27;" u2="&#xf3;" k="92" />
    <hkern u1="&#x27;" u2="&#xf2;" k="92" />
    <hkern u1="&#x27;" u2="&#xf0;" k="92" />
    <hkern u1="&#x27;" u2="&#xeb;" k="92" />
    <hkern u1="&#x27;" u2="&#xea;" k="92" />
    <hkern u1="&#x27;" u2="&#xe9;" k="92" />
    <hkern u1="&#x27;" u2="&#xe8;" k="92" />
    <hkern u1="&#x27;" u2="&#xe7;" k="92" />
    <hkern u1="&#x27;" u2="&#xe6;" k="64" />
    <hkern u1="&#x27;" u2="&#xe5;" k="64" />
    <hkern u1="&#x27;" u2="&#xe4;" k="64" />
    <hkern u1="&#x27;" u2="&#xe3;" k="64" />
    <hkern u1="&#x27;" u2="&#xe2;" k="64" />
    <hkern u1="&#x27;" u2="&#xe1;" k="64" />
    <hkern u1="&#x27;" u2="&#xe0;" k="64" />
    <hkern u1="&#x27;" u2="&#xdd;" k="-30" />
    <hkern u1="&#x27;" u2="&#xd8;" k="46" />
    <hkern u1="&#x27;" u2="&#xd6;" k="46" />
    <hkern u1="&#x27;" u2="&#xd5;" k="46" />
    <hkern u1="&#x27;" u2="&#xd4;" k="46" />
    <hkern u1="&#x27;" u2="&#xd3;" k="46" />
    <hkern u1="&#x27;" u2="&#xd2;" k="46" />
    <hkern u1="&#x27;" u2="&#xc7;" k="46" />
    <hkern u1="&#x27;" u2="&#xc6;" k="182" />
    <hkern u1="&#x27;" u2="&#xc5;" k="182" />
    <hkern u1="&#x27;" u2="&#xc4;" k="182" />
    <hkern u1="&#x27;" u2="&#xc3;" k="182" />
    <hkern u1="&#x27;" u2="&#xc2;" k="182" />
    <hkern u1="&#x27;" u2="&#xc1;" k="182" />
    <hkern u1="&#x27;" u2="&#xc0;" k="182" />
    <hkern u1="&#x27;" u2="&#xbb;" k="178" />
    <hkern u1="&#x27;" u2="&#xb7;" k="178" />
    <hkern u1="&#x27;" u2="&#xad;" k="178" />
    <hkern u1="&#x27;" u2="&#xab;" k="178" />
    <hkern u1="&#x27;" u2="q" k="92" />
    <hkern u1="&#x27;" u2="o" k="92" />
    <hkern u1="&#x27;" u2="e" k="92" />
    <hkern u1="&#x27;" u2="d" k="92" />
    <hkern u1="&#x27;" u2="c" k="92" />
    <hkern u1="&#x27;" u2="a" k="64" />
    <hkern u1="&#x27;" u2="\" k="-48" />
    <hkern u1="&#x27;" u2="Y" k="-30" />
    <hkern u1="&#x27;" u2="W" k="-48" />
    <hkern u1="&#x27;" u2="V" k="-48" />
    <hkern u1="&#x27;" u2="Q" k="46" />
    <hkern u1="&#x27;" u2="O" k="46" />
    <hkern u1="&#x27;" u2="G" k="46" />
    <hkern u1="&#x27;" u2="C" k="46" />
    <hkern u1="&#x27;" u2="A" k="182" />
    <hkern u1="&#x27;" u2="&#x40;" k="46" />
    <hkern u1="&#x27;" u2="&#x2f;" k="182" />
    <hkern u1="&#x27;" u2="&#x2e;" k="228" />
    <hkern u1="&#x27;" u2="&#x2d;" k="178" />
    <hkern u1="&#x27;" u2="&#x2c;" k="228" />
    <hkern u1="&#x27;" u2="&#x26;" k="182" />
    <hkern u1="&#x28;" u2="&#x153;" k="32" />
    <hkern u1="&#x28;" u2="&#x152;" k="40" />
    <hkern u1="&#x28;" u2="&#x119;" k="32" />
    <hkern u1="&#x28;" u2="&#x107;" k="32" />
    <hkern u1="&#x28;" u2="&#x106;" k="40" />
    <hkern u1="&#x28;" u2="&#xf8;" k="32" />
    <hkern u1="&#x28;" u2="&#xf6;" k="32" />
    <hkern u1="&#x28;" u2="&#xf5;" k="32" />
    <hkern u1="&#x28;" u2="&#xf4;" k="32" />
    <hkern u1="&#x28;" u2="&#xf3;" k="32" />
    <hkern u1="&#x28;" u2="&#xf2;" k="32" />
    <hkern u1="&#x28;" u2="&#xf0;" k="32" />
    <hkern u1="&#x28;" u2="&#xeb;" k="32" />
    <hkern u1="&#x28;" u2="&#xea;" k="32" />
    <hkern u1="&#x28;" u2="&#xe9;" k="32" />
    <hkern u1="&#x28;" u2="&#xe8;" k="32" />
    <hkern u1="&#x28;" u2="&#xe7;" k="32" />
    <hkern u1="&#x28;" u2="&#xd8;" k="40" />
    <hkern u1="&#x28;" u2="&#xd6;" k="40" />
    <hkern u1="&#x28;" u2="&#xd5;" k="40" />
    <hkern u1="&#x28;" u2="&#xd4;" k="40" />
    <hkern u1="&#x28;" u2="&#xd3;" k="40" />
    <hkern u1="&#x28;" u2="&#xd2;" k="40" />
    <hkern u1="&#x28;" u2="&#xc7;" k="40" />
    <hkern u1="&#x28;" u2="q" k="32" />
    <hkern u1="&#x28;" u2="o" k="32" />
    <hkern u1="&#x28;" u2="e" k="32" />
    <hkern u1="&#x28;" u2="d" k="32" />
    <hkern u1="&#x28;" u2="c" k="32" />
    <hkern u1="&#x28;" u2="Q" k="40" />
    <hkern u1="&#x28;" u2="O" k="40" />
    <hkern u1="&#x28;" u2="G" k="40" />
    <hkern u1="&#x28;" u2="C" k="40" />
    <hkern u1="&#x28;" u2="&#x40;" k="40" />
    <hkern u1="&#x2a;" u2="&#x2206;" k="182" />
    <hkern u1="&#x2a;" u2="&#x203a;" k="178" />
    <hkern u1="&#x2a;" u2="&#x2039;" k="178" />
    <hkern u1="&#x2a;" u2="&#x2022;" k="178" />
    <hkern u1="&#x2a;" u2="&#x201e;" k="228" />
    <hkern u1="&#x2a;" u2="&#x201a;" k="228" />
    <hkern u1="&#x2a;" u2="&#x2014;" k="178" />
    <hkern u1="&#x2a;" u2="&#x2013;" k="178" />
    <hkern u1="&#x2a;" u2="&#x178;" k="-30" />
    <hkern u1="&#x2a;" u2="&#x153;" k="92" />
    <hkern u1="&#x2a;" u2="&#x152;" k="46" />
    <hkern u1="&#x2a;" u2="&#x119;" k="92" />
    <hkern u1="&#x2a;" u2="&#x107;" k="92" />
    <hkern u1="&#x2a;" u2="&#x106;" k="46" />
    <hkern u1="&#x2a;" u2="&#x105;" k="64" />
    <hkern u1="&#x2a;" u2="&#x104;" k="182" />
    <hkern u1="&#x2a;" u2="&#xf8;" k="92" />
    <hkern u1="&#x2a;" u2="&#xf6;" k="92" />
    <hkern u1="&#x2a;" u2="&#xf5;" k="92" />
    <hkern u1="&#x2a;" u2="&#xf4;" k="92" />
    <hkern u1="&#x2a;" u2="&#xf3;" k="92" />
    <hkern u1="&#x2a;" u2="&#xf2;" k="92" />
    <hkern u1="&#x2a;" u2="&#xf0;" k="92" />
    <hkern u1="&#x2a;" u2="&#xeb;" k="92" />
    <hkern u1="&#x2a;" u2="&#xea;" k="92" />
    <hkern u1="&#x2a;" u2="&#xe9;" k="92" />
    <hkern u1="&#x2a;" u2="&#xe8;" k="92" />
    <hkern u1="&#x2a;" u2="&#xe7;" k="92" />
    <hkern u1="&#x2a;" u2="&#xe6;" k="64" />
    <hkern u1="&#x2a;" u2="&#xe5;" k="64" />
    <hkern u1="&#x2a;" u2="&#xe4;" k="64" />
    <hkern u1="&#x2a;" u2="&#xe3;" k="64" />
    <hkern u1="&#x2a;" u2="&#xe2;" k="64" />
    <hkern u1="&#x2a;" u2="&#xe1;" k="64" />
    <hkern u1="&#x2a;" u2="&#xe0;" k="64" />
    <hkern u1="&#x2a;" u2="&#xdd;" k="-30" />
    <hkern u1="&#x2a;" u2="&#xd8;" k="46" />
    <hkern u1="&#x2a;" u2="&#xd6;" k="46" />
    <hkern u1="&#x2a;" u2="&#xd5;" k="46" />
    <hkern u1="&#x2a;" u2="&#xd4;" k="46" />
    <hkern u1="&#x2a;" u2="&#xd3;" k="46" />
    <hkern u1="&#x2a;" u2="&#xd2;" k="46" />
    <hkern u1="&#x2a;" u2="&#xc7;" k="46" />
    <hkern u1="&#x2a;" u2="&#xc6;" k="182" />
    <hkern u1="&#x2a;" u2="&#xc5;" k="182" />
    <hkern u1="&#x2a;" u2="&#xc4;" k="182" />
    <hkern u1="&#x2a;" u2="&#xc3;" k="182" />
    <hkern u1="&#x2a;" u2="&#xc2;" k="182" />
    <hkern u1="&#x2a;" u2="&#xc1;" k="182" />
    <hkern u1="&#x2a;" u2="&#xc0;" k="182" />
    <hkern u1="&#x2a;" u2="&#xbb;" k="178" />
    <hkern u1="&#x2a;" u2="&#xb7;" k="178" />
    <hkern u1="&#x2a;" u2="&#xad;" k="178" />
    <hkern u1="&#x2a;" u2="&#xab;" k="178" />
    <hkern u1="&#x2a;" u2="q" k="92" />
    <hkern u1="&#x2a;" u2="o" k="92" />
    <hkern u1="&#x2a;" u2="e" k="92" />
    <hkern u1="&#x2a;" u2="d" k="92" />
    <hkern u1="&#x2a;" u2="c" k="92" />
    <hkern u1="&#x2a;" u2="a" k="64" />
    <hkern u1="&#x2a;" u2="\" k="-48" />
    <hkern u1="&#x2a;" u2="Y" k="-30" />
    <hkern u1="&#x2a;" u2="W" k="-48" />
    <hkern u1="&#x2a;" u2="V" k="-48" />
    <hkern u1="&#x2a;" u2="Q" k="46" />
    <hkern u1="&#x2a;" u2="O" k="46" />
    <hkern u1="&#x2a;" u2="G" k="46" />
    <hkern u1="&#x2a;" u2="C" k="46" />
    <hkern u1="&#x2a;" u2="A" k="182" />
    <hkern u1="&#x2a;" u2="&#x40;" k="46" />
    <hkern u1="&#x2a;" u2="&#x2f;" k="182" />
    <hkern u1="&#x2a;" u2="&#x2e;" k="228" />
    <hkern u1="&#x2a;" u2="&#x2d;" k="178" />
    <hkern u1="&#x2a;" u2="&#x2c;" k="228" />
    <hkern u1="&#x2a;" u2="&#x26;" k="182" />
    <hkern u1="&#x2c;" u2="&#x2122;" k="228" />
    <hkern u1="&#x2c;" u2="&#x203a;" k="136" />
    <hkern u1="&#x2c;" u2="&#x2039;" k="136" />
    <hkern u1="&#x2c;" u2="&#x2022;" k="136" />
    <hkern u1="&#x2c;" u2="&#x201d;" k="228" />
    <hkern u1="&#x2c;" u2="&#x201c;" k="228" />
    <hkern u1="&#x2c;" u2="&#x2019;" k="228" />
    <hkern u1="&#x2c;" u2="&#x2018;" k="228" />
    <hkern u1="&#x2c;" u2="&#x2014;" k="136" />
    <hkern u1="&#x2c;" u2="&#x2013;" k="136" />
    <hkern u1="&#x2c;" u2="&#x178;" k="152" />
    <hkern u1="&#x2c;" u2="&#x152;" k="56" />
    <hkern u1="&#x2c;" u2="&#x106;" k="56" />
    <hkern u1="&#x2c;" u2="&#xdd;" k="152" />
    <hkern u1="&#x2c;" u2="&#xd8;" k="56" />
    <hkern u1="&#x2c;" u2="&#xd6;" k="56" />
    <hkern u1="&#x2c;" u2="&#xd5;" k="56" />
    <hkern u1="&#x2c;" u2="&#xd4;" k="56" />
    <hkern u1="&#x2c;" u2="&#xd3;" k="56" />
    <hkern u1="&#x2c;" u2="&#xd2;" k="56" />
    <hkern u1="&#x2c;" u2="&#xc7;" k="56" />
    <hkern u1="&#x2c;" u2="&#xbb;" k="136" />
    <hkern u1="&#x2c;" u2="&#xba;" k="228" />
    <hkern u1="&#x2c;" u2="&#xb7;" k="136" />
    <hkern u1="&#x2c;" u2="&#xb0;" k="228" />
    <hkern u1="&#x2c;" u2="&#xad;" k="136" />
    <hkern u1="&#x2c;" u2="&#xab;" k="136" />
    <hkern u1="&#x2c;" u2="&#xaa;" k="228" />
    <hkern u1="&#x2c;" u2="y" k="132" />
    <hkern u1="&#x2c;" u2="w" k="62" />
    <hkern u1="&#x2c;" u2="v" k="132" />
    <hkern u1="&#x2c;" u2="\" k="180" />
    <hkern u1="&#x2c;" u2="Y" k="152" />
    <hkern u1="&#x2c;" u2="W" k="122" />
    <hkern u1="&#x2c;" u2="V" k="180" />
    <hkern u1="&#x2c;" u2="T" k="180" />
    <hkern u1="&#x2c;" u2="Q" k="56" />
    <hkern u1="&#x2c;" u2="O" k="56" />
    <hkern u1="&#x2c;" u2="G" k="56" />
    <hkern u1="&#x2c;" u2="C" k="56" />
    <hkern u1="&#x2c;" u2="&#x40;" k="56" />
    <hkern u1="&#x2c;" u2="&#x2d;" k="136" />
    <hkern u1="&#x2c;" u2="&#x2a;" k="228" />
    <hkern u1="&#x2c;" u2="&#x27;" k="228" />
    <hkern u1="&#x2c;" u2="&#x22;" k="228" />
    <hkern u1="&#x2d;" u2="&#x2206;" k="52" />
    <hkern u1="&#x2d;" u2="&#x2122;" k="178" />
    <hkern u1="&#x2d;" u2="&#x201e;" k="136" />
    <hkern u1="&#x2d;" u2="&#x201d;" k="178" />
    <hkern u1="&#x2d;" u2="&#x201c;" k="178" />
    <hkern u1="&#x2d;" u2="&#x201a;" k="136" />
    <hkern u1="&#x2d;" u2="&#x2019;" k="178" />
    <hkern u1="&#x2d;" u2="&#x2018;" k="178" />
    <hkern u1="&#x2d;" u2="&#x17d;" k="46" />
    <hkern u1="&#x2d;" u2="&#x17b;" k="46" />
    <hkern u1="&#x2d;" u2="&#x179;" k="46" />
    <hkern u1="&#x2d;" u2="&#x178;" k="160" />
    <hkern u1="&#x2d;" u2="&#x104;" k="52" />
    <hkern u1="&#x2d;" u2="&#xdd;" k="160" />
    <hkern u1="&#x2d;" u2="&#xc6;" k="52" />
    <hkern u1="&#x2d;" u2="&#xc5;" k="52" />
    <hkern u1="&#x2d;" u2="&#xc4;" k="52" />
    <hkern u1="&#x2d;" u2="&#xc3;" k="52" />
    <hkern u1="&#x2d;" u2="&#xc2;" k="52" />
    <hkern u1="&#x2d;" u2="&#xc1;" k="52" />
    <hkern u1="&#x2d;" u2="&#xc0;" k="52" />
    <hkern u1="&#x2d;" u2="&#xba;" k="178" />
    <hkern u1="&#x2d;" u2="&#xb0;" k="178" />
    <hkern u1="&#x2d;" u2="&#xaa;" k="178" />
    <hkern u1="&#x2d;" u2="\" k="112" />
    <hkern u1="&#x2d;" u2="Z" k="46" />
    <hkern u1="&#x2d;" u2="Y" k="160" />
    <hkern u1="&#x2d;" u2="X" k="62" />
    <hkern u1="&#x2d;" u2="W" k="32" />
    <hkern u1="&#x2d;" u2="V" k="112" />
    <hkern u1="&#x2d;" u2="T" k="180" />
    <hkern u1="&#x2d;" u2="A" k="52" />
    <hkern u1="&#x2d;" u2="&#x2f;" k="52" />
    <hkern u1="&#x2d;" u2="&#x2e;" k="136" />
    <hkern u1="&#x2d;" u2="&#x2c;" k="136" />
    <hkern u1="&#x2d;" u2="&#x2a;" k="178" />
    <hkern u1="&#x2d;" u2="&#x27;" k="178" />
    <hkern u1="&#x2d;" u2="&#x26;" k="52" />
    <hkern u1="&#x2d;" u2="&#x22;" k="178" />
    <hkern u1="&#x2e;" u2="&#x2122;" k="228" />
    <hkern u1="&#x2e;" u2="&#x203a;" k="136" />
    <hkern u1="&#x2e;" u2="&#x2039;" k="136" />
    <hkern u1="&#x2e;" u2="&#x2022;" k="136" />
    <hkern u1="&#x2e;" u2="&#x201d;" k="228" />
    <hkern u1="&#x2e;" u2="&#x201c;" k="228" />
    <hkern u1="&#x2e;" u2="&#x2019;" k="228" />
    <hkern u1="&#x2e;" u2="&#x2018;" k="228" />
    <hkern u1="&#x2e;" u2="&#x2014;" k="136" />
    <hkern u1="&#x2e;" u2="&#x2013;" k="136" />
    <hkern u1="&#x2e;" u2="&#x178;" k="152" />
    <hkern u1="&#x2e;" u2="&#x152;" k="56" />
    <hkern u1="&#x2e;" u2="&#x106;" k="56" />
    <hkern u1="&#x2e;" u2="&#xdd;" k="152" />
    <hkern u1="&#x2e;" u2="&#xd8;" k="56" />
    <hkern u1="&#x2e;" u2="&#xd6;" k="56" />
    <hkern u1="&#x2e;" u2="&#xd5;" k="56" />
    <hkern u1="&#x2e;" u2="&#xd4;" k="56" />
    <hkern u1="&#x2e;" u2="&#xd3;" k="56" />
    <hkern u1="&#x2e;" u2="&#xd2;" k="56" />
    <hkern u1="&#x2e;" u2="&#xc7;" k="56" />
    <hkern u1="&#x2e;" u2="&#xbb;" k="136" />
    <hkern u1="&#x2e;" u2="&#xba;" k="228" />
    <hkern u1="&#x2e;" u2="&#xb7;" k="136" />
    <hkern u1="&#x2e;" u2="&#xb0;" k="228" />
    <hkern u1="&#x2e;" u2="&#xad;" k="136" />
    <hkern u1="&#x2e;" u2="&#xab;" k="136" />
    <hkern u1="&#x2e;" u2="&#xaa;" k="228" />
    <hkern u1="&#x2e;" u2="y" k="132" />
    <hkern u1="&#x2e;" u2="w" k="62" />
    <hkern u1="&#x2e;" u2="v" k="132" />
    <hkern u1="&#x2e;" u2="\" k="180" />
    <hkern u1="&#x2e;" u2="Y" k="152" />
    <hkern u1="&#x2e;" u2="W" k="122" />
    <hkern u1="&#x2e;" u2="V" k="180" />
    <hkern u1="&#x2e;" u2="T" k="180" />
    <hkern u1="&#x2e;" u2="Q" k="56" />
    <hkern u1="&#x2e;" u2="O" k="56" />
    <hkern u1="&#x2e;" u2="G" k="56" />
    <hkern u1="&#x2e;" u2="C" k="56" />
    <hkern u1="&#x2e;" u2="&#x40;" k="56" />
    <hkern u1="&#x2e;" u2="&#x2d;" k="136" />
    <hkern u1="&#x2e;" u2="&#x2a;" k="228" />
    <hkern u1="&#x2e;" u2="&#x27;" k="228" />
    <hkern u1="&#x2e;" u2="&#x22;" k="228" />
    <hkern u1="&#x2f;" u2="&#x2206;" k="136" />
    <hkern u1="&#x2f;" u2="&#x2122;" k="-48" />
    <hkern u1="&#x2f;" u2="&#x203a;" k="112" />
    <hkern u1="&#x2f;" u2="&#x2039;" k="112" />
    <hkern u1="&#x2f;" u2="&#x2022;" k="112" />
    <hkern u1="&#x2f;" u2="&#x201e;" k="192" />
    <hkern u1="&#x2f;" u2="&#x201d;" k="-48" />
    <hkern u1="&#x2f;" u2="&#x201c;" k="-48" />
    <hkern u1="&#x2f;" u2="&#x201a;" k="192" />
    <hkern u1="&#x2f;" u2="&#x2019;" k="-48" />
    <hkern u1="&#x2f;" u2="&#x2018;" k="-48" />
    <hkern u1="&#x2f;" u2="&#x2014;" k="112" />
    <hkern u1="&#x2f;" u2="&#x2013;" k="112" />
    <hkern u1="&#x2f;" u2="&#x153;" k="116" />
    <hkern u1="&#x2f;" u2="&#x152;" k="52" />
    <hkern u1="&#x2f;" u2="&#x144;" k="88" />
    <hkern u1="&#x2f;" u2="&#x119;" k="116" />
    <hkern u1="&#x2f;" u2="&#x107;" k="116" />
    <hkern u1="&#x2f;" u2="&#x106;" k="52" />
    <hkern u1="&#x2f;" u2="&#x105;" k="116" />
    <hkern u1="&#x2f;" u2="&#x104;" k="136" />
    <hkern u1="&#x2f;" u2="&#xfc;" k="88" />
    <hkern u1="&#x2f;" u2="&#xfb;" k="88" />
    <hkern u1="&#x2f;" u2="&#xfa;" k="88" />
    <hkern u1="&#x2f;" u2="&#xf9;" k="88" />
    <hkern u1="&#x2f;" u2="&#xf8;" k="116" />
    <hkern u1="&#x2f;" u2="&#xf6;" k="116" />
    <hkern u1="&#x2f;" u2="&#xf5;" k="116" />
    <hkern u1="&#x2f;" u2="&#xf4;" k="116" />
    <hkern u1="&#x2f;" u2="&#xf3;" k="116" />
    <hkern u1="&#x2f;" u2="&#xf2;" k="116" />
    <hkern u1="&#x2f;" u2="&#xf1;" k="88" />
    <hkern u1="&#x2f;" u2="&#xf0;" k="116" />
    <hkern u1="&#x2f;" u2="&#xeb;" k="116" />
    <hkern u1="&#x2f;" u2="&#xea;" k="116" />
    <hkern u1="&#x2f;" u2="&#xe9;" k="116" />
    <hkern u1="&#x2f;" u2="&#xe8;" k="116" />
    <hkern u1="&#x2f;" u2="&#xe7;" k="116" />
    <hkern u1="&#x2f;" u2="&#xe6;" k="116" />
    <hkern u1="&#x2f;" u2="&#xe5;" k="116" />
    <hkern u1="&#x2f;" u2="&#xe4;" k="116" />
    <hkern u1="&#x2f;" u2="&#xe3;" k="116" />
    <hkern u1="&#x2f;" u2="&#xe2;" k="116" />
    <hkern u1="&#x2f;" u2="&#xe1;" k="116" />
    <hkern u1="&#x2f;" u2="&#xe0;" k="116" />
    <hkern u1="&#x2f;" u2="&#xd8;" k="52" />
    <hkern u1="&#x2f;" u2="&#xd6;" k="52" />
    <hkern u1="&#x2f;" u2="&#xd5;" k="52" />
    <hkern u1="&#x2f;" u2="&#xd4;" k="52" />
    <hkern u1="&#x2f;" u2="&#xd3;" k="52" />
    <hkern u1="&#x2f;" u2="&#xd2;" k="52" />
    <hkern u1="&#x2f;" u2="&#xc7;" k="52" />
    <hkern u1="&#x2f;" u2="&#xc6;" k="136" />
    <hkern u1="&#x2f;" u2="&#xc5;" k="136" />
    <hkern u1="&#x2f;" u2="&#xc4;" k="136" />
    <hkern u1="&#x2f;" u2="&#xc3;" k="136" />
    <hkern u1="&#x2f;" u2="&#xc2;" k="136" />
    <hkern u1="&#x2f;" u2="&#xc1;" k="136" />
    <hkern u1="&#x2f;" u2="&#xc0;" k="136" />
    <hkern u1="&#x2f;" u2="&#xbb;" k="112" />
    <hkern u1="&#x2f;" u2="&#xba;" k="-48" />
    <hkern u1="&#x2f;" u2="&#xb9;" k="-58" />
    <hkern u1="&#x2f;" u2="&#xb7;" k="112" />
    <hkern u1="&#x2f;" u2="&#xb5;" k="88" />
    <hkern u1="&#x2f;" u2="&#xb3;" k="-58" />
    <hkern u1="&#x2f;" u2="&#xb2;" k="-58" />
    <hkern u1="&#x2f;" u2="&#xb0;" k="-48" />
    <hkern u1="&#x2f;" u2="&#xad;" k="112" />
    <hkern u1="&#x2f;" u2="&#xab;" k="112" />
    <hkern u1="&#x2f;" u2="&#xaa;" k="-48" />
    <hkern u1="&#x2f;" u2="z" k="82" />
    <hkern u1="&#x2f;" u2="y" k="48" />
    <hkern u1="&#x2f;" u2="x" k="52" />
    <hkern u1="&#x2f;" u2="v" k="48" />
    <hkern u1="&#x2f;" u2="u" k="88" />
    <hkern u1="&#x2f;" u2="t" k="42" />
    <hkern u1="&#x2f;" u2="s" k="106" />
    <hkern u1="&#x2f;" u2="r" k="88" />
    <hkern u1="&#x2f;" u2="q" k="116" />
    <hkern u1="&#x2f;" u2="p" k="88" />
    <hkern u1="&#x2f;" u2="o" k="116" />
    <hkern u1="&#x2f;" u2="n" k="88" />
    <hkern u1="&#x2f;" u2="m" k="88" />
    <hkern u1="&#x2f;" u2="g" k="136" />
    <hkern u1="&#x2f;" u2="f" k="30" />
    <hkern u1="&#x2f;" u2="e" k="116" />
    <hkern u1="&#x2f;" u2="d" k="116" />
    <hkern u1="&#x2f;" u2="c" k="116" />
    <hkern u1="&#x2f;" u2="a" k="116" />
    <hkern u1="&#x2f;" u2="Q" k="52" />
    <hkern u1="&#x2f;" u2="O" k="52" />
    <hkern u1="&#x2f;" u2="J" k="152" />
    <hkern u1="&#x2f;" u2="G" k="52" />
    <hkern u1="&#x2f;" u2="C" k="52" />
    <hkern u1="&#x2f;" u2="A" k="136" />
    <hkern u1="&#x2f;" u2="&#x40;" k="52" />
    <hkern u1="&#x2f;" u2="&#x3f;" k="-48" />
    <hkern u1="&#x2f;" u2="&#x3b;" k="88" />
    <hkern u1="&#x2f;" u2="&#x3a;" k="88" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="136" />
    <hkern u1="&#x2f;" u2="&#x2e;" k="192" />
    <hkern u1="&#x2f;" u2="&#x2d;" k="112" />
    <hkern u1="&#x2f;" u2="&#x2c;" k="192" />
    <hkern u1="&#x2f;" u2="&#x2a;" k="-48" />
    <hkern u1="&#x2f;" u2="&#x27;" k="-48" />
    <hkern u1="&#x2f;" u2="&#x26;" k="136" />
    <hkern u1="&#x2f;" u2="&#x22;" k="-48" />
    <hkern u1="&#x40;" u2="&#x2206;" k="42" />
    <hkern u1="&#x40;" u2="&#x2122;" k="46" />
    <hkern u1="&#x40;" u2="&#x201e;" k="56" />
    <hkern u1="&#x40;" u2="&#x201d;" k="46" />
    <hkern u1="&#x40;" u2="&#x201c;" k="46" />
    <hkern u1="&#x40;" u2="&#x201a;" k="56" />
    <hkern u1="&#x40;" u2="&#x2019;" k="46" />
    <hkern u1="&#x40;" u2="&#x2018;" k="46" />
    <hkern u1="&#x40;" u2="&#x17d;" k="70" />
    <hkern u1="&#x40;" u2="&#x17b;" k="70" />
    <hkern u1="&#x40;" u2="&#x179;" k="70" />
    <hkern u1="&#x40;" u2="&#x178;" k="80" />
    <hkern u1="&#x40;" u2="&#x104;" k="42" />
    <hkern u1="&#x40;" u2="&#xdd;" k="80" />
    <hkern u1="&#x40;" u2="&#xc6;" k="42" />
    <hkern u1="&#x40;" u2="&#xc5;" k="42" />
    <hkern u1="&#x40;" u2="&#xc4;" k="42" />
    <hkern u1="&#x40;" u2="&#xc3;" k="42" />
    <hkern u1="&#x40;" u2="&#xc2;" k="42" />
    <hkern u1="&#x40;" u2="&#xc1;" k="42" />
    <hkern u1="&#x40;" u2="&#xc0;" k="42" />
    <hkern u1="&#x40;" u2="&#xba;" k="46" />
    <hkern u1="&#x40;" u2="&#xb0;" k="46" />
    <hkern u1="&#x40;" u2="&#xaa;" k="46" />
    <hkern u1="&#x40;" u2="&#x7d;" k="40" />
    <hkern u1="&#x40;" u2="]" k="40" />
    <hkern u1="&#x40;" u2="\" k="52" />
    <hkern u1="&#x40;" u2="Z" k="70" />
    <hkern u1="&#x40;" u2="Y" k="80" />
    <hkern u1="&#x40;" u2="X" k="30" />
    <hkern u1="&#x40;" u2="V" k="52" />
    <hkern u1="&#x40;" u2="T" k="98" />
    <hkern u1="&#x40;" u2="A" k="42" />
    <hkern u1="&#x40;" u2="&#x2f;" k="42" />
    <hkern u1="&#x40;" u2="&#x2e;" k="56" />
    <hkern u1="&#x40;" u2="&#x2c;" k="56" />
    <hkern u1="&#x40;" u2="&#x2a;" k="46" />
    <hkern u1="&#x40;" u2="&#x29;" k="40" />
    <hkern u1="&#x40;" u2="&#x27;" k="46" />
    <hkern u1="&#x40;" u2="&#x26;" k="42" />
    <hkern u1="&#x40;" u2="&#x22;" k="46" />
    <hkern u1="A" u2="&#x2122;" k="182" />
    <hkern u1="A" u2="&#x203a;" k="52" />
    <hkern u1="A" u2="&#x2039;" k="52" />
    <hkern u1="A" u2="&#x2022;" k="52" />
    <hkern u1="A" u2="&#x201d;" k="182" />
    <hkern u1="A" u2="&#x201c;" k="182" />
    <hkern u1="A" u2="&#x2019;" k="182" />
    <hkern u1="A" u2="&#x2018;" k="182" />
    <hkern u1="A" u2="&#x2014;" k="52" />
    <hkern u1="A" u2="&#x2013;" k="52" />
    <hkern u1="A" u2="&#x178;" k="164" />
    <hkern u1="A" u2="&#x152;" k="42" />
    <hkern u1="A" u2="&#x106;" k="42" />
    <hkern u1="A" u2="&#xdd;" k="164" />
    <hkern u1="A" u2="&#xdc;" k="56" />
    <hkern u1="A" u2="&#xdb;" k="56" />
    <hkern u1="A" u2="&#xda;" k="56" />
    <hkern u1="A" u2="&#xd9;" k="56" />
    <hkern u1="A" u2="&#xd8;" k="42" />
    <hkern u1="A" u2="&#xd6;" k="42" />
    <hkern u1="A" u2="&#xd5;" k="42" />
    <hkern u1="A" u2="&#xd4;" k="42" />
    <hkern u1="A" u2="&#xd3;" k="42" />
    <hkern u1="A" u2="&#xd2;" k="42" />
    <hkern u1="A" u2="&#xc7;" k="42" />
    <hkern u1="A" u2="&#xbb;" k="52" />
    <hkern u1="A" u2="&#xba;" k="182" />
    <hkern u1="A" u2="&#xb9;" k="184" />
    <hkern u1="A" u2="&#xb7;" k="52" />
    <hkern u1="A" u2="&#xb3;" k="184" />
    <hkern u1="A" u2="&#xb2;" k="184" />
    <hkern u1="A" u2="&#xb0;" k="182" />
    <hkern u1="A" u2="&#xad;" k="52" />
    <hkern u1="A" u2="&#xab;" k="52" />
    <hkern u1="A" u2="&#xaa;" k="182" />
    <hkern u1="A" u2="y" k="82" />
    <hkern u1="A" u2="v" k="82" />
    <hkern u1="A" u2="\" k="136" />
    <hkern u1="A" u2="Y" k="164" />
    <hkern u1="A" u2="W" k="84" />
    <hkern u1="A" u2="V" k="136" />
    <hkern u1="A" u2="U" k="56" />
    <hkern u1="A" u2="T" k="132" />
    <hkern u1="A" u2="Q" k="42" />
    <hkern u1="A" u2="O" k="42" />
    <hkern u1="A" u2="J" k="-50" />
    <hkern u1="A" u2="G" k="42" />
    <hkern u1="A" u2="C" k="42" />
    <hkern u1="A" u2="&#x40;" k="42" />
    <hkern u1="A" u2="&#x3f;" k="56" />
    <hkern u1="A" u2="&#x2d;" k="52" />
    <hkern u1="A" u2="&#x2a;" k="182" />
    <hkern u1="A" u2="&#x27;" k="182" />
    <hkern u1="A" u2="&#x22;" k="182" />
    <hkern u1="C" u2="&#x203a;" k="150" />
    <hkern u1="C" u2="&#x2039;" k="150" />
    <hkern u1="C" u2="&#x2022;" k="150" />
    <hkern u1="C" u2="&#x2014;" k="150" />
    <hkern u1="C" u2="&#x2013;" k="150" />
    <hkern u1="C" u2="&#xbb;" k="150" />
    <hkern u1="C" u2="&#xb7;" k="150" />
    <hkern u1="C" u2="&#xad;" k="150" />
    <hkern u1="C" u2="&#xab;" k="150" />
    <hkern u1="C" u2="&#x2d;" k="150" />
    <hkern u1="D" u2="&#x2206;" k="42" />
    <hkern u1="D" u2="&#x2122;" k="46" />
    <hkern u1="D" u2="&#x201e;" k="56" />
    <hkern u1="D" u2="&#x201d;" k="46" />
    <hkern u1="D" u2="&#x201c;" k="46" />
    <hkern u1="D" u2="&#x201a;" k="56" />
    <hkern u1="D" u2="&#x2019;" k="46" />
    <hkern u1="D" u2="&#x2018;" k="46" />
    <hkern u1="D" u2="&#x17d;" k="70" />
    <hkern u1="D" u2="&#x17b;" k="70" />
    <hkern u1="D" u2="&#x179;" k="70" />
    <hkern u1="D" u2="&#x178;" k="80" />
    <hkern u1="D" u2="&#x104;" k="42" />
    <hkern u1="D" u2="&#xdd;" k="80" />
    <hkern u1="D" u2="&#xc6;" k="42" />
    <hkern u1="D" u2="&#xc5;" k="42" />
    <hkern u1="D" u2="&#xc4;" k="42" />
    <hkern u1="D" u2="&#xc3;" k="42" />
    <hkern u1="D" u2="&#xc2;" k="42" />
    <hkern u1="D" u2="&#xc1;" k="42" />
    <hkern u1="D" u2="&#xc0;" k="42" />
    <hkern u1="D" u2="&#xba;" k="46" />
    <hkern u1="D" u2="&#xb0;" k="46" />
    <hkern u1="D" u2="&#xaa;" k="46" />
    <hkern u1="D" u2="&#x7d;" k="40" />
    <hkern u1="D" u2="]" k="40" />
    <hkern u1="D" u2="\" k="52" />
    <hkern u1="D" u2="Z" k="70" />
    <hkern u1="D" u2="Y" k="80" />
    <hkern u1="D" u2="X" k="30" />
    <hkern u1="D" u2="V" k="52" />
    <hkern u1="D" u2="T" k="98" />
    <hkern u1="D" u2="A" k="42" />
    <hkern u1="D" u2="&#x2f;" k="42" />
    <hkern u1="D" u2="&#x2e;" k="56" />
    <hkern u1="D" u2="&#x2c;" k="56" />
    <hkern u1="D" u2="&#x2a;" k="46" />
    <hkern u1="D" u2="&#x29;" k="40" />
    <hkern u1="D" u2="&#x27;" k="46" />
    <hkern u1="D" u2="&#x26;" k="42" />
    <hkern u1="D" u2="&#x22;" k="46" />
    <hkern u1="F" u2="&#x2206;" k="132" />
    <hkern u1="F" u2="&#x201e;" k="180" />
    <hkern u1="F" u2="&#x201a;" k="180" />
    <hkern u1="F" u2="&#x153;" k="70" />
    <hkern u1="F" u2="&#x144;" k="60" />
    <hkern u1="F" u2="&#x119;" k="70" />
    <hkern u1="F" u2="&#x107;" k="70" />
    <hkern u1="F" u2="&#x104;" k="132" />
    <hkern u1="F" u2="&#xfc;" k="60" />
    <hkern u1="F" u2="&#xfb;" k="60" />
    <hkern u1="F" u2="&#xfa;" k="60" />
    <hkern u1="F" u2="&#xf9;" k="60" />
    <hkern u1="F" u2="&#xf8;" k="70" />
    <hkern u1="F" u2="&#xf6;" k="70" />
    <hkern u1="F" u2="&#xf5;" k="70" />
    <hkern u1="F" u2="&#xf4;" k="70" />
    <hkern u1="F" u2="&#xf3;" k="70" />
    <hkern u1="F" u2="&#xf2;" k="70" />
    <hkern u1="F" u2="&#xf1;" k="60" />
    <hkern u1="F" u2="&#xf0;" k="70" />
    <hkern u1="F" u2="&#xeb;" k="70" />
    <hkern u1="F" u2="&#xea;" k="70" />
    <hkern u1="F" u2="&#xe9;" k="70" />
    <hkern u1="F" u2="&#xe8;" k="70" />
    <hkern u1="F" u2="&#xe7;" k="70" />
    <hkern u1="F" u2="&#xc6;" k="132" />
    <hkern u1="F" u2="&#xc5;" k="132" />
    <hkern u1="F" u2="&#xc4;" k="132" />
    <hkern u1="F" u2="&#xc3;" k="132" />
    <hkern u1="F" u2="&#xc2;" k="132" />
    <hkern u1="F" u2="&#xc1;" k="132" />
    <hkern u1="F" u2="&#xc0;" k="132" />
    <hkern u1="F" u2="&#xb5;" k="60" />
    <hkern u1="F" u2="u" k="60" />
    <hkern u1="F" u2="r" k="60" />
    <hkern u1="F" u2="q" k="70" />
    <hkern u1="F" u2="p" k="60" />
    <hkern u1="F" u2="o" k="70" />
    <hkern u1="F" u2="n" k="60" />
    <hkern u1="F" u2="m" k="60" />
    <hkern u1="F" u2="e" k="70" />
    <hkern u1="F" u2="d" k="70" />
    <hkern u1="F" u2="c" k="70" />
    <hkern u1="F" u2="J" k="198" />
    <hkern u1="F" u2="A" k="132" />
    <hkern u1="F" u2="&#x3f;" k="-30" />
    <hkern u1="F" u2="&#x3b;" k="60" />
    <hkern u1="F" u2="&#x3a;" k="60" />
    <hkern u1="F" u2="&#x2f;" k="132" />
    <hkern u1="F" u2="&#x2e;" k="180" />
    <hkern u1="F" u2="&#x2c;" k="180" />
    <hkern u1="F" u2="&#x26;" k="132" />
    <hkern u1="J" u2="&#x2206;" k="56" />
    <hkern u1="J" u2="&#x201e;" k="50" />
    <hkern u1="J" u2="&#x201a;" k="50" />
    <hkern u1="J" u2="&#x104;" k="56" />
    <hkern u1="J" u2="&#xc6;" k="56" />
    <hkern u1="J" u2="&#xc5;" k="56" />
    <hkern u1="J" u2="&#xc4;" k="56" />
    <hkern u1="J" u2="&#xc3;" k="56" />
    <hkern u1="J" u2="&#xc2;" k="56" />
    <hkern u1="J" u2="&#xc1;" k="56" />
    <hkern u1="J" u2="&#xc0;" k="56" />
    <hkern u1="J" u2="A" k="56" />
    <hkern u1="J" u2="&#x2f;" k="56" />
    <hkern u1="J" u2="&#x2e;" k="50" />
    <hkern u1="J" u2="&#x2c;" k="50" />
    <hkern u1="J" u2="&#x26;" k="56" />
    <hkern u1="K" u2="&#x203a;" k="62" />
    <hkern u1="K" u2="&#x2039;" k="62" />
    <hkern u1="K" u2="&#x2022;" k="62" />
    <hkern u1="K" u2="&#x2014;" k="62" />
    <hkern u1="K" u2="&#x2013;" k="62" />
    <hkern u1="K" u2="&#x153;" k="36" />
    <hkern u1="K" u2="&#x152;" k="30" />
    <hkern u1="K" u2="&#x119;" k="36" />
    <hkern u1="K" u2="&#x107;" k="36" />
    <hkern u1="K" u2="&#x106;" k="30" />
    <hkern u1="K" u2="&#xf8;" k="36" />
    <hkern u1="K" u2="&#xf6;" k="36" />
    <hkern u1="K" u2="&#xf5;" k="36" />
    <hkern u1="K" u2="&#xf4;" k="36" />
    <hkern u1="K" u2="&#xf3;" k="36" />
    <hkern u1="K" u2="&#xf2;" k="36" />
    <hkern u1="K" u2="&#xf0;" k="36" />
    <hkern u1="K" u2="&#xeb;" k="36" />
    <hkern u1="K" u2="&#xea;" k="36" />
    <hkern u1="K" u2="&#xe9;" k="36" />
    <hkern u1="K" u2="&#xe8;" k="36" />
    <hkern u1="K" u2="&#xe7;" k="36" />
    <hkern u1="K" u2="&#xd8;" k="30" />
    <hkern u1="K" u2="&#xd6;" k="30" />
    <hkern u1="K" u2="&#xd5;" k="30" />
    <hkern u1="K" u2="&#xd4;" k="30" />
    <hkern u1="K" u2="&#xd3;" k="30" />
    <hkern u1="K" u2="&#xd2;" k="30" />
    <hkern u1="K" u2="&#xc7;" k="30" />
    <hkern u1="K" u2="&#xbb;" k="62" />
    <hkern u1="K" u2="&#xb7;" k="62" />
    <hkern u1="K" u2="&#xad;" k="62" />
    <hkern u1="K" u2="&#xab;" k="62" />
    <hkern u1="K" u2="y" k="66" />
    <hkern u1="K" u2="w" k="56" />
    <hkern u1="K" u2="v" k="66" />
    <hkern u1="K" u2="t" k="82" />
    <hkern u1="K" u2="q" k="36" />
    <hkern u1="K" u2="o" k="36" />
    <hkern u1="K" u2="f" k="52" />
    <hkern u1="K" u2="e" k="36" />
    <hkern u1="K" u2="d" k="36" />
    <hkern u1="K" u2="c" k="36" />
    <hkern u1="K" u2="Q" k="30" />
    <hkern u1="K" u2="O" k="30" />
    <hkern u1="K" u2="G" k="30" />
    <hkern u1="K" u2="C" k="30" />
    <hkern u1="K" u2="&#x40;" k="30" />
    <hkern u1="K" u2="&#x2d;" k="62" />
    <hkern u1="L" u2="&#x2122;" k="290" />
    <hkern u1="L" u2="&#x203a;" k="198" />
    <hkern u1="L" u2="&#x2039;" k="198" />
    <hkern u1="L" u2="&#x2022;" k="198" />
    <hkern u1="L" u2="&#x201e;" k="-54" />
    <hkern u1="L" u2="&#x201d;" k="290" />
    <hkern u1="L" u2="&#x201c;" k="290" />
    <hkern u1="L" u2="&#x201a;" k="-54" />
    <hkern u1="L" u2="&#x2019;" k="290" />
    <hkern u1="L" u2="&#x2018;" k="290" />
    <hkern u1="L" u2="&#x2014;" k="198" />
    <hkern u1="L" u2="&#x2013;" k="198" />
    <hkern u1="L" u2="&#x178;" k="212" />
    <hkern u1="L" u2="&#x153;" k="36" />
    <hkern u1="L" u2="&#x152;" k="80" />
    <hkern u1="L" u2="&#x119;" k="36" />
    <hkern u1="L" u2="&#x107;" k="36" />
    <hkern u1="L" u2="&#x106;" k="80" />
    <hkern u1="L" u2="&#xf8;" k="36" />
    <hkern u1="L" u2="&#xf6;" k="36" />
    <hkern u1="L" u2="&#xf5;" k="36" />
    <hkern u1="L" u2="&#xf4;" k="36" />
    <hkern u1="L" u2="&#xf3;" k="36" />
    <hkern u1="L" u2="&#xf2;" k="36" />
    <hkern u1="L" u2="&#xf0;" k="36" />
    <hkern u1="L" u2="&#xeb;" k="36" />
    <hkern u1="L" u2="&#xea;" k="36" />
    <hkern u1="L" u2="&#xe9;" k="36" />
    <hkern u1="L" u2="&#xe8;" k="36" />
    <hkern u1="L" u2="&#xe7;" k="36" />
    <hkern u1="L" u2="&#xdd;" k="212" />
    <hkern u1="L" u2="&#xd8;" k="80" />
    <hkern u1="L" u2="&#xd6;" k="80" />
    <hkern u1="L" u2="&#xd5;" k="80" />
    <hkern u1="L" u2="&#xd4;" k="80" />
    <hkern u1="L" u2="&#xd3;" k="80" />
    <hkern u1="L" u2="&#xd2;" k="80" />
    <hkern u1="L" u2="&#xc7;" k="80" />
    <hkern u1="L" u2="&#xbb;" k="198" />
    <hkern u1="L" u2="&#xba;" k="290" />
    <hkern u1="L" u2="&#xb9;" k="202" />
    <hkern u1="L" u2="&#xb7;" k="198" />
    <hkern u1="L" u2="&#xb3;" k="202" />
    <hkern u1="L" u2="&#xb2;" k="202" />
    <hkern u1="L" u2="&#xb0;" k="290" />
    <hkern u1="L" u2="&#xad;" k="198" />
    <hkern u1="L" u2="&#xab;" k="198" />
    <hkern u1="L" u2="&#xaa;" k="290" />
    <hkern u1="L" u2="y" k="108" />
    <hkern u1="L" u2="w" k="80" />
    <hkern u1="L" u2="v" k="108" />
    <hkern u1="L" u2="q" k="36" />
    <hkern u1="L" u2="o" k="36" />
    <hkern u1="L" u2="e" k="36" />
    <hkern u1="L" u2="d" k="36" />
    <hkern u1="L" u2="c" k="36" />
    <hkern u1="L" u2="\" k="182" />
    <hkern u1="L" u2="Y" k="212" />
    <hkern u1="L" u2="W" k="152" />
    <hkern u1="L" u2="V" k="182" />
    <hkern u1="L" u2="T" k="172" />
    <hkern u1="L" u2="Q" k="80" />
    <hkern u1="L" u2="O" k="80" />
    <hkern u1="L" u2="G" k="80" />
    <hkern u1="L" u2="C" k="80" />
    <hkern u1="L" u2="&#x40;" k="80" />
    <hkern u1="L" u2="&#x3f;" k="50" />
    <hkern u1="L" u2="&#x2e;" k="-54" />
    <hkern u1="L" u2="&#x2d;" k="198" />
    <hkern u1="L" u2="&#x2c;" k="-54" />
    <hkern u1="L" u2="&#x2a;" k="290" />
    <hkern u1="L" u2="&#x27;" k="290" />
    <hkern u1="L" u2="&#x22;" k="290" />
    <hkern u1="O" u2="&#x2206;" k="42" />
    <hkern u1="O" u2="&#x2122;" k="46" />
    <hkern u1="O" u2="&#x201e;" k="56" />
    <hkern u1="O" u2="&#x201d;" k="46" />
    <hkern u1="O" u2="&#x201c;" k="46" />
    <hkern u1="O" u2="&#x201a;" k="56" />
    <hkern u1="O" u2="&#x2019;" k="46" />
    <hkern u1="O" u2="&#x2018;" k="46" />
    <hkern u1="O" u2="&#x17d;" k="70" />
    <hkern u1="O" u2="&#x17b;" k="70" />
    <hkern u1="O" u2="&#x179;" k="70" />
    <hkern u1="O" u2="&#x178;" k="80" />
    <hkern u1="O" u2="&#x104;" k="42" />
    <hkern u1="O" u2="&#xdd;" k="80" />
    <hkern u1="O" u2="&#xc6;" k="42" />
    <hkern u1="O" u2="&#xc5;" k="42" />
    <hkern u1="O" u2="&#xc4;" k="42" />
    <hkern u1="O" u2="&#xc3;" k="42" />
    <hkern u1="O" u2="&#xc2;" k="42" />
    <hkern u1="O" u2="&#xc1;" k="42" />
    <hkern u1="O" u2="&#xc0;" k="42" />
    <hkern u1="O" u2="&#xba;" k="46" />
    <hkern u1="O" u2="&#xb0;" k="46" />
    <hkern u1="O" u2="&#xaa;" k="46" />
    <hkern u1="O" u2="&#x7d;" k="40" />
    <hkern u1="O" u2="]" k="40" />
    <hkern u1="O" u2="\" k="52" />
    <hkern u1="O" u2="Z" k="70" />
    <hkern u1="O" u2="Y" k="80" />
    <hkern u1="O" u2="X" k="30" />
    <hkern u1="O" u2="V" k="52" />
    <hkern u1="O" u2="T" k="98" />
    <hkern u1="O" u2="A" k="42" />
    <hkern u1="O" u2="&#x2f;" k="42" />
    <hkern u1="O" u2="&#x2e;" k="56" />
    <hkern u1="O" u2="&#x2c;" k="56" />
    <hkern u1="O" u2="&#x2a;" k="46" />
    <hkern u1="O" u2="&#x29;" k="40" />
    <hkern u1="O" u2="&#x27;" k="46" />
    <hkern u1="O" u2="&#x26;" k="42" />
    <hkern u1="O" u2="&#x22;" k="46" />
    <hkern u1="P" u2="&#x2206;" k="138" />
    <hkern u1="P" u2="&#x201e;" k="248" />
    <hkern u1="P" u2="&#x201a;" k="248" />
    <hkern u1="P" u2="&#x153;" k="30" />
    <hkern u1="P" u2="&#x119;" k="30" />
    <hkern u1="P" u2="&#x107;" k="30" />
    <hkern u1="P" u2="&#x105;" k="50" />
    <hkern u1="P" u2="&#x104;" k="138" />
    <hkern u1="P" u2="&#xf8;" k="30" />
    <hkern u1="P" u2="&#xf6;" k="30" />
    <hkern u1="P" u2="&#xf5;" k="30" />
    <hkern u1="P" u2="&#xf4;" k="30" />
    <hkern u1="P" u2="&#xf3;" k="30" />
    <hkern u1="P" u2="&#xf2;" k="30" />
    <hkern u1="P" u2="&#xf0;" k="30" />
    <hkern u1="P" u2="&#xeb;" k="30" />
    <hkern u1="P" u2="&#xea;" k="30" />
    <hkern u1="P" u2="&#xe9;" k="30" />
    <hkern u1="P" u2="&#xe8;" k="30" />
    <hkern u1="P" u2="&#xe7;" k="30" />
    <hkern u1="P" u2="&#xe6;" k="50" />
    <hkern u1="P" u2="&#xe5;" k="50" />
    <hkern u1="P" u2="&#xe4;" k="50" />
    <hkern u1="P" u2="&#xe3;" k="50" />
    <hkern u1="P" u2="&#xe2;" k="50" />
    <hkern u1="P" u2="&#xe1;" k="50" />
    <hkern u1="P" u2="&#xe0;" k="50" />
    <hkern u1="P" u2="&#xc6;" k="138" />
    <hkern u1="P" u2="&#xc5;" k="138" />
    <hkern u1="P" u2="&#xc4;" k="138" />
    <hkern u1="P" u2="&#xc3;" k="138" />
    <hkern u1="P" u2="&#xc2;" k="138" />
    <hkern u1="P" u2="&#xc1;" k="138" />
    <hkern u1="P" u2="&#xc0;" k="138" />
    <hkern u1="P" u2="q" k="30" />
    <hkern u1="P" u2="o" k="30" />
    <hkern u1="P" u2="e" k="30" />
    <hkern u1="P" u2="d" k="30" />
    <hkern u1="P" u2="c" k="30" />
    <hkern u1="P" u2="a" k="50" />
    <hkern u1="P" u2="J" k="182" />
    <hkern u1="P" u2="A" k="138" />
    <hkern u1="P" u2="&#x2f;" k="138" />
    <hkern u1="P" u2="&#x2e;" k="248" />
    <hkern u1="P" u2="&#x2c;" k="248" />
    <hkern u1="P" u2="&#x26;" k="138" />
    <hkern u1="Q" u2="&#x2206;" k="42" />
    <hkern u1="Q" u2="&#x2122;" k="46" />
    <hkern u1="Q" u2="&#x201e;" k="56" />
    <hkern u1="Q" u2="&#x201d;" k="46" />
    <hkern u1="Q" u2="&#x201c;" k="46" />
    <hkern u1="Q" u2="&#x201a;" k="56" />
    <hkern u1="Q" u2="&#x2019;" k="46" />
    <hkern u1="Q" u2="&#x2018;" k="46" />
    <hkern u1="Q" u2="&#x17d;" k="70" />
    <hkern u1="Q" u2="&#x17b;" k="70" />
    <hkern u1="Q" u2="&#x179;" k="70" />
    <hkern u1="Q" u2="&#x178;" k="80" />
    <hkern u1="Q" u2="&#x104;" k="42" />
    <hkern u1="Q" u2="&#xdd;" k="80" />
    <hkern u1="Q" u2="&#xc6;" k="42" />
    <hkern u1="Q" u2="&#xc5;" k="42" />
    <hkern u1="Q" u2="&#xc4;" k="42" />
    <hkern u1="Q" u2="&#xc3;" k="42" />
    <hkern u1="Q" u2="&#xc2;" k="42" />
    <hkern u1="Q" u2="&#xc1;" k="42" />
    <hkern u1="Q" u2="&#xc0;" k="42" />
    <hkern u1="Q" u2="&#xba;" k="46" />
    <hkern u1="Q" u2="&#xb0;" k="46" />
    <hkern u1="Q" u2="&#xaa;" k="46" />
    <hkern u1="Q" u2="&#x7d;" k="40" />
    <hkern u1="Q" u2="]" k="40" />
    <hkern u1="Q" u2="\" k="52" />
    <hkern u1="Q" u2="Z" k="70" />
    <hkern u1="Q" u2="Y" k="80" />
    <hkern u1="Q" u2="X" k="30" />
    <hkern u1="Q" u2="V" k="52" />
    <hkern u1="Q" u2="T" k="98" />
    <hkern u1="Q" u2="A" k="42" />
    <hkern u1="Q" u2="&#x2f;" k="42" />
    <hkern u1="Q" u2="&#x2e;" k="56" />
    <hkern u1="Q" u2="&#x2c;" k="56" />
    <hkern u1="Q" u2="&#x2a;" k="46" />
    <hkern u1="Q" u2="&#x29;" k="40" />
    <hkern u1="Q" u2="&#x27;" k="46" />
    <hkern u1="Q" u2="&#x26;" k="42" />
    <hkern u1="Q" u2="&#x22;" k="46" />
    <hkern u1="R" u2="&#x152;" k="46" />
    <hkern u1="R" u2="&#x106;" k="46" />
    <hkern u1="R" u2="&#xdc;" k="42" />
    <hkern u1="R" u2="&#xdb;" k="42" />
    <hkern u1="R" u2="&#xda;" k="42" />
    <hkern u1="R" u2="&#xd9;" k="42" />
    <hkern u1="R" u2="&#xd8;" k="46" />
    <hkern u1="R" u2="&#xd6;" k="46" />
    <hkern u1="R" u2="&#xd5;" k="46" />
    <hkern u1="R" u2="&#xd4;" k="46" />
    <hkern u1="R" u2="&#xd3;" k="46" />
    <hkern u1="R" u2="&#xd2;" k="46" />
    <hkern u1="R" u2="&#xc7;" k="46" />
    <hkern u1="R" u2="U" k="42" />
    <hkern u1="R" u2="T" k="52" />
    <hkern u1="R" u2="Q" k="46" />
    <hkern u1="R" u2="O" k="46" />
    <hkern u1="R" u2="G" k="46" />
    <hkern u1="R" u2="C" k="46" />
    <hkern u1="R" u2="&#x40;" k="46" />
    <hkern u1="T" u2="&#x2206;" k="132" />
    <hkern u1="T" u2="&#x203a;" k="180" />
    <hkern u1="T" u2="&#x2039;" k="180" />
    <hkern u1="T" u2="&#x2022;" k="180" />
    <hkern u1="T" u2="&#x201e;" k="180" />
    <hkern u1="T" u2="&#x201a;" k="180" />
    <hkern u1="T" u2="&#x2014;" k="180" />
    <hkern u1="T" u2="&#x2013;" k="180" />
    <hkern u1="T" u2="&#x153;" k="210" />
    <hkern u1="T" u2="&#x152;" k="98" />
    <hkern u1="T" u2="&#x144;" k="160" />
    <hkern u1="T" u2="&#x119;" k="210" />
    <hkern u1="T" u2="&#x107;" k="210" />
    <hkern u1="T" u2="&#x106;" k="98" />
    <hkern u1="T" u2="&#x105;" k="250" />
    <hkern u1="T" u2="&#x104;" k="132" />
    <hkern u1="T" u2="&#xfc;" k="160" />
    <hkern u1="T" u2="&#xfb;" k="160" />
    <hkern u1="T" u2="&#xfa;" k="160" />
    <hkern u1="T" u2="&#xf9;" k="160" />
    <hkern u1="T" u2="&#xf8;" k="210" />
    <hkern u1="T" u2="&#xf6;" k="210" />
    <hkern u1="T" u2="&#xf5;" k="210" />
    <hkern u1="T" u2="&#xf4;" k="210" />
    <hkern u1="T" u2="&#xf3;" k="210" />
    <hkern u1="T" u2="&#xf2;" k="210" />
    <hkern u1="T" u2="&#xf1;" k="160" />
    <hkern u1="T" u2="&#xf0;" k="210" />
    <hkern u1="T" u2="&#xeb;" k="210" />
    <hkern u1="T" u2="&#xea;" k="210" />
    <hkern u1="T" u2="&#xe9;" k="210" />
    <hkern u1="T" u2="&#xe8;" k="210" />
    <hkern u1="T" u2="&#xe7;" k="210" />
    <hkern u1="T" u2="&#xe6;" k="250" />
    <hkern u1="T" u2="&#xe5;" k="250" />
    <hkern u1="T" u2="&#xe4;" k="250" />
    <hkern u1="T" u2="&#xe3;" k="250" />
    <hkern u1="T" u2="&#xe2;" k="250" />
    <hkern u1="T" u2="&#xe1;" k="250" />
    <hkern u1="T" u2="&#xe0;" k="250" />
    <hkern u1="T" u2="&#xd8;" k="98" />
    <hkern u1="T" u2="&#xd6;" k="98" />
    <hkern u1="T" u2="&#xd5;" k="98" />
    <hkern u1="T" u2="&#xd4;" k="98" />
    <hkern u1="T" u2="&#xd3;" k="98" />
    <hkern u1="T" u2="&#xd2;" k="98" />
    <hkern u1="T" u2="&#xc7;" k="98" />
    <hkern u1="T" u2="&#xc6;" k="132" />
    <hkern u1="T" u2="&#xc5;" k="132" />
    <hkern u1="T" u2="&#xc4;" k="132" />
    <hkern u1="T" u2="&#xc3;" k="132" />
    <hkern u1="T" u2="&#xc2;" k="132" />
    <hkern u1="T" u2="&#xc1;" k="132" />
    <hkern u1="T" u2="&#xc0;" k="132" />
    <hkern u1="T" u2="&#xbb;" k="180" />
    <hkern u1="T" u2="&#xb7;" k="180" />
    <hkern u1="T" u2="&#xb5;" k="160" />
    <hkern u1="T" u2="&#xad;" k="180" />
    <hkern u1="T" u2="&#xab;" k="180" />
    <hkern u1="T" u2="z" k="120" />
    <hkern u1="T" u2="y" k="180" />
    <hkern u1="T" u2="x" k="144" />
    <hkern u1="T" u2="w" k="140" />
    <hkern u1="T" u2="v" k="180" />
    <hkern u1="T" u2="u" k="160" />
    <hkern u1="T" u2="s" k="162" />
    <hkern u1="T" u2="r" k="160" />
    <hkern u1="T" u2="q" k="210" />
    <hkern u1="T" u2="p" k="160" />
    <hkern u1="T" u2="o" k="210" />
    <hkern u1="T" u2="n" k="160" />
    <hkern u1="T" u2="m" k="160" />
    <hkern u1="T" u2="g" k="188" />
    <hkern u1="T" u2="e" k="210" />
    <hkern u1="T" u2="d" k="210" />
    <hkern u1="T" u2="c" k="210" />
    <hkern u1="T" u2="a" k="250" />
    <hkern u1="T" u2="Q" k="98" />
    <hkern u1="T" u2="O" k="98" />
    <hkern u1="T" u2="J" k="200" />
    <hkern u1="T" u2="G" k="98" />
    <hkern u1="T" u2="C" k="98" />
    <hkern u1="T" u2="A" k="132" />
    <hkern u1="T" u2="&#x40;" k="98" />
    <hkern u1="T" u2="&#x3b;" k="160" />
    <hkern u1="T" u2="&#x3a;" k="160" />
    <hkern u1="T" u2="&#x2f;" k="132" />
    <hkern u1="T" u2="&#x2e;" k="180" />
    <hkern u1="T" u2="&#x2d;" k="180" />
    <hkern u1="T" u2="&#x2c;" k="180" />
    <hkern u1="T" u2="&#x26;" k="132" />
    <hkern u1="U" u2="&#x2206;" k="56" />
    <hkern u1="U" u2="&#x201e;" k="50" />
    <hkern u1="U" u2="&#x201a;" k="50" />
    <hkern u1="U" u2="&#x104;" k="56" />
    <hkern u1="U" u2="&#xc6;" k="56" />
    <hkern u1="U" u2="&#xc5;" k="56" />
    <hkern u1="U" u2="&#xc4;" k="56" />
    <hkern u1="U" u2="&#xc3;" k="56" />
    <hkern u1="U" u2="&#xc2;" k="56" />
    <hkern u1="U" u2="&#xc1;" k="56" />
    <hkern u1="U" u2="&#xc0;" k="56" />
    <hkern u1="U" u2="A" k="56" />
    <hkern u1="U" u2="&#x2f;" k="56" />
    <hkern u1="U" u2="&#x2e;" k="50" />
    <hkern u1="U" u2="&#x2c;" k="50" />
    <hkern u1="U" u2="&#x26;" k="56" />
    <hkern u1="V" u2="&#x2206;" k="136" />
    <hkern u1="V" u2="&#x2122;" k="-48" />
    <hkern u1="V" u2="&#x203a;" k="112" />
    <hkern u1="V" u2="&#x2039;" k="112" />
    <hkern u1="V" u2="&#x2022;" k="112" />
    <hkern u1="V" u2="&#x201e;" k="192" />
    <hkern u1="V" u2="&#x201d;" k="-48" />
    <hkern u1="V" u2="&#x201c;" k="-48" />
    <hkern u1="V" u2="&#x201a;" k="192" />
    <hkern u1="V" u2="&#x2019;" k="-48" />
    <hkern u1="V" u2="&#x2018;" k="-48" />
    <hkern u1="V" u2="&#x2014;" k="112" />
    <hkern u1="V" u2="&#x2013;" k="112" />
    <hkern u1="V" u2="&#x153;" k="116" />
    <hkern u1="V" u2="&#x152;" k="52" />
    <hkern u1="V" u2="&#x144;" k="88" />
    <hkern u1="V" u2="&#x119;" k="116" />
    <hkern u1="V" u2="&#x107;" k="116" />
    <hkern u1="V" u2="&#x106;" k="52" />
    <hkern u1="V" u2="&#x105;" k="116" />
    <hkern u1="V" u2="&#x104;" k="136" />
    <hkern u1="V" u2="&#xfc;" k="88" />
    <hkern u1="V" u2="&#xfb;" k="88" />
    <hkern u1="V" u2="&#xfa;" k="88" />
    <hkern u1="V" u2="&#xf9;" k="88" />
    <hkern u1="V" u2="&#xf8;" k="116" />
    <hkern u1="V" u2="&#xf6;" k="116" />
    <hkern u1="V" u2="&#xf5;" k="116" />
    <hkern u1="V" u2="&#xf4;" k="116" />
    <hkern u1="V" u2="&#xf3;" k="116" />
    <hkern u1="V" u2="&#xf2;" k="116" />
    <hkern u1="V" u2="&#xf1;" k="88" />
    <hkern u1="V" u2="&#xf0;" k="116" />
    <hkern u1="V" u2="&#xeb;" k="116" />
    <hkern u1="V" u2="&#xea;" k="116" />
    <hkern u1="V" u2="&#xe9;" k="116" />
    <hkern u1="V" u2="&#xe8;" k="116" />
    <hkern u1="V" u2="&#xe7;" k="116" />
    <hkern u1="V" u2="&#xe6;" k="116" />
    <hkern u1="V" u2="&#xe5;" k="116" />
    <hkern u1="V" u2="&#xe4;" k="116" />
    <hkern u1="V" u2="&#xe3;" k="116" />
    <hkern u1="V" u2="&#xe2;" k="116" />
    <hkern u1="V" u2="&#xe1;" k="116" />
    <hkern u1="V" u2="&#xe0;" k="116" />
    <hkern u1="V" u2="&#xd8;" k="52" />
    <hkern u1="V" u2="&#xd6;" k="52" />
    <hkern u1="V" u2="&#xd5;" k="52" />
    <hkern u1="V" u2="&#xd4;" k="52" />
    <hkern u1="V" u2="&#xd3;" k="52" />
    <hkern u1="V" u2="&#xd2;" k="52" />
    <hkern u1="V" u2="&#xc7;" k="52" />
    <hkern u1="V" u2="&#xc6;" k="136" />
    <hkern u1="V" u2="&#xc5;" k="136" />
    <hkern u1="V" u2="&#xc4;" k="136" />
    <hkern u1="V" u2="&#xc3;" k="136" />
    <hkern u1="V" u2="&#xc2;" k="136" />
    <hkern u1="V" u2="&#xc1;" k="136" />
    <hkern u1="V" u2="&#xc0;" k="136" />
    <hkern u1="V" u2="&#xbb;" k="112" />
    <hkern u1="V" u2="&#xba;" k="-48" />
    <hkern u1="V" u2="&#xb9;" k="-58" />
    <hkern u1="V" u2="&#xb7;" k="112" />
    <hkern u1="V" u2="&#xb5;" k="88" />
    <hkern u1="V" u2="&#xb3;" k="-58" />
    <hkern u1="V" u2="&#xb2;" k="-58" />
    <hkern u1="V" u2="&#xb0;" k="-48" />
    <hkern u1="V" u2="&#xad;" k="112" />
    <hkern u1="V" u2="&#xab;" k="112" />
    <hkern u1="V" u2="&#xaa;" k="-48" />
    <hkern u1="V" u2="z" k="82" />
    <hkern u1="V" u2="y" k="48" />
    <hkern u1="V" u2="x" k="52" />
    <hkern u1="V" u2="v" k="48" />
    <hkern u1="V" u2="u" k="88" />
    <hkern u1="V" u2="t" k="42" />
    <hkern u1="V" u2="s" k="106" />
    <hkern u1="V" u2="r" k="88" />
    <hkern u1="V" u2="q" k="116" />
    <hkern u1="V" u2="p" k="88" />
    <hkern u1="V" u2="o" k="116" />
    <hkern u1="V" u2="n" k="88" />
    <hkern u1="V" u2="m" k="88" />
    <hkern u1="V" u2="g" k="136" />
    <hkern u1="V" u2="f" k="30" />
    <hkern u1="V" u2="e" k="116" />
    <hkern u1="V" u2="d" k="116" />
    <hkern u1="V" u2="c" k="116" />
    <hkern u1="V" u2="a" k="116" />
    <hkern u1="V" u2="Q" k="52" />
    <hkern u1="V" u2="O" k="52" />
    <hkern u1="V" u2="J" k="152" />
    <hkern u1="V" u2="G" k="52" />
    <hkern u1="V" u2="C" k="52" />
    <hkern u1="V" u2="A" k="136" />
    <hkern u1="V" u2="&#x40;" k="52" />
    <hkern u1="V" u2="&#x3f;" k="-48" />
    <hkern u1="V" u2="&#x3b;" k="88" />
    <hkern u1="V" u2="&#x3a;" k="88" />
    <hkern u1="V" u2="&#x2f;" k="136" />
    <hkern u1="V" u2="&#x2e;" k="192" />
    <hkern u1="V" u2="&#x2d;" k="112" />
    <hkern u1="V" u2="&#x2c;" k="192" />
    <hkern u1="V" u2="&#x2a;" k="-48" />
    <hkern u1="V" u2="&#x27;" k="-48" />
    <hkern u1="V" u2="&#x26;" k="136" />
    <hkern u1="V" u2="&#x22;" k="-48" />
    <hkern u1="W" u2="&#x2206;" k="94" />
    <hkern u1="W" u2="&#x2122;" k="-48" />
    <hkern u1="W" u2="&#x203a;" k="32" />
    <hkern u1="W" u2="&#x2039;" k="32" />
    <hkern u1="W" u2="&#x2022;" k="32" />
    <hkern u1="W" u2="&#x201e;" k="122" />
    <hkern u1="W" u2="&#x201d;" k="-48" />
    <hkern u1="W" u2="&#x201c;" k="-48" />
    <hkern u1="W" u2="&#x201a;" k="122" />
    <hkern u1="W" u2="&#x2019;" k="-48" />
    <hkern u1="W" u2="&#x2018;" k="-48" />
    <hkern u1="W" u2="&#x2014;" k="32" />
    <hkern u1="W" u2="&#x2013;" k="32" />
    <hkern u1="W" u2="&#x153;" k="32" />
    <hkern u1="W" u2="&#x119;" k="32" />
    <hkern u1="W" u2="&#x107;" k="32" />
    <hkern u1="W" u2="&#x105;" k="88" />
    <hkern u1="W" u2="&#x104;" k="94" />
    <hkern u1="W" u2="&#xf8;" k="32" />
    <hkern u1="W" u2="&#xf6;" k="32" />
    <hkern u1="W" u2="&#xf5;" k="32" />
    <hkern u1="W" u2="&#xf4;" k="32" />
    <hkern u1="W" u2="&#xf3;" k="32" />
    <hkern u1="W" u2="&#xf2;" k="32" />
    <hkern u1="W" u2="&#xf0;" k="32" />
    <hkern u1="W" u2="&#xeb;" k="32" />
    <hkern u1="W" u2="&#xea;" k="32" />
    <hkern u1="W" u2="&#xe9;" k="32" />
    <hkern u1="W" u2="&#xe8;" k="32" />
    <hkern u1="W" u2="&#xe7;" k="32" />
    <hkern u1="W" u2="&#xe6;" k="88" />
    <hkern u1="W" u2="&#xe5;" k="88" />
    <hkern u1="W" u2="&#xe4;" k="88" />
    <hkern u1="W" u2="&#xe3;" k="88" />
    <hkern u1="W" u2="&#xe2;" k="88" />
    <hkern u1="W" u2="&#xe1;" k="88" />
    <hkern u1="W" u2="&#xe0;" k="88" />
    <hkern u1="W" u2="&#xc6;" k="94" />
    <hkern u1="W" u2="&#xc5;" k="94" />
    <hkern u1="W" u2="&#xc4;" k="94" />
    <hkern u1="W" u2="&#xc3;" k="94" />
    <hkern u1="W" u2="&#xc2;" k="94" />
    <hkern u1="W" u2="&#xc1;" k="94" />
    <hkern u1="W" u2="&#xc0;" k="94" />
    <hkern u1="W" u2="&#xbb;" k="32" />
    <hkern u1="W" u2="&#xba;" k="-48" />
    <hkern u1="W" u2="&#xb9;" k="-48" />
    <hkern u1="W" u2="&#xb7;" k="32" />
    <hkern u1="W" u2="&#xb3;" k="-48" />
    <hkern u1="W" u2="&#xb2;" k="-48" />
    <hkern u1="W" u2="&#xb0;" k="-48" />
    <hkern u1="W" u2="&#xad;" k="32" />
    <hkern u1="W" u2="&#xab;" k="32" />
    <hkern u1="W" u2="&#xaa;" k="-48" />
    <hkern u1="W" u2="s" k="46" />
    <hkern u1="W" u2="q" k="32" />
    <hkern u1="W" u2="o" k="32" />
    <hkern u1="W" u2="g" k="98" />
    <hkern u1="W" u2="e" k="32" />
    <hkern u1="W" u2="d" k="32" />
    <hkern u1="W" u2="c" k="32" />
    <hkern u1="W" u2="a" k="88" />
    <hkern u1="W" u2="J" k="102" />
    <hkern u1="W" u2="A" k="94" />
    <hkern u1="W" u2="&#x3f;" k="-34" />
    <hkern u1="W" u2="&#x2f;" k="94" />
    <hkern u1="W" u2="&#x2e;" k="122" />
    <hkern u1="W" u2="&#x2d;" k="32" />
    <hkern u1="W" u2="&#x2c;" k="122" />
    <hkern u1="W" u2="&#x2a;" k="-48" />
    <hkern u1="W" u2="&#x27;" k="-48" />
    <hkern u1="W" u2="&#x26;" k="94" />
    <hkern u1="W" u2="&#x22;" k="-48" />
    <hkern u1="X" u2="&#x203a;" k="62" />
    <hkern u1="X" u2="&#x2039;" k="62" />
    <hkern u1="X" u2="&#x2022;" k="62" />
    <hkern u1="X" u2="&#x2014;" k="62" />
    <hkern u1="X" u2="&#x2013;" k="62" />
    <hkern u1="X" u2="&#x153;" k="36" />
    <hkern u1="X" u2="&#x152;" k="30" />
    <hkern u1="X" u2="&#x119;" k="36" />
    <hkern u1="X" u2="&#x107;" k="36" />
    <hkern u1="X" u2="&#x106;" k="30" />
    <hkern u1="X" u2="&#xf8;" k="36" />
    <hkern u1="X" u2="&#xf6;" k="36" />
    <hkern u1="X" u2="&#xf5;" k="36" />
    <hkern u1="X" u2="&#xf4;" k="36" />
    <hkern u1="X" u2="&#xf3;" k="36" />
    <hkern u1="X" u2="&#xf2;" k="36" />
    <hkern u1="X" u2="&#xf0;" k="36" />
    <hkern u1="X" u2="&#xeb;" k="36" />
    <hkern u1="X" u2="&#xea;" k="36" />
    <hkern u1="X" u2="&#xe9;" k="36" />
    <hkern u1="X" u2="&#xe8;" k="36" />
    <hkern u1="X" u2="&#xe7;" k="36" />
    <hkern u1="X" u2="&#xd8;" k="30" />
    <hkern u1="X" u2="&#xd6;" k="30" />
    <hkern u1="X" u2="&#xd5;" k="30" />
    <hkern u1="X" u2="&#xd4;" k="30" />
    <hkern u1="X" u2="&#xd3;" k="30" />
    <hkern u1="X" u2="&#xd2;" k="30" />
    <hkern u1="X" u2="&#xc7;" k="30" />
    <hkern u1="X" u2="&#xbb;" k="62" />
    <hkern u1="X" u2="&#xb7;" k="62" />
    <hkern u1="X" u2="&#xad;" k="62" />
    <hkern u1="X" u2="&#xab;" k="62" />
    <hkern u1="X" u2="y" k="66" />
    <hkern u1="X" u2="w" k="56" />
    <hkern u1="X" u2="v" k="66" />
    <hkern u1="X" u2="t" k="82" />
    <hkern u1="X" u2="q" k="36" />
    <hkern u1="X" u2="o" k="36" />
    <hkern u1="X" u2="f" k="52" />
    <hkern u1="X" u2="e" k="36" />
    <hkern u1="X" u2="d" k="36" />
    <hkern u1="X" u2="c" k="36" />
    <hkern u1="X" u2="Q" k="30" />
    <hkern u1="X" u2="O" k="30" />
    <hkern u1="X" u2="G" k="30" />
    <hkern u1="X" u2="C" k="30" />
    <hkern u1="X" u2="&#x40;" k="30" />
    <hkern u1="X" u2="&#x2d;" k="62" />
    <hkern u1="Y" u2="&#x2206;" k="164" />
    <hkern u1="Y" u2="&#x2122;" k="-30" />
    <hkern u1="Y" u2="&#x203a;" k="160" />
    <hkern u1="Y" u2="&#x2039;" k="160" />
    <hkern u1="Y" u2="&#x2022;" k="160" />
    <hkern u1="Y" u2="&#x201e;" k="152" />
    <hkern u1="Y" u2="&#x201d;" k="-30" />
    <hkern u1="Y" u2="&#x201c;" k="-30" />
    <hkern u1="Y" u2="&#x201a;" k="152" />
    <hkern u1="Y" u2="&#x2019;" k="-30" />
    <hkern u1="Y" u2="&#x2018;" k="-30" />
    <hkern u1="Y" u2="&#x2014;" k="160" />
    <hkern u1="Y" u2="&#x2013;" k="160" />
    <hkern u1="Y" u2="&#x153;" k="160" />
    <hkern u1="Y" u2="&#x152;" k="80" />
    <hkern u1="Y" u2="&#x144;" k="122" />
    <hkern u1="Y" u2="&#x119;" k="160" />
    <hkern u1="Y" u2="&#x107;" k="160" />
    <hkern u1="Y" u2="&#x106;" k="80" />
    <hkern u1="Y" u2="&#x105;" k="128" />
    <hkern u1="Y" u2="&#x104;" k="164" />
    <hkern u1="Y" u2="&#xfc;" k="122" />
    <hkern u1="Y" u2="&#xfb;" k="122" />
    <hkern u1="Y" u2="&#xfa;" k="122" />
    <hkern u1="Y" u2="&#xf9;" k="122" />
    <hkern u1="Y" u2="&#xf8;" k="160" />
    <hkern u1="Y" u2="&#xf6;" k="160" />
    <hkern u1="Y" u2="&#xf5;" k="160" />
    <hkern u1="Y" u2="&#xf4;" k="160" />
    <hkern u1="Y" u2="&#xf3;" k="160" />
    <hkern u1="Y" u2="&#xf2;" k="160" />
    <hkern u1="Y" u2="&#xf1;" k="122" />
    <hkern u1="Y" u2="&#xf0;" k="160" />
    <hkern u1="Y" u2="&#xeb;" k="160" />
    <hkern u1="Y" u2="&#xea;" k="160" />
    <hkern u1="Y" u2="&#xe9;" k="160" />
    <hkern u1="Y" u2="&#xe8;" k="160" />
    <hkern u1="Y" u2="&#xe7;" k="160" />
    <hkern u1="Y" u2="&#xe6;" k="128" />
    <hkern u1="Y" u2="&#xe5;" k="128" />
    <hkern u1="Y" u2="&#xe4;" k="128" />
    <hkern u1="Y" u2="&#xe3;" k="128" />
    <hkern u1="Y" u2="&#xe2;" k="128" />
    <hkern u1="Y" u2="&#xe1;" k="128" />
    <hkern u1="Y" u2="&#xe0;" k="128" />
    <hkern u1="Y" u2="&#xd8;" k="80" />
    <hkern u1="Y" u2="&#xd6;" k="80" />
    <hkern u1="Y" u2="&#xd5;" k="80" />
    <hkern u1="Y" u2="&#xd4;" k="80" />
    <hkern u1="Y" u2="&#xd3;" k="80" />
    <hkern u1="Y" u2="&#xd2;" k="80" />
    <hkern u1="Y" u2="&#xc7;" k="80" />
    <hkern u1="Y" u2="&#xc6;" k="164" />
    <hkern u1="Y" u2="&#xc5;" k="164" />
    <hkern u1="Y" u2="&#xc4;" k="164" />
    <hkern u1="Y" u2="&#xc3;" k="164" />
    <hkern u1="Y" u2="&#xc2;" k="164" />
    <hkern u1="Y" u2="&#xc1;" k="164" />
    <hkern u1="Y" u2="&#xc0;" k="164" />
    <hkern u1="Y" u2="&#xbb;" k="160" />
    <hkern u1="Y" u2="&#xba;" k="-30" />
    <hkern u1="Y" u2="&#xb9;" k="-50" />
    <hkern u1="Y" u2="&#xb7;" k="160" />
    <hkern u1="Y" u2="&#xb5;" k="122" />
    <hkern u1="Y" u2="&#xb3;" k="-50" />
    <hkern u1="Y" u2="&#xb2;" k="-50" />
    <hkern u1="Y" u2="&#xb0;" k="-30" />
    <hkern u1="Y" u2="&#xad;" k="160" />
    <hkern u1="Y" u2="&#xab;" k="160" />
    <hkern u1="Y" u2="&#xaa;" k="-30" />
    <hkern u1="Y" u2="y" k="100" />
    <hkern u1="Y" u2="x" k="132" />
    <hkern u1="Y" u2="w" k="92" />
    <hkern u1="Y" u2="v" k="100" />
    <hkern u1="Y" u2="u" k="122" />
    <hkern u1="Y" u2="s" k="128" />
    <hkern u1="Y" u2="r" k="122" />
    <hkern u1="Y" u2="q" k="160" />
    <hkern u1="Y" u2="p" k="122" />
    <hkern u1="Y" u2="o" k="160" />
    <hkern u1="Y" u2="n" k="122" />
    <hkern u1="Y" u2="m" k="122" />
    <hkern u1="Y" u2="g" k="172" />
    <hkern u1="Y" u2="e" k="160" />
    <hkern u1="Y" u2="d" k="160" />
    <hkern u1="Y" u2="c" k="160" />
    <hkern u1="Y" u2="a" k="128" />
    <hkern u1="Y" u2="Q" k="80" />
    <hkern u1="Y" u2="O" k="80" />
    <hkern u1="Y" u2="J" k="200" />
    <hkern u1="Y" u2="G" k="80" />
    <hkern u1="Y" u2="C" k="80" />
    <hkern u1="Y" u2="A" k="164" />
    <hkern u1="Y" u2="&#x40;" k="80" />
    <hkern u1="Y" u2="&#x3f;" k="-34" />
    <hkern u1="Y" u2="&#x3b;" k="122" />
    <hkern u1="Y" u2="&#x3a;" k="122" />
    <hkern u1="Y" u2="&#x2f;" k="164" />
    <hkern u1="Y" u2="&#x2e;" k="152" />
    <hkern u1="Y" u2="&#x2d;" k="160" />
    <hkern u1="Y" u2="&#x2c;" k="152" />
    <hkern u1="Y" u2="&#x2a;" k="-30" />
    <hkern u1="Y" u2="&#x27;" k="-30" />
    <hkern u1="Y" u2="&#x26;" k="164" />
    <hkern u1="Y" u2="&#x22;" k="-30" />
    <hkern u1="Z" u2="&#x203a;" k="70" />
    <hkern u1="Z" u2="&#x2039;" k="70" />
    <hkern u1="Z" u2="&#x2022;" k="70" />
    <hkern u1="Z" u2="&#x2014;" k="70" />
    <hkern u1="Z" u2="&#x2013;" k="70" />
    <hkern u1="Z" u2="&#x153;" k="38" />
    <hkern u1="Z" u2="&#x152;" k="58" />
    <hkern u1="Z" u2="&#x119;" k="38" />
    <hkern u1="Z" u2="&#x107;" k="38" />
    <hkern u1="Z" u2="&#x106;" k="58" />
    <hkern u1="Z" u2="&#xf8;" k="38" />
    <hkern u1="Z" u2="&#xf6;" k="38" />
    <hkern u1="Z" u2="&#xf5;" k="38" />
    <hkern u1="Z" u2="&#xf4;" k="38" />
    <hkern u1="Z" u2="&#xf3;" k="38" />
    <hkern u1="Z" u2="&#xf2;" k="38" />
    <hkern u1="Z" u2="&#xf0;" k="38" />
    <hkern u1="Z" u2="&#xeb;" k="38" />
    <hkern u1="Z" u2="&#xea;" k="38" />
    <hkern u1="Z" u2="&#xe9;" k="38" />
    <hkern u1="Z" u2="&#xe8;" k="38" />
    <hkern u1="Z" u2="&#xe7;" k="38" />
    <hkern u1="Z" u2="&#xd8;" k="58" />
    <hkern u1="Z" u2="&#xd6;" k="58" />
    <hkern u1="Z" u2="&#xd5;" k="58" />
    <hkern u1="Z" u2="&#xd4;" k="58" />
    <hkern u1="Z" u2="&#xd3;" k="58" />
    <hkern u1="Z" u2="&#xd2;" k="58" />
    <hkern u1="Z" u2="&#xc7;" k="58" />
    <hkern u1="Z" u2="&#xbb;" k="70" />
    <hkern u1="Z" u2="&#xb7;" k="70" />
    <hkern u1="Z" u2="&#xad;" k="70" />
    <hkern u1="Z" u2="&#xab;" k="70" />
    <hkern u1="Z" u2="y" k="40" />
    <hkern u1="Z" u2="v" k="40" />
    <hkern u1="Z" u2="s" k="28" />
    <hkern u1="Z" u2="q" k="38" />
    <hkern u1="Z" u2="o" k="38" />
    <hkern u1="Z" u2="e" k="38" />
    <hkern u1="Z" u2="d" k="38" />
    <hkern u1="Z" u2="c" k="38" />
    <hkern u1="Z" u2="Q" k="58" />
    <hkern u1="Z" u2="O" k="58" />
    <hkern u1="Z" u2="G" k="58" />
    <hkern u1="Z" u2="C" k="58" />
    <hkern u1="Z" u2="&#x40;" k="58" />
    <hkern u1="Z" u2="&#x3f;" k="-34" />
    <hkern u1="Z" u2="&#x2d;" k="70" />
    <hkern u1="[" u2="&#x153;" k="32" />
    <hkern u1="[" u2="&#x152;" k="40" />
    <hkern u1="[" u2="&#x119;" k="32" />
    <hkern u1="[" u2="&#x107;" k="32" />
    <hkern u1="[" u2="&#x106;" k="40" />
    <hkern u1="[" u2="&#xf8;" k="32" />
    <hkern u1="[" u2="&#xf6;" k="32" />
    <hkern u1="[" u2="&#xf5;" k="32" />
    <hkern u1="[" u2="&#xf4;" k="32" />
    <hkern u1="[" u2="&#xf3;" k="32" />
    <hkern u1="[" u2="&#xf2;" k="32" />
    <hkern u1="[" u2="&#xf0;" k="32" />
    <hkern u1="[" u2="&#xeb;" k="32" />
    <hkern u1="[" u2="&#xea;" k="32" />
    <hkern u1="[" u2="&#xe9;" k="32" />
    <hkern u1="[" u2="&#xe8;" k="32" />
    <hkern u1="[" u2="&#xe7;" k="32" />
    <hkern u1="[" u2="&#xd8;" k="40" />
    <hkern u1="[" u2="&#xd6;" k="40" />
    <hkern u1="[" u2="&#xd5;" k="40" />
    <hkern u1="[" u2="&#xd4;" k="40" />
    <hkern u1="[" u2="&#xd3;" k="40" />
    <hkern u1="[" u2="&#xd2;" k="40" />
    <hkern u1="[" u2="&#xc7;" k="40" />
    <hkern u1="[" u2="q" k="32" />
    <hkern u1="[" u2="o" k="32" />
    <hkern u1="[" u2="e" k="32" />
    <hkern u1="[" u2="d" k="32" />
    <hkern u1="[" u2="c" k="32" />
    <hkern u1="[" u2="Q" k="40" />
    <hkern u1="[" u2="O" k="40" />
    <hkern u1="[" u2="G" k="40" />
    <hkern u1="[" u2="C" k="40" />
    <hkern u1="[" u2="&#x40;" k="40" />
    <hkern u1="\" u2="&#x2122;" k="182" />
    <hkern u1="\" u2="&#x203a;" k="52" />
    <hkern u1="\" u2="&#x2039;" k="52" />
    <hkern u1="\" u2="&#x2022;" k="52" />
    <hkern u1="\" u2="&#x201d;" k="182" />
    <hkern u1="\" u2="&#x201c;" k="182" />
    <hkern u1="\" u2="&#x2019;" k="182" />
    <hkern u1="\" u2="&#x2018;" k="182" />
    <hkern u1="\" u2="&#x2014;" k="52" />
    <hkern u1="\" u2="&#x2013;" k="52" />
    <hkern u1="\" u2="&#x178;" k="164" />
    <hkern u1="\" u2="&#x152;" k="42" />
    <hkern u1="\" u2="&#x106;" k="42" />
    <hkern u1="\" u2="&#xdd;" k="164" />
    <hkern u1="\" u2="&#xdc;" k="56" />
    <hkern u1="\" u2="&#xdb;" k="56" />
    <hkern u1="\" u2="&#xda;" k="56" />
    <hkern u1="\" u2="&#xd9;" k="56" />
    <hkern u1="\" u2="&#xd8;" k="42" />
    <hkern u1="\" u2="&#xd6;" k="42" />
    <hkern u1="\" u2="&#xd5;" k="42" />
    <hkern u1="\" u2="&#xd4;" k="42" />
    <hkern u1="\" u2="&#xd3;" k="42" />
    <hkern u1="\" u2="&#xd2;" k="42" />
    <hkern u1="\" u2="&#xc7;" k="42" />
    <hkern u1="\" u2="&#xbb;" k="52" />
    <hkern u1="\" u2="&#xba;" k="182" />
    <hkern u1="\" u2="&#xb9;" k="184" />
    <hkern u1="\" u2="&#xb7;" k="52" />
    <hkern u1="\" u2="&#xb3;" k="184" />
    <hkern u1="\" u2="&#xb2;" k="184" />
    <hkern u1="\" u2="&#xb0;" k="182" />
    <hkern u1="\" u2="&#xad;" k="52" />
    <hkern u1="\" u2="&#xab;" k="52" />
    <hkern u1="\" u2="&#xaa;" k="182" />
    <hkern u1="\" u2="y" k="82" />
    <hkern u1="\" u2="v" k="82" />
    <hkern u1="\" u2="\" k="136" />
    <hkern u1="\" u2="Y" k="164" />
    <hkern u1="\" u2="W" k="84" />
    <hkern u1="\" u2="V" k="136" />
    <hkern u1="\" u2="U" k="56" />
    <hkern u1="\" u2="T" k="132" />
    <hkern u1="\" u2="Q" k="42" />
    <hkern u1="\" u2="O" k="42" />
    <hkern u1="\" u2="J" k="-50" />
    <hkern u1="\" u2="G" k="42" />
    <hkern u1="\" u2="C" k="42" />
    <hkern u1="\" u2="&#x40;" k="42" />
    <hkern u1="\" u2="&#x3f;" k="56" />
    <hkern u1="\" u2="&#x2d;" k="52" />
    <hkern u1="\" u2="&#x2a;" k="182" />
    <hkern u1="\" u2="&#x27;" k="182" />
    <hkern u1="\" u2="&#x22;" k="182" />
    <hkern u1="a" u2="&#x2122;" k="72" />
    <hkern u1="a" u2="&#x201d;" k="72" />
    <hkern u1="a" u2="&#x201c;" k="72" />
    <hkern u1="a" u2="&#x2019;" k="72" />
    <hkern u1="a" u2="&#x2018;" k="72" />
    <hkern u1="a" u2="&#xba;" k="72" />
    <hkern u1="a" u2="&#xb9;" k="72" />
    <hkern u1="a" u2="&#xb3;" k="72" />
    <hkern u1="a" u2="&#xb2;" k="72" />
    <hkern u1="a" u2="&#xb0;" k="72" />
    <hkern u1="a" u2="&#xaa;" k="72" />
    <hkern u1="a" u2="y" k="32" />
    <hkern u1="a" u2="w" k="16" />
    <hkern u1="a" u2="v" k="32" />
    <hkern u1="a" u2="&#x2a;" k="72" />
    <hkern u1="a" u2="&#x27;" k="72" />
    <hkern u1="a" u2="&#x22;" k="72" />
    <hkern u1="b" u2="&#x2122;" k="92" />
    <hkern u1="b" u2="&#x201d;" k="92" />
    <hkern u1="b" u2="&#x201c;" k="92" />
    <hkern u1="b" u2="&#x2019;" k="92" />
    <hkern u1="b" u2="&#x2018;" k="92" />
    <hkern u1="b" u2="&#xba;" k="92" />
    <hkern u1="b" u2="&#xb0;" k="92" />
    <hkern u1="b" u2="&#xaa;" k="92" />
    <hkern u1="b" u2="&#x7d;" k="32" />
    <hkern u1="b" u2="y" k="26" />
    <hkern u1="b" u2="x" k="60" />
    <hkern u1="b" u2="v" k="26" />
    <hkern u1="b" u2="]" k="32" />
    <hkern u1="b" u2="\" k="116" />
    <hkern u1="b" u2="W" k="32" />
    <hkern u1="b" u2="V" k="116" />
    <hkern u1="b" u2="&#x2a;" k="92" />
    <hkern u1="b" u2="&#x29;" k="32" />
    <hkern u1="b" u2="&#x27;" k="92" />
    <hkern u1="b" u2="&#x22;" k="92" />
    <hkern u1="e" u2="&#x2122;" k="92" />
    <hkern u1="e" u2="&#x201d;" k="92" />
    <hkern u1="e" u2="&#x201c;" k="92" />
    <hkern u1="e" u2="&#x2019;" k="92" />
    <hkern u1="e" u2="&#x2018;" k="92" />
    <hkern u1="e" u2="&#xba;" k="92" />
    <hkern u1="e" u2="&#xb0;" k="92" />
    <hkern u1="e" u2="&#xaa;" k="92" />
    <hkern u1="e" u2="&#x7d;" k="32" />
    <hkern u1="e" u2="y" k="26" />
    <hkern u1="e" u2="x" k="60" />
    <hkern u1="e" u2="v" k="26" />
    <hkern u1="e" u2="]" k="32" />
    <hkern u1="e" u2="\" k="116" />
    <hkern u1="e" u2="W" k="32" />
    <hkern u1="e" u2="V" k="116" />
    <hkern u1="e" u2="&#x2a;" k="92" />
    <hkern u1="e" u2="&#x29;" k="32" />
    <hkern u1="e" u2="&#x27;" k="92" />
    <hkern u1="e" u2="&#x22;" k="92" />
    <hkern u1="f" u2="&#x2122;" k="-68" />
    <hkern u1="f" u2="&#x201e;" k="130" />
    <hkern u1="f" u2="&#x201d;" k="-68" />
    <hkern u1="f" u2="&#x201c;" k="-68" />
    <hkern u1="f" u2="&#x201a;" k="130" />
    <hkern u1="f" u2="&#x2019;" k="-68" />
    <hkern u1="f" u2="&#x2018;" k="-68" />
    <hkern u1="f" u2="&#xba;" k="-68" />
    <hkern u1="f" u2="&#xb9;" k="-100" />
    <hkern u1="f" u2="&#xb3;" k="-100" />
    <hkern u1="f" u2="&#xb2;" k="-100" />
    <hkern u1="f" u2="&#xb0;" k="-68" />
    <hkern u1="f" u2="&#xaa;" k="-68" />
    <hkern u1="f" u2="&#x2e;" k="130" />
    <hkern u1="f" u2="&#x2c;" k="130" />
    <hkern u1="f" u2="&#x2a;" k="-68" />
    <hkern u1="f" u2="&#x27;" k="-68" />
    <hkern u1="f" u2="&#x22;" k="-68" />
    <hkern u1="h" u2="&#x2122;" k="72" />
    <hkern u1="h" u2="&#x201d;" k="72" />
    <hkern u1="h" u2="&#x201c;" k="72" />
    <hkern u1="h" u2="&#x2019;" k="72" />
    <hkern u1="h" u2="&#x2018;" k="72" />
    <hkern u1="h" u2="&#xba;" k="72" />
    <hkern u1="h" u2="&#xb9;" k="72" />
    <hkern u1="h" u2="&#xb3;" k="72" />
    <hkern u1="h" u2="&#xb2;" k="72" />
    <hkern u1="h" u2="&#xb0;" k="72" />
    <hkern u1="h" u2="&#xaa;" k="72" />
    <hkern u1="h" u2="y" k="32" />
    <hkern u1="h" u2="w" k="16" />
    <hkern u1="h" u2="v" k="32" />
    <hkern u1="h" u2="&#x2a;" k="72" />
    <hkern u1="h" u2="&#x27;" k="72" />
    <hkern u1="h" u2="&#x22;" k="72" />
    <hkern u1="k" u2="&#x153;" k="60" />
    <hkern u1="k" u2="&#x119;" k="60" />
    <hkern u1="k" u2="&#x107;" k="60" />
    <hkern u1="k" u2="&#xf8;" k="60" />
    <hkern u1="k" u2="&#xf6;" k="60" />
    <hkern u1="k" u2="&#xf5;" k="60" />
    <hkern u1="k" u2="&#xf4;" k="60" />
    <hkern u1="k" u2="&#xf3;" k="60" />
    <hkern u1="k" u2="&#xf2;" k="60" />
    <hkern u1="k" u2="&#xf0;" k="60" />
    <hkern u1="k" u2="&#xeb;" k="60" />
    <hkern u1="k" u2="&#xea;" k="60" />
    <hkern u1="k" u2="&#xe9;" k="60" />
    <hkern u1="k" u2="&#xe8;" k="60" />
    <hkern u1="k" u2="&#xe7;" k="60" />
    <hkern u1="k" u2="q" k="60" />
    <hkern u1="k" u2="o" k="60" />
    <hkern u1="k" u2="e" k="60" />
    <hkern u1="k" u2="d" k="60" />
    <hkern u1="k" u2="c" k="60" />
    <hkern u1="m" u2="&#x2122;" k="72" />
    <hkern u1="m" u2="&#x201d;" k="72" />
    <hkern u1="m" u2="&#x201c;" k="72" />
    <hkern u1="m" u2="&#x2019;" k="72" />
    <hkern u1="m" u2="&#x2018;" k="72" />
    <hkern u1="m" u2="&#xba;" k="72" />
    <hkern u1="m" u2="&#xb9;" k="72" />
    <hkern u1="m" u2="&#xb3;" k="72" />
    <hkern u1="m" u2="&#xb2;" k="72" />
    <hkern u1="m" u2="&#xb0;" k="72" />
    <hkern u1="m" u2="&#xaa;" k="72" />
    <hkern u1="m" u2="y" k="32" />
    <hkern u1="m" u2="w" k="16" />
    <hkern u1="m" u2="v" k="32" />
    <hkern u1="m" u2="&#x2a;" k="72" />
    <hkern u1="m" u2="&#x27;" k="72" />
    <hkern u1="m" u2="&#x22;" k="72" />
    <hkern u1="n" u2="&#x2122;" k="72" />
    <hkern u1="n" u2="&#x201d;" k="72" />
    <hkern u1="n" u2="&#x201c;" k="72" />
    <hkern u1="n" u2="&#x2019;" k="72" />
    <hkern u1="n" u2="&#x2018;" k="72" />
    <hkern u1="n" u2="&#xba;" k="72" />
    <hkern u1="n" u2="&#xb9;" k="72" />
    <hkern u1="n" u2="&#xb3;" k="72" />
    <hkern u1="n" u2="&#xb2;" k="72" />
    <hkern u1="n" u2="&#xb0;" k="72" />
    <hkern u1="n" u2="&#xaa;" k="72" />
    <hkern u1="n" u2="y" k="32" />
    <hkern u1="n" u2="w" k="16" />
    <hkern u1="n" u2="v" k="32" />
    <hkern u1="n" u2="&#x2a;" k="72" />
    <hkern u1="n" u2="&#x27;" k="72" />
    <hkern u1="n" u2="&#x22;" k="72" />
    <hkern u1="o" u2="&#x2122;" k="92" />
    <hkern u1="o" u2="&#x201d;" k="92" />
    <hkern u1="o" u2="&#x201c;" k="92" />
    <hkern u1="o" u2="&#x2019;" k="92" />
    <hkern u1="o" u2="&#x2018;" k="92" />
    <hkern u1="o" u2="&#xba;" k="92" />
    <hkern u1="o" u2="&#xb0;" k="92" />
    <hkern u1="o" u2="&#xaa;" k="92" />
    <hkern u1="o" u2="&#x7d;" k="32" />
    <hkern u1="o" u2="y" k="26" />
    <hkern u1="o" u2="x" k="60" />
    <hkern u1="o" u2="v" k="26" />
    <hkern u1="o" u2="]" k="32" />
    <hkern u1="o" u2="\" k="116" />
    <hkern u1="o" u2="W" k="32" />
    <hkern u1="o" u2="V" k="116" />
    <hkern u1="o" u2="&#x2a;" k="92" />
    <hkern u1="o" u2="&#x29;" k="32" />
    <hkern u1="o" u2="&#x27;" k="92" />
    <hkern u1="o" u2="&#x22;" k="92" />
    <hkern u1="p" u2="&#x2122;" k="92" />
    <hkern u1="p" u2="&#x201d;" k="92" />
    <hkern u1="p" u2="&#x201c;" k="92" />
    <hkern u1="p" u2="&#x2019;" k="92" />
    <hkern u1="p" u2="&#x2018;" k="92" />
    <hkern u1="p" u2="&#xba;" k="92" />
    <hkern u1="p" u2="&#xb0;" k="92" />
    <hkern u1="p" u2="&#xaa;" k="92" />
    <hkern u1="p" u2="&#x7d;" k="32" />
    <hkern u1="p" u2="y" k="26" />
    <hkern u1="p" u2="x" k="60" />
    <hkern u1="p" u2="v" k="26" />
    <hkern u1="p" u2="]" k="32" />
    <hkern u1="p" u2="\" k="116" />
    <hkern u1="p" u2="W" k="32" />
    <hkern u1="p" u2="V" k="116" />
    <hkern u1="p" u2="&#x2a;" k="92" />
    <hkern u1="p" u2="&#x29;" k="32" />
    <hkern u1="p" u2="&#x27;" k="92" />
    <hkern u1="p" u2="&#x22;" k="92" />
    <hkern u1="r" u2="&#x201e;" k="132" />
    <hkern u1="r" u2="&#x201a;" k="132" />
    <hkern u1="r" u2="&#x105;" k="38" />
    <hkern u1="r" u2="&#xe6;" k="38" />
    <hkern u1="r" u2="&#xe5;" k="38" />
    <hkern u1="r" u2="&#xe4;" k="38" />
    <hkern u1="r" u2="&#xe3;" k="38" />
    <hkern u1="r" u2="&#xe2;" k="38" />
    <hkern u1="r" u2="&#xe1;" k="38" />
    <hkern u1="r" u2="&#xe0;" k="38" />
    <hkern u1="r" u2="a" k="38" />
    <hkern u1="r" u2="&#x2e;" k="132" />
    <hkern u1="r" u2="&#x2c;" k="132" />
    <hkern u1="v" u2="&#x2206;" k="82" />
    <hkern u1="v" u2="&#x201e;" k="132" />
    <hkern u1="v" u2="&#x201a;" k="132" />
    <hkern u1="v" u2="&#x153;" k="26" />
    <hkern u1="v" u2="&#x119;" k="26" />
    <hkern u1="v" u2="&#x107;" k="26" />
    <hkern u1="v" u2="&#x104;" k="82" />
    <hkern u1="v" u2="&#xf8;" k="26" />
    <hkern u1="v" u2="&#xf6;" k="26" />
    <hkern u1="v" u2="&#xf5;" k="26" />
    <hkern u1="v" u2="&#xf4;" k="26" />
    <hkern u1="v" u2="&#xf3;" k="26" />
    <hkern u1="v" u2="&#xf2;" k="26" />
    <hkern u1="v" u2="&#xf0;" k="26" />
    <hkern u1="v" u2="&#xeb;" k="26" />
    <hkern u1="v" u2="&#xea;" k="26" />
    <hkern u1="v" u2="&#xe9;" k="26" />
    <hkern u1="v" u2="&#xe8;" k="26" />
    <hkern u1="v" u2="&#xe7;" k="26" />
    <hkern u1="v" u2="&#xc6;" k="82" />
    <hkern u1="v" u2="&#xc5;" k="82" />
    <hkern u1="v" u2="&#xc4;" k="82" />
    <hkern u1="v" u2="&#xc3;" k="82" />
    <hkern u1="v" u2="&#xc2;" k="82" />
    <hkern u1="v" u2="&#xc1;" k="82" />
    <hkern u1="v" u2="&#xc0;" k="82" />
    <hkern u1="v" u2="q" k="26" />
    <hkern u1="v" u2="o" k="26" />
    <hkern u1="v" u2="e" k="26" />
    <hkern u1="v" u2="d" k="26" />
    <hkern u1="v" u2="c" k="26" />
    <hkern u1="v" u2="A" k="82" />
    <hkern u1="v" u2="&#x2f;" k="82" />
    <hkern u1="v" u2="&#x2e;" k="132" />
    <hkern u1="v" u2="&#x2c;" k="132" />
    <hkern u1="v" u2="&#x26;" k="82" />
    <hkern u1="w" u2="&#x201e;" k="62" />
    <hkern u1="w" u2="&#x201a;" k="62" />
    <hkern u1="w" u2="&#x2e;" k="62" />
    <hkern u1="w" u2="&#x2c;" k="62" />
    <hkern u1="x" u2="&#x153;" k="60" />
    <hkern u1="x" u2="&#x119;" k="60" />
    <hkern u1="x" u2="&#x107;" k="60" />
    <hkern u1="x" u2="&#xf8;" k="60" />
    <hkern u1="x" u2="&#xf6;" k="60" />
    <hkern u1="x" u2="&#xf5;" k="60" />
    <hkern u1="x" u2="&#xf4;" k="60" />
    <hkern u1="x" u2="&#xf3;" k="60" />
    <hkern u1="x" u2="&#xf2;" k="60" />
    <hkern u1="x" u2="&#xf0;" k="60" />
    <hkern u1="x" u2="&#xeb;" k="60" />
    <hkern u1="x" u2="&#xea;" k="60" />
    <hkern u1="x" u2="&#xe9;" k="60" />
    <hkern u1="x" u2="&#xe8;" k="60" />
    <hkern u1="x" u2="&#xe7;" k="60" />
    <hkern u1="x" u2="q" k="60" />
    <hkern u1="x" u2="o" k="60" />
    <hkern u1="x" u2="e" k="60" />
    <hkern u1="x" u2="d" k="60" />
    <hkern u1="x" u2="c" k="60" />
    <hkern u1="y" u2="&#x2206;" k="82" />
    <hkern u1="y" u2="&#x201e;" k="132" />
    <hkern u1="y" u2="&#x201a;" k="132" />
    <hkern u1="y" u2="&#x153;" k="26" />
    <hkern u1="y" u2="&#x119;" k="26" />
    <hkern u1="y" u2="&#x107;" k="26" />
    <hkern u1="y" u2="&#x104;" k="82" />
    <hkern u1="y" u2="&#xf8;" k="26" />
    <hkern u1="y" u2="&#xf6;" k="26" />
    <hkern u1="y" u2="&#xf5;" k="26" />
    <hkern u1="y" u2="&#xf4;" k="26" />
    <hkern u1="y" u2="&#xf3;" k="26" />
    <hkern u1="y" u2="&#xf2;" k="26" />
    <hkern u1="y" u2="&#xf0;" k="26" />
    <hkern u1="y" u2="&#xeb;" k="26" />
    <hkern u1="y" u2="&#xea;" k="26" />
    <hkern u1="y" u2="&#xe9;" k="26" />
    <hkern u1="y" u2="&#xe8;" k="26" />
    <hkern u1="y" u2="&#xe7;" k="26" />
    <hkern u1="y" u2="&#xc6;" k="82" />
    <hkern u1="y" u2="&#xc5;" k="82" />
    <hkern u1="y" u2="&#xc4;" k="82" />
    <hkern u1="y" u2="&#xc3;" k="82" />
    <hkern u1="y" u2="&#xc2;" k="82" />
    <hkern u1="y" u2="&#xc1;" k="82" />
    <hkern u1="y" u2="&#xc0;" k="82" />
    <hkern u1="y" u2="q" k="26" />
    <hkern u1="y" u2="o" k="26" />
    <hkern u1="y" u2="e" k="26" />
    <hkern u1="y" u2="d" k="26" />
    <hkern u1="y" u2="c" k="26" />
    <hkern u1="y" u2="A" k="82" />
    <hkern u1="y" u2="&#x2f;" k="82" />
    <hkern u1="y" u2="&#x2e;" k="132" />
    <hkern u1="y" u2="&#x2c;" k="132" />
    <hkern u1="y" u2="&#x26;" k="82" />
    <hkern u1="&#x7b;" u2="&#x153;" k="32" />
    <hkern u1="&#x7b;" u2="&#x152;" k="40" />
    <hkern u1="&#x7b;" u2="&#x119;" k="32" />
    <hkern u1="&#x7b;" u2="&#x107;" k="32" />
    <hkern u1="&#x7b;" u2="&#x106;" k="40" />
    <hkern u1="&#x7b;" u2="&#xf8;" k="32" />
    <hkern u1="&#x7b;" u2="&#xf6;" k="32" />
    <hkern u1="&#x7b;" u2="&#xf5;" k="32" />
    <hkern u1="&#x7b;" u2="&#xf4;" k="32" />
    <hkern u1="&#x7b;" u2="&#xf3;" k="32" />
    <hkern u1="&#x7b;" u2="&#xf2;" k="32" />
    <hkern u1="&#x7b;" u2="&#xf0;" k="32" />
    <hkern u1="&#x7b;" u2="&#xeb;" k="32" />
    <hkern u1="&#x7b;" u2="&#xea;" k="32" />
    <hkern u1="&#x7b;" u2="&#xe9;" k="32" />
    <hkern u1="&#x7b;" u2="&#xe8;" k="32" />
    <hkern u1="&#x7b;" u2="&#xe7;" k="32" />
    <hkern u1="&#x7b;" u2="&#xd8;" k="40" />
    <hkern u1="&#x7b;" u2="&#xd6;" k="40" />
    <hkern u1="&#x7b;" u2="&#xd5;" k="40" />
    <hkern u1="&#x7b;" u2="&#xd4;" k="40" />
    <hkern u1="&#x7b;" u2="&#xd3;" k="40" />
    <hkern u1="&#x7b;" u2="&#xd2;" k="40" />
    <hkern u1="&#x7b;" u2="&#xc7;" k="40" />
    <hkern u1="&#x7b;" u2="q" k="32" />
    <hkern u1="&#x7b;" u2="o" k="32" />
    <hkern u1="&#x7b;" u2="e" k="32" />
    <hkern u1="&#x7b;" u2="d" k="32" />
    <hkern u1="&#x7b;" u2="c" k="32" />
    <hkern u1="&#x7b;" u2="Q" k="40" />
    <hkern u1="&#x7b;" u2="O" k="40" />
    <hkern u1="&#x7b;" u2="G" k="40" />
    <hkern u1="&#x7b;" u2="C" k="40" />
    <hkern u1="&#x7b;" u2="&#x40;" k="40" />
    <hkern u1="&#xaa;" u2="&#x2206;" k="182" />
    <hkern u1="&#xaa;" u2="&#x203a;" k="178" />
    <hkern u1="&#xaa;" u2="&#x2039;" k="178" />
    <hkern u1="&#xaa;" u2="&#x2022;" k="178" />
    <hkern u1="&#xaa;" u2="&#x201e;" k="228" />
    <hkern u1="&#xaa;" u2="&#x201a;" k="228" />
    <hkern u1="&#xaa;" u2="&#x2014;" k="178" />
    <hkern u1="&#xaa;" u2="&#x2013;" k="178" />
    <hkern u1="&#xaa;" u2="&#x178;" k="-30" />
    <hkern u1="&#xaa;" u2="&#x153;" k="92" />
    <hkern u1="&#xaa;" u2="&#x152;" k="46" />
    <hkern u1="&#xaa;" u2="&#x119;" k="92" />
    <hkern u1="&#xaa;" u2="&#x107;" k="92" />
    <hkern u1="&#xaa;" u2="&#x106;" k="46" />
    <hkern u1="&#xaa;" u2="&#x105;" k="64" />
    <hkern u1="&#xaa;" u2="&#x104;" k="182" />
    <hkern u1="&#xaa;" u2="&#xf8;" k="92" />
    <hkern u1="&#xaa;" u2="&#xf6;" k="92" />
    <hkern u1="&#xaa;" u2="&#xf5;" k="92" />
    <hkern u1="&#xaa;" u2="&#xf4;" k="92" />
    <hkern u1="&#xaa;" u2="&#xf3;" k="92" />
    <hkern u1="&#xaa;" u2="&#xf2;" k="92" />
    <hkern u1="&#xaa;" u2="&#xf0;" k="92" />
    <hkern u1="&#xaa;" u2="&#xeb;" k="92" />
    <hkern u1="&#xaa;" u2="&#xea;" k="92" />
    <hkern u1="&#xaa;" u2="&#xe9;" k="92" />
    <hkern u1="&#xaa;" u2="&#xe8;" k="92" />
    <hkern u1="&#xaa;" u2="&#xe7;" k="92" />
    <hkern u1="&#xaa;" u2="&#xe6;" k="64" />
    <hkern u1="&#xaa;" u2="&#xe5;" k="64" />
    <hkern u1="&#xaa;" u2="&#xe4;" k="64" />
    <hkern u1="&#xaa;" u2="&#xe3;" k="64" />
    <hkern u1="&#xaa;" u2="&#xe2;" k="64" />
    <hkern u1="&#xaa;" u2="&#xe1;" k="64" />
    <hkern u1="&#xaa;" u2="&#xe0;" k="64" />
    <hkern u1="&#xaa;" u2="&#xdd;" k="-30" />
    <hkern u1="&#xaa;" u2="&#xd8;" k="46" />
    <hkern u1="&#xaa;" u2="&#xd6;" k="46" />
    <hkern u1="&#xaa;" u2="&#xd5;" k="46" />
    <hkern u1="&#xaa;" u2="&#xd4;" k="46" />
    <hkern u1="&#xaa;" u2="&#xd3;" k="46" />
    <hkern u1="&#xaa;" u2="&#xd2;" k="46" />
    <hkern u1="&#xaa;" u2="&#xc7;" k="46" />
    <hkern u1="&#xaa;" u2="&#xc6;" k="182" />
    <hkern u1="&#xaa;" u2="&#xc5;" k="182" />
    <hkern u1="&#xaa;" u2="&#xc4;" k="182" />
    <hkern u1="&#xaa;" u2="&#xc3;" k="182" />
    <hkern u1="&#xaa;" u2="&#xc2;" k="182" />
    <hkern u1="&#xaa;" u2="&#xc1;" k="182" />
    <hkern u1="&#xaa;" u2="&#xc0;" k="182" />
    <hkern u1="&#xaa;" u2="&#xbb;" k="178" />
    <hkern u1="&#xaa;" u2="&#xb7;" k="178" />
    <hkern u1="&#xaa;" u2="&#xad;" k="178" />
    <hkern u1="&#xaa;" u2="&#xab;" k="178" />
    <hkern u1="&#xaa;" u2="q" k="92" />
    <hkern u1="&#xaa;" u2="o" k="92" />
    <hkern u1="&#xaa;" u2="e" k="92" />
    <hkern u1="&#xaa;" u2="d" k="92" />
    <hkern u1="&#xaa;" u2="c" k="92" />
    <hkern u1="&#xaa;" u2="a" k="64" />
    <hkern u1="&#xaa;" u2="\" k="-48" />
    <hkern u1="&#xaa;" u2="Y" k="-30" />
    <hkern u1="&#xaa;" u2="W" k="-48" />
    <hkern u1="&#xaa;" u2="V" k="-48" />
    <hkern u1="&#xaa;" u2="Q" k="46" />
    <hkern u1="&#xaa;" u2="O" k="46" />
    <hkern u1="&#xaa;" u2="G" k="46" />
    <hkern u1="&#xaa;" u2="C" k="46" />
    <hkern u1="&#xaa;" u2="A" k="182" />
    <hkern u1="&#xaa;" u2="&#x40;" k="46" />
    <hkern u1="&#xaa;" u2="&#x2f;" k="182" />
    <hkern u1="&#xaa;" u2="&#x2e;" k="228" />
    <hkern u1="&#xaa;" u2="&#x2d;" k="178" />
    <hkern u1="&#xaa;" u2="&#x2c;" k="228" />
    <hkern u1="&#xaa;" u2="&#x26;" k="182" />
    <hkern u1="&#xab;" u2="&#x2206;" k="52" />
    <hkern u1="&#xab;" u2="&#x2122;" k="178" />
    <hkern u1="&#xab;" u2="&#x201e;" k="136" />
    <hkern u1="&#xab;" u2="&#x201d;" k="178" />
    <hkern u1="&#xab;" u2="&#x201c;" k="178" />
    <hkern u1="&#xab;" u2="&#x201a;" k="136" />
    <hkern u1="&#xab;" u2="&#x2019;" k="178" />
    <hkern u1="&#xab;" u2="&#x2018;" k="178" />
    <hkern u1="&#xab;" u2="&#x17d;" k="46" />
    <hkern u1="&#xab;" u2="&#x17b;" k="46" />
    <hkern u1="&#xab;" u2="&#x179;" k="46" />
    <hkern u1="&#xab;" u2="&#x178;" k="160" />
    <hkern u1="&#xab;" u2="&#x104;" k="52" />
    <hkern u1="&#xab;" u2="&#xdd;" k="160" />
    <hkern u1="&#xab;" u2="&#xc6;" k="52" />
    <hkern u1="&#xab;" u2="&#xc5;" k="52" />
    <hkern u1="&#xab;" u2="&#xc4;" k="52" />
    <hkern u1="&#xab;" u2="&#xc3;" k="52" />
    <hkern u1="&#xab;" u2="&#xc2;" k="52" />
    <hkern u1="&#xab;" u2="&#xc1;" k="52" />
    <hkern u1="&#xab;" u2="&#xc0;" k="52" />
    <hkern u1="&#xab;" u2="&#xba;" k="178" />
    <hkern u1="&#xab;" u2="&#xb0;" k="178" />
    <hkern u1="&#xab;" u2="&#xaa;" k="178" />
    <hkern u1="&#xab;" u2="\" k="112" />
    <hkern u1="&#xab;" u2="Z" k="46" />
    <hkern u1="&#xab;" u2="Y" k="160" />
    <hkern u1="&#xab;" u2="X" k="62" />
    <hkern u1="&#xab;" u2="W" k="32" />
    <hkern u1="&#xab;" u2="V" k="112" />
    <hkern u1="&#xab;" u2="T" k="180" />
    <hkern u1="&#xab;" u2="A" k="52" />
    <hkern u1="&#xab;" u2="&#x2f;" k="52" />
    <hkern u1="&#xab;" u2="&#x2e;" k="136" />
    <hkern u1="&#xab;" u2="&#x2c;" k="136" />
    <hkern u1="&#xab;" u2="&#x2a;" k="178" />
    <hkern u1="&#xab;" u2="&#x27;" k="178" />
    <hkern u1="&#xab;" u2="&#x26;" k="52" />
    <hkern u1="&#xab;" u2="&#x22;" k="178" />
    <hkern u1="&#xad;" u2="&#x2206;" k="52" />
    <hkern u1="&#xad;" u2="&#x2122;" k="178" />
    <hkern u1="&#xad;" u2="&#x201e;" k="136" />
    <hkern u1="&#xad;" u2="&#x201d;" k="178" />
    <hkern u1="&#xad;" u2="&#x201c;" k="178" />
    <hkern u1="&#xad;" u2="&#x201a;" k="136" />
    <hkern u1="&#xad;" u2="&#x2019;" k="178" />
    <hkern u1="&#xad;" u2="&#x2018;" k="178" />
    <hkern u1="&#xad;" u2="&#x17d;" k="46" />
    <hkern u1="&#xad;" u2="&#x17b;" k="46" />
    <hkern u1="&#xad;" u2="&#x179;" k="46" />
    <hkern u1="&#xad;" u2="&#x178;" k="160" />
    <hkern u1="&#xad;" u2="&#x104;" k="52" />
    <hkern u1="&#xad;" u2="&#xdd;" k="160" />
    <hkern u1="&#xad;" u2="&#xc6;" k="52" />
    <hkern u1="&#xad;" u2="&#xc5;" k="52" />
    <hkern u1="&#xad;" u2="&#xc4;" k="52" />
    <hkern u1="&#xad;" u2="&#xc3;" k="52" />
    <hkern u1="&#xad;" u2="&#xc2;" k="52" />
    <hkern u1="&#xad;" u2="&#xc1;" k="52" />
    <hkern u1="&#xad;" u2="&#xc0;" k="52" />
    <hkern u1="&#xad;" u2="&#xba;" k="178" />
    <hkern u1="&#xad;" u2="&#xb0;" k="178" />
    <hkern u1="&#xad;" u2="&#xaa;" k="178" />
    <hkern u1="&#xad;" u2="\" k="112" />
    <hkern u1="&#xad;" u2="Z" k="46" />
    <hkern u1="&#xad;" u2="Y" k="160" />
    <hkern u1="&#xad;" u2="X" k="62" />
    <hkern u1="&#xad;" u2="W" k="32" />
    <hkern u1="&#xad;" u2="V" k="112" />
    <hkern u1="&#xad;" u2="T" k="180" />
    <hkern u1="&#xad;" u2="A" k="52" />
    <hkern u1="&#xad;" u2="&#x2f;" k="52" />
    <hkern u1="&#xad;" u2="&#x2e;" k="136" />
    <hkern u1="&#xad;" u2="&#x2c;" k="136" />
    <hkern u1="&#xad;" u2="&#x2a;" k="178" />
    <hkern u1="&#xad;" u2="&#x27;" k="178" />
    <hkern u1="&#xad;" u2="&#x26;" k="52" />
    <hkern u1="&#xad;" u2="&#x22;" k="178" />
    <hkern u1="&#xae;" u2="&#x2206;" k="42" />
    <hkern u1="&#xae;" u2="&#x2122;" k="46" />
    <hkern u1="&#xae;" u2="&#x201e;" k="56" />
    <hkern u1="&#xae;" u2="&#x201d;" k="46" />
    <hkern u1="&#xae;" u2="&#x201c;" k="46" />
    <hkern u1="&#xae;" u2="&#x201a;" k="56" />
    <hkern u1="&#xae;" u2="&#x2019;" k="46" />
    <hkern u1="&#xae;" u2="&#x2018;" k="46" />
    <hkern u1="&#xae;" u2="&#x17d;" k="70" />
    <hkern u1="&#xae;" u2="&#x17b;" k="70" />
    <hkern u1="&#xae;" u2="&#x179;" k="70" />
    <hkern u1="&#xae;" u2="&#x178;" k="80" />
    <hkern u1="&#xae;" u2="&#x104;" k="42" />
    <hkern u1="&#xae;" u2="&#xdd;" k="80" />
    <hkern u1="&#xae;" u2="&#xc6;" k="42" />
    <hkern u1="&#xae;" u2="&#xc5;" k="42" />
    <hkern u1="&#xae;" u2="&#xc4;" k="42" />
    <hkern u1="&#xae;" u2="&#xc3;" k="42" />
    <hkern u1="&#xae;" u2="&#xc2;" k="42" />
    <hkern u1="&#xae;" u2="&#xc1;" k="42" />
    <hkern u1="&#xae;" u2="&#xc0;" k="42" />
    <hkern u1="&#xae;" u2="&#xba;" k="46" />
    <hkern u1="&#xae;" u2="&#xb0;" k="46" />
    <hkern u1="&#xae;" u2="&#xaa;" k="46" />
    <hkern u1="&#xae;" u2="&#x7d;" k="40" />
    <hkern u1="&#xae;" u2="]" k="40" />
    <hkern u1="&#xae;" u2="\" k="52" />
    <hkern u1="&#xae;" u2="Z" k="70" />
    <hkern u1="&#xae;" u2="Y" k="80" />
    <hkern u1="&#xae;" u2="X" k="30" />
    <hkern u1="&#xae;" u2="V" k="52" />
    <hkern u1="&#xae;" u2="T" k="98" />
    <hkern u1="&#xae;" u2="A" k="42" />
    <hkern u1="&#xae;" u2="&#x2f;" k="42" />
    <hkern u1="&#xae;" u2="&#x2e;" k="56" />
    <hkern u1="&#xae;" u2="&#x2c;" k="56" />
    <hkern u1="&#xae;" u2="&#x2a;" k="46" />
    <hkern u1="&#xae;" u2="&#x29;" k="40" />
    <hkern u1="&#xae;" u2="&#x27;" k="46" />
    <hkern u1="&#xae;" u2="&#x26;" k="42" />
    <hkern u1="&#xae;" u2="&#x22;" k="46" />
    <hkern u1="&#xb0;" u2="&#x2206;" k="182" />
    <hkern u1="&#xb0;" u2="&#x203a;" k="178" />
    <hkern u1="&#xb0;" u2="&#x2039;" k="178" />
    <hkern u1="&#xb0;" u2="&#x2022;" k="178" />
    <hkern u1="&#xb0;" u2="&#x201e;" k="228" />
    <hkern u1="&#xb0;" u2="&#x201a;" k="228" />
    <hkern u1="&#xb0;" u2="&#x2014;" k="178" />
    <hkern u1="&#xb0;" u2="&#x2013;" k="178" />
    <hkern u1="&#xb0;" u2="&#x178;" k="-30" />
    <hkern u1="&#xb0;" u2="&#x153;" k="92" />
    <hkern u1="&#xb0;" u2="&#x152;" k="46" />
    <hkern u1="&#xb0;" u2="&#x119;" k="92" />
    <hkern u1="&#xb0;" u2="&#x107;" k="92" />
    <hkern u1="&#xb0;" u2="&#x106;" k="46" />
    <hkern u1="&#xb0;" u2="&#x105;" k="64" />
    <hkern u1="&#xb0;" u2="&#x104;" k="182" />
    <hkern u1="&#xb0;" u2="&#xf8;" k="92" />
    <hkern u1="&#xb0;" u2="&#xf6;" k="92" />
    <hkern u1="&#xb0;" u2="&#xf5;" k="92" />
    <hkern u1="&#xb0;" u2="&#xf4;" k="92" />
    <hkern u1="&#xb0;" u2="&#xf3;" k="92" />
    <hkern u1="&#xb0;" u2="&#xf2;" k="92" />
    <hkern u1="&#xb0;" u2="&#xf0;" k="92" />
    <hkern u1="&#xb0;" u2="&#xeb;" k="92" />
    <hkern u1="&#xb0;" u2="&#xea;" k="92" />
    <hkern u1="&#xb0;" u2="&#xe9;" k="92" />
    <hkern u1="&#xb0;" u2="&#xe8;" k="92" />
    <hkern u1="&#xb0;" u2="&#xe7;" k="92" />
    <hkern u1="&#xb0;" u2="&#xe6;" k="64" />
    <hkern u1="&#xb0;" u2="&#xe5;" k="64" />
    <hkern u1="&#xb0;" u2="&#xe4;" k="64" />
    <hkern u1="&#xb0;" u2="&#xe3;" k="64" />
    <hkern u1="&#xb0;" u2="&#xe2;" k="64" />
    <hkern u1="&#xb0;" u2="&#xe1;" k="64" />
    <hkern u1="&#xb0;" u2="&#xe0;" k="64" />
    <hkern u1="&#xb0;" u2="&#xdd;" k="-30" />
    <hkern u1="&#xb0;" u2="&#xd8;" k="46" />
    <hkern u1="&#xb0;" u2="&#xd6;" k="46" />
    <hkern u1="&#xb0;" u2="&#xd5;" k="46" />
    <hkern u1="&#xb0;" u2="&#xd4;" k="46" />
    <hkern u1="&#xb0;" u2="&#xd3;" k="46" />
    <hkern u1="&#xb0;" u2="&#xd2;" k="46" />
    <hkern u1="&#xb0;" u2="&#xc7;" k="46" />
    <hkern u1="&#xb0;" u2="&#xc6;" k="182" />
    <hkern u1="&#xb0;" u2="&#xc5;" k="182" />
    <hkern u1="&#xb0;" u2="&#xc4;" k="182" />
    <hkern u1="&#xb0;" u2="&#xc3;" k="182" />
    <hkern u1="&#xb0;" u2="&#xc2;" k="182" />
    <hkern u1="&#xb0;" u2="&#xc1;" k="182" />
    <hkern u1="&#xb0;" u2="&#xc0;" k="182" />
    <hkern u1="&#xb0;" u2="&#xbb;" k="178" />
    <hkern u1="&#xb0;" u2="&#xb7;" k="178" />
    <hkern u1="&#xb0;" u2="&#xad;" k="178" />
    <hkern u1="&#xb0;" u2="&#xab;" k="178" />
    <hkern u1="&#xb0;" u2="q" k="92" />
    <hkern u1="&#xb0;" u2="o" k="92" />
    <hkern u1="&#xb0;" u2="e" k="92" />
    <hkern u1="&#xb0;" u2="d" k="92" />
    <hkern u1="&#xb0;" u2="c" k="92" />
    <hkern u1="&#xb0;" u2="a" k="64" />
    <hkern u1="&#xb0;" u2="\" k="-48" />
    <hkern u1="&#xb0;" u2="Y" k="-30" />
    <hkern u1="&#xb0;" u2="W" k="-48" />
    <hkern u1="&#xb0;" u2="V" k="-48" />
    <hkern u1="&#xb0;" u2="Q" k="46" />
    <hkern u1="&#xb0;" u2="O" k="46" />
    <hkern u1="&#xb0;" u2="G" k="46" />
    <hkern u1="&#xb0;" u2="C" k="46" />
    <hkern u1="&#xb0;" u2="A" k="182" />
    <hkern u1="&#xb0;" u2="&#x40;" k="46" />
    <hkern u1="&#xb0;" u2="&#x2f;" k="182" />
    <hkern u1="&#xb0;" u2="&#x2e;" k="228" />
    <hkern u1="&#xb0;" u2="&#x2d;" k="178" />
    <hkern u1="&#xb0;" u2="&#x2c;" k="228" />
    <hkern u1="&#xb0;" u2="&#x26;" k="182" />
    <hkern u1="&#xb2;" u2="&#x2206;" k="184" />
    <hkern u1="&#xb2;" u2="&#x178;" k="-40" />
    <hkern u1="&#xb2;" u2="&#x104;" k="184" />
    <hkern u1="&#xb2;" u2="&#xdd;" k="-40" />
    <hkern u1="&#xb2;" u2="&#xc6;" k="184" />
    <hkern u1="&#xb2;" u2="&#xc5;" k="184" />
    <hkern u1="&#xb2;" u2="&#xc4;" k="184" />
    <hkern u1="&#xb2;" u2="&#xc3;" k="184" />
    <hkern u1="&#xb2;" u2="&#xc2;" k="184" />
    <hkern u1="&#xb2;" u2="&#xc1;" k="184" />
    <hkern u1="&#xb2;" u2="&#xc0;" k="184" />
    <hkern u1="&#xb2;" u2="\" k="-58" />
    <hkern u1="&#xb2;" u2="Y" k="-40" />
    <hkern u1="&#xb2;" u2="W" k="-58" />
    <hkern u1="&#xb2;" u2="V" k="-58" />
    <hkern u1="&#xb2;" u2="A" k="184" />
    <hkern u1="&#xb2;" u2="&#x2f;" k="184" />
    <hkern u1="&#xb2;" u2="&#x26;" k="184" />
    <hkern u1="&#xb3;" u2="&#x2206;" k="184" />
    <hkern u1="&#xb3;" u2="&#x178;" k="-40" />
    <hkern u1="&#xb3;" u2="&#x104;" k="184" />
    <hkern u1="&#xb3;" u2="&#xdd;" k="-40" />
    <hkern u1="&#xb3;" u2="&#xc6;" k="184" />
    <hkern u1="&#xb3;" u2="&#xc5;" k="184" />
    <hkern u1="&#xb3;" u2="&#xc4;" k="184" />
    <hkern u1="&#xb3;" u2="&#xc3;" k="184" />
    <hkern u1="&#xb3;" u2="&#xc2;" k="184" />
    <hkern u1="&#xb3;" u2="&#xc1;" k="184" />
    <hkern u1="&#xb3;" u2="&#xc0;" k="184" />
    <hkern u1="&#xb3;" u2="\" k="-58" />
    <hkern u1="&#xb3;" u2="Y" k="-40" />
    <hkern u1="&#xb3;" u2="W" k="-58" />
    <hkern u1="&#xb3;" u2="V" k="-58" />
    <hkern u1="&#xb3;" u2="A" k="184" />
    <hkern u1="&#xb3;" u2="&#x2f;" k="184" />
    <hkern u1="&#xb3;" u2="&#x26;" k="184" />
    <hkern u1="&#xb7;" u2="&#x2206;" k="52" />
    <hkern u1="&#xb7;" u2="&#x2122;" k="178" />
    <hkern u1="&#xb7;" u2="&#x201e;" k="136" />
    <hkern u1="&#xb7;" u2="&#x201d;" k="178" />
    <hkern u1="&#xb7;" u2="&#x201c;" k="178" />
    <hkern u1="&#xb7;" u2="&#x201a;" k="136" />
    <hkern u1="&#xb7;" u2="&#x2019;" k="178" />
    <hkern u1="&#xb7;" u2="&#x2018;" k="178" />
    <hkern u1="&#xb7;" u2="&#x17d;" k="46" />
    <hkern u1="&#xb7;" u2="&#x17b;" k="46" />
    <hkern u1="&#xb7;" u2="&#x179;" k="46" />
    <hkern u1="&#xb7;" u2="&#x178;" k="160" />
    <hkern u1="&#xb7;" u2="&#x104;" k="52" />
    <hkern u1="&#xb7;" u2="&#xdd;" k="160" />
    <hkern u1="&#xb7;" u2="&#xc6;" k="52" />
    <hkern u1="&#xb7;" u2="&#xc5;" k="52" />
    <hkern u1="&#xb7;" u2="&#xc4;" k="52" />
    <hkern u1="&#xb7;" u2="&#xc3;" k="52" />
    <hkern u1="&#xb7;" u2="&#xc2;" k="52" />
    <hkern u1="&#xb7;" u2="&#xc1;" k="52" />
    <hkern u1="&#xb7;" u2="&#xc0;" k="52" />
    <hkern u1="&#xb7;" u2="&#xba;" k="178" />
    <hkern u1="&#xb7;" u2="&#xb0;" k="178" />
    <hkern u1="&#xb7;" u2="&#xaa;" k="178" />
    <hkern u1="&#xb7;" u2="\" k="112" />
    <hkern u1="&#xb7;" u2="Z" k="46" />
    <hkern u1="&#xb7;" u2="Y" k="160" />
    <hkern u1="&#xb7;" u2="X" k="62" />
    <hkern u1="&#xb7;" u2="W" k="32" />
    <hkern u1="&#xb7;" u2="V" k="112" />
    <hkern u1="&#xb7;" u2="T" k="180" />
    <hkern u1="&#xb7;" u2="A" k="52" />
    <hkern u1="&#xb7;" u2="&#x2f;" k="52" />
    <hkern u1="&#xb7;" u2="&#x2e;" k="136" />
    <hkern u1="&#xb7;" u2="&#x2c;" k="136" />
    <hkern u1="&#xb7;" u2="&#x2a;" k="178" />
    <hkern u1="&#xb7;" u2="&#x27;" k="178" />
    <hkern u1="&#xb7;" u2="&#x26;" k="52" />
    <hkern u1="&#xb7;" u2="&#x22;" k="178" />
    <hkern u1="&#xb9;" u2="&#x2206;" k="184" />
    <hkern u1="&#xb9;" u2="&#x178;" k="-40" />
    <hkern u1="&#xb9;" u2="&#x104;" k="184" />
    <hkern u1="&#xb9;" u2="&#xdd;" k="-40" />
    <hkern u1="&#xb9;" u2="&#xc6;" k="184" />
    <hkern u1="&#xb9;" u2="&#xc5;" k="184" />
    <hkern u1="&#xb9;" u2="&#xc4;" k="184" />
    <hkern u1="&#xb9;" u2="&#xc3;" k="184" />
    <hkern u1="&#xb9;" u2="&#xc2;" k="184" />
    <hkern u1="&#xb9;" u2="&#xc1;" k="184" />
    <hkern u1="&#xb9;" u2="&#xc0;" k="184" />
    <hkern u1="&#xb9;" u2="\" k="-58" />
    <hkern u1="&#xb9;" u2="Y" k="-40" />
    <hkern u1="&#xb9;" u2="W" k="-58" />
    <hkern u1="&#xb9;" u2="V" k="-58" />
    <hkern u1="&#xb9;" u2="A" k="184" />
    <hkern u1="&#xb9;" u2="&#x2f;" k="184" />
    <hkern u1="&#xb9;" u2="&#x26;" k="184" />
    <hkern u1="&#xba;" u2="&#x2206;" k="182" />
    <hkern u1="&#xba;" u2="&#x203a;" k="178" />
    <hkern u1="&#xba;" u2="&#x2039;" k="178" />
    <hkern u1="&#xba;" u2="&#x2022;" k="178" />
    <hkern u1="&#xba;" u2="&#x201e;" k="228" />
    <hkern u1="&#xba;" u2="&#x201a;" k="228" />
    <hkern u1="&#xba;" u2="&#x2014;" k="178" />
    <hkern u1="&#xba;" u2="&#x2013;" k="178" />
    <hkern u1="&#xba;" u2="&#x178;" k="-30" />
    <hkern u1="&#xba;" u2="&#x153;" k="92" />
    <hkern u1="&#xba;" u2="&#x152;" k="46" />
    <hkern u1="&#xba;" u2="&#x119;" k="92" />
    <hkern u1="&#xba;" u2="&#x107;" k="92" />
    <hkern u1="&#xba;" u2="&#x106;" k="46" />
    <hkern u1="&#xba;" u2="&#x105;" k="64" />
    <hkern u1="&#xba;" u2="&#x104;" k="182" />
    <hkern u1="&#xba;" u2="&#xf8;" k="92" />
    <hkern u1="&#xba;" u2="&#xf6;" k="92" />
    <hkern u1="&#xba;" u2="&#xf5;" k="92" />
    <hkern u1="&#xba;" u2="&#xf4;" k="92" />
    <hkern u1="&#xba;" u2="&#xf3;" k="92" />
    <hkern u1="&#xba;" u2="&#xf2;" k="92" />
    <hkern u1="&#xba;" u2="&#xf0;" k="92" />
    <hkern u1="&#xba;" u2="&#xeb;" k="92" />
    <hkern u1="&#xba;" u2="&#xea;" k="92" />
    <hkern u1="&#xba;" u2="&#xe9;" k="92" />
    <hkern u1="&#xba;" u2="&#xe8;" k="92" />
    <hkern u1="&#xba;" u2="&#xe7;" k="92" />
    <hkern u1="&#xba;" u2="&#xe6;" k="64" />
    <hkern u1="&#xba;" u2="&#xe5;" k="64" />
    <hkern u1="&#xba;" u2="&#xe4;" k="64" />
    <hkern u1="&#xba;" u2="&#xe3;" k="64" />
    <hkern u1="&#xba;" u2="&#xe2;" k="64" />
    <hkern u1="&#xba;" u2="&#xe1;" k="64" />
    <hkern u1="&#xba;" u2="&#xe0;" k="64" />
    <hkern u1="&#xba;" u2="&#xdd;" k="-30" />
    <hkern u1="&#xba;" u2="&#xd8;" k="46" />
    <hkern u1="&#xba;" u2="&#xd6;" k="46" />
    <hkern u1="&#xba;" u2="&#xd5;" k="46" />
    <hkern u1="&#xba;" u2="&#xd4;" k="46" />
    <hkern u1="&#xba;" u2="&#xd3;" k="46" />
    <hkern u1="&#xba;" u2="&#xd2;" k="46" />
    <hkern u1="&#xba;" u2="&#xc7;" k="46" />
    <hkern u1="&#xba;" u2="&#xc6;" k="182" />
    <hkern u1="&#xba;" u2="&#xc5;" k="182" />
    <hkern u1="&#xba;" u2="&#xc4;" k="182" />
    <hkern u1="&#xba;" u2="&#xc3;" k="182" />
    <hkern u1="&#xba;" u2="&#xc2;" k="182" />
    <hkern u1="&#xba;" u2="&#xc1;" k="182" />
    <hkern u1="&#xba;" u2="&#xc0;" k="182" />
    <hkern u1="&#xba;" u2="&#xbb;" k="178" />
    <hkern u1="&#xba;" u2="&#xb7;" k="178" />
    <hkern u1="&#xba;" u2="&#xad;" k="178" />
    <hkern u1="&#xba;" u2="&#xab;" k="178" />
    <hkern u1="&#xba;" u2="q" k="92" />
    <hkern u1="&#xba;" u2="o" k="92" />
    <hkern u1="&#xba;" u2="e" k="92" />
    <hkern u1="&#xba;" u2="d" k="92" />
    <hkern u1="&#xba;" u2="c" k="92" />
    <hkern u1="&#xba;" u2="a" k="64" />
    <hkern u1="&#xba;" u2="\" k="-48" />
    <hkern u1="&#xba;" u2="Y" k="-30" />
    <hkern u1="&#xba;" u2="W" k="-48" />
    <hkern u1="&#xba;" u2="V" k="-48" />
    <hkern u1="&#xba;" u2="Q" k="46" />
    <hkern u1="&#xba;" u2="O" k="46" />
    <hkern u1="&#xba;" u2="G" k="46" />
    <hkern u1="&#xba;" u2="C" k="46" />
    <hkern u1="&#xba;" u2="A" k="182" />
    <hkern u1="&#xba;" u2="&#x40;" k="46" />
    <hkern u1="&#xba;" u2="&#x2f;" k="182" />
    <hkern u1="&#xba;" u2="&#x2e;" k="228" />
    <hkern u1="&#xba;" u2="&#x2d;" k="178" />
    <hkern u1="&#xba;" u2="&#x2c;" k="228" />
    <hkern u1="&#xba;" u2="&#x26;" k="182" />
    <hkern u1="&#xbb;" u2="&#x2206;" k="52" />
    <hkern u1="&#xbb;" u2="&#x2122;" k="178" />
    <hkern u1="&#xbb;" u2="&#x201e;" k="136" />
    <hkern u1="&#xbb;" u2="&#x201d;" k="178" />
    <hkern u1="&#xbb;" u2="&#x201c;" k="178" />
    <hkern u1="&#xbb;" u2="&#x201a;" k="136" />
    <hkern u1="&#xbb;" u2="&#x2019;" k="178" />
    <hkern u1="&#xbb;" u2="&#x2018;" k="178" />
    <hkern u1="&#xbb;" u2="&#x17d;" k="46" />
    <hkern u1="&#xbb;" u2="&#x17b;" k="46" />
    <hkern u1="&#xbb;" u2="&#x179;" k="46" />
    <hkern u1="&#xbb;" u2="&#x178;" k="160" />
    <hkern u1="&#xbb;" u2="&#x104;" k="52" />
    <hkern u1="&#xbb;" u2="&#xdd;" k="160" />
    <hkern u1="&#xbb;" u2="&#xc6;" k="52" />
    <hkern u1="&#xbb;" u2="&#xc5;" k="52" />
    <hkern u1="&#xbb;" u2="&#xc4;" k="52" />
    <hkern u1="&#xbb;" u2="&#xc3;" k="52" />
    <hkern u1="&#xbb;" u2="&#xc2;" k="52" />
    <hkern u1="&#xbb;" u2="&#xc1;" k="52" />
    <hkern u1="&#xbb;" u2="&#xc0;" k="52" />
    <hkern u1="&#xbb;" u2="&#xba;" k="178" />
    <hkern u1="&#xbb;" u2="&#xb0;" k="178" />
    <hkern u1="&#xbb;" u2="&#xaa;" k="178" />
    <hkern u1="&#xbb;" u2="\" k="112" />
    <hkern u1="&#xbb;" u2="Z" k="46" />
    <hkern u1="&#xbb;" u2="Y" k="160" />
    <hkern u1="&#xbb;" u2="X" k="62" />
    <hkern u1="&#xbb;" u2="W" k="32" />
    <hkern u1="&#xbb;" u2="V" k="112" />
    <hkern u1="&#xbb;" u2="T" k="180" />
    <hkern u1="&#xbb;" u2="A" k="52" />
    <hkern u1="&#xbb;" u2="&#x2f;" k="52" />
    <hkern u1="&#xbb;" u2="&#x2e;" k="136" />
    <hkern u1="&#xbb;" u2="&#x2c;" k="136" />
    <hkern u1="&#xbb;" u2="&#x2a;" k="178" />
    <hkern u1="&#xbb;" u2="&#x27;" k="178" />
    <hkern u1="&#xbb;" u2="&#x26;" k="52" />
    <hkern u1="&#xbb;" u2="&#x22;" k="178" />
    <hkern u1="&#xc0;" u2="&#x2122;" k="182" />
    <hkern u1="&#xc0;" u2="&#x203a;" k="52" />
    <hkern u1="&#xc0;" u2="&#x2039;" k="52" />
    <hkern u1="&#xc0;" u2="&#x2022;" k="52" />
    <hkern u1="&#xc0;" u2="&#x201d;" k="182" />
    <hkern u1="&#xc0;" u2="&#x201c;" k="182" />
    <hkern u1="&#xc0;" u2="&#x2019;" k="182" />
    <hkern u1="&#xc0;" u2="&#x2018;" k="182" />
    <hkern u1="&#xc0;" u2="&#x2014;" k="52" />
    <hkern u1="&#xc0;" u2="&#x2013;" k="52" />
    <hkern u1="&#xc0;" u2="&#x178;" k="164" />
    <hkern u1="&#xc0;" u2="&#x152;" k="42" />
    <hkern u1="&#xc0;" u2="&#x106;" k="42" />
    <hkern u1="&#xc0;" u2="&#xdd;" k="164" />
    <hkern u1="&#xc0;" u2="&#xdc;" k="56" />
    <hkern u1="&#xc0;" u2="&#xdb;" k="56" />
    <hkern u1="&#xc0;" u2="&#xda;" k="56" />
    <hkern u1="&#xc0;" u2="&#xd9;" k="56" />
    <hkern u1="&#xc0;" u2="&#xd8;" k="42" />
    <hkern u1="&#xc0;" u2="&#xd6;" k="42" />
    <hkern u1="&#xc0;" u2="&#xd5;" k="42" />
    <hkern u1="&#xc0;" u2="&#xd4;" k="42" />
    <hkern u1="&#xc0;" u2="&#xd3;" k="42" />
    <hkern u1="&#xc0;" u2="&#xd2;" k="42" />
    <hkern u1="&#xc0;" u2="&#xc7;" k="42" />
    <hkern u1="&#xc0;" u2="&#xbb;" k="52" />
    <hkern u1="&#xc0;" u2="&#xba;" k="182" />
    <hkern u1="&#xc0;" u2="&#xb9;" k="184" />
    <hkern u1="&#xc0;" u2="&#xb7;" k="52" />
    <hkern u1="&#xc0;" u2="&#xb3;" k="184" />
    <hkern u1="&#xc0;" u2="&#xb2;" k="184" />
    <hkern u1="&#xc0;" u2="&#xb0;" k="182" />
    <hkern u1="&#xc0;" u2="&#xad;" k="52" />
    <hkern u1="&#xc0;" u2="&#xab;" k="52" />
    <hkern u1="&#xc0;" u2="&#xaa;" k="182" />
    <hkern u1="&#xc0;" u2="y" k="82" />
    <hkern u1="&#xc0;" u2="v" k="82" />
    <hkern u1="&#xc0;" u2="\" k="136" />
    <hkern u1="&#xc0;" u2="Y" k="164" />
    <hkern u1="&#xc0;" u2="W" k="84" />
    <hkern u1="&#xc0;" u2="V" k="136" />
    <hkern u1="&#xc0;" u2="U" k="56" />
    <hkern u1="&#xc0;" u2="T" k="132" />
    <hkern u1="&#xc0;" u2="Q" k="42" />
    <hkern u1="&#xc0;" u2="O" k="42" />
    <hkern u1="&#xc0;" u2="J" k="-50" />
    <hkern u1="&#xc0;" u2="G" k="42" />
    <hkern u1="&#xc0;" u2="C" k="42" />
    <hkern u1="&#xc0;" u2="&#x40;" k="42" />
    <hkern u1="&#xc0;" u2="&#x3f;" k="56" />
    <hkern u1="&#xc0;" u2="&#x2d;" k="52" />
    <hkern u1="&#xc0;" u2="&#x2a;" k="182" />
    <hkern u1="&#xc0;" u2="&#x27;" k="182" />
    <hkern u1="&#xc0;" u2="&#x22;" k="182" />
    <hkern u1="&#xc1;" u2="&#x2122;" k="182" />
    <hkern u1="&#xc1;" u2="&#x203a;" k="52" />
    <hkern u1="&#xc1;" u2="&#x2039;" k="52" />
    <hkern u1="&#xc1;" u2="&#x2022;" k="52" />
    <hkern u1="&#xc1;" u2="&#x201d;" k="182" />
    <hkern u1="&#xc1;" u2="&#x201c;" k="182" />
    <hkern u1="&#xc1;" u2="&#x2019;" k="182" />
    <hkern u1="&#xc1;" u2="&#x2018;" k="182" />
    <hkern u1="&#xc1;" u2="&#x2014;" k="52" />
    <hkern u1="&#xc1;" u2="&#x2013;" k="52" />
    <hkern u1="&#xc1;" u2="&#x178;" k="164" />
    <hkern u1="&#xc1;" u2="&#x152;" k="42" />
    <hkern u1="&#xc1;" u2="&#x106;" k="42" />
    <hkern u1="&#xc1;" u2="&#xdd;" k="164" />
    <hkern u1="&#xc1;" u2="&#xdc;" k="56" />
    <hkern u1="&#xc1;" u2="&#xdb;" k="56" />
    <hkern u1="&#xc1;" u2="&#xda;" k="56" />
    <hkern u1="&#xc1;" u2="&#xd9;" k="56" />
    <hkern u1="&#xc1;" u2="&#xd8;" k="42" />
    <hkern u1="&#xc1;" u2="&#xd6;" k="42" />
    <hkern u1="&#xc1;" u2="&#xd5;" k="42" />
    <hkern u1="&#xc1;" u2="&#xd4;" k="42" />
    <hkern u1="&#xc1;" u2="&#xd3;" k="42" />
    <hkern u1="&#xc1;" u2="&#xd2;" k="42" />
    <hkern u1="&#xc1;" u2="&#xc7;" k="42" />
    <hkern u1="&#xc1;" u2="&#xbb;" k="52" />
    <hkern u1="&#xc1;" u2="&#xba;" k="182" />
    <hkern u1="&#xc1;" u2="&#xb9;" k="184" />
    <hkern u1="&#xc1;" u2="&#xb7;" k="52" />
    <hkern u1="&#xc1;" u2="&#xb3;" k="184" />
    <hkern u1="&#xc1;" u2="&#xb2;" k="184" />
    <hkern u1="&#xc1;" u2="&#xb0;" k="182" />
    <hkern u1="&#xc1;" u2="&#xad;" k="52" />
    <hkern u1="&#xc1;" u2="&#xab;" k="52" />
    <hkern u1="&#xc1;" u2="&#xaa;" k="182" />
    <hkern u1="&#xc1;" u2="y" k="82" />
    <hkern u1="&#xc1;" u2="v" k="82" />
    <hkern u1="&#xc1;" u2="\" k="136" />
    <hkern u1="&#xc1;" u2="Y" k="164" />
    <hkern u1="&#xc1;" u2="W" k="84" />
    <hkern u1="&#xc1;" u2="V" k="136" />
    <hkern u1="&#xc1;" u2="U" k="56" />
    <hkern u1="&#xc1;" u2="T" k="132" />
    <hkern u1="&#xc1;" u2="Q" k="42" />
    <hkern u1="&#xc1;" u2="O" k="42" />
    <hkern u1="&#xc1;" u2="J" k="-50" />
    <hkern u1="&#xc1;" u2="G" k="42" />
    <hkern u1="&#xc1;" u2="C" k="42" />
    <hkern u1="&#xc1;" u2="&#x40;" k="42" />
    <hkern u1="&#xc1;" u2="&#x3f;" k="56" />
    <hkern u1="&#xc1;" u2="&#x2d;" k="52" />
    <hkern u1="&#xc1;" u2="&#x2a;" k="182" />
    <hkern u1="&#xc1;" u2="&#x27;" k="182" />
    <hkern u1="&#xc1;" u2="&#x22;" k="182" />
    <hkern u1="&#xc2;" u2="&#x2122;" k="182" />
    <hkern u1="&#xc2;" u2="&#x203a;" k="52" />
    <hkern u1="&#xc2;" u2="&#x2039;" k="52" />
    <hkern u1="&#xc2;" u2="&#x2022;" k="52" />
    <hkern u1="&#xc2;" u2="&#x201d;" k="182" />
    <hkern u1="&#xc2;" u2="&#x201c;" k="182" />
    <hkern u1="&#xc2;" u2="&#x2019;" k="182" />
    <hkern u1="&#xc2;" u2="&#x2018;" k="182" />
    <hkern u1="&#xc2;" u2="&#x2014;" k="52" />
    <hkern u1="&#xc2;" u2="&#x2013;" k="52" />
    <hkern u1="&#xc2;" u2="&#x178;" k="164" />
    <hkern u1="&#xc2;" u2="&#x152;" k="42" />
    <hkern u1="&#xc2;" u2="&#x106;" k="42" />
    <hkern u1="&#xc2;" u2="&#xdd;" k="164" />
    <hkern u1="&#xc2;" u2="&#xdc;" k="56" />
    <hkern u1="&#xc2;" u2="&#xdb;" k="56" />
    <hkern u1="&#xc2;" u2="&#xda;" k="56" />
    <hkern u1="&#xc2;" u2="&#xd9;" k="56" />
    <hkern u1="&#xc2;" u2="&#xd8;" k="42" />
    <hkern u1="&#xc2;" u2="&#xd6;" k="42" />
    <hkern u1="&#xc2;" u2="&#xd5;" k="42" />
    <hkern u1="&#xc2;" u2="&#xd4;" k="42" />
    <hkern u1="&#xc2;" u2="&#xd3;" k="42" />
    <hkern u1="&#xc2;" u2="&#xd2;" k="42" />
    <hkern u1="&#xc2;" u2="&#xc7;" k="42" />
    <hkern u1="&#xc2;" u2="&#xbb;" k="52" />
    <hkern u1="&#xc2;" u2="&#xba;" k="182" />
    <hkern u1="&#xc2;" u2="&#xb9;" k="184" />
    <hkern u1="&#xc2;" u2="&#xb7;" k="52" />
    <hkern u1="&#xc2;" u2="&#xb3;" k="184" />
    <hkern u1="&#xc2;" u2="&#xb2;" k="184" />
    <hkern u1="&#xc2;" u2="&#xb0;" k="182" />
    <hkern u1="&#xc2;" u2="&#xad;" k="52" />
    <hkern u1="&#xc2;" u2="&#xab;" k="52" />
    <hkern u1="&#xc2;" u2="&#xaa;" k="182" />
    <hkern u1="&#xc2;" u2="y" k="82" />
    <hkern u1="&#xc2;" u2="v" k="82" />
    <hkern u1="&#xc2;" u2="\" k="136" />
    <hkern u1="&#xc2;" u2="Y" k="164" />
    <hkern u1="&#xc2;" u2="W" k="84" />
    <hkern u1="&#xc2;" u2="V" k="136" />
    <hkern u1="&#xc2;" u2="U" k="56" />
    <hkern u1="&#xc2;" u2="T" k="132" />
    <hkern u1="&#xc2;" u2="Q" k="42" />
    <hkern u1="&#xc2;" u2="O" k="42" />
    <hkern u1="&#xc2;" u2="J" k="-50" />
    <hkern u1="&#xc2;" u2="G" k="42" />
    <hkern u1="&#xc2;" u2="C" k="42" />
    <hkern u1="&#xc2;" u2="&#x40;" k="42" />
    <hkern u1="&#xc2;" u2="&#x3f;" k="56" />
    <hkern u1="&#xc2;" u2="&#x2d;" k="52" />
    <hkern u1="&#xc2;" u2="&#x2a;" k="182" />
    <hkern u1="&#xc2;" u2="&#x27;" k="182" />
    <hkern u1="&#xc2;" u2="&#x22;" k="182" />
    <hkern u1="&#xc3;" u2="&#x2122;" k="182" />
    <hkern u1="&#xc3;" u2="&#x203a;" k="52" />
    <hkern u1="&#xc3;" u2="&#x2039;" k="52" />
    <hkern u1="&#xc3;" u2="&#x2022;" k="52" />
    <hkern u1="&#xc3;" u2="&#x201d;" k="182" />
    <hkern u1="&#xc3;" u2="&#x201c;" k="182" />
    <hkern u1="&#xc3;" u2="&#x2019;" k="182" />
    <hkern u1="&#xc3;" u2="&#x2018;" k="182" />
    <hkern u1="&#xc3;" u2="&#x2014;" k="52" />
    <hkern u1="&#xc3;" u2="&#x2013;" k="52" />
    <hkern u1="&#xc3;" u2="&#x178;" k="164" />
    <hkern u1="&#xc3;" u2="&#x152;" k="42" />
    <hkern u1="&#xc3;" u2="&#x106;" k="42" />
    <hkern u1="&#xc3;" u2="&#xdd;" k="164" />
    <hkern u1="&#xc3;" u2="&#xdc;" k="56" />
    <hkern u1="&#xc3;" u2="&#xdb;" k="56" />
    <hkern u1="&#xc3;" u2="&#xda;" k="56" />
    <hkern u1="&#xc3;" u2="&#xd9;" k="56" />
    <hkern u1="&#xc3;" u2="&#xd8;" k="42" />
    <hkern u1="&#xc3;" u2="&#xd6;" k="42" />
    <hkern u1="&#xc3;" u2="&#xd5;" k="42" />
    <hkern u1="&#xc3;" u2="&#xd4;" k="42" />
    <hkern u1="&#xc3;" u2="&#xd3;" k="42" />
    <hkern u1="&#xc3;" u2="&#xd2;" k="42" />
    <hkern u1="&#xc3;" u2="&#xc7;" k="42" />
    <hkern u1="&#xc3;" u2="&#xbb;" k="52" />
    <hkern u1="&#xc3;" u2="&#xba;" k="182" />
    <hkern u1="&#xc3;" u2="&#xb9;" k="184" />
    <hkern u1="&#xc3;" u2="&#xb7;" k="52" />
    <hkern u1="&#xc3;" u2="&#xb3;" k="184" />
    <hkern u1="&#xc3;" u2="&#xb2;" k="184" />
    <hkern u1="&#xc3;" u2="&#xb0;" k="182" />
    <hkern u1="&#xc3;" u2="&#xad;" k="52" />
    <hkern u1="&#xc3;" u2="&#xab;" k="52" />
    <hkern u1="&#xc3;" u2="&#xaa;" k="182" />
    <hkern u1="&#xc3;" u2="y" k="82" />
    <hkern u1="&#xc3;" u2="v" k="82" />
    <hkern u1="&#xc3;" u2="\" k="136" />
    <hkern u1="&#xc3;" u2="Y" k="164" />
    <hkern u1="&#xc3;" u2="W" k="84" />
    <hkern u1="&#xc3;" u2="V" k="136" />
    <hkern u1="&#xc3;" u2="U" k="56" />
    <hkern u1="&#xc3;" u2="T" k="132" />
    <hkern u1="&#xc3;" u2="Q" k="42" />
    <hkern u1="&#xc3;" u2="O" k="42" />
    <hkern u1="&#xc3;" u2="J" k="-50" />
    <hkern u1="&#xc3;" u2="G" k="42" />
    <hkern u1="&#xc3;" u2="C" k="42" />
    <hkern u1="&#xc3;" u2="&#x40;" k="42" />
    <hkern u1="&#xc3;" u2="&#x3f;" k="56" />
    <hkern u1="&#xc3;" u2="&#x2d;" k="52" />
    <hkern u1="&#xc3;" u2="&#x2a;" k="182" />
    <hkern u1="&#xc3;" u2="&#x27;" k="182" />
    <hkern u1="&#xc3;" u2="&#x22;" k="182" />
    <hkern u1="&#xc4;" u2="&#x2122;" k="182" />
    <hkern u1="&#xc4;" u2="&#x203a;" k="52" />
    <hkern u1="&#xc4;" u2="&#x2039;" k="52" />
    <hkern u1="&#xc4;" u2="&#x2022;" k="52" />
    <hkern u1="&#xc4;" u2="&#x201d;" k="182" />
    <hkern u1="&#xc4;" u2="&#x201c;" k="182" />
    <hkern u1="&#xc4;" u2="&#x2019;" k="182" />
    <hkern u1="&#xc4;" u2="&#x2018;" k="182" />
    <hkern u1="&#xc4;" u2="&#x2014;" k="52" />
    <hkern u1="&#xc4;" u2="&#x2013;" k="52" />
    <hkern u1="&#xc4;" u2="&#x178;" k="164" />
    <hkern u1="&#xc4;" u2="&#x152;" k="42" />
    <hkern u1="&#xc4;" u2="&#x106;" k="42" />
    <hkern u1="&#xc4;" u2="&#xdd;" k="164" />
    <hkern u1="&#xc4;" u2="&#xdc;" k="56" />
    <hkern u1="&#xc4;" u2="&#xdb;" k="56" />
    <hkern u1="&#xc4;" u2="&#xda;" k="56" />
    <hkern u1="&#xc4;" u2="&#xd9;" k="56" />
    <hkern u1="&#xc4;" u2="&#xd8;" k="42" />
    <hkern u1="&#xc4;" u2="&#xd6;" k="42" />
    <hkern u1="&#xc4;" u2="&#xd5;" k="42" />
    <hkern u1="&#xc4;" u2="&#xd4;" k="42" />
    <hkern u1="&#xc4;" u2="&#xd3;" k="42" />
    <hkern u1="&#xc4;" u2="&#xd2;" k="42" />
    <hkern u1="&#xc4;" u2="&#xc7;" k="42" />
    <hkern u1="&#xc4;" u2="&#xbb;" k="52" />
    <hkern u1="&#xc4;" u2="&#xba;" k="182" />
    <hkern u1="&#xc4;" u2="&#xb9;" k="184" />
    <hkern u1="&#xc4;" u2="&#xb7;" k="52" />
    <hkern u1="&#xc4;" u2="&#xb3;" k="184" />
    <hkern u1="&#xc4;" u2="&#xb2;" k="184" />
    <hkern u1="&#xc4;" u2="&#xb0;" k="182" />
    <hkern u1="&#xc4;" u2="&#xad;" k="52" />
    <hkern u1="&#xc4;" u2="&#xab;" k="52" />
    <hkern u1="&#xc4;" u2="&#xaa;" k="182" />
    <hkern u1="&#xc4;" u2="y" k="82" />
    <hkern u1="&#xc4;" u2="v" k="82" />
    <hkern u1="&#xc4;" u2="\" k="136" />
    <hkern u1="&#xc4;" u2="Y" k="164" />
    <hkern u1="&#xc4;" u2="W" k="84" />
    <hkern u1="&#xc4;" u2="V" k="136" />
    <hkern u1="&#xc4;" u2="U" k="56" />
    <hkern u1="&#xc4;" u2="T" k="132" />
    <hkern u1="&#xc4;" u2="Q" k="42" />
    <hkern u1="&#xc4;" u2="O" k="42" />
    <hkern u1="&#xc4;" u2="J" k="-50" />
    <hkern u1="&#xc4;" u2="G" k="42" />
    <hkern u1="&#xc4;" u2="C" k="42" />
    <hkern u1="&#xc4;" u2="&#x40;" k="42" />
    <hkern u1="&#xc4;" u2="&#x3f;" k="56" />
    <hkern u1="&#xc4;" u2="&#x2d;" k="52" />
    <hkern u1="&#xc4;" u2="&#x2a;" k="182" />
    <hkern u1="&#xc4;" u2="&#x27;" k="182" />
    <hkern u1="&#xc4;" u2="&#x22;" k="182" />
    <hkern u1="&#xc5;" u2="&#x2122;" k="182" />
    <hkern u1="&#xc5;" u2="&#x203a;" k="52" />
    <hkern u1="&#xc5;" u2="&#x2039;" k="52" />
    <hkern u1="&#xc5;" u2="&#x2022;" k="52" />
    <hkern u1="&#xc5;" u2="&#x201d;" k="182" />
    <hkern u1="&#xc5;" u2="&#x201c;" k="182" />
    <hkern u1="&#xc5;" u2="&#x2019;" k="182" />
    <hkern u1="&#xc5;" u2="&#x2018;" k="182" />
    <hkern u1="&#xc5;" u2="&#x2014;" k="52" />
    <hkern u1="&#xc5;" u2="&#x2013;" k="52" />
    <hkern u1="&#xc5;" u2="&#x178;" k="164" />
    <hkern u1="&#xc5;" u2="&#x152;" k="42" />
    <hkern u1="&#xc5;" u2="&#x106;" k="42" />
    <hkern u1="&#xc5;" u2="&#xdd;" k="164" />
    <hkern u1="&#xc5;" u2="&#xdc;" k="56" />
    <hkern u1="&#xc5;" u2="&#xdb;" k="56" />
    <hkern u1="&#xc5;" u2="&#xda;" k="56" />
    <hkern u1="&#xc5;" u2="&#xd9;" k="56" />
    <hkern u1="&#xc5;" u2="&#xd8;" k="42" />
    <hkern u1="&#xc5;" u2="&#xd6;" k="42" />
    <hkern u1="&#xc5;" u2="&#xd5;" k="42" />
    <hkern u1="&#xc5;" u2="&#xd4;" k="42" />
    <hkern u1="&#xc5;" u2="&#xd3;" k="42" />
    <hkern u1="&#xc5;" u2="&#xd2;" k="42" />
    <hkern u1="&#xc5;" u2="&#xc7;" k="42" />
    <hkern u1="&#xc5;" u2="&#xbb;" k="52" />
    <hkern u1="&#xc5;" u2="&#xba;" k="182" />
    <hkern u1="&#xc5;" u2="&#xb9;" k="184" />
    <hkern u1="&#xc5;" u2="&#xb7;" k="52" />
    <hkern u1="&#xc5;" u2="&#xb3;" k="184" />
    <hkern u1="&#xc5;" u2="&#xb2;" k="184" />
    <hkern u1="&#xc5;" u2="&#xb0;" k="182" />
    <hkern u1="&#xc5;" u2="&#xad;" k="52" />
    <hkern u1="&#xc5;" u2="&#xab;" k="52" />
    <hkern u1="&#xc5;" u2="&#xaa;" k="182" />
    <hkern u1="&#xc5;" u2="y" k="82" />
    <hkern u1="&#xc5;" u2="v" k="82" />
    <hkern u1="&#xc5;" u2="\" k="136" />
    <hkern u1="&#xc5;" u2="Y" k="164" />
    <hkern u1="&#xc5;" u2="W" k="84" />
    <hkern u1="&#xc5;" u2="V" k="136" />
    <hkern u1="&#xc5;" u2="U" k="56" />
    <hkern u1="&#xc5;" u2="T" k="132" />
    <hkern u1="&#xc5;" u2="Q" k="42" />
    <hkern u1="&#xc5;" u2="O" k="42" />
    <hkern u1="&#xc5;" u2="J" k="-50" />
    <hkern u1="&#xc5;" u2="G" k="42" />
    <hkern u1="&#xc5;" u2="C" k="42" />
    <hkern u1="&#xc5;" u2="&#x40;" k="42" />
    <hkern u1="&#xc5;" u2="&#x3f;" k="56" />
    <hkern u1="&#xc5;" u2="&#x2d;" k="52" />
    <hkern u1="&#xc5;" u2="&#x2a;" k="182" />
    <hkern u1="&#xc5;" u2="&#x27;" k="182" />
    <hkern u1="&#xc5;" u2="&#x22;" k="182" />
    <hkern u1="&#xc7;" u2="&#x203a;" k="150" />
    <hkern u1="&#xc7;" u2="&#x2039;" k="150" />
    <hkern u1="&#xc7;" u2="&#x2022;" k="150" />
    <hkern u1="&#xc7;" u2="&#x2014;" k="150" />
    <hkern u1="&#xc7;" u2="&#x2013;" k="150" />
    <hkern u1="&#xc7;" u2="&#xbb;" k="150" />
    <hkern u1="&#xc7;" u2="&#xb7;" k="150" />
    <hkern u1="&#xc7;" u2="&#xad;" k="150" />
    <hkern u1="&#xc7;" u2="&#xab;" k="150" />
    <hkern u1="&#xc7;" u2="&#x2d;" k="150" />
    <hkern u1="&#xd0;" u2="&#x2206;" k="42" />
    <hkern u1="&#xd0;" u2="&#x2122;" k="46" />
    <hkern u1="&#xd0;" u2="&#x201e;" k="56" />
    <hkern u1="&#xd0;" u2="&#x201d;" k="46" />
    <hkern u1="&#xd0;" u2="&#x201c;" k="46" />
    <hkern u1="&#xd0;" u2="&#x201a;" k="56" />
    <hkern u1="&#xd0;" u2="&#x2019;" k="46" />
    <hkern u1="&#xd0;" u2="&#x2018;" k="46" />
    <hkern u1="&#xd0;" u2="&#x17d;" k="70" />
    <hkern u1="&#xd0;" u2="&#x17b;" k="70" />
    <hkern u1="&#xd0;" u2="&#x179;" k="70" />
    <hkern u1="&#xd0;" u2="&#x178;" k="80" />
    <hkern u1="&#xd0;" u2="&#x104;" k="42" />
    <hkern u1="&#xd0;" u2="&#xdd;" k="80" />
    <hkern u1="&#xd0;" u2="&#xc6;" k="42" />
    <hkern u1="&#xd0;" u2="&#xc5;" k="42" />
    <hkern u1="&#xd0;" u2="&#xc4;" k="42" />
    <hkern u1="&#xd0;" u2="&#xc3;" k="42" />
    <hkern u1="&#xd0;" u2="&#xc2;" k="42" />
    <hkern u1="&#xd0;" u2="&#xc1;" k="42" />
    <hkern u1="&#xd0;" u2="&#xc0;" k="42" />
    <hkern u1="&#xd0;" u2="&#xba;" k="46" />
    <hkern u1="&#xd0;" u2="&#xb0;" k="46" />
    <hkern u1="&#xd0;" u2="&#xaa;" k="46" />
    <hkern u1="&#xd0;" u2="&#x7d;" k="40" />
    <hkern u1="&#xd0;" u2="]" k="40" />
    <hkern u1="&#xd0;" u2="\" k="52" />
    <hkern u1="&#xd0;" u2="Z" k="70" />
    <hkern u1="&#xd0;" u2="Y" k="80" />
    <hkern u1="&#xd0;" u2="X" k="30" />
    <hkern u1="&#xd0;" u2="V" k="52" />
    <hkern u1="&#xd0;" u2="T" k="98" />
    <hkern u1="&#xd0;" u2="A" k="42" />
    <hkern u1="&#xd0;" u2="&#x2f;" k="42" />
    <hkern u1="&#xd0;" u2="&#x2e;" k="56" />
    <hkern u1="&#xd0;" u2="&#x2c;" k="56" />
    <hkern u1="&#xd0;" u2="&#x2a;" k="46" />
    <hkern u1="&#xd0;" u2="&#x29;" k="40" />
    <hkern u1="&#xd0;" u2="&#x27;" k="46" />
    <hkern u1="&#xd0;" u2="&#x26;" k="42" />
    <hkern u1="&#xd0;" u2="&#x22;" k="46" />
    <hkern u1="&#xd2;" u2="&#x2206;" k="42" />
    <hkern u1="&#xd2;" u2="&#x2122;" k="46" />
    <hkern u1="&#xd2;" u2="&#x201e;" k="56" />
    <hkern u1="&#xd2;" u2="&#x201d;" k="46" />
    <hkern u1="&#xd2;" u2="&#x201c;" k="46" />
    <hkern u1="&#xd2;" u2="&#x201a;" k="56" />
    <hkern u1="&#xd2;" u2="&#x2019;" k="46" />
    <hkern u1="&#xd2;" u2="&#x2018;" k="46" />
    <hkern u1="&#xd2;" u2="&#x17d;" k="70" />
    <hkern u1="&#xd2;" u2="&#x17b;" k="70" />
    <hkern u1="&#xd2;" u2="&#x179;" k="70" />
    <hkern u1="&#xd2;" u2="&#x178;" k="80" />
    <hkern u1="&#xd2;" u2="&#x104;" k="42" />
    <hkern u1="&#xd2;" u2="&#xdd;" k="80" />
    <hkern u1="&#xd2;" u2="&#xc6;" k="42" />
    <hkern u1="&#xd2;" u2="&#xc5;" k="42" />
    <hkern u1="&#xd2;" u2="&#xc4;" k="42" />
    <hkern u1="&#xd2;" u2="&#xc3;" k="42" />
    <hkern u1="&#xd2;" u2="&#xc2;" k="42" />
    <hkern u1="&#xd2;" u2="&#xc1;" k="42" />
    <hkern u1="&#xd2;" u2="&#xc0;" k="42" />
    <hkern u1="&#xd2;" u2="&#xba;" k="46" />
    <hkern u1="&#xd2;" u2="&#xb0;" k="46" />
    <hkern u1="&#xd2;" u2="&#xaa;" k="46" />
    <hkern u1="&#xd2;" u2="&#x7d;" k="40" />
    <hkern u1="&#xd2;" u2="]" k="40" />
    <hkern u1="&#xd2;" u2="\" k="52" />
    <hkern u1="&#xd2;" u2="Z" k="70" />
    <hkern u1="&#xd2;" u2="Y" k="80" />
    <hkern u1="&#xd2;" u2="X" k="30" />
    <hkern u1="&#xd2;" u2="V" k="52" />
    <hkern u1="&#xd2;" u2="T" k="98" />
    <hkern u1="&#xd2;" u2="A" k="42" />
    <hkern u1="&#xd2;" u2="&#x2f;" k="42" />
    <hkern u1="&#xd2;" u2="&#x2e;" k="56" />
    <hkern u1="&#xd2;" u2="&#x2c;" k="56" />
    <hkern u1="&#xd2;" u2="&#x2a;" k="46" />
    <hkern u1="&#xd2;" u2="&#x29;" k="40" />
    <hkern u1="&#xd2;" u2="&#x27;" k="46" />
    <hkern u1="&#xd2;" u2="&#x26;" k="42" />
    <hkern u1="&#xd2;" u2="&#x22;" k="46" />
    <hkern u1="&#xd3;" u2="&#x2206;" k="42" />
    <hkern u1="&#xd3;" u2="&#x2122;" k="46" />
    <hkern u1="&#xd3;" u2="&#x201e;" k="56" />
    <hkern u1="&#xd3;" u2="&#x201d;" k="46" />
    <hkern u1="&#xd3;" u2="&#x201c;" k="46" />
    <hkern u1="&#xd3;" u2="&#x201a;" k="56" />
    <hkern u1="&#xd3;" u2="&#x2019;" k="46" />
    <hkern u1="&#xd3;" u2="&#x2018;" k="46" />
    <hkern u1="&#xd3;" u2="&#x17d;" k="70" />
    <hkern u1="&#xd3;" u2="&#x17b;" k="70" />
    <hkern u1="&#xd3;" u2="&#x179;" k="70" />
    <hkern u1="&#xd3;" u2="&#x178;" k="80" />
    <hkern u1="&#xd3;" u2="&#x104;" k="42" />
    <hkern u1="&#xd3;" u2="&#xdd;" k="80" />
    <hkern u1="&#xd3;" u2="&#xc6;" k="42" />
    <hkern u1="&#xd3;" u2="&#xc5;" k="42" />
    <hkern u1="&#xd3;" u2="&#xc4;" k="42" />
    <hkern u1="&#xd3;" u2="&#xc3;" k="42" />
    <hkern u1="&#xd3;" u2="&#xc2;" k="42" />
    <hkern u1="&#xd3;" u2="&#xc1;" k="42" />
    <hkern u1="&#xd3;" u2="&#xc0;" k="42" />
    <hkern u1="&#xd3;" u2="&#xba;" k="46" />
    <hkern u1="&#xd3;" u2="&#xb0;" k="46" />
    <hkern u1="&#xd3;" u2="&#xaa;" k="46" />
    <hkern u1="&#xd3;" u2="&#x7d;" k="40" />
    <hkern u1="&#xd3;" u2="]" k="40" />
    <hkern u1="&#xd3;" u2="\" k="52" />
    <hkern u1="&#xd3;" u2="Z" k="70" />
    <hkern u1="&#xd3;" u2="Y" k="80" />
    <hkern u1="&#xd3;" u2="X" k="30" />
    <hkern u1="&#xd3;" u2="V" k="52" />
    <hkern u1="&#xd3;" u2="T" k="98" />
    <hkern u1="&#xd3;" u2="A" k="42" />
    <hkern u1="&#xd3;" u2="&#x2f;" k="42" />
    <hkern u1="&#xd3;" u2="&#x2e;" k="56" />
    <hkern u1="&#xd3;" u2="&#x2c;" k="56" />
    <hkern u1="&#xd3;" u2="&#x2a;" k="46" />
    <hkern u1="&#xd3;" u2="&#x29;" k="40" />
    <hkern u1="&#xd3;" u2="&#x27;" k="46" />
    <hkern u1="&#xd3;" u2="&#x26;" k="42" />
    <hkern u1="&#xd3;" u2="&#x22;" k="46" />
    <hkern u1="&#xd4;" u2="&#x2206;" k="42" />
    <hkern u1="&#xd4;" u2="&#x2122;" k="46" />
    <hkern u1="&#xd4;" u2="&#x201e;" k="56" />
    <hkern u1="&#xd4;" u2="&#x201d;" k="46" />
    <hkern u1="&#xd4;" u2="&#x201c;" k="46" />
    <hkern u1="&#xd4;" u2="&#x201a;" k="56" />
    <hkern u1="&#xd4;" u2="&#x2019;" k="46" />
    <hkern u1="&#xd4;" u2="&#x2018;" k="46" />
    <hkern u1="&#xd4;" u2="&#x17d;" k="70" />
    <hkern u1="&#xd4;" u2="&#x17b;" k="70" />
    <hkern u1="&#xd4;" u2="&#x179;" k="70" />
    <hkern u1="&#xd4;" u2="&#x178;" k="80" />
    <hkern u1="&#xd4;" u2="&#x104;" k="42" />
    <hkern u1="&#xd4;" u2="&#xdd;" k="80" />
    <hkern u1="&#xd4;" u2="&#xc6;" k="42" />
    <hkern u1="&#xd4;" u2="&#xc5;" k="42" />
    <hkern u1="&#xd4;" u2="&#xc4;" k="42" />
    <hkern u1="&#xd4;" u2="&#xc3;" k="42" />
    <hkern u1="&#xd4;" u2="&#xc2;" k="42" />
    <hkern u1="&#xd4;" u2="&#xc1;" k="42" />
    <hkern u1="&#xd4;" u2="&#xc0;" k="42" />
    <hkern u1="&#xd4;" u2="&#xba;" k="46" />
    <hkern u1="&#xd4;" u2="&#xb0;" k="46" />
    <hkern u1="&#xd4;" u2="&#xaa;" k="46" />
    <hkern u1="&#xd4;" u2="&#x7d;" k="40" />
    <hkern u1="&#xd4;" u2="]" k="40" />
    <hkern u1="&#xd4;" u2="\" k="52" />
    <hkern u1="&#xd4;" u2="Z" k="70" />
    <hkern u1="&#xd4;" u2="Y" k="80" />
    <hkern u1="&#xd4;" u2="X" k="30" />
    <hkern u1="&#xd4;" u2="V" k="52" />
    <hkern u1="&#xd4;" u2="T" k="98" />
    <hkern u1="&#xd4;" u2="A" k="42" />
    <hkern u1="&#xd4;" u2="&#x2f;" k="42" />
    <hkern u1="&#xd4;" u2="&#x2e;" k="56" />
    <hkern u1="&#xd4;" u2="&#x2c;" k="56" />
    <hkern u1="&#xd4;" u2="&#x2a;" k="46" />
    <hkern u1="&#xd4;" u2="&#x29;" k="40" />
    <hkern u1="&#xd4;" u2="&#x27;" k="46" />
    <hkern u1="&#xd4;" u2="&#x26;" k="42" />
    <hkern u1="&#xd4;" u2="&#x22;" k="46" />
    <hkern u1="&#xd5;" u2="&#x2206;" k="42" />
    <hkern u1="&#xd5;" u2="&#x2122;" k="46" />
    <hkern u1="&#xd5;" u2="&#x201e;" k="56" />
    <hkern u1="&#xd5;" u2="&#x201d;" k="46" />
    <hkern u1="&#xd5;" u2="&#x201c;" k="46" />
    <hkern u1="&#xd5;" u2="&#x201a;" k="56" />
    <hkern u1="&#xd5;" u2="&#x2019;" k="46" />
    <hkern u1="&#xd5;" u2="&#x2018;" k="46" />
    <hkern u1="&#xd5;" u2="&#x17d;" k="70" />
    <hkern u1="&#xd5;" u2="&#x17b;" k="70" />
    <hkern u1="&#xd5;" u2="&#x179;" k="70" />
    <hkern u1="&#xd5;" u2="&#x178;" k="80" />
    <hkern u1="&#xd5;" u2="&#x104;" k="42" />
    <hkern u1="&#xd5;" u2="&#xdd;" k="80" />
    <hkern u1="&#xd5;" u2="&#xc6;" k="42" />
    <hkern u1="&#xd5;" u2="&#xc5;" k="42" />
    <hkern u1="&#xd5;" u2="&#xc4;" k="42" />
    <hkern u1="&#xd5;" u2="&#xc3;" k="42" />
    <hkern u1="&#xd5;" u2="&#xc2;" k="42" />
    <hkern u1="&#xd5;" u2="&#xc1;" k="42" />
    <hkern u1="&#xd5;" u2="&#xc0;" k="42" />
    <hkern u1="&#xd5;" u2="&#xba;" k="46" />
    <hkern u1="&#xd5;" u2="&#xb0;" k="46" />
    <hkern u1="&#xd5;" u2="&#xaa;" k="46" />
    <hkern u1="&#xd5;" u2="&#x7d;" k="40" />
    <hkern u1="&#xd5;" u2="]" k="40" />
    <hkern u1="&#xd5;" u2="\" k="52" />
    <hkern u1="&#xd5;" u2="Z" k="70" />
    <hkern u1="&#xd5;" u2="Y" k="80" />
    <hkern u1="&#xd5;" u2="X" k="30" />
    <hkern u1="&#xd5;" u2="V" k="52" />
    <hkern u1="&#xd5;" u2="T" k="98" />
    <hkern u1="&#xd5;" u2="A" k="42" />
    <hkern u1="&#xd5;" u2="&#x2f;" k="42" />
    <hkern u1="&#xd5;" u2="&#x2e;" k="56" />
    <hkern u1="&#xd5;" u2="&#x2c;" k="56" />
    <hkern u1="&#xd5;" u2="&#x2a;" k="46" />
    <hkern u1="&#xd5;" u2="&#x29;" k="40" />
    <hkern u1="&#xd5;" u2="&#x27;" k="46" />
    <hkern u1="&#xd5;" u2="&#x26;" k="42" />
    <hkern u1="&#xd5;" u2="&#x22;" k="46" />
    <hkern u1="&#xd6;" u2="&#x2206;" k="42" />
    <hkern u1="&#xd6;" u2="&#x2122;" k="46" />
    <hkern u1="&#xd6;" u2="&#x201e;" k="56" />
    <hkern u1="&#xd6;" u2="&#x201d;" k="46" />
    <hkern u1="&#xd6;" u2="&#x201c;" k="46" />
    <hkern u1="&#xd6;" u2="&#x201a;" k="56" />
    <hkern u1="&#xd6;" u2="&#x2019;" k="46" />
    <hkern u1="&#xd6;" u2="&#x2018;" k="46" />
    <hkern u1="&#xd6;" u2="&#x17d;" k="70" />
    <hkern u1="&#xd6;" u2="&#x17b;" k="70" />
    <hkern u1="&#xd6;" u2="&#x179;" k="70" />
    <hkern u1="&#xd6;" u2="&#x178;" k="80" />
    <hkern u1="&#xd6;" u2="&#x104;" k="42" />
    <hkern u1="&#xd6;" u2="&#xdd;" k="80" />
    <hkern u1="&#xd6;" u2="&#xc6;" k="42" />
    <hkern u1="&#xd6;" u2="&#xc5;" k="42" />
    <hkern u1="&#xd6;" u2="&#xc4;" k="42" />
    <hkern u1="&#xd6;" u2="&#xc3;" k="42" />
    <hkern u1="&#xd6;" u2="&#xc2;" k="42" />
    <hkern u1="&#xd6;" u2="&#xc1;" k="42" />
    <hkern u1="&#xd6;" u2="&#xc0;" k="42" />
    <hkern u1="&#xd6;" u2="&#xba;" k="46" />
    <hkern u1="&#xd6;" u2="&#xb0;" k="46" />
    <hkern u1="&#xd6;" u2="&#xaa;" k="46" />
    <hkern u1="&#xd6;" u2="&#x7d;" k="40" />
    <hkern u1="&#xd6;" u2="]" k="40" />
    <hkern u1="&#xd6;" u2="\" k="52" />
    <hkern u1="&#xd6;" u2="Z" k="70" />
    <hkern u1="&#xd6;" u2="Y" k="80" />
    <hkern u1="&#xd6;" u2="X" k="30" />
    <hkern u1="&#xd6;" u2="V" k="52" />
    <hkern u1="&#xd6;" u2="T" k="98" />
    <hkern u1="&#xd6;" u2="A" k="42" />
    <hkern u1="&#xd6;" u2="&#x2f;" k="42" />
    <hkern u1="&#xd6;" u2="&#x2e;" k="56" />
    <hkern u1="&#xd6;" u2="&#x2c;" k="56" />
    <hkern u1="&#xd6;" u2="&#x2a;" k="46" />
    <hkern u1="&#xd6;" u2="&#x29;" k="40" />
    <hkern u1="&#xd6;" u2="&#x27;" k="46" />
    <hkern u1="&#xd6;" u2="&#x26;" k="42" />
    <hkern u1="&#xd6;" u2="&#x22;" k="46" />
    <hkern u1="&#xd9;" u2="&#x2206;" k="56" />
    <hkern u1="&#xd9;" u2="&#x201e;" k="50" />
    <hkern u1="&#xd9;" u2="&#x201a;" k="50" />
    <hkern u1="&#xd9;" u2="&#x104;" k="56" />
    <hkern u1="&#xd9;" u2="&#xc6;" k="56" />
    <hkern u1="&#xd9;" u2="&#xc5;" k="56" />
    <hkern u1="&#xd9;" u2="&#xc4;" k="56" />
    <hkern u1="&#xd9;" u2="&#xc3;" k="56" />
    <hkern u1="&#xd9;" u2="&#xc2;" k="56" />
    <hkern u1="&#xd9;" u2="&#xc1;" k="56" />
    <hkern u1="&#xd9;" u2="&#xc0;" k="56" />
    <hkern u1="&#xd9;" u2="A" k="56" />
    <hkern u1="&#xd9;" u2="&#x2f;" k="56" />
    <hkern u1="&#xd9;" u2="&#x2e;" k="50" />
    <hkern u1="&#xd9;" u2="&#x2c;" k="50" />
    <hkern u1="&#xd9;" u2="&#x26;" k="56" />
    <hkern u1="&#xda;" u2="&#x2206;" k="56" />
    <hkern u1="&#xda;" u2="&#x201e;" k="50" />
    <hkern u1="&#xda;" u2="&#x201a;" k="50" />
    <hkern u1="&#xda;" u2="&#x104;" k="56" />
    <hkern u1="&#xda;" u2="&#xc6;" k="56" />
    <hkern u1="&#xda;" u2="&#xc5;" k="56" />
    <hkern u1="&#xda;" u2="&#xc4;" k="56" />
    <hkern u1="&#xda;" u2="&#xc3;" k="56" />
    <hkern u1="&#xda;" u2="&#xc2;" k="56" />
    <hkern u1="&#xda;" u2="&#xc1;" k="56" />
    <hkern u1="&#xda;" u2="&#xc0;" k="56" />
    <hkern u1="&#xda;" u2="A" k="56" />
    <hkern u1="&#xda;" u2="&#x2f;" k="56" />
    <hkern u1="&#xda;" u2="&#x2e;" k="50" />
    <hkern u1="&#xda;" u2="&#x2c;" k="50" />
    <hkern u1="&#xda;" u2="&#x26;" k="56" />
    <hkern u1="&#xdb;" u2="&#x2206;" k="56" />
    <hkern u1="&#xdb;" u2="&#x201e;" k="50" />
    <hkern u1="&#xdb;" u2="&#x201a;" k="50" />
    <hkern u1="&#xdb;" u2="&#x104;" k="56" />
    <hkern u1="&#xdb;" u2="&#xc6;" k="56" />
    <hkern u1="&#xdb;" u2="&#xc5;" k="56" />
    <hkern u1="&#xdb;" u2="&#xc4;" k="56" />
    <hkern u1="&#xdb;" u2="&#xc3;" k="56" />
    <hkern u1="&#xdb;" u2="&#xc2;" k="56" />
    <hkern u1="&#xdb;" u2="&#xc1;" k="56" />
    <hkern u1="&#xdb;" u2="&#xc0;" k="56" />
    <hkern u1="&#xdb;" u2="A" k="56" />
    <hkern u1="&#xdb;" u2="&#x2f;" k="56" />
    <hkern u1="&#xdb;" u2="&#x2e;" k="50" />
    <hkern u1="&#xdb;" u2="&#x2c;" k="50" />
    <hkern u1="&#xdb;" u2="&#x26;" k="56" />
    <hkern u1="&#xdc;" u2="&#x2206;" k="56" />
    <hkern u1="&#xdc;" u2="&#x201e;" k="50" />
    <hkern u1="&#xdc;" u2="&#x201a;" k="50" />
    <hkern u1="&#xdc;" u2="&#x104;" k="56" />
    <hkern u1="&#xdc;" u2="&#xc6;" k="56" />
    <hkern u1="&#xdc;" u2="&#xc5;" k="56" />
    <hkern u1="&#xdc;" u2="&#xc4;" k="56" />
    <hkern u1="&#xdc;" u2="&#xc3;" k="56" />
    <hkern u1="&#xdc;" u2="&#xc2;" k="56" />
    <hkern u1="&#xdc;" u2="&#xc1;" k="56" />
    <hkern u1="&#xdc;" u2="&#xc0;" k="56" />
    <hkern u1="&#xdc;" u2="A" k="56" />
    <hkern u1="&#xdc;" u2="&#x2f;" k="56" />
    <hkern u1="&#xdc;" u2="&#x2e;" k="50" />
    <hkern u1="&#xdc;" u2="&#x2c;" k="50" />
    <hkern u1="&#xdc;" u2="&#x26;" k="56" />
    <hkern u1="&#xdd;" u2="&#x2206;" k="164" />
    <hkern u1="&#xdd;" u2="&#x2122;" k="-30" />
    <hkern u1="&#xdd;" u2="&#x203a;" k="160" />
    <hkern u1="&#xdd;" u2="&#x2039;" k="160" />
    <hkern u1="&#xdd;" u2="&#x2022;" k="160" />
    <hkern u1="&#xdd;" u2="&#x201e;" k="152" />
    <hkern u1="&#xdd;" u2="&#x201d;" k="-30" />
    <hkern u1="&#xdd;" u2="&#x201c;" k="-30" />
    <hkern u1="&#xdd;" u2="&#x201a;" k="152" />
    <hkern u1="&#xdd;" u2="&#x2019;" k="-30" />
    <hkern u1="&#xdd;" u2="&#x2018;" k="-30" />
    <hkern u1="&#xdd;" u2="&#x2014;" k="160" />
    <hkern u1="&#xdd;" u2="&#x2013;" k="160" />
    <hkern u1="&#xdd;" u2="&#x153;" k="160" />
    <hkern u1="&#xdd;" u2="&#x152;" k="80" />
    <hkern u1="&#xdd;" u2="&#x144;" k="122" />
    <hkern u1="&#xdd;" u2="&#x119;" k="160" />
    <hkern u1="&#xdd;" u2="&#x107;" k="160" />
    <hkern u1="&#xdd;" u2="&#x106;" k="80" />
    <hkern u1="&#xdd;" u2="&#x105;" k="128" />
    <hkern u1="&#xdd;" u2="&#x104;" k="164" />
    <hkern u1="&#xdd;" u2="&#xfc;" k="122" />
    <hkern u1="&#xdd;" u2="&#xfb;" k="122" />
    <hkern u1="&#xdd;" u2="&#xfa;" k="122" />
    <hkern u1="&#xdd;" u2="&#xf9;" k="122" />
    <hkern u1="&#xdd;" u2="&#xf8;" k="160" />
    <hkern u1="&#xdd;" u2="&#xf6;" k="160" />
    <hkern u1="&#xdd;" u2="&#xf5;" k="160" />
    <hkern u1="&#xdd;" u2="&#xf4;" k="160" />
    <hkern u1="&#xdd;" u2="&#xf3;" k="160" />
    <hkern u1="&#xdd;" u2="&#xf2;" k="160" />
    <hkern u1="&#xdd;" u2="&#xf1;" k="122" />
    <hkern u1="&#xdd;" u2="&#xf0;" k="160" />
    <hkern u1="&#xdd;" u2="&#xeb;" k="160" />
    <hkern u1="&#xdd;" u2="&#xea;" k="160" />
    <hkern u1="&#xdd;" u2="&#xe9;" k="160" />
    <hkern u1="&#xdd;" u2="&#xe8;" k="160" />
    <hkern u1="&#xdd;" u2="&#xe7;" k="160" />
    <hkern u1="&#xdd;" u2="&#xe6;" k="128" />
    <hkern u1="&#xdd;" u2="&#xe5;" k="128" />
    <hkern u1="&#xdd;" u2="&#xe4;" k="128" />
    <hkern u1="&#xdd;" u2="&#xe3;" k="128" />
    <hkern u1="&#xdd;" u2="&#xe2;" k="128" />
    <hkern u1="&#xdd;" u2="&#xe1;" k="128" />
    <hkern u1="&#xdd;" u2="&#xe0;" k="128" />
    <hkern u1="&#xdd;" u2="&#xd8;" k="80" />
    <hkern u1="&#xdd;" u2="&#xd6;" k="80" />
    <hkern u1="&#xdd;" u2="&#xd5;" k="80" />
    <hkern u1="&#xdd;" u2="&#xd4;" k="80" />
    <hkern u1="&#xdd;" u2="&#xd3;" k="80" />
    <hkern u1="&#xdd;" u2="&#xd2;" k="80" />
    <hkern u1="&#xdd;" u2="&#xc7;" k="80" />
    <hkern u1="&#xdd;" u2="&#xc6;" k="164" />
    <hkern u1="&#xdd;" u2="&#xc5;" k="164" />
    <hkern u1="&#xdd;" u2="&#xc4;" k="164" />
    <hkern u1="&#xdd;" u2="&#xc3;" k="164" />
    <hkern u1="&#xdd;" u2="&#xc2;" k="164" />
    <hkern u1="&#xdd;" u2="&#xc1;" k="164" />
    <hkern u1="&#xdd;" u2="&#xc0;" k="164" />
    <hkern u1="&#xdd;" u2="&#xbb;" k="160" />
    <hkern u1="&#xdd;" u2="&#xba;" k="-30" />
    <hkern u1="&#xdd;" u2="&#xb9;" k="-50" />
    <hkern u1="&#xdd;" u2="&#xb7;" k="160" />
    <hkern u1="&#xdd;" u2="&#xb5;" k="122" />
    <hkern u1="&#xdd;" u2="&#xb3;" k="-50" />
    <hkern u1="&#xdd;" u2="&#xb2;" k="-50" />
    <hkern u1="&#xdd;" u2="&#xb0;" k="-30" />
    <hkern u1="&#xdd;" u2="&#xad;" k="160" />
    <hkern u1="&#xdd;" u2="&#xab;" k="160" />
    <hkern u1="&#xdd;" u2="&#xaa;" k="-30" />
    <hkern u1="&#xdd;" u2="y" k="100" />
    <hkern u1="&#xdd;" u2="x" k="132" />
    <hkern u1="&#xdd;" u2="w" k="92" />
    <hkern u1="&#xdd;" u2="v" k="100" />
    <hkern u1="&#xdd;" u2="u" k="122" />
    <hkern u1="&#xdd;" u2="s" k="128" />
    <hkern u1="&#xdd;" u2="r" k="122" />
    <hkern u1="&#xdd;" u2="q" k="160" />
    <hkern u1="&#xdd;" u2="p" k="122" />
    <hkern u1="&#xdd;" u2="o" k="160" />
    <hkern u1="&#xdd;" u2="n" k="122" />
    <hkern u1="&#xdd;" u2="m" k="122" />
    <hkern u1="&#xdd;" u2="g" k="172" />
    <hkern u1="&#xdd;" u2="e" k="160" />
    <hkern u1="&#xdd;" u2="d" k="160" />
    <hkern u1="&#xdd;" u2="c" k="160" />
    <hkern u1="&#xdd;" u2="a" k="128" />
    <hkern u1="&#xdd;" u2="Q" k="80" />
    <hkern u1="&#xdd;" u2="O" k="80" />
    <hkern u1="&#xdd;" u2="J" k="200" />
    <hkern u1="&#xdd;" u2="G" k="80" />
    <hkern u1="&#xdd;" u2="C" k="80" />
    <hkern u1="&#xdd;" u2="A" k="164" />
    <hkern u1="&#xdd;" u2="&#x40;" k="80" />
    <hkern u1="&#xdd;" u2="&#x3f;" k="-34" />
    <hkern u1="&#xdd;" u2="&#x3b;" k="122" />
    <hkern u1="&#xdd;" u2="&#x3a;" k="122" />
    <hkern u1="&#xdd;" u2="&#x2f;" k="164" />
    <hkern u1="&#xdd;" u2="&#x2e;" k="152" />
    <hkern u1="&#xdd;" u2="&#x2d;" k="160" />
    <hkern u1="&#xdd;" u2="&#x2c;" k="152" />
    <hkern u1="&#xdd;" u2="&#x2a;" k="-30" />
    <hkern u1="&#xdd;" u2="&#x27;" k="-30" />
    <hkern u1="&#xdd;" u2="&#x26;" k="164" />
    <hkern u1="&#xdd;" u2="&#x22;" k="-30" />
    <hkern u1="&#xde;" u2="&#x2206;" k="42" />
    <hkern u1="&#xde;" u2="&#x2122;" k="46" />
    <hkern u1="&#xde;" u2="&#x201e;" k="56" />
    <hkern u1="&#xde;" u2="&#x201d;" k="46" />
    <hkern u1="&#xde;" u2="&#x201c;" k="46" />
    <hkern u1="&#xde;" u2="&#x201a;" k="56" />
    <hkern u1="&#xde;" u2="&#x2019;" k="46" />
    <hkern u1="&#xde;" u2="&#x2018;" k="46" />
    <hkern u1="&#xde;" u2="&#x17d;" k="70" />
    <hkern u1="&#xde;" u2="&#x17b;" k="70" />
    <hkern u1="&#xde;" u2="&#x179;" k="70" />
    <hkern u1="&#xde;" u2="&#x178;" k="80" />
    <hkern u1="&#xde;" u2="&#x104;" k="42" />
    <hkern u1="&#xde;" u2="&#xdd;" k="80" />
    <hkern u1="&#xde;" u2="&#xc6;" k="42" />
    <hkern u1="&#xde;" u2="&#xc5;" k="42" />
    <hkern u1="&#xde;" u2="&#xc4;" k="42" />
    <hkern u1="&#xde;" u2="&#xc3;" k="42" />
    <hkern u1="&#xde;" u2="&#xc2;" k="42" />
    <hkern u1="&#xde;" u2="&#xc1;" k="42" />
    <hkern u1="&#xde;" u2="&#xc0;" k="42" />
    <hkern u1="&#xde;" u2="&#xba;" k="46" />
    <hkern u1="&#xde;" u2="&#xb0;" k="46" />
    <hkern u1="&#xde;" u2="&#xaa;" k="46" />
    <hkern u1="&#xde;" u2="&#x7d;" k="40" />
    <hkern u1="&#xde;" u2="]" k="40" />
    <hkern u1="&#xde;" u2="\" k="52" />
    <hkern u1="&#xde;" u2="Z" k="70" />
    <hkern u1="&#xde;" u2="Y" k="80" />
    <hkern u1="&#xde;" u2="X" k="30" />
    <hkern u1="&#xde;" u2="V" k="52" />
    <hkern u1="&#xde;" u2="T" k="98" />
    <hkern u1="&#xde;" u2="A" k="42" />
    <hkern u1="&#xde;" u2="&#x2f;" k="42" />
    <hkern u1="&#xde;" u2="&#x2e;" k="56" />
    <hkern u1="&#xde;" u2="&#x2c;" k="56" />
    <hkern u1="&#xde;" u2="&#x2a;" k="46" />
    <hkern u1="&#xde;" u2="&#x29;" k="40" />
    <hkern u1="&#xde;" u2="&#x27;" k="46" />
    <hkern u1="&#xde;" u2="&#x26;" k="42" />
    <hkern u1="&#xde;" u2="&#x22;" k="46" />
    <hkern u1="&#xe0;" u2="&#x2122;" k="72" />
    <hkern u1="&#xe0;" u2="&#x201d;" k="72" />
    <hkern u1="&#xe0;" u2="&#x201c;" k="72" />
    <hkern u1="&#xe0;" u2="&#x2019;" k="72" />
    <hkern u1="&#xe0;" u2="&#x2018;" k="72" />
    <hkern u1="&#xe0;" u2="&#xba;" k="72" />
    <hkern u1="&#xe0;" u2="&#xb9;" k="72" />
    <hkern u1="&#xe0;" u2="&#xb3;" k="72" />
    <hkern u1="&#xe0;" u2="&#xb2;" k="72" />
    <hkern u1="&#xe0;" u2="&#xb0;" k="72" />
    <hkern u1="&#xe0;" u2="&#xaa;" k="72" />
    <hkern u1="&#xe0;" u2="y" k="32" />
    <hkern u1="&#xe0;" u2="w" k="16" />
    <hkern u1="&#xe0;" u2="v" k="32" />
    <hkern u1="&#xe0;" u2="&#x2a;" k="72" />
    <hkern u1="&#xe0;" u2="&#x27;" k="72" />
    <hkern u1="&#xe0;" u2="&#x22;" k="72" />
    <hkern u1="&#xe1;" u2="&#x2122;" k="72" />
    <hkern u1="&#xe1;" u2="&#x201d;" k="72" />
    <hkern u1="&#xe1;" u2="&#x201c;" k="72" />
    <hkern u1="&#xe1;" u2="&#x2019;" k="72" />
    <hkern u1="&#xe1;" u2="&#x2018;" k="72" />
    <hkern u1="&#xe1;" u2="&#xba;" k="72" />
    <hkern u1="&#xe1;" u2="&#xb9;" k="72" />
    <hkern u1="&#xe1;" u2="&#xb3;" k="72" />
    <hkern u1="&#xe1;" u2="&#xb2;" k="72" />
    <hkern u1="&#xe1;" u2="&#xb0;" k="72" />
    <hkern u1="&#xe1;" u2="&#xaa;" k="72" />
    <hkern u1="&#xe1;" u2="y" k="32" />
    <hkern u1="&#xe1;" u2="w" k="16" />
    <hkern u1="&#xe1;" u2="v" k="32" />
    <hkern u1="&#xe1;" u2="&#x2a;" k="72" />
    <hkern u1="&#xe1;" u2="&#x27;" k="72" />
    <hkern u1="&#xe1;" u2="&#x22;" k="72" />
    <hkern u1="&#xe2;" u2="&#x2122;" k="72" />
    <hkern u1="&#xe2;" u2="&#x201d;" k="72" />
    <hkern u1="&#xe2;" u2="&#x201c;" k="72" />
    <hkern u1="&#xe2;" u2="&#x2019;" k="72" />
    <hkern u1="&#xe2;" u2="&#x2018;" k="72" />
    <hkern u1="&#xe2;" u2="&#xba;" k="72" />
    <hkern u1="&#xe2;" u2="&#xb9;" k="72" />
    <hkern u1="&#xe2;" u2="&#xb3;" k="72" />
    <hkern u1="&#xe2;" u2="&#xb2;" k="72" />
    <hkern u1="&#xe2;" u2="&#xb0;" k="72" />
    <hkern u1="&#xe2;" u2="&#xaa;" k="72" />
    <hkern u1="&#xe2;" u2="y" k="32" />
    <hkern u1="&#xe2;" u2="w" k="16" />
    <hkern u1="&#xe2;" u2="v" k="32" />
    <hkern u1="&#xe2;" u2="&#x2a;" k="72" />
    <hkern u1="&#xe2;" u2="&#x27;" k="72" />
    <hkern u1="&#xe2;" u2="&#x22;" k="72" />
    <hkern u1="&#xe3;" u2="&#x2122;" k="72" />
    <hkern u1="&#xe3;" u2="&#x201d;" k="72" />
    <hkern u1="&#xe3;" u2="&#x201c;" k="72" />
    <hkern u1="&#xe3;" u2="&#x2019;" k="72" />
    <hkern u1="&#xe3;" u2="&#x2018;" k="72" />
    <hkern u1="&#xe3;" u2="&#xba;" k="72" />
    <hkern u1="&#xe3;" u2="&#xb9;" k="72" />
    <hkern u1="&#xe3;" u2="&#xb3;" k="72" />
    <hkern u1="&#xe3;" u2="&#xb2;" k="72" />
    <hkern u1="&#xe3;" u2="&#xb0;" k="72" />
    <hkern u1="&#xe3;" u2="&#xaa;" k="72" />
    <hkern u1="&#xe3;" u2="y" k="32" />
    <hkern u1="&#xe3;" u2="w" k="16" />
    <hkern u1="&#xe3;" u2="v" k="32" />
    <hkern u1="&#xe3;" u2="&#x2a;" k="72" />
    <hkern u1="&#xe3;" u2="&#x27;" k="72" />
    <hkern u1="&#xe3;" u2="&#x22;" k="72" />
    <hkern u1="&#xe4;" u2="&#x2122;" k="72" />
    <hkern u1="&#xe4;" u2="&#x201d;" k="72" />
    <hkern u1="&#xe4;" u2="&#x201c;" k="72" />
    <hkern u1="&#xe4;" u2="&#x2019;" k="72" />
    <hkern u1="&#xe4;" u2="&#x2018;" k="72" />
    <hkern u1="&#xe4;" u2="&#xba;" k="72" />
    <hkern u1="&#xe4;" u2="&#xb9;" k="72" />
    <hkern u1="&#xe4;" u2="&#xb3;" k="72" />
    <hkern u1="&#xe4;" u2="&#xb2;" k="72" />
    <hkern u1="&#xe4;" u2="&#xb0;" k="72" />
    <hkern u1="&#xe4;" u2="&#xaa;" k="72" />
    <hkern u1="&#xe4;" u2="y" k="32" />
    <hkern u1="&#xe4;" u2="w" k="16" />
    <hkern u1="&#xe4;" u2="v" k="32" />
    <hkern u1="&#xe4;" u2="&#x2a;" k="72" />
    <hkern u1="&#xe4;" u2="&#x27;" k="72" />
    <hkern u1="&#xe4;" u2="&#x22;" k="72" />
    <hkern u1="&#xe5;" u2="&#x2122;" k="72" />
    <hkern u1="&#xe5;" u2="&#x201d;" k="72" />
    <hkern u1="&#xe5;" u2="&#x201c;" k="72" />
    <hkern u1="&#xe5;" u2="&#x2019;" k="72" />
    <hkern u1="&#xe5;" u2="&#x2018;" k="72" />
    <hkern u1="&#xe5;" u2="&#xba;" k="72" />
    <hkern u1="&#xe5;" u2="&#xb9;" k="72" />
    <hkern u1="&#xe5;" u2="&#xb3;" k="72" />
    <hkern u1="&#xe5;" u2="&#xb2;" k="72" />
    <hkern u1="&#xe5;" u2="&#xb0;" k="72" />
    <hkern u1="&#xe5;" u2="&#xaa;" k="72" />
    <hkern u1="&#xe5;" u2="y" k="32" />
    <hkern u1="&#xe5;" u2="w" k="16" />
    <hkern u1="&#xe5;" u2="v" k="32" />
    <hkern u1="&#xe5;" u2="&#x2a;" k="72" />
    <hkern u1="&#xe5;" u2="&#x27;" k="72" />
    <hkern u1="&#xe5;" u2="&#x22;" k="72" />
    <hkern u1="&#xe6;" u2="&#x2122;" k="92" />
    <hkern u1="&#xe6;" u2="&#x201d;" k="92" />
    <hkern u1="&#xe6;" u2="&#x201c;" k="92" />
    <hkern u1="&#xe6;" u2="&#x2019;" k="92" />
    <hkern u1="&#xe6;" u2="&#x2018;" k="92" />
    <hkern u1="&#xe6;" u2="&#xba;" k="92" />
    <hkern u1="&#xe6;" u2="&#xb0;" k="92" />
    <hkern u1="&#xe6;" u2="&#xaa;" k="92" />
    <hkern u1="&#xe6;" u2="&#x7d;" k="32" />
    <hkern u1="&#xe6;" u2="y" k="26" />
    <hkern u1="&#xe6;" u2="x" k="60" />
    <hkern u1="&#xe6;" u2="v" k="26" />
    <hkern u1="&#xe6;" u2="]" k="32" />
    <hkern u1="&#xe6;" u2="\" k="116" />
    <hkern u1="&#xe6;" u2="W" k="32" />
    <hkern u1="&#xe6;" u2="V" k="116" />
    <hkern u1="&#xe6;" u2="&#x2a;" k="92" />
    <hkern u1="&#xe6;" u2="&#x29;" k="32" />
    <hkern u1="&#xe6;" u2="&#x27;" k="92" />
    <hkern u1="&#xe6;" u2="&#x22;" k="92" />
    <hkern u1="&#xe8;" u2="&#x2122;" k="92" />
    <hkern u1="&#xe8;" u2="&#x201d;" k="92" />
    <hkern u1="&#xe8;" u2="&#x201c;" k="92" />
    <hkern u1="&#xe8;" u2="&#x2019;" k="92" />
    <hkern u1="&#xe8;" u2="&#x2018;" k="92" />
    <hkern u1="&#xe8;" u2="&#xba;" k="92" />
    <hkern u1="&#xe8;" u2="&#xb0;" k="92" />
    <hkern u1="&#xe8;" u2="&#xaa;" k="92" />
    <hkern u1="&#xe8;" u2="&#x7d;" k="32" />
    <hkern u1="&#xe8;" u2="y" k="26" />
    <hkern u1="&#xe8;" u2="x" k="60" />
    <hkern u1="&#xe8;" u2="v" k="26" />
    <hkern u1="&#xe8;" u2="]" k="32" />
    <hkern u1="&#xe8;" u2="\" k="116" />
    <hkern u1="&#xe8;" u2="W" k="32" />
    <hkern u1="&#xe8;" u2="V" k="116" />
    <hkern u1="&#xe8;" u2="&#x2a;" k="92" />
    <hkern u1="&#xe8;" u2="&#x29;" k="32" />
    <hkern u1="&#xe8;" u2="&#x27;" k="92" />
    <hkern u1="&#xe8;" u2="&#x22;" k="92" />
    <hkern u1="&#xe9;" u2="&#x2122;" k="92" />
    <hkern u1="&#xe9;" u2="&#x201d;" k="92" />
    <hkern u1="&#xe9;" u2="&#x201c;" k="92" />
    <hkern u1="&#xe9;" u2="&#x2019;" k="92" />
    <hkern u1="&#xe9;" u2="&#x2018;" k="92" />
    <hkern u1="&#xe9;" u2="&#xba;" k="92" />
    <hkern u1="&#xe9;" u2="&#xb0;" k="92" />
    <hkern u1="&#xe9;" u2="&#xaa;" k="92" />
    <hkern u1="&#xe9;" u2="&#x7d;" k="32" />
    <hkern u1="&#xe9;" u2="y" k="26" />
    <hkern u1="&#xe9;" u2="x" k="60" />
    <hkern u1="&#xe9;" u2="v" k="26" />
    <hkern u1="&#xe9;" u2="]" k="32" />
    <hkern u1="&#xe9;" u2="\" k="116" />
    <hkern u1="&#xe9;" u2="W" k="32" />
    <hkern u1="&#xe9;" u2="V" k="116" />
    <hkern u1="&#xe9;" u2="&#x2a;" k="92" />
    <hkern u1="&#xe9;" u2="&#x29;" k="32" />
    <hkern u1="&#xe9;" u2="&#x27;" k="92" />
    <hkern u1="&#xe9;" u2="&#x22;" k="92" />
    <hkern u1="&#xea;" u2="&#x2122;" k="92" />
    <hkern u1="&#xea;" u2="&#x201d;" k="92" />
    <hkern u1="&#xea;" u2="&#x201c;" k="92" />
    <hkern u1="&#xea;" u2="&#x2019;" k="92" />
    <hkern u1="&#xea;" u2="&#x2018;" k="92" />
    <hkern u1="&#xea;" u2="&#xba;" k="92" />
    <hkern u1="&#xea;" u2="&#xb0;" k="92" />
    <hkern u1="&#xea;" u2="&#xaa;" k="92" />
    <hkern u1="&#xea;" u2="&#x7d;" k="32" />
    <hkern u1="&#xea;" u2="y" k="26" />
    <hkern u1="&#xea;" u2="x" k="60" />
    <hkern u1="&#xea;" u2="v" k="26" />
    <hkern u1="&#xea;" u2="]" k="32" />
    <hkern u1="&#xea;" u2="\" k="116" />
    <hkern u1="&#xea;" u2="W" k="32" />
    <hkern u1="&#xea;" u2="V" k="116" />
    <hkern u1="&#xea;" u2="&#x2a;" k="92" />
    <hkern u1="&#xea;" u2="&#x29;" k="32" />
    <hkern u1="&#xea;" u2="&#x27;" k="92" />
    <hkern u1="&#xea;" u2="&#x22;" k="92" />
    <hkern u1="&#xeb;" u2="&#x2122;" k="92" />
    <hkern u1="&#xeb;" u2="&#x201d;" k="92" />
    <hkern u1="&#xeb;" u2="&#x201c;" k="92" />
    <hkern u1="&#xeb;" u2="&#x2019;" k="92" />
    <hkern u1="&#xeb;" u2="&#x2018;" k="92" />
    <hkern u1="&#xeb;" u2="&#xba;" k="92" />
    <hkern u1="&#xeb;" u2="&#xb0;" k="92" />
    <hkern u1="&#xeb;" u2="&#xaa;" k="92" />
    <hkern u1="&#xeb;" u2="&#x7d;" k="32" />
    <hkern u1="&#xeb;" u2="y" k="26" />
    <hkern u1="&#xeb;" u2="x" k="60" />
    <hkern u1="&#xeb;" u2="v" k="26" />
    <hkern u1="&#xeb;" u2="]" k="32" />
    <hkern u1="&#xeb;" u2="\" k="116" />
    <hkern u1="&#xeb;" u2="W" k="32" />
    <hkern u1="&#xeb;" u2="V" k="116" />
    <hkern u1="&#xeb;" u2="&#x2a;" k="92" />
    <hkern u1="&#xeb;" u2="&#x29;" k="32" />
    <hkern u1="&#xeb;" u2="&#x27;" k="92" />
    <hkern u1="&#xeb;" u2="&#x22;" k="92" />
    <hkern u1="&#xf1;" u2="&#x2122;" k="72" />
    <hkern u1="&#xf1;" u2="&#x201d;" k="72" />
    <hkern u1="&#xf1;" u2="&#x201c;" k="72" />
    <hkern u1="&#xf1;" u2="&#x2019;" k="72" />
    <hkern u1="&#xf1;" u2="&#x2018;" k="72" />
    <hkern u1="&#xf1;" u2="&#xba;" k="72" />
    <hkern u1="&#xf1;" u2="&#xb9;" k="72" />
    <hkern u1="&#xf1;" u2="&#xb3;" k="72" />
    <hkern u1="&#xf1;" u2="&#xb2;" k="72" />
    <hkern u1="&#xf1;" u2="&#xb0;" k="72" />
    <hkern u1="&#xf1;" u2="&#xaa;" k="72" />
    <hkern u1="&#xf1;" u2="y" k="32" />
    <hkern u1="&#xf1;" u2="w" k="16" />
    <hkern u1="&#xf1;" u2="v" k="32" />
    <hkern u1="&#xf1;" u2="&#x2a;" k="72" />
    <hkern u1="&#xf1;" u2="&#x27;" k="72" />
    <hkern u1="&#xf1;" u2="&#x22;" k="72" />
    <hkern u1="&#xf2;" u2="&#x2122;" k="92" />
    <hkern u1="&#xf2;" u2="&#x201d;" k="92" />
    <hkern u1="&#xf2;" u2="&#x201c;" k="92" />
    <hkern u1="&#xf2;" u2="&#x2019;" k="92" />
    <hkern u1="&#xf2;" u2="&#x2018;" k="92" />
    <hkern u1="&#xf2;" u2="&#xba;" k="92" />
    <hkern u1="&#xf2;" u2="&#xb0;" k="92" />
    <hkern u1="&#xf2;" u2="&#xaa;" k="92" />
    <hkern u1="&#xf2;" u2="&#x7d;" k="32" />
    <hkern u1="&#xf2;" u2="y" k="26" />
    <hkern u1="&#xf2;" u2="x" k="60" />
    <hkern u1="&#xf2;" u2="v" k="26" />
    <hkern u1="&#xf2;" u2="]" k="32" />
    <hkern u1="&#xf2;" u2="\" k="116" />
    <hkern u1="&#xf2;" u2="W" k="32" />
    <hkern u1="&#xf2;" u2="V" k="116" />
    <hkern u1="&#xf2;" u2="&#x2a;" k="92" />
    <hkern u1="&#xf2;" u2="&#x29;" k="32" />
    <hkern u1="&#xf2;" u2="&#x27;" k="92" />
    <hkern u1="&#xf2;" u2="&#x22;" k="92" />
    <hkern u1="&#xf3;" u2="&#x2122;" k="92" />
    <hkern u1="&#xf3;" u2="&#x201d;" k="92" />
    <hkern u1="&#xf3;" u2="&#x201c;" k="92" />
    <hkern u1="&#xf3;" u2="&#x2019;" k="92" />
    <hkern u1="&#xf3;" u2="&#x2018;" k="92" />
    <hkern u1="&#xf3;" u2="&#xba;" k="92" />
    <hkern u1="&#xf3;" u2="&#xb0;" k="92" />
    <hkern u1="&#xf3;" u2="&#xaa;" k="92" />
    <hkern u1="&#xf3;" u2="&#x7d;" k="32" />
    <hkern u1="&#xf3;" u2="y" k="26" />
    <hkern u1="&#xf3;" u2="x" k="60" />
    <hkern u1="&#xf3;" u2="v" k="26" />
    <hkern u1="&#xf3;" u2="]" k="32" />
    <hkern u1="&#xf3;" u2="\" k="116" />
    <hkern u1="&#xf3;" u2="W" k="32" />
    <hkern u1="&#xf3;" u2="V" k="116" />
    <hkern u1="&#xf3;" u2="&#x2a;" k="92" />
    <hkern u1="&#xf3;" u2="&#x29;" k="32" />
    <hkern u1="&#xf3;" u2="&#x27;" k="92" />
    <hkern u1="&#xf3;" u2="&#x22;" k="92" />
    <hkern u1="&#xf4;" u2="&#x2122;" k="92" />
    <hkern u1="&#xf4;" u2="&#x201d;" k="92" />
    <hkern u1="&#xf4;" u2="&#x201c;" k="92" />
    <hkern u1="&#xf4;" u2="&#x2019;" k="92" />
    <hkern u1="&#xf4;" u2="&#x2018;" k="92" />
    <hkern u1="&#xf4;" u2="&#xba;" k="92" />
    <hkern u1="&#xf4;" u2="&#xb0;" k="92" />
    <hkern u1="&#xf4;" u2="&#xaa;" k="92" />
    <hkern u1="&#xf4;" u2="&#x7d;" k="32" />
    <hkern u1="&#xf4;" u2="y" k="26" />
    <hkern u1="&#xf4;" u2="x" k="60" />
    <hkern u1="&#xf4;" u2="v" k="26" />
    <hkern u1="&#xf4;" u2="]" k="32" />
    <hkern u1="&#xf4;" u2="\" k="116" />
    <hkern u1="&#xf4;" u2="W" k="32" />
    <hkern u1="&#xf4;" u2="V" k="116" />
    <hkern u1="&#xf4;" u2="&#x2a;" k="92" />
    <hkern u1="&#xf4;" u2="&#x29;" k="32" />
    <hkern u1="&#xf4;" u2="&#x27;" k="92" />
    <hkern u1="&#xf4;" u2="&#x22;" k="92" />
    <hkern u1="&#xf5;" u2="&#x2122;" k="92" />
    <hkern u1="&#xf5;" u2="&#x201d;" k="92" />
    <hkern u1="&#xf5;" u2="&#x201c;" k="92" />
    <hkern u1="&#xf5;" u2="&#x2019;" k="92" />
    <hkern u1="&#xf5;" u2="&#x2018;" k="92" />
    <hkern u1="&#xf5;" u2="&#xba;" k="92" />
    <hkern u1="&#xf5;" u2="&#xb0;" k="92" />
    <hkern u1="&#xf5;" u2="&#xaa;" k="92" />
    <hkern u1="&#xf5;" u2="&#x7d;" k="32" />
    <hkern u1="&#xf5;" u2="y" k="26" />
    <hkern u1="&#xf5;" u2="x" k="60" />
    <hkern u1="&#xf5;" u2="v" k="26" />
    <hkern u1="&#xf5;" u2="]" k="32" />
    <hkern u1="&#xf5;" u2="\" k="116" />
    <hkern u1="&#xf5;" u2="W" k="32" />
    <hkern u1="&#xf5;" u2="V" k="116" />
    <hkern u1="&#xf5;" u2="&#x2a;" k="92" />
    <hkern u1="&#xf5;" u2="&#x29;" k="32" />
    <hkern u1="&#xf5;" u2="&#x27;" k="92" />
    <hkern u1="&#xf5;" u2="&#x22;" k="92" />
    <hkern u1="&#xf6;" u2="&#x2122;" k="92" />
    <hkern u1="&#xf6;" u2="&#x201d;" k="92" />
    <hkern u1="&#xf6;" u2="&#x201c;" k="92" />
    <hkern u1="&#xf6;" u2="&#x2019;" k="92" />
    <hkern u1="&#xf6;" u2="&#x2018;" k="92" />
    <hkern u1="&#xf6;" u2="&#xba;" k="92" />
    <hkern u1="&#xf6;" u2="&#xb0;" k="92" />
    <hkern u1="&#xf6;" u2="&#xaa;" k="92" />
    <hkern u1="&#xf6;" u2="&#x7d;" k="32" />
    <hkern u1="&#xf6;" u2="y" k="26" />
    <hkern u1="&#xf6;" u2="x" k="60" />
    <hkern u1="&#xf6;" u2="v" k="26" />
    <hkern u1="&#xf6;" u2="]" k="32" />
    <hkern u1="&#xf6;" u2="\" k="116" />
    <hkern u1="&#xf6;" u2="W" k="32" />
    <hkern u1="&#xf6;" u2="V" k="116" />
    <hkern u1="&#xf6;" u2="&#x2a;" k="92" />
    <hkern u1="&#xf6;" u2="&#x29;" k="32" />
    <hkern u1="&#xf6;" u2="&#x27;" k="92" />
    <hkern u1="&#xf6;" u2="&#x22;" k="92" />
    <hkern u1="&#xf8;" u2="&#x2122;" k="92" />
    <hkern u1="&#xf8;" u2="&#x201d;" k="92" />
    <hkern u1="&#xf8;" u2="&#x201c;" k="92" />
    <hkern u1="&#xf8;" u2="&#x2019;" k="92" />
    <hkern u1="&#xf8;" u2="&#x2018;" k="92" />
    <hkern u1="&#xf8;" u2="&#xba;" k="92" />
    <hkern u1="&#xf8;" u2="&#xb0;" k="92" />
    <hkern u1="&#xf8;" u2="&#xaa;" k="92" />
    <hkern u1="&#xf8;" u2="&#x7d;" k="32" />
    <hkern u1="&#xf8;" u2="y" k="26" />
    <hkern u1="&#xf8;" u2="x" k="60" />
    <hkern u1="&#xf8;" u2="v" k="26" />
    <hkern u1="&#xf8;" u2="]" k="32" />
    <hkern u1="&#xf8;" u2="\" k="116" />
    <hkern u1="&#xf8;" u2="W" k="32" />
    <hkern u1="&#xf8;" u2="V" k="116" />
    <hkern u1="&#xf8;" u2="&#x2a;" k="92" />
    <hkern u1="&#xf8;" u2="&#x29;" k="32" />
    <hkern u1="&#xf8;" u2="&#x27;" k="92" />
    <hkern u1="&#xf8;" u2="&#x22;" k="92" />
    <hkern u1="&#xfe;" u2="&#x2122;" k="92" />
    <hkern u1="&#xfe;" u2="&#x201d;" k="92" />
    <hkern u1="&#xfe;" u2="&#x201c;" k="92" />
    <hkern u1="&#xfe;" u2="&#x2019;" k="92" />
    <hkern u1="&#xfe;" u2="&#x2018;" k="92" />
    <hkern u1="&#xfe;" u2="&#xba;" k="92" />
    <hkern u1="&#xfe;" u2="&#xb0;" k="92" />
    <hkern u1="&#xfe;" u2="&#xaa;" k="92" />
    <hkern u1="&#xfe;" u2="&#x7d;" k="32" />
    <hkern u1="&#xfe;" u2="y" k="26" />
    <hkern u1="&#xfe;" u2="x" k="60" />
    <hkern u1="&#xfe;" u2="v" k="26" />
    <hkern u1="&#xfe;" u2="]" k="32" />
    <hkern u1="&#xfe;" u2="\" k="116" />
    <hkern u1="&#xfe;" u2="W" k="32" />
    <hkern u1="&#xfe;" u2="V" k="116" />
    <hkern u1="&#xfe;" u2="&#x2a;" k="92" />
    <hkern u1="&#xfe;" u2="&#x29;" k="32" />
    <hkern u1="&#xfe;" u2="&#x27;" k="92" />
    <hkern u1="&#xfe;" u2="&#x22;" k="92" />
    <hkern u1="&#x104;" u2="&#x2122;" k="182" />
    <hkern u1="&#x104;" u2="&#x203a;" k="52" />
    <hkern u1="&#x104;" u2="&#x2039;" k="52" />
    <hkern u1="&#x104;" u2="&#x2022;" k="52" />
    <hkern u1="&#x104;" u2="&#x201d;" k="182" />
    <hkern u1="&#x104;" u2="&#x201c;" k="182" />
    <hkern u1="&#x104;" u2="&#x2019;" k="182" />
    <hkern u1="&#x104;" u2="&#x2018;" k="182" />
    <hkern u1="&#x104;" u2="&#x2014;" k="52" />
    <hkern u1="&#x104;" u2="&#x2013;" k="52" />
    <hkern u1="&#x104;" u2="&#x178;" k="164" />
    <hkern u1="&#x104;" u2="&#x152;" k="42" />
    <hkern u1="&#x104;" u2="&#x106;" k="42" />
    <hkern u1="&#x104;" u2="&#xdd;" k="164" />
    <hkern u1="&#x104;" u2="&#xdc;" k="56" />
    <hkern u1="&#x104;" u2="&#xdb;" k="56" />
    <hkern u1="&#x104;" u2="&#xda;" k="56" />
    <hkern u1="&#x104;" u2="&#xd9;" k="56" />
    <hkern u1="&#x104;" u2="&#xd8;" k="42" />
    <hkern u1="&#x104;" u2="&#xd6;" k="42" />
    <hkern u1="&#x104;" u2="&#xd5;" k="42" />
    <hkern u1="&#x104;" u2="&#xd4;" k="42" />
    <hkern u1="&#x104;" u2="&#xd3;" k="42" />
    <hkern u1="&#x104;" u2="&#xd2;" k="42" />
    <hkern u1="&#x104;" u2="&#xc7;" k="42" />
    <hkern u1="&#x104;" u2="&#xbb;" k="52" />
    <hkern u1="&#x104;" u2="&#xba;" k="182" />
    <hkern u1="&#x104;" u2="&#xb9;" k="184" />
    <hkern u1="&#x104;" u2="&#xb7;" k="52" />
    <hkern u1="&#x104;" u2="&#xb3;" k="184" />
    <hkern u1="&#x104;" u2="&#xb2;" k="184" />
    <hkern u1="&#x104;" u2="&#xb0;" k="182" />
    <hkern u1="&#x104;" u2="&#xad;" k="52" />
    <hkern u1="&#x104;" u2="&#xab;" k="52" />
    <hkern u1="&#x104;" u2="&#xaa;" k="182" />
    <hkern u1="&#x104;" u2="y" k="82" />
    <hkern u1="&#x104;" u2="v" k="82" />
    <hkern u1="&#x104;" u2="\" k="136" />
    <hkern u1="&#x104;" u2="Y" k="164" />
    <hkern u1="&#x104;" u2="W" k="84" />
    <hkern u1="&#x104;" u2="V" k="136" />
    <hkern u1="&#x104;" u2="U" k="56" />
    <hkern u1="&#x104;" u2="T" k="132" />
    <hkern u1="&#x104;" u2="Q" k="42" />
    <hkern u1="&#x104;" u2="O" k="42" />
    <hkern u1="&#x104;" u2="J" k="-50" />
    <hkern u1="&#x104;" u2="G" k="42" />
    <hkern u1="&#x104;" u2="C" k="42" />
    <hkern u1="&#x104;" u2="&#x40;" k="42" />
    <hkern u1="&#x104;" u2="&#x3f;" k="56" />
    <hkern u1="&#x104;" u2="&#x2d;" k="52" />
    <hkern u1="&#x104;" u2="&#x2a;" k="182" />
    <hkern u1="&#x104;" u2="&#x27;" k="182" />
    <hkern u1="&#x104;" u2="&#x22;" k="182" />
    <hkern u1="&#x105;" u2="&#x2122;" k="72" />
    <hkern u1="&#x105;" u2="&#x201d;" k="72" />
    <hkern u1="&#x105;" u2="&#x201c;" k="72" />
    <hkern u1="&#x105;" u2="&#x2019;" k="72" />
    <hkern u1="&#x105;" u2="&#x2018;" k="72" />
    <hkern u1="&#x105;" u2="&#xba;" k="72" />
    <hkern u1="&#x105;" u2="&#xb9;" k="72" />
    <hkern u1="&#x105;" u2="&#xb3;" k="72" />
    <hkern u1="&#x105;" u2="&#xb2;" k="72" />
    <hkern u1="&#x105;" u2="&#xb0;" k="72" />
    <hkern u1="&#x105;" u2="&#xaa;" k="72" />
    <hkern u1="&#x105;" u2="y" k="32" />
    <hkern u1="&#x105;" u2="w" k="16" />
    <hkern u1="&#x105;" u2="v" k="32" />
    <hkern u1="&#x105;" u2="&#x2a;" k="72" />
    <hkern u1="&#x105;" u2="&#x27;" k="72" />
    <hkern u1="&#x105;" u2="&#x22;" k="72" />
    <hkern u1="&#x106;" u2="&#x203a;" k="150" />
    <hkern u1="&#x106;" u2="&#x2039;" k="150" />
    <hkern u1="&#x106;" u2="&#x2022;" k="150" />
    <hkern u1="&#x106;" u2="&#x2014;" k="150" />
    <hkern u1="&#x106;" u2="&#x2013;" k="150" />
    <hkern u1="&#x106;" u2="&#xbb;" k="150" />
    <hkern u1="&#x106;" u2="&#xb7;" k="150" />
    <hkern u1="&#x106;" u2="&#xad;" k="150" />
    <hkern u1="&#x106;" u2="&#xab;" k="150" />
    <hkern u1="&#x106;" u2="&#x2d;" k="150" />
    <hkern u1="&#x119;" u2="&#x2122;" k="92" />
    <hkern u1="&#x119;" u2="&#x201d;" k="92" />
    <hkern u1="&#x119;" u2="&#x201c;" k="92" />
    <hkern u1="&#x119;" u2="&#x2019;" k="92" />
    <hkern u1="&#x119;" u2="&#x2018;" k="92" />
    <hkern u1="&#x119;" u2="&#xba;" k="92" />
    <hkern u1="&#x119;" u2="&#xb0;" k="92" />
    <hkern u1="&#x119;" u2="&#xaa;" k="92" />
    <hkern u1="&#x119;" u2="&#x7d;" k="32" />
    <hkern u1="&#x119;" u2="y" k="26" />
    <hkern u1="&#x119;" u2="x" k="60" />
    <hkern u1="&#x119;" u2="v" k="26" />
    <hkern u1="&#x119;" u2="]" k="32" />
    <hkern u1="&#x119;" u2="\" k="116" />
    <hkern u1="&#x119;" u2="W" k="32" />
    <hkern u1="&#x119;" u2="V" k="116" />
    <hkern u1="&#x119;" u2="&#x2a;" k="92" />
    <hkern u1="&#x119;" u2="&#x29;" k="32" />
    <hkern u1="&#x119;" u2="&#x27;" k="92" />
    <hkern u1="&#x119;" u2="&#x22;" k="92" />
    <hkern u1="&#x141;" u2="&#x2122;" k="140" />
    <hkern u1="&#x141;" u2="&#x203a;" k="128" />
    <hkern u1="&#x141;" u2="&#x2039;" k="128" />
    <hkern u1="&#x141;" u2="&#x2022;" k="128" />
    <hkern u1="&#x141;" u2="&#x201d;" k="140" />
    <hkern u1="&#x141;" u2="&#x201c;" k="140" />
    <hkern u1="&#x141;" u2="&#x2019;" k="140" />
    <hkern u1="&#x141;" u2="&#x2018;" k="140" />
    <hkern u1="&#x141;" u2="&#x2014;" k="128" />
    <hkern u1="&#x141;" u2="&#x2013;" k="128" />
    <hkern u1="&#x141;" u2="&#x178;" k="152" />
    <hkern u1="&#x141;" u2="&#xdd;" k="152" />
    <hkern u1="&#x141;" u2="&#xbb;" k="128" />
    <hkern u1="&#x141;" u2="&#xba;" k="140" />
    <hkern u1="&#x141;" u2="&#xb9;" k="132" />
    <hkern u1="&#x141;" u2="&#xb7;" k="128" />
    <hkern u1="&#x141;" u2="&#xb3;" k="132" />
    <hkern u1="&#x141;" u2="&#xb2;" k="132" />
    <hkern u1="&#x141;" u2="&#xb0;" k="140" />
    <hkern u1="&#x141;" u2="&#xad;" k="128" />
    <hkern u1="&#x141;" u2="&#xab;" k="128" />
    <hkern u1="&#x141;" u2="&#xaa;" k="140" />
    <hkern u1="&#x141;" u2="y" k="56" />
    <hkern u1="&#x141;" u2="v" k="56" />
    <hkern u1="&#x141;" u2="\" k="162" />
    <hkern u1="&#x141;" u2="Y" k="152" />
    <hkern u1="&#x141;" u2="W" k="122" />
    <hkern u1="&#x141;" u2="V" k="162" />
    <hkern u1="&#x141;" u2="&#x2d;" k="128" />
    <hkern u1="&#x141;" u2="&#x2a;" k="140" />
    <hkern u1="&#x141;" u2="&#x27;" k="140" />
    <hkern u1="&#x141;" u2="&#x22;" k="140" />
    <hkern u1="&#x144;" u2="&#x2122;" k="72" />
    <hkern u1="&#x144;" u2="&#x201d;" k="72" />
    <hkern u1="&#x144;" u2="&#x201c;" k="72" />
    <hkern u1="&#x144;" u2="&#x2019;" k="72" />
    <hkern u1="&#x144;" u2="&#x2018;" k="72" />
    <hkern u1="&#x144;" u2="&#xba;" k="72" />
    <hkern u1="&#x144;" u2="&#xb9;" k="72" />
    <hkern u1="&#x144;" u2="&#xb3;" k="72" />
    <hkern u1="&#x144;" u2="&#xb2;" k="72" />
    <hkern u1="&#x144;" u2="&#xb0;" k="72" />
    <hkern u1="&#x144;" u2="&#xaa;" k="72" />
    <hkern u1="&#x144;" u2="y" k="32" />
    <hkern u1="&#x144;" u2="w" k="16" />
    <hkern u1="&#x144;" u2="v" k="32" />
    <hkern u1="&#x144;" u2="&#x2a;" k="72" />
    <hkern u1="&#x144;" u2="&#x27;" k="72" />
    <hkern u1="&#x144;" u2="&#x22;" k="72" />
    <hkern u1="&#x153;" u2="&#x2122;" k="92" />
    <hkern u1="&#x153;" u2="&#x201d;" k="92" />
    <hkern u1="&#x153;" u2="&#x201c;" k="92" />
    <hkern u1="&#x153;" u2="&#x2019;" k="92" />
    <hkern u1="&#x153;" u2="&#x2018;" k="92" />
    <hkern u1="&#x153;" u2="&#xba;" k="92" />
    <hkern u1="&#x153;" u2="&#xb0;" k="92" />
    <hkern u1="&#x153;" u2="&#xaa;" k="92" />
    <hkern u1="&#x153;" u2="&#x7d;" k="32" />
    <hkern u1="&#x153;" u2="y" k="26" />
    <hkern u1="&#x153;" u2="x" k="60" />
    <hkern u1="&#x153;" u2="v" k="26" />
    <hkern u1="&#x153;" u2="]" k="32" />
    <hkern u1="&#x153;" u2="\" k="116" />
    <hkern u1="&#x153;" u2="W" k="32" />
    <hkern u1="&#x153;" u2="V" k="116" />
    <hkern u1="&#x153;" u2="&#x2a;" k="92" />
    <hkern u1="&#x153;" u2="&#x29;" k="32" />
    <hkern u1="&#x153;" u2="&#x27;" k="92" />
    <hkern u1="&#x153;" u2="&#x22;" k="92" />
    <hkern u1="&#x178;" u2="&#x2206;" k="164" />
    <hkern u1="&#x178;" u2="&#x2122;" k="-30" />
    <hkern u1="&#x178;" u2="&#x203a;" k="160" />
    <hkern u1="&#x178;" u2="&#x2039;" k="160" />
    <hkern u1="&#x178;" u2="&#x2022;" k="160" />
    <hkern u1="&#x178;" u2="&#x201e;" k="152" />
    <hkern u1="&#x178;" u2="&#x201d;" k="-30" />
    <hkern u1="&#x178;" u2="&#x201c;" k="-30" />
    <hkern u1="&#x178;" u2="&#x201a;" k="152" />
    <hkern u1="&#x178;" u2="&#x2019;" k="-30" />
    <hkern u1="&#x178;" u2="&#x2018;" k="-30" />
    <hkern u1="&#x178;" u2="&#x2014;" k="160" />
    <hkern u1="&#x178;" u2="&#x2013;" k="160" />
    <hkern u1="&#x178;" u2="&#x153;" k="160" />
    <hkern u1="&#x178;" u2="&#x152;" k="80" />
    <hkern u1="&#x178;" u2="&#x144;" k="122" />
    <hkern u1="&#x178;" u2="&#x119;" k="160" />
    <hkern u1="&#x178;" u2="&#x107;" k="160" />
    <hkern u1="&#x178;" u2="&#x106;" k="80" />
    <hkern u1="&#x178;" u2="&#x105;" k="128" />
    <hkern u1="&#x178;" u2="&#x104;" k="164" />
    <hkern u1="&#x178;" u2="&#xfc;" k="122" />
    <hkern u1="&#x178;" u2="&#xfb;" k="122" />
    <hkern u1="&#x178;" u2="&#xfa;" k="122" />
    <hkern u1="&#x178;" u2="&#xf9;" k="122" />
    <hkern u1="&#x178;" u2="&#xf8;" k="160" />
    <hkern u1="&#x178;" u2="&#xf6;" k="160" />
    <hkern u1="&#x178;" u2="&#xf5;" k="160" />
    <hkern u1="&#x178;" u2="&#xf4;" k="160" />
    <hkern u1="&#x178;" u2="&#xf3;" k="160" />
    <hkern u1="&#x178;" u2="&#xf2;" k="160" />
    <hkern u1="&#x178;" u2="&#xf1;" k="122" />
    <hkern u1="&#x178;" u2="&#xf0;" k="160" />
    <hkern u1="&#x178;" u2="&#xeb;" k="160" />
    <hkern u1="&#x178;" u2="&#xea;" k="160" />
    <hkern u1="&#x178;" u2="&#xe9;" k="160" />
    <hkern u1="&#x178;" u2="&#xe8;" k="160" />
    <hkern u1="&#x178;" u2="&#xe7;" k="160" />
    <hkern u1="&#x178;" u2="&#xe6;" k="128" />
    <hkern u1="&#x178;" u2="&#xe5;" k="128" />
    <hkern u1="&#x178;" u2="&#xe4;" k="128" />
    <hkern u1="&#x178;" u2="&#xe3;" k="128" />
    <hkern u1="&#x178;" u2="&#xe2;" k="128" />
    <hkern u1="&#x178;" u2="&#xe1;" k="128" />
    <hkern u1="&#x178;" u2="&#xe0;" k="128" />
    <hkern u1="&#x178;" u2="&#xd8;" k="80" />
    <hkern u1="&#x178;" u2="&#xd6;" k="80" />
    <hkern u1="&#x178;" u2="&#xd5;" k="80" />
    <hkern u1="&#x178;" u2="&#xd4;" k="80" />
    <hkern u1="&#x178;" u2="&#xd3;" k="80" />
    <hkern u1="&#x178;" u2="&#xd2;" k="80" />
    <hkern u1="&#x178;" u2="&#xc7;" k="80" />
    <hkern u1="&#x178;" u2="&#xc6;" k="164" />
    <hkern u1="&#x178;" u2="&#xc5;" k="164" />
    <hkern u1="&#x178;" u2="&#xc4;" k="164" />
    <hkern u1="&#x178;" u2="&#xc3;" k="164" />
    <hkern u1="&#x178;" u2="&#xc2;" k="164" />
    <hkern u1="&#x178;" u2="&#xc1;" k="164" />
    <hkern u1="&#x178;" u2="&#xc0;" k="164" />
    <hkern u1="&#x178;" u2="&#xbb;" k="160" />
    <hkern u1="&#x178;" u2="&#xba;" k="-30" />
    <hkern u1="&#x178;" u2="&#xb9;" k="-50" />
    <hkern u1="&#x178;" u2="&#xb7;" k="160" />
    <hkern u1="&#x178;" u2="&#xb5;" k="122" />
    <hkern u1="&#x178;" u2="&#xb3;" k="-50" />
    <hkern u1="&#x178;" u2="&#xb2;" k="-50" />
    <hkern u1="&#x178;" u2="&#xb0;" k="-30" />
    <hkern u1="&#x178;" u2="&#xad;" k="160" />
    <hkern u1="&#x178;" u2="&#xab;" k="160" />
    <hkern u1="&#x178;" u2="&#xaa;" k="-30" />
    <hkern u1="&#x178;" u2="y" k="100" />
    <hkern u1="&#x178;" u2="x" k="132" />
    <hkern u1="&#x178;" u2="w" k="92" />
    <hkern u1="&#x178;" u2="v" k="100" />
    <hkern u1="&#x178;" u2="u" k="122" />
    <hkern u1="&#x178;" u2="s" k="128" />
    <hkern u1="&#x178;" u2="r" k="122" />
    <hkern u1="&#x178;" u2="q" k="160" />
    <hkern u1="&#x178;" u2="p" k="122" />
    <hkern u1="&#x178;" u2="o" k="160" />
    <hkern u1="&#x178;" u2="n" k="122" />
    <hkern u1="&#x178;" u2="m" k="122" />
    <hkern u1="&#x178;" u2="g" k="172" />
    <hkern u1="&#x178;" u2="e" k="160" />
    <hkern u1="&#x178;" u2="d" k="160" />
    <hkern u1="&#x178;" u2="c" k="160" />
    <hkern u1="&#x178;" u2="a" k="128" />
    <hkern u1="&#x178;" u2="Q" k="80" />
    <hkern u1="&#x178;" u2="O" k="80" />
    <hkern u1="&#x178;" u2="J" k="200" />
    <hkern u1="&#x178;" u2="G" k="80" />
    <hkern u1="&#x178;" u2="C" k="80" />
    <hkern u1="&#x178;" u2="A" k="164" />
    <hkern u1="&#x178;" u2="&#x40;" k="80" />
    <hkern u1="&#x178;" u2="&#x3f;" k="-34" />
    <hkern u1="&#x178;" u2="&#x3b;" k="122" />
    <hkern u1="&#x178;" u2="&#x3a;" k="122" />
    <hkern u1="&#x178;" u2="&#x2f;" k="164" />
    <hkern u1="&#x178;" u2="&#x2e;" k="152" />
    <hkern u1="&#x178;" u2="&#x2d;" k="160" />
    <hkern u1="&#x178;" u2="&#x2c;" k="152" />
    <hkern u1="&#x178;" u2="&#x2a;" k="-30" />
    <hkern u1="&#x178;" u2="&#x27;" k="-30" />
    <hkern u1="&#x178;" u2="&#x26;" k="164" />
    <hkern u1="&#x178;" u2="&#x22;" k="-30" />
    <hkern u1="&#x179;" u2="&#x203a;" k="70" />
    <hkern u1="&#x179;" u2="&#x2039;" k="70" />
    <hkern u1="&#x179;" u2="&#x2022;" k="70" />
    <hkern u1="&#x179;" u2="&#x2014;" k="70" />
    <hkern u1="&#x179;" u2="&#x2013;" k="70" />
    <hkern u1="&#x179;" u2="&#x153;" k="38" />
    <hkern u1="&#x179;" u2="&#x152;" k="58" />
    <hkern u1="&#x179;" u2="&#x119;" k="38" />
    <hkern u1="&#x179;" u2="&#x107;" k="38" />
    <hkern u1="&#x179;" u2="&#x106;" k="58" />
    <hkern u1="&#x179;" u2="&#xf8;" k="38" />
    <hkern u1="&#x179;" u2="&#xf6;" k="38" />
    <hkern u1="&#x179;" u2="&#xf5;" k="38" />
    <hkern u1="&#x179;" u2="&#xf4;" k="38" />
    <hkern u1="&#x179;" u2="&#xf3;" k="38" />
    <hkern u1="&#x179;" u2="&#xf2;" k="38" />
    <hkern u1="&#x179;" u2="&#xf0;" k="38" />
    <hkern u1="&#x179;" u2="&#xeb;" k="38" />
    <hkern u1="&#x179;" u2="&#xea;" k="38" />
    <hkern u1="&#x179;" u2="&#xe9;" k="38" />
    <hkern u1="&#x179;" u2="&#xe8;" k="38" />
    <hkern u1="&#x179;" u2="&#xe7;" k="38" />
    <hkern u1="&#x179;" u2="&#xd8;" k="58" />
    <hkern u1="&#x179;" u2="&#xd6;" k="58" />
    <hkern u1="&#x179;" u2="&#xd5;" k="58" />
    <hkern u1="&#x179;" u2="&#xd4;" k="58" />
    <hkern u1="&#x179;" u2="&#xd3;" k="58" />
    <hkern u1="&#x179;" u2="&#xd2;" k="58" />
    <hkern u1="&#x179;" u2="&#xc7;" k="58" />
    <hkern u1="&#x179;" u2="&#xbb;" k="70" />
    <hkern u1="&#x179;" u2="&#xb7;" k="70" />
    <hkern u1="&#x179;" u2="&#xad;" k="70" />
    <hkern u1="&#x179;" u2="&#xab;" k="70" />
    <hkern u1="&#x179;" u2="y" k="40" />
    <hkern u1="&#x179;" u2="v" k="40" />
    <hkern u1="&#x179;" u2="s" k="28" />
    <hkern u1="&#x179;" u2="q" k="38" />
    <hkern u1="&#x179;" u2="o" k="38" />
    <hkern u1="&#x179;" u2="e" k="38" />
    <hkern u1="&#x179;" u2="d" k="38" />
    <hkern u1="&#x179;" u2="c" k="38" />
    <hkern u1="&#x179;" u2="Q" k="58" />
    <hkern u1="&#x179;" u2="O" k="58" />
    <hkern u1="&#x179;" u2="G" k="58" />
    <hkern u1="&#x179;" u2="C" k="58" />
    <hkern u1="&#x179;" u2="&#x40;" k="58" />
    <hkern u1="&#x179;" u2="&#x3f;" k="-34" />
    <hkern u1="&#x179;" u2="&#x2d;" k="70" />
    <hkern u1="&#x17b;" u2="&#x203a;" k="70" />
    <hkern u1="&#x17b;" u2="&#x2039;" k="70" />
    <hkern u1="&#x17b;" u2="&#x2022;" k="70" />
    <hkern u1="&#x17b;" u2="&#x2014;" k="70" />
    <hkern u1="&#x17b;" u2="&#x2013;" k="70" />
    <hkern u1="&#x17b;" u2="&#x153;" k="38" />
    <hkern u1="&#x17b;" u2="&#x152;" k="58" />
    <hkern u1="&#x17b;" u2="&#x119;" k="38" />
    <hkern u1="&#x17b;" u2="&#x107;" k="38" />
    <hkern u1="&#x17b;" u2="&#x106;" k="58" />
    <hkern u1="&#x17b;" u2="&#xf8;" k="38" />
    <hkern u1="&#x17b;" u2="&#xf6;" k="38" />
    <hkern u1="&#x17b;" u2="&#xf5;" k="38" />
    <hkern u1="&#x17b;" u2="&#xf4;" k="38" />
    <hkern u1="&#x17b;" u2="&#xf3;" k="38" />
    <hkern u1="&#x17b;" u2="&#xf2;" k="38" />
    <hkern u1="&#x17b;" u2="&#xf0;" k="38" />
    <hkern u1="&#x17b;" u2="&#xeb;" k="38" />
    <hkern u1="&#x17b;" u2="&#xea;" k="38" />
    <hkern u1="&#x17b;" u2="&#xe9;" k="38" />
    <hkern u1="&#x17b;" u2="&#xe8;" k="38" />
    <hkern u1="&#x17b;" u2="&#xe7;" k="38" />
    <hkern u1="&#x17b;" u2="&#xd8;" k="58" />
    <hkern u1="&#x17b;" u2="&#xd6;" k="58" />
    <hkern u1="&#x17b;" u2="&#xd5;" k="58" />
    <hkern u1="&#x17b;" u2="&#xd4;" k="58" />
    <hkern u1="&#x17b;" u2="&#xd3;" k="58" />
    <hkern u1="&#x17b;" u2="&#xd2;" k="58" />
    <hkern u1="&#x17b;" u2="&#xc7;" k="58" />
    <hkern u1="&#x17b;" u2="&#xbb;" k="70" />
    <hkern u1="&#x17b;" u2="&#xb7;" k="70" />
    <hkern u1="&#x17b;" u2="&#xad;" k="70" />
    <hkern u1="&#x17b;" u2="&#xab;" k="70" />
    <hkern u1="&#x17b;" u2="y" k="40" />
    <hkern u1="&#x17b;" u2="v" k="40" />
    <hkern u1="&#x17b;" u2="s" k="28" />
    <hkern u1="&#x17b;" u2="q" k="38" />
    <hkern u1="&#x17b;" u2="o" k="38" />
    <hkern u1="&#x17b;" u2="e" k="38" />
    <hkern u1="&#x17b;" u2="d" k="38" />
    <hkern u1="&#x17b;" u2="c" k="38" />
    <hkern u1="&#x17b;" u2="Q" k="58" />
    <hkern u1="&#x17b;" u2="O" k="58" />
    <hkern u1="&#x17b;" u2="G" k="58" />
    <hkern u1="&#x17b;" u2="C" k="58" />
    <hkern u1="&#x17b;" u2="&#x40;" k="58" />
    <hkern u1="&#x17b;" u2="&#x3f;" k="-34" />
    <hkern u1="&#x17b;" u2="&#x2d;" k="70" />
    <hkern u1="&#x17d;" u2="&#x203a;" k="70" />
    <hkern u1="&#x17d;" u2="&#x2039;" k="70" />
    <hkern u1="&#x17d;" u2="&#x2022;" k="70" />
    <hkern u1="&#x17d;" u2="&#x2014;" k="70" />
    <hkern u1="&#x17d;" u2="&#x2013;" k="70" />
    <hkern u1="&#x17d;" u2="&#x153;" k="38" />
    <hkern u1="&#x17d;" u2="&#x152;" k="58" />
    <hkern u1="&#x17d;" u2="&#x119;" k="38" />
    <hkern u1="&#x17d;" u2="&#x107;" k="38" />
    <hkern u1="&#x17d;" u2="&#x106;" k="58" />
    <hkern u1="&#x17d;" u2="&#xf8;" k="38" />
    <hkern u1="&#x17d;" u2="&#xf6;" k="38" />
    <hkern u1="&#x17d;" u2="&#xf5;" k="38" />
    <hkern u1="&#x17d;" u2="&#xf4;" k="38" />
    <hkern u1="&#x17d;" u2="&#xf3;" k="38" />
    <hkern u1="&#x17d;" u2="&#xf2;" k="38" />
    <hkern u1="&#x17d;" u2="&#xf0;" k="38" />
    <hkern u1="&#x17d;" u2="&#xeb;" k="38" />
    <hkern u1="&#x17d;" u2="&#xea;" k="38" />
    <hkern u1="&#x17d;" u2="&#xe9;" k="38" />
    <hkern u1="&#x17d;" u2="&#xe8;" k="38" />
    <hkern u1="&#x17d;" u2="&#xe7;" k="38" />
    <hkern u1="&#x17d;" u2="&#xd8;" k="58" />
    <hkern u1="&#x17d;" u2="&#xd6;" k="58" />
    <hkern u1="&#x17d;" u2="&#xd5;" k="58" />
    <hkern u1="&#x17d;" u2="&#xd4;" k="58" />
    <hkern u1="&#x17d;" u2="&#xd3;" k="58" />
    <hkern u1="&#x17d;" u2="&#xd2;" k="58" />
    <hkern u1="&#x17d;" u2="&#xc7;" k="58" />
    <hkern u1="&#x17d;" u2="&#xbb;" k="70" />
    <hkern u1="&#x17d;" u2="&#xb7;" k="70" />
    <hkern u1="&#x17d;" u2="&#xad;" k="70" />
    <hkern u1="&#x17d;" u2="&#xab;" k="70" />
    <hkern u1="&#x17d;" u2="y" k="40" />
    <hkern u1="&#x17d;" u2="v" k="40" />
    <hkern u1="&#x17d;" u2="s" k="28" />
    <hkern u1="&#x17d;" u2="q" k="38" />
    <hkern u1="&#x17d;" u2="o" k="38" />
    <hkern u1="&#x17d;" u2="e" k="38" />
    <hkern u1="&#x17d;" u2="d" k="38" />
    <hkern u1="&#x17d;" u2="c" k="38" />
    <hkern u1="&#x17d;" u2="Q" k="58" />
    <hkern u1="&#x17d;" u2="O" k="58" />
    <hkern u1="&#x17d;" u2="G" k="58" />
    <hkern u1="&#x17d;" u2="C" k="58" />
    <hkern u1="&#x17d;" u2="&#x40;" k="58" />
    <hkern u1="&#x17d;" u2="&#x3f;" k="-34" />
    <hkern u1="&#x17d;" u2="&#x2d;" k="70" />
    <hkern u1="&#x2013;" u2="&#x2206;" k="52" />
    <hkern u1="&#x2013;" u2="&#x2122;" k="178" />
    <hkern u1="&#x2013;" u2="&#x201e;" k="136" />
    <hkern u1="&#x2013;" u2="&#x201d;" k="178" />
    <hkern u1="&#x2013;" u2="&#x201c;" k="178" />
    <hkern u1="&#x2013;" u2="&#x201a;" k="136" />
    <hkern u1="&#x2013;" u2="&#x2019;" k="178" />
    <hkern u1="&#x2013;" u2="&#x2018;" k="178" />
    <hkern u1="&#x2013;" u2="&#x17d;" k="46" />
    <hkern u1="&#x2013;" u2="&#x17b;" k="46" />
    <hkern u1="&#x2013;" u2="&#x179;" k="46" />
    <hkern u1="&#x2013;" u2="&#x178;" k="160" />
    <hkern u1="&#x2013;" u2="&#x104;" k="52" />
    <hkern u1="&#x2013;" u2="&#xdd;" k="160" />
    <hkern u1="&#x2013;" u2="&#xc6;" k="52" />
    <hkern u1="&#x2013;" u2="&#xc5;" k="52" />
    <hkern u1="&#x2013;" u2="&#xc4;" k="52" />
    <hkern u1="&#x2013;" u2="&#xc3;" k="52" />
    <hkern u1="&#x2013;" u2="&#xc2;" k="52" />
    <hkern u1="&#x2013;" u2="&#xc1;" k="52" />
    <hkern u1="&#x2013;" u2="&#xc0;" k="52" />
    <hkern u1="&#x2013;" u2="&#xba;" k="178" />
    <hkern u1="&#x2013;" u2="&#xb0;" k="178" />
    <hkern u1="&#x2013;" u2="&#xaa;" k="178" />
    <hkern u1="&#x2013;" u2="\" k="112" />
    <hkern u1="&#x2013;" u2="Z" k="46" />
    <hkern u1="&#x2013;" u2="Y" k="160" />
    <hkern u1="&#x2013;" u2="X" k="62" />
    <hkern u1="&#x2013;" u2="W" k="32" />
    <hkern u1="&#x2013;" u2="V" k="112" />
    <hkern u1="&#x2013;" u2="T" k="180" />
    <hkern u1="&#x2013;" u2="A" k="52" />
    <hkern u1="&#x2013;" u2="&#x2f;" k="52" />
    <hkern u1="&#x2013;" u2="&#x2e;" k="136" />
    <hkern u1="&#x2013;" u2="&#x2c;" k="136" />
    <hkern u1="&#x2013;" u2="&#x2a;" k="178" />
    <hkern u1="&#x2013;" u2="&#x27;" k="178" />
    <hkern u1="&#x2013;" u2="&#x26;" k="52" />
    <hkern u1="&#x2013;" u2="&#x22;" k="178" />
    <hkern u1="&#x2014;" u2="&#x2206;" k="52" />
    <hkern u1="&#x2014;" u2="&#x2122;" k="178" />
    <hkern u1="&#x2014;" u2="&#x201e;" k="136" />
    <hkern u1="&#x2014;" u2="&#x201d;" k="178" />
    <hkern u1="&#x2014;" u2="&#x201c;" k="178" />
    <hkern u1="&#x2014;" u2="&#x201a;" k="136" />
    <hkern u1="&#x2014;" u2="&#x2019;" k="178" />
    <hkern u1="&#x2014;" u2="&#x2018;" k="178" />
    <hkern u1="&#x2014;" u2="&#x17d;" k="46" />
    <hkern u1="&#x2014;" u2="&#x17b;" k="46" />
    <hkern u1="&#x2014;" u2="&#x179;" k="46" />
    <hkern u1="&#x2014;" u2="&#x178;" k="160" />
    <hkern u1="&#x2014;" u2="&#x104;" k="52" />
    <hkern u1="&#x2014;" u2="&#xdd;" k="160" />
    <hkern u1="&#x2014;" u2="&#xc6;" k="52" />
    <hkern u1="&#x2014;" u2="&#xc5;" k="52" />
    <hkern u1="&#x2014;" u2="&#xc4;" k="52" />
    <hkern u1="&#x2014;" u2="&#xc3;" k="52" />
    <hkern u1="&#x2014;" u2="&#xc2;" k="52" />
    <hkern u1="&#x2014;" u2="&#xc1;" k="52" />
    <hkern u1="&#x2014;" u2="&#xc0;" k="52" />
    <hkern u1="&#x2014;" u2="&#xba;" k="178" />
    <hkern u1="&#x2014;" u2="&#xb0;" k="178" />
    <hkern u1="&#x2014;" u2="&#xaa;" k="178" />
    <hkern u1="&#x2014;" u2="\" k="112" />
    <hkern u1="&#x2014;" u2="Z" k="46" />
    <hkern u1="&#x2014;" u2="Y" k="160" />
    <hkern u1="&#x2014;" u2="X" k="62" />
    <hkern u1="&#x2014;" u2="W" k="32" />
    <hkern u1="&#x2014;" u2="V" k="112" />
    <hkern u1="&#x2014;" u2="T" k="180" />
    <hkern u1="&#x2014;" u2="A" k="52" />
    <hkern u1="&#x2014;" u2="&#x2f;" k="52" />
    <hkern u1="&#x2014;" u2="&#x2e;" k="136" />
    <hkern u1="&#x2014;" u2="&#x2c;" k="136" />
    <hkern u1="&#x2014;" u2="&#x2a;" k="178" />
    <hkern u1="&#x2014;" u2="&#x27;" k="178" />
    <hkern u1="&#x2014;" u2="&#x26;" k="52" />
    <hkern u1="&#x2014;" u2="&#x22;" k="178" />
    <hkern u1="&#x2018;" u2="&#x2206;" k="182" />
    <hkern u1="&#x2018;" u2="&#x203a;" k="178" />
    <hkern u1="&#x2018;" u2="&#x2039;" k="178" />
    <hkern u1="&#x2018;" u2="&#x2022;" k="178" />
    <hkern u1="&#x2018;" u2="&#x201e;" k="228" />
    <hkern u1="&#x2018;" u2="&#x201a;" k="228" />
    <hkern u1="&#x2018;" u2="&#x2014;" k="178" />
    <hkern u1="&#x2018;" u2="&#x2013;" k="178" />
    <hkern u1="&#x2018;" u2="&#x178;" k="-30" />
    <hkern u1="&#x2018;" u2="&#x153;" k="92" />
    <hkern u1="&#x2018;" u2="&#x152;" k="46" />
    <hkern u1="&#x2018;" u2="&#x119;" k="92" />
    <hkern u1="&#x2018;" u2="&#x107;" k="92" />
    <hkern u1="&#x2018;" u2="&#x106;" k="46" />
    <hkern u1="&#x2018;" u2="&#x105;" k="64" />
    <hkern u1="&#x2018;" u2="&#x104;" k="182" />
    <hkern u1="&#x2018;" u2="&#xf8;" k="92" />
    <hkern u1="&#x2018;" u2="&#xf6;" k="92" />
    <hkern u1="&#x2018;" u2="&#xf5;" k="92" />
    <hkern u1="&#x2018;" u2="&#xf4;" k="92" />
    <hkern u1="&#x2018;" u2="&#xf3;" k="92" />
    <hkern u1="&#x2018;" u2="&#xf2;" k="92" />
    <hkern u1="&#x2018;" u2="&#xf0;" k="92" />
    <hkern u1="&#x2018;" u2="&#xeb;" k="92" />
    <hkern u1="&#x2018;" u2="&#xea;" k="92" />
    <hkern u1="&#x2018;" u2="&#xe9;" k="92" />
    <hkern u1="&#x2018;" u2="&#xe8;" k="92" />
    <hkern u1="&#x2018;" u2="&#xe7;" k="92" />
    <hkern u1="&#x2018;" u2="&#xe6;" k="64" />
    <hkern u1="&#x2018;" u2="&#xe5;" k="64" />
    <hkern u1="&#x2018;" u2="&#xe4;" k="64" />
    <hkern u1="&#x2018;" u2="&#xe3;" k="64" />
    <hkern u1="&#x2018;" u2="&#xe2;" k="64" />
    <hkern u1="&#x2018;" u2="&#xe1;" k="64" />
    <hkern u1="&#x2018;" u2="&#xe0;" k="64" />
    <hkern u1="&#x2018;" u2="&#xdd;" k="-30" />
    <hkern u1="&#x2018;" u2="&#xd8;" k="46" />
    <hkern u1="&#x2018;" u2="&#xd6;" k="46" />
    <hkern u1="&#x2018;" u2="&#xd5;" k="46" />
    <hkern u1="&#x2018;" u2="&#xd4;" k="46" />
    <hkern u1="&#x2018;" u2="&#xd3;" k="46" />
    <hkern u1="&#x2018;" u2="&#xd2;" k="46" />
    <hkern u1="&#x2018;" u2="&#xc7;" k="46" />
    <hkern u1="&#x2018;" u2="&#xc6;" k="182" />
    <hkern u1="&#x2018;" u2="&#xc5;" k="182" />
    <hkern u1="&#x2018;" u2="&#xc4;" k="182" />
    <hkern u1="&#x2018;" u2="&#xc3;" k="182" />
    <hkern u1="&#x2018;" u2="&#xc2;" k="182" />
    <hkern u1="&#x2018;" u2="&#xc1;" k="182" />
    <hkern u1="&#x2018;" u2="&#xc0;" k="182" />
    <hkern u1="&#x2018;" u2="&#xbb;" k="178" />
    <hkern u1="&#x2018;" u2="&#xb7;" k="178" />
    <hkern u1="&#x2018;" u2="&#xad;" k="178" />
    <hkern u1="&#x2018;" u2="&#xab;" k="178" />
    <hkern u1="&#x2018;" u2="q" k="92" />
    <hkern u1="&#x2018;" u2="o" k="92" />
    <hkern u1="&#x2018;" u2="e" k="92" />
    <hkern u1="&#x2018;" u2="d" k="92" />
    <hkern u1="&#x2018;" u2="c" k="92" />
    <hkern u1="&#x2018;" u2="a" k="64" />
    <hkern u1="&#x2018;" u2="\" k="-48" />
    <hkern u1="&#x2018;" u2="Y" k="-30" />
    <hkern u1="&#x2018;" u2="W" k="-48" />
    <hkern u1="&#x2018;" u2="V" k="-48" />
    <hkern u1="&#x2018;" u2="Q" k="46" />
    <hkern u1="&#x2018;" u2="O" k="46" />
    <hkern u1="&#x2018;" u2="G" k="46" />
    <hkern u1="&#x2018;" u2="C" k="46" />
    <hkern u1="&#x2018;" u2="A" k="182" />
    <hkern u1="&#x2018;" u2="&#x40;" k="46" />
    <hkern u1="&#x2018;" u2="&#x2f;" k="182" />
    <hkern u1="&#x2018;" u2="&#x2e;" k="228" />
    <hkern u1="&#x2018;" u2="&#x2d;" k="178" />
    <hkern u1="&#x2018;" u2="&#x2c;" k="228" />
    <hkern u1="&#x2018;" u2="&#x26;" k="182" />
    <hkern u1="&#x2019;" u2="&#x2206;" k="182" />
    <hkern u1="&#x2019;" u2="&#x203a;" k="178" />
    <hkern u1="&#x2019;" u2="&#x2039;" k="178" />
    <hkern u1="&#x2019;" u2="&#x2022;" k="178" />
    <hkern u1="&#x2019;" u2="&#x201e;" k="228" />
    <hkern u1="&#x2019;" u2="&#x201a;" k="228" />
    <hkern u1="&#x2019;" u2="&#x2014;" k="178" />
    <hkern u1="&#x2019;" u2="&#x2013;" k="178" />
    <hkern u1="&#x2019;" u2="&#x178;" k="-30" />
    <hkern u1="&#x2019;" u2="&#x153;" k="92" />
    <hkern u1="&#x2019;" u2="&#x152;" k="46" />
    <hkern u1="&#x2019;" u2="&#x119;" k="92" />
    <hkern u1="&#x2019;" u2="&#x107;" k="92" />
    <hkern u1="&#x2019;" u2="&#x106;" k="46" />
    <hkern u1="&#x2019;" u2="&#x105;" k="64" />
    <hkern u1="&#x2019;" u2="&#x104;" k="182" />
    <hkern u1="&#x2019;" u2="&#xf8;" k="92" />
    <hkern u1="&#x2019;" u2="&#xf6;" k="92" />
    <hkern u1="&#x2019;" u2="&#xf5;" k="92" />
    <hkern u1="&#x2019;" u2="&#xf4;" k="92" />
    <hkern u1="&#x2019;" u2="&#xf3;" k="92" />
    <hkern u1="&#x2019;" u2="&#xf2;" k="92" />
    <hkern u1="&#x2019;" u2="&#xf0;" k="92" />
    <hkern u1="&#x2019;" u2="&#xeb;" k="92" />
    <hkern u1="&#x2019;" u2="&#xea;" k="92" />
    <hkern u1="&#x2019;" u2="&#xe9;" k="92" />
    <hkern u1="&#x2019;" u2="&#xe8;" k="92" />
    <hkern u1="&#x2019;" u2="&#xe7;" k="92" />
    <hkern u1="&#x2019;" u2="&#xe6;" k="64" />
    <hkern u1="&#x2019;" u2="&#xe5;" k="64" />
    <hkern u1="&#x2019;" u2="&#xe4;" k="64" />
    <hkern u1="&#x2019;" u2="&#xe3;" k="64" />
    <hkern u1="&#x2019;" u2="&#xe2;" k="64" />
    <hkern u1="&#x2019;" u2="&#xe1;" k="64" />
    <hkern u1="&#x2019;" u2="&#xe0;" k="64" />
    <hkern u1="&#x2019;" u2="&#xdd;" k="-30" />
    <hkern u1="&#x2019;" u2="&#xd8;" k="46" />
    <hkern u1="&#x2019;" u2="&#xd6;" k="46" />
    <hkern u1="&#x2019;" u2="&#xd5;" k="46" />
    <hkern u1="&#x2019;" u2="&#xd4;" k="46" />
    <hkern u1="&#x2019;" u2="&#xd3;" k="46" />
    <hkern u1="&#x2019;" u2="&#xd2;" k="46" />
    <hkern u1="&#x2019;" u2="&#xc7;" k="46" />
    <hkern u1="&#x2019;" u2="&#xc6;" k="182" />
    <hkern u1="&#x2019;" u2="&#xc5;" k="182" />
    <hkern u1="&#x2019;" u2="&#xc4;" k="182" />
    <hkern u1="&#x2019;" u2="&#xc3;" k="182" />
    <hkern u1="&#x2019;" u2="&#xc2;" k="182" />
    <hkern u1="&#x2019;" u2="&#xc1;" k="182" />
    <hkern u1="&#x2019;" u2="&#xc0;" k="182" />
    <hkern u1="&#x2019;" u2="&#xbb;" k="178" />
    <hkern u1="&#x2019;" u2="&#xb7;" k="178" />
    <hkern u1="&#x2019;" u2="&#xad;" k="178" />
    <hkern u1="&#x2019;" u2="&#xab;" k="178" />
    <hkern u1="&#x2019;" u2="q" k="92" />
    <hkern u1="&#x2019;" u2="o" k="92" />
    <hkern u1="&#x2019;" u2="e" k="92" />
    <hkern u1="&#x2019;" u2="d" k="92" />
    <hkern u1="&#x2019;" u2="c" k="92" />
    <hkern u1="&#x2019;" u2="a" k="64" />
    <hkern u1="&#x2019;" u2="\" k="-48" />
    <hkern u1="&#x2019;" u2="Y" k="-30" />
    <hkern u1="&#x2019;" u2="W" k="-48" />
    <hkern u1="&#x2019;" u2="V" k="-48" />
    <hkern u1="&#x2019;" u2="Q" k="46" />
    <hkern u1="&#x2019;" u2="O" k="46" />
    <hkern u1="&#x2019;" u2="G" k="46" />
    <hkern u1="&#x2019;" u2="C" k="46" />
    <hkern u1="&#x2019;" u2="A" k="182" />
    <hkern u1="&#x2019;" u2="&#x40;" k="46" />
    <hkern u1="&#x2019;" u2="&#x2f;" k="182" />
    <hkern u1="&#x2019;" u2="&#x2e;" k="228" />
    <hkern u1="&#x2019;" u2="&#x2d;" k="178" />
    <hkern u1="&#x2019;" u2="&#x2c;" k="228" />
    <hkern u1="&#x2019;" u2="&#x26;" k="182" />
    <hkern u1="&#x201a;" u2="&#x2122;" k="228" />
    <hkern u1="&#x201a;" u2="&#x203a;" k="136" />
    <hkern u1="&#x201a;" u2="&#x2039;" k="136" />
    <hkern u1="&#x201a;" u2="&#x2022;" k="136" />
    <hkern u1="&#x201a;" u2="&#x201d;" k="228" />
    <hkern u1="&#x201a;" u2="&#x201c;" k="228" />
    <hkern u1="&#x201a;" u2="&#x2019;" k="228" />
    <hkern u1="&#x201a;" u2="&#x2018;" k="228" />
    <hkern u1="&#x201a;" u2="&#x2014;" k="136" />
    <hkern u1="&#x201a;" u2="&#x2013;" k="136" />
    <hkern u1="&#x201a;" u2="&#x178;" k="152" />
    <hkern u1="&#x201a;" u2="&#x152;" k="56" />
    <hkern u1="&#x201a;" u2="&#x106;" k="56" />
    <hkern u1="&#x201a;" u2="&#xdd;" k="152" />
    <hkern u1="&#x201a;" u2="&#xd8;" k="56" />
    <hkern u1="&#x201a;" u2="&#xd6;" k="56" />
    <hkern u1="&#x201a;" u2="&#xd5;" k="56" />
    <hkern u1="&#x201a;" u2="&#xd4;" k="56" />
    <hkern u1="&#x201a;" u2="&#xd3;" k="56" />
    <hkern u1="&#x201a;" u2="&#xd2;" k="56" />
    <hkern u1="&#x201a;" u2="&#xc7;" k="56" />
    <hkern u1="&#x201a;" u2="&#xbb;" k="136" />
    <hkern u1="&#x201a;" u2="&#xba;" k="228" />
    <hkern u1="&#x201a;" u2="&#xb7;" k="136" />
    <hkern u1="&#x201a;" u2="&#xb0;" k="228" />
    <hkern u1="&#x201a;" u2="&#xad;" k="136" />
    <hkern u1="&#x201a;" u2="&#xab;" k="136" />
    <hkern u1="&#x201a;" u2="&#xaa;" k="228" />
    <hkern u1="&#x201a;" u2="y" k="132" />
    <hkern u1="&#x201a;" u2="w" k="62" />
    <hkern u1="&#x201a;" u2="v" k="132" />
    <hkern u1="&#x201a;" u2="\" k="180" />
    <hkern u1="&#x201a;" u2="Y" k="152" />
    <hkern u1="&#x201a;" u2="W" k="122" />
    <hkern u1="&#x201a;" u2="V" k="180" />
    <hkern u1="&#x201a;" u2="T" k="180" />
    <hkern u1="&#x201a;" u2="Q" k="56" />
    <hkern u1="&#x201a;" u2="O" k="56" />
    <hkern u1="&#x201a;" u2="G" k="56" />
    <hkern u1="&#x201a;" u2="C" k="56" />
    <hkern u1="&#x201a;" u2="&#x40;" k="56" />
    <hkern u1="&#x201a;" u2="&#x2d;" k="136" />
    <hkern u1="&#x201a;" u2="&#x2a;" k="228" />
    <hkern u1="&#x201a;" u2="&#x27;" k="228" />
    <hkern u1="&#x201a;" u2="&#x22;" k="228" />
    <hkern u1="&#x201c;" u2="&#x2206;" k="182" />
    <hkern u1="&#x201c;" u2="&#x203a;" k="178" />
    <hkern u1="&#x201c;" u2="&#x2039;" k="178" />
    <hkern u1="&#x201c;" u2="&#x2022;" k="178" />
    <hkern u1="&#x201c;" u2="&#x201e;" k="228" />
    <hkern u1="&#x201c;" u2="&#x201a;" k="228" />
    <hkern u1="&#x201c;" u2="&#x2014;" k="178" />
    <hkern u1="&#x201c;" u2="&#x2013;" k="178" />
    <hkern u1="&#x201c;" u2="&#x178;" k="-30" />
    <hkern u1="&#x201c;" u2="&#x153;" k="92" />
    <hkern u1="&#x201c;" u2="&#x152;" k="46" />
    <hkern u1="&#x201c;" u2="&#x119;" k="92" />
    <hkern u1="&#x201c;" u2="&#x107;" k="92" />
    <hkern u1="&#x201c;" u2="&#x106;" k="46" />
    <hkern u1="&#x201c;" u2="&#x105;" k="64" />
    <hkern u1="&#x201c;" u2="&#x104;" k="182" />
    <hkern u1="&#x201c;" u2="&#xf8;" k="92" />
    <hkern u1="&#x201c;" u2="&#xf6;" k="92" />
    <hkern u1="&#x201c;" u2="&#xf5;" k="92" />
    <hkern u1="&#x201c;" u2="&#xf4;" k="92" />
    <hkern u1="&#x201c;" u2="&#xf3;" k="92" />
    <hkern u1="&#x201c;" u2="&#xf2;" k="92" />
    <hkern u1="&#x201c;" u2="&#xf0;" k="92" />
    <hkern u1="&#x201c;" u2="&#xeb;" k="92" />
    <hkern u1="&#x201c;" u2="&#xea;" k="92" />
    <hkern u1="&#x201c;" u2="&#xe9;" k="92" />
    <hkern u1="&#x201c;" u2="&#xe8;" k="92" />
    <hkern u1="&#x201c;" u2="&#xe7;" k="92" />
    <hkern u1="&#x201c;" u2="&#xe6;" k="64" />
    <hkern u1="&#x201c;" u2="&#xe5;" k="64" />
    <hkern u1="&#x201c;" u2="&#xe4;" k="64" />
    <hkern u1="&#x201c;" u2="&#xe3;" k="64" />
    <hkern u1="&#x201c;" u2="&#xe2;" k="64" />
    <hkern u1="&#x201c;" u2="&#xe1;" k="64" />
    <hkern u1="&#x201c;" u2="&#xe0;" k="64" />
    <hkern u1="&#x201c;" u2="&#xdd;" k="-30" />
    <hkern u1="&#x201c;" u2="&#xd8;" k="46" />
    <hkern u1="&#x201c;" u2="&#xd6;" k="46" />
    <hkern u1="&#x201c;" u2="&#xd5;" k="46" />
    <hkern u1="&#x201c;" u2="&#xd4;" k="46" />
    <hkern u1="&#x201c;" u2="&#xd3;" k="46" />
    <hkern u1="&#x201c;" u2="&#xd2;" k="46" />
    <hkern u1="&#x201c;" u2="&#xc7;" k="46" />
    <hkern u1="&#x201c;" u2="&#xc6;" k="182" />
    <hkern u1="&#x201c;" u2="&#xc5;" k="182" />
    <hkern u1="&#x201c;" u2="&#xc4;" k="182" />
    <hkern u1="&#x201c;" u2="&#xc3;" k="182" />
    <hkern u1="&#x201c;" u2="&#xc2;" k="182" />
    <hkern u1="&#x201c;" u2="&#xc1;" k="182" />
    <hkern u1="&#x201c;" u2="&#xc0;" k="182" />
    <hkern u1="&#x201c;" u2="&#xbb;" k="178" />
    <hkern u1="&#x201c;" u2="&#xb7;" k="178" />
    <hkern u1="&#x201c;" u2="&#xad;" k="178" />
    <hkern u1="&#x201c;" u2="&#xab;" k="178" />
    <hkern u1="&#x201c;" u2="q" k="92" />
    <hkern u1="&#x201c;" u2="o" k="92" />
    <hkern u1="&#x201c;" u2="e" k="92" />
    <hkern u1="&#x201c;" u2="d" k="92" />
    <hkern u1="&#x201c;" u2="c" k="92" />
    <hkern u1="&#x201c;" u2="a" k="64" />
    <hkern u1="&#x201c;" u2="\" k="-48" />
    <hkern u1="&#x201c;" u2="Y" k="-30" />
    <hkern u1="&#x201c;" u2="W" k="-48" />
    <hkern u1="&#x201c;" u2="V" k="-48" />
    <hkern u1="&#x201c;" u2="Q" k="46" />
    <hkern u1="&#x201c;" u2="O" k="46" />
    <hkern u1="&#x201c;" u2="G" k="46" />
    <hkern u1="&#x201c;" u2="C" k="46" />
    <hkern u1="&#x201c;" u2="A" k="182" />
    <hkern u1="&#x201c;" u2="&#x40;" k="46" />
    <hkern u1="&#x201c;" u2="&#x2f;" k="182" />
    <hkern u1="&#x201c;" u2="&#x2e;" k="228" />
    <hkern u1="&#x201c;" u2="&#x2d;" k="178" />
    <hkern u1="&#x201c;" u2="&#x2c;" k="228" />
    <hkern u1="&#x201c;" u2="&#x26;" k="182" />
    <hkern u1="&#x201d;" u2="&#x2206;" k="182" />
    <hkern u1="&#x201d;" u2="&#x203a;" k="178" />
    <hkern u1="&#x201d;" u2="&#x2039;" k="178" />
    <hkern u1="&#x201d;" u2="&#x2022;" k="178" />
    <hkern u1="&#x201d;" u2="&#x201e;" k="228" />
    <hkern u1="&#x201d;" u2="&#x201a;" k="228" />
    <hkern u1="&#x201d;" u2="&#x2014;" k="178" />
    <hkern u1="&#x201d;" u2="&#x2013;" k="178" />
    <hkern u1="&#x201d;" u2="&#x178;" k="-30" />
    <hkern u1="&#x201d;" u2="&#x153;" k="92" />
    <hkern u1="&#x201d;" u2="&#x152;" k="46" />
    <hkern u1="&#x201d;" u2="&#x119;" k="92" />
    <hkern u1="&#x201d;" u2="&#x107;" k="92" />
    <hkern u1="&#x201d;" u2="&#x106;" k="46" />
    <hkern u1="&#x201d;" u2="&#x105;" k="64" />
    <hkern u1="&#x201d;" u2="&#x104;" k="182" />
    <hkern u1="&#x201d;" u2="&#xf8;" k="92" />
    <hkern u1="&#x201d;" u2="&#xf6;" k="92" />
    <hkern u1="&#x201d;" u2="&#xf5;" k="92" />
    <hkern u1="&#x201d;" u2="&#xf4;" k="92" />
    <hkern u1="&#x201d;" u2="&#xf3;" k="92" />
    <hkern u1="&#x201d;" u2="&#xf2;" k="92" />
    <hkern u1="&#x201d;" u2="&#xf0;" k="92" />
    <hkern u1="&#x201d;" u2="&#xeb;" k="92" />
    <hkern u1="&#x201d;" u2="&#xea;" k="92" />
    <hkern u1="&#x201d;" u2="&#xe9;" k="92" />
    <hkern u1="&#x201d;" u2="&#xe8;" k="92" />
    <hkern u1="&#x201d;" u2="&#xe7;" k="92" />
    <hkern u1="&#x201d;" u2="&#xe6;" k="64" />
    <hkern u1="&#x201d;" u2="&#xe5;" k="64" />
    <hkern u1="&#x201d;" u2="&#xe4;" k="64" />
    <hkern u1="&#x201d;" u2="&#xe3;" k="64" />
    <hkern u1="&#x201d;" u2="&#xe2;" k="64" />
    <hkern u1="&#x201d;" u2="&#xe1;" k="64" />
    <hkern u1="&#x201d;" u2="&#xe0;" k="64" />
    <hkern u1="&#x201d;" u2="&#xdd;" k="-30" />
    <hkern u1="&#x201d;" u2="&#xd8;" k="46" />
    <hkern u1="&#x201d;" u2="&#xd6;" k="46" />
    <hkern u1="&#x201d;" u2="&#xd5;" k="46" />
    <hkern u1="&#x201d;" u2="&#xd4;" k="46" />
    <hkern u1="&#x201d;" u2="&#xd3;" k="46" />
    <hkern u1="&#x201d;" u2="&#xd2;" k="46" />
    <hkern u1="&#x201d;" u2="&#xc7;" k="46" />
    <hkern u1="&#x201d;" u2="&#xc6;" k="182" />
    <hkern u1="&#x201d;" u2="&#xc5;" k="182" />
    <hkern u1="&#x201d;" u2="&#xc4;" k="182" />
    <hkern u1="&#x201d;" u2="&#xc3;" k="182" />
    <hkern u1="&#x201d;" u2="&#xc2;" k="182" />
    <hkern u1="&#x201d;" u2="&#xc1;" k="182" />
    <hkern u1="&#x201d;" u2="&#xc0;" k="182" />
    <hkern u1="&#x201d;" u2="&#xbb;" k="178" />
    <hkern u1="&#x201d;" u2="&#xb7;" k="178" />
    <hkern u1="&#x201d;" u2="&#xad;" k="178" />
    <hkern u1="&#x201d;" u2="&#xab;" k="178" />
    <hkern u1="&#x201d;" u2="q" k="92" />
    <hkern u1="&#x201d;" u2="o" k="92" />
    <hkern u1="&#x201d;" u2="e" k="92" />
    <hkern u1="&#x201d;" u2="d" k="92" />
    <hkern u1="&#x201d;" u2="c" k="92" />
    <hkern u1="&#x201d;" u2="a" k="64" />
    <hkern u1="&#x201d;" u2="\" k="-48" />
    <hkern u1="&#x201d;" u2="Y" k="-30" />
    <hkern u1="&#x201d;" u2="W" k="-48" />
    <hkern u1="&#x201d;" u2="V" k="-48" />
    <hkern u1="&#x201d;" u2="Q" k="46" />
    <hkern u1="&#x201d;" u2="O" k="46" />
    <hkern u1="&#x201d;" u2="G" k="46" />
    <hkern u1="&#x201d;" u2="C" k="46" />
    <hkern u1="&#x201d;" u2="A" k="182" />
    <hkern u1="&#x201d;" u2="&#x40;" k="46" />
    <hkern u1="&#x201d;" u2="&#x2f;" k="182" />
    <hkern u1="&#x201d;" u2="&#x2e;" k="228" />
    <hkern u1="&#x201d;" u2="&#x2d;" k="178" />
    <hkern u1="&#x201d;" u2="&#x2c;" k="228" />
    <hkern u1="&#x201d;" u2="&#x26;" k="182" />
    <hkern u1="&#x201e;" u2="&#x2122;" k="228" />
    <hkern u1="&#x201e;" u2="&#x203a;" k="136" />
    <hkern u1="&#x201e;" u2="&#x2039;" k="136" />
    <hkern u1="&#x201e;" u2="&#x2022;" k="136" />
    <hkern u1="&#x201e;" u2="&#x201d;" k="228" />
    <hkern u1="&#x201e;" u2="&#x201c;" k="228" />
    <hkern u1="&#x201e;" u2="&#x2019;" k="228" />
    <hkern u1="&#x201e;" u2="&#x2018;" k="228" />
    <hkern u1="&#x201e;" u2="&#x2014;" k="136" />
    <hkern u1="&#x201e;" u2="&#x2013;" k="136" />
    <hkern u1="&#x201e;" u2="&#x178;" k="152" />
    <hkern u1="&#x201e;" u2="&#x152;" k="56" />
    <hkern u1="&#x201e;" u2="&#x106;" k="56" />
    <hkern u1="&#x201e;" u2="&#xdd;" k="152" />
    <hkern u1="&#x201e;" u2="&#xd8;" k="56" />
    <hkern u1="&#x201e;" u2="&#xd6;" k="56" />
    <hkern u1="&#x201e;" u2="&#xd5;" k="56" />
    <hkern u1="&#x201e;" u2="&#xd4;" k="56" />
    <hkern u1="&#x201e;" u2="&#xd3;" k="56" />
    <hkern u1="&#x201e;" u2="&#xd2;" k="56" />
    <hkern u1="&#x201e;" u2="&#xc7;" k="56" />
    <hkern u1="&#x201e;" u2="&#xbb;" k="136" />
    <hkern u1="&#x201e;" u2="&#xba;" k="228" />
    <hkern u1="&#x201e;" u2="&#xb7;" k="136" />
    <hkern u1="&#x201e;" u2="&#xb0;" k="228" />
    <hkern u1="&#x201e;" u2="&#xad;" k="136" />
    <hkern u1="&#x201e;" u2="&#xab;" k="136" />
    <hkern u1="&#x201e;" u2="&#xaa;" k="228" />
    <hkern u1="&#x201e;" u2="y" k="132" />
    <hkern u1="&#x201e;" u2="w" k="62" />
    <hkern u1="&#x201e;" u2="v" k="132" />
    <hkern u1="&#x201e;" u2="\" k="180" />
    <hkern u1="&#x201e;" u2="Y" k="152" />
    <hkern u1="&#x201e;" u2="W" k="122" />
    <hkern u1="&#x201e;" u2="V" k="180" />
    <hkern u1="&#x201e;" u2="T" k="180" />
    <hkern u1="&#x201e;" u2="Q" k="56" />
    <hkern u1="&#x201e;" u2="O" k="56" />
    <hkern u1="&#x201e;" u2="G" k="56" />
    <hkern u1="&#x201e;" u2="C" k="56" />
    <hkern u1="&#x201e;" u2="&#x40;" k="56" />
    <hkern u1="&#x201e;" u2="&#x2d;" k="136" />
    <hkern u1="&#x201e;" u2="&#x2a;" k="228" />
    <hkern u1="&#x201e;" u2="&#x27;" k="228" />
    <hkern u1="&#x201e;" u2="&#x22;" k="228" />
    <hkern u1="&#x2022;" u2="&#x2206;" k="52" />
    <hkern u1="&#x2022;" u2="&#x2122;" k="178" />
    <hkern u1="&#x2022;" u2="&#x201e;" k="136" />
    <hkern u1="&#x2022;" u2="&#x201d;" k="178" />
    <hkern u1="&#x2022;" u2="&#x201c;" k="178" />
    <hkern u1="&#x2022;" u2="&#x201a;" k="136" />
    <hkern u1="&#x2022;" u2="&#x2019;" k="178" />
    <hkern u1="&#x2022;" u2="&#x2018;" k="178" />
    <hkern u1="&#x2022;" u2="&#x17d;" k="46" />
    <hkern u1="&#x2022;" u2="&#x17b;" k="46" />
    <hkern u1="&#x2022;" u2="&#x179;" k="46" />
    <hkern u1="&#x2022;" u2="&#x178;" k="160" />
    <hkern u1="&#x2022;" u2="&#x104;" k="52" />
    <hkern u1="&#x2022;" u2="&#xdd;" k="160" />
    <hkern u1="&#x2022;" u2="&#xc6;" k="52" />
    <hkern u1="&#x2022;" u2="&#xc5;" k="52" />
    <hkern u1="&#x2022;" u2="&#xc4;" k="52" />
    <hkern u1="&#x2022;" u2="&#xc3;" k="52" />
    <hkern u1="&#x2022;" u2="&#xc2;" k="52" />
    <hkern u1="&#x2022;" u2="&#xc1;" k="52" />
    <hkern u1="&#x2022;" u2="&#xc0;" k="52" />
    <hkern u1="&#x2022;" u2="&#xba;" k="178" />
    <hkern u1="&#x2022;" u2="&#xb0;" k="178" />
    <hkern u1="&#x2022;" u2="&#xaa;" k="178" />
    <hkern u1="&#x2022;" u2="\" k="112" />
    <hkern u1="&#x2022;" u2="Z" k="46" />
    <hkern u1="&#x2022;" u2="Y" k="160" />
    <hkern u1="&#x2022;" u2="X" k="62" />
    <hkern u1="&#x2022;" u2="W" k="32" />
    <hkern u1="&#x2022;" u2="V" k="112" />
    <hkern u1="&#x2022;" u2="T" k="180" />
    <hkern u1="&#x2022;" u2="A" k="52" />
    <hkern u1="&#x2022;" u2="&#x2f;" k="52" />
    <hkern u1="&#x2022;" u2="&#x2e;" k="136" />
    <hkern u1="&#x2022;" u2="&#x2c;" k="136" />
    <hkern u1="&#x2022;" u2="&#x2a;" k="178" />
    <hkern u1="&#x2022;" u2="&#x27;" k="178" />
    <hkern u1="&#x2022;" u2="&#x26;" k="52" />
    <hkern u1="&#x2022;" u2="&#x22;" k="178" />
    <hkern u1="&#x2039;" u2="&#x2206;" k="52" />
    <hkern u1="&#x2039;" u2="&#x2122;" k="178" />
    <hkern u1="&#x2039;" u2="&#x201e;" k="136" />
    <hkern u1="&#x2039;" u2="&#x201d;" k="178" />
    <hkern u1="&#x2039;" u2="&#x201c;" k="178" />
    <hkern u1="&#x2039;" u2="&#x201a;" k="136" />
    <hkern u1="&#x2039;" u2="&#x2019;" k="178" />
    <hkern u1="&#x2039;" u2="&#x2018;" k="178" />
    <hkern u1="&#x2039;" u2="&#x17d;" k="46" />
    <hkern u1="&#x2039;" u2="&#x17b;" k="46" />
    <hkern u1="&#x2039;" u2="&#x179;" k="46" />
    <hkern u1="&#x2039;" u2="&#x178;" k="160" />
    <hkern u1="&#x2039;" u2="&#x104;" k="52" />
    <hkern u1="&#x2039;" u2="&#xdd;" k="160" />
    <hkern u1="&#x2039;" u2="&#xc6;" k="52" />
    <hkern u1="&#x2039;" u2="&#xc5;" k="52" />
    <hkern u1="&#x2039;" u2="&#xc4;" k="52" />
    <hkern u1="&#x2039;" u2="&#xc3;" k="52" />
    <hkern u1="&#x2039;" u2="&#xc2;" k="52" />
    <hkern u1="&#x2039;" u2="&#xc1;" k="52" />
    <hkern u1="&#x2039;" u2="&#xc0;" k="52" />
    <hkern u1="&#x2039;" u2="&#xba;" k="178" />
    <hkern u1="&#x2039;" u2="&#xb0;" k="178" />
    <hkern u1="&#x2039;" u2="&#xaa;" k="178" />
    <hkern u1="&#x2039;" u2="\" k="112" />
    <hkern u1="&#x2039;" u2="Z" k="46" />
    <hkern u1="&#x2039;" u2="Y" k="160" />
    <hkern u1="&#x2039;" u2="X" k="62" />
    <hkern u1="&#x2039;" u2="W" k="32" />
    <hkern u1="&#x2039;" u2="V" k="112" />
    <hkern u1="&#x2039;" u2="T" k="180" />
    <hkern u1="&#x2039;" u2="A" k="52" />
    <hkern u1="&#x2039;" u2="&#x2f;" k="52" />
    <hkern u1="&#x2039;" u2="&#x2e;" k="136" />
    <hkern u1="&#x2039;" u2="&#x2c;" k="136" />
    <hkern u1="&#x2039;" u2="&#x2a;" k="178" />
    <hkern u1="&#x2039;" u2="&#x27;" k="178" />
    <hkern u1="&#x2039;" u2="&#x26;" k="52" />
    <hkern u1="&#x2039;" u2="&#x22;" k="178" />
    <hkern u1="&#x203a;" u2="&#x2206;" k="52" />
    <hkern u1="&#x203a;" u2="&#x2122;" k="178" />
    <hkern u1="&#x203a;" u2="&#x201e;" k="136" />
    <hkern u1="&#x203a;" u2="&#x201d;" k="178" />
    <hkern u1="&#x203a;" u2="&#x201c;" k="178" />
    <hkern u1="&#x203a;" u2="&#x201a;" k="136" />
    <hkern u1="&#x203a;" u2="&#x2019;" k="178" />
    <hkern u1="&#x203a;" u2="&#x2018;" k="178" />
    <hkern u1="&#x203a;" u2="&#x17d;" k="46" />
    <hkern u1="&#x203a;" u2="&#x17b;" k="46" />
    <hkern u1="&#x203a;" u2="&#x179;" k="46" />
    <hkern u1="&#x203a;" u2="&#x178;" k="160" />
    <hkern u1="&#x203a;" u2="&#x104;" k="52" />
    <hkern u1="&#x203a;" u2="&#xdd;" k="160" />
    <hkern u1="&#x203a;" u2="&#xc6;" k="52" />
    <hkern u1="&#x203a;" u2="&#xc5;" k="52" />
    <hkern u1="&#x203a;" u2="&#xc4;" k="52" />
    <hkern u1="&#x203a;" u2="&#xc3;" k="52" />
    <hkern u1="&#x203a;" u2="&#xc2;" k="52" />
    <hkern u1="&#x203a;" u2="&#xc1;" k="52" />
    <hkern u1="&#x203a;" u2="&#xc0;" k="52" />
    <hkern u1="&#x203a;" u2="&#xba;" k="178" />
    <hkern u1="&#x203a;" u2="&#xb0;" k="178" />
    <hkern u1="&#x203a;" u2="&#xaa;" k="178" />
    <hkern u1="&#x203a;" u2="\" k="112" />
    <hkern u1="&#x203a;" u2="Z" k="46" />
    <hkern u1="&#x203a;" u2="Y" k="160" />
    <hkern u1="&#x203a;" u2="X" k="62" />
    <hkern u1="&#x203a;" u2="W" k="32" />
    <hkern u1="&#x203a;" u2="V" k="112" />
    <hkern u1="&#x203a;" u2="T" k="180" />
    <hkern u1="&#x203a;" u2="A" k="52" />
    <hkern u1="&#x203a;" u2="&#x2f;" k="52" />
    <hkern u1="&#x203a;" u2="&#x2e;" k="136" />
    <hkern u1="&#x203a;" u2="&#x2c;" k="136" />
    <hkern u1="&#x203a;" u2="&#x2a;" k="178" />
    <hkern u1="&#x203a;" u2="&#x27;" k="178" />
    <hkern u1="&#x203a;" u2="&#x26;" k="52" />
    <hkern u1="&#x203a;" u2="&#x22;" k="178" />
    <hkern u1="&#x2122;" u2="&#x2206;" k="182" />
    <hkern u1="&#x2122;" u2="&#x203a;" k="178" />
    <hkern u1="&#x2122;" u2="&#x2039;" k="178" />
    <hkern u1="&#x2122;" u2="&#x2022;" k="178" />
    <hkern u1="&#x2122;" u2="&#x201e;" k="228" />
    <hkern u1="&#x2122;" u2="&#x201a;" k="228" />
    <hkern u1="&#x2122;" u2="&#x2014;" k="178" />
    <hkern u1="&#x2122;" u2="&#x2013;" k="178" />
    <hkern u1="&#x2122;" u2="&#x178;" k="-30" />
    <hkern u1="&#x2122;" u2="&#x153;" k="92" />
    <hkern u1="&#x2122;" u2="&#x152;" k="46" />
    <hkern u1="&#x2122;" u2="&#x119;" k="92" />
    <hkern u1="&#x2122;" u2="&#x107;" k="92" />
    <hkern u1="&#x2122;" u2="&#x106;" k="46" />
    <hkern u1="&#x2122;" u2="&#x105;" k="64" />
    <hkern u1="&#x2122;" u2="&#x104;" k="182" />
    <hkern u1="&#x2122;" u2="&#xf8;" k="92" />
    <hkern u1="&#x2122;" u2="&#xf6;" k="92" />
    <hkern u1="&#x2122;" u2="&#xf5;" k="92" />
    <hkern u1="&#x2122;" u2="&#xf4;" k="92" />
    <hkern u1="&#x2122;" u2="&#xf3;" k="92" />
    <hkern u1="&#x2122;" u2="&#xf2;" k="92" />
    <hkern u1="&#x2122;" u2="&#xf0;" k="92" />
    <hkern u1="&#x2122;" u2="&#xeb;" k="92" />
    <hkern u1="&#x2122;" u2="&#xea;" k="92" />
    <hkern u1="&#x2122;" u2="&#xe9;" k="92" />
    <hkern u1="&#x2122;" u2="&#xe8;" k="92" />
    <hkern u1="&#x2122;" u2="&#xe7;" k="92" />
    <hkern u1="&#x2122;" u2="&#xe6;" k="64" />
    <hkern u1="&#x2122;" u2="&#xe5;" k="64" />
    <hkern u1="&#x2122;" u2="&#xe4;" k="64" />
    <hkern u1="&#x2122;" u2="&#xe3;" k="64" />
    <hkern u1="&#x2122;" u2="&#xe2;" k="64" />
    <hkern u1="&#x2122;" u2="&#xe1;" k="64" />
    <hkern u1="&#x2122;" u2="&#xe0;" k="64" />
    <hkern u1="&#x2122;" u2="&#xdd;" k="-30" />
    <hkern u1="&#x2122;" u2="&#xd8;" k="46" />
    <hkern u1="&#x2122;" u2="&#xd6;" k="46" />
    <hkern u1="&#x2122;" u2="&#xd5;" k="46" />
    <hkern u1="&#x2122;" u2="&#xd4;" k="46" />
    <hkern u1="&#x2122;" u2="&#xd3;" k="46" />
    <hkern u1="&#x2122;" u2="&#xd2;" k="46" />
    <hkern u1="&#x2122;" u2="&#xc7;" k="46" />
    <hkern u1="&#x2122;" u2="&#xc6;" k="182" />
    <hkern u1="&#x2122;" u2="&#xc5;" k="182" />
    <hkern u1="&#x2122;" u2="&#xc4;" k="182" />
    <hkern u1="&#x2122;" u2="&#xc3;" k="182" />
    <hkern u1="&#x2122;" u2="&#xc2;" k="182" />
    <hkern u1="&#x2122;" u2="&#xc1;" k="182" />
    <hkern u1="&#x2122;" u2="&#xc0;" k="182" />
    <hkern u1="&#x2122;" u2="&#xbb;" k="178" />
    <hkern u1="&#x2122;" u2="&#xb7;" k="178" />
    <hkern u1="&#x2122;" u2="&#xad;" k="178" />
    <hkern u1="&#x2122;" u2="&#xab;" k="178" />
    <hkern u1="&#x2122;" u2="q" k="92" />
    <hkern u1="&#x2122;" u2="o" k="92" />
    <hkern u1="&#x2122;" u2="e" k="92" />
    <hkern u1="&#x2122;" u2="d" k="92" />
    <hkern u1="&#x2122;" u2="c" k="92" />
    <hkern u1="&#x2122;" u2="a" k="64" />
    <hkern u1="&#x2122;" u2="\" k="-48" />
    <hkern u1="&#x2122;" u2="Y" k="-30" />
    <hkern u1="&#x2122;" u2="W" k="-48" />
    <hkern u1="&#x2122;" u2="V" k="-48" />
    <hkern u1="&#x2122;" u2="Q" k="46" />
    <hkern u1="&#x2122;" u2="O" k="46" />
    <hkern u1="&#x2122;" u2="G" k="46" />
    <hkern u1="&#x2122;" u2="C" k="46" />
    <hkern u1="&#x2122;" u2="A" k="182" />
    <hkern u1="&#x2122;" u2="&#x40;" k="46" />
    <hkern u1="&#x2122;" u2="&#x2f;" k="182" />
    <hkern u1="&#x2122;" u2="&#x2e;" k="228" />
    <hkern u1="&#x2122;" u2="&#x2d;" k="178" />
    <hkern u1="&#x2122;" u2="&#x2c;" k="228" />
    <hkern u1="&#x2122;" u2="&#x26;" k="182" />
    <hkern u1="&#x2206;" u2="&#x2122;" k="182" />
    <hkern u1="&#x2206;" u2="&#x203a;" k="52" />
    <hkern u1="&#x2206;" u2="&#x2039;" k="52" />
    <hkern u1="&#x2206;" u2="&#x2022;" k="52" />
    <hkern u1="&#x2206;" u2="&#x201d;" k="182" />
    <hkern u1="&#x2206;" u2="&#x201c;" k="182" />
    <hkern u1="&#x2206;" u2="&#x2019;" k="182" />
    <hkern u1="&#x2206;" u2="&#x2018;" k="182" />
    <hkern u1="&#x2206;" u2="&#x2014;" k="52" />
    <hkern u1="&#x2206;" u2="&#x2013;" k="52" />
    <hkern u1="&#x2206;" u2="&#x178;" k="164" />
    <hkern u1="&#x2206;" u2="&#x152;" k="42" />
    <hkern u1="&#x2206;" u2="&#x106;" k="42" />
    <hkern u1="&#x2206;" u2="&#xdd;" k="164" />
    <hkern u1="&#x2206;" u2="&#xdc;" k="56" />
    <hkern u1="&#x2206;" u2="&#xdb;" k="56" />
    <hkern u1="&#x2206;" u2="&#xda;" k="56" />
    <hkern u1="&#x2206;" u2="&#xd9;" k="56" />
    <hkern u1="&#x2206;" u2="&#xd8;" k="42" />
    <hkern u1="&#x2206;" u2="&#xd6;" k="42" />
    <hkern u1="&#x2206;" u2="&#xd5;" k="42" />
    <hkern u1="&#x2206;" u2="&#xd4;" k="42" />
    <hkern u1="&#x2206;" u2="&#xd3;" k="42" />
    <hkern u1="&#x2206;" u2="&#xd2;" k="42" />
    <hkern u1="&#x2206;" u2="&#xc7;" k="42" />
    <hkern u1="&#x2206;" u2="&#xbb;" k="52" />
    <hkern u1="&#x2206;" u2="&#xba;" k="182" />
    <hkern u1="&#x2206;" u2="&#xb9;" k="184" />
    <hkern u1="&#x2206;" u2="&#xb7;" k="52" />
    <hkern u1="&#x2206;" u2="&#xb3;" k="184" />
    <hkern u1="&#x2206;" u2="&#xb2;" k="184" />
    <hkern u1="&#x2206;" u2="&#xb0;" k="182" />
    <hkern u1="&#x2206;" u2="&#xad;" k="52" />
    <hkern u1="&#x2206;" u2="&#xab;" k="52" />
    <hkern u1="&#x2206;" u2="&#xaa;" k="182" />
    <hkern u1="&#x2206;" u2="y" k="82" />
    <hkern u1="&#x2206;" u2="v" k="82" />
    <hkern u1="&#x2206;" u2="\" k="136" />
    <hkern u1="&#x2206;" u2="Y" k="164" />
    <hkern u1="&#x2206;" u2="W" k="84" />
    <hkern u1="&#x2206;" u2="V" k="136" />
    <hkern u1="&#x2206;" u2="U" k="56" />
    <hkern u1="&#x2206;" u2="T" k="132" />
    <hkern u1="&#x2206;" u2="Q" k="42" />
    <hkern u1="&#x2206;" u2="O" k="42" />
    <hkern u1="&#x2206;" u2="J" k="-50" />
    <hkern u1="&#x2206;" u2="G" k="42" />
    <hkern u1="&#x2206;" u2="C" k="42" />
    <hkern u1="&#x2206;" u2="&#x40;" k="42" />
    <hkern u1="&#x2206;" u2="&#x3f;" k="56" />
    <hkern u1="&#x2206;" u2="&#x2d;" k="52" />
    <hkern u1="&#x2206;" u2="&#x2a;" k="182" />
    <hkern u1="&#x2206;" u2="&#x27;" k="182" />
    <hkern u1="&#x2206;" u2="&#x22;" k="182" />
  </font>
</defs></svg>
