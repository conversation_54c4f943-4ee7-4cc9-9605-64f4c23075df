//Switch toggle
$('.switchBTN__toogle[type="radio"]').click(function(){
	var _this = $(this);
	$('.switchBTN__toogle[type="radio"]').siblings('label').removeClass('selected');
	_this.siblings('label').addClass('selected');
});

//File dialog selection
function getFileName(thisObj, uploadObj)
{
  $("#"+uploadObj).html(thisObj.files.item(0).name);
}

$(".section_heading h2 a").on("click", function(e)
{
	var $this = $(this);
	$this.toggleClass("collapsed");
	$this.parents(".section_heading").next("div.section_data").slideToggle("fast");
});

//Numeric validation
$(function(){
  $('.number-only').bind('keydown',function(e){
	  //alert(e.which);
    if (e.which < 48 || e.which > 57)
	{
		if (e.which == 8 || e.which == 37 || e.which == 39|| e.which == 46)
		return true;	
			
        return false;
	}
	else
	{
    	return true;
	}
})
});