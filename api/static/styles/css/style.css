/*-- common utilities/Atomic utilities --*/
.clearfix::after{display:block;clear:both;content:""}

.pull-left{float:left;}
.pull-right{float:right;}
.text-center{ text-align:center; }

.append_bottom5{margin-bottom:5px;}
.append_bottom10{margin-bottom:10px;}
.append_bottom15{margin-bottom:15px;}
.append_bottom20{margin-bottom:20px;}
.append_bottom25{margin-bottom:25px;}
.append_bottom30{margin-bottom:30px;}

.make-relative{position: relative;}

.card { background: #fff; border-radius: 4px; box-shadow: 0 0 4px 0 rgba(0,0,0,.35), 0 3px 2px 0 rgba(0,0,0,.18); margin-bottom: 15px; padding:15px 20px; }

.primary_btn,.secondry_btn { border-radius:2.625rem; color:#fff; text-align: center; font-family:'Lato-Bold', Arial, Helvetica, sans-serif;outline:none; font-size:0.875rem; line-height:0.875rem; display:inline-block; cursor:pointer; }
.primary_btn{  background-image: linear-gradient(94deg, #f0772c, #f95776);filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#f0772c', endColorstr='#f95776',GradientType=1);padding:12px 20px;}
.primary_btn,.secondry_btn{padding:11px 20px;}
.primary_btn:hover,.secondry_btn:hover,.tertiary_btn:hover{  box-shadow: 0 5px 10px 0 rgba(246, 95, 98, 0.3);}
.secondry_btn{background-color: #fff;border:1px solid #595959;color:#595959;}
.tertiary_btn{border:1px solid #f0772d; border-radius:42px;height:2.25rem; padding:0.6875rem 1.25rem; text-align:center;font-size:0.875rem; line-height:0.875rem;font-family:'Lato-Bold', Arial, Helvetica, sans-serif;display:inline-block;cursor:pointer;background:#fff;}

.btn--large { padding:1rem 1.2rem; font-size:1.2rem; line-height:1.2rem; }
.btn--small { padding:.429rem 1.2rem; font-size:0.6rem; line-height:0.6rem; }

/*-- /common utilities/Atomic utilities --*/

body{ font-family:lato-regular, Arial, Helvetica, sans-serif; font-size:0.875rem; overflow-y:scroll; }
.arrow { background:url(../images/sprite.png) no-repeat; display: inline-block; }

.container{ width:1200px; margin:0 auto; }

/*-- header --*/
.ch__mainNavSearchContainer { position: relative; width:1200px; margin: 0 auto; padding:0.630rem 0; }
.ch__innerHeader { font-size:0.875rem; background: #134781; }
.ch__innerHeader a{ border-left: 1px solid #fff;color: #ffffff;font-size:1.13rem; line-height:1.4rem; margin:0.69rem 0 0 0.69rem;padding: 0 0 0 1rem;}
/*-- /header --*/

/*-- section heading --*/
.section_heading h2{ font-size: 1.2rem; font-weight: normal; color: #484848; }
.section_heading a{ color:#444; }
.arrow{background-position: -123px -122px;width: 15px;height: 10px;margin: 0 8px 0 0; transform: rotate(0deg); transition: 0.2s transform ease-in-out 0s;}
.collapsed .arrow{ transform: rotate(-90deg); }
/*-- /section heading --*/

/*-- table data --*/
.booster_table{ border:1px solid #ddd; text-align:center; }
.booster_table th{background-color: #f4f4f4; font-size:0.875rem; font-weight: normal; }
.booster_table td, .booster_table th{  border-right: 1px solid #ddd;border-bottom: 1px solid #ddd;padding: 15px 10px; border-collapse:collapse;color: #4a4a4a;font-size:0.8125rem;}
.booster_table tr:hover{background-color: #eff6ff; cursor: pointer;}
.action_link{ font-size:1rem; text-decoration:underline;  line-height:1.4em; }
.action_link:hover{ text-decoration:none; }
/*-- /table data --*/

/*-- Index page UI - elements --*/
.badge { background-color: #eb2026; border-radius:1.25rem; color: #fff; display: inline-block; font-size:0.875rem; font-weight:400; margin: 0 0.275rem; min-width: 21px; padding: 0.125rem 0.375rem; text-shadow: none; line-height:1; }
.collapsed .badge{ display:inline-block; }
.section_data{ margin-left:25px; }
.hide{ display:none; }

.form_data label{ font-size:1rem; width:200px; }
.form_data input[type='text'],.form_data textarea{ border:solid 1px #d7d7d7; padding:5px 10px; border-radius:4px; transition:border linear .2s; box-shadow:inset 0 3px 0 rgba(0,0,0,.05); width:400px; }
.form_data input:focus{ border:1px solid #124680; outline:0; }
.upload_file_wrap input{position: absolute; left: 0px; top:-18px; width: 140px; height:40px; padding-top: 15px; cursor: pointer; opacity:0; overflow:hidden; }
.bulk_delete_file_wrap input{position: absolute; left: 0px; top:-18px; width: 140px; height:40px; padding-top: 15px; cursor: pointer; opacity:0; overflow:hidden; }
.form_data input[type="submit"], .form_data input[type="button"]{ border:0px; cursor:pointer; }
/*-- /Index page UI - elements --*/


/*-- 05-Mar --*/
.switchBTN{float:left;margin-left:15px; }
.switchBTN input{position:absolute;margin-left:-9999px;}
.switchBTN input+label{display:block;position:relative;cursor:pointer;outline:0;user-select:none;sfont-weight:700;padding:4px 20px;background-color:transparent;border-radius:20px;}
.switchBTN input+label:after, .switchBTN input+label:before{display:block;position:absolute;top:5px;right:7px;bottom:1px;content:"";}
.switchBTN input+label{text-align:center;font-size:16px; border:solid 1px #d6d6d6; box-shadow:0 2px 3px #d6d6d6; color:#adadad;}
.switchBTN .selected{background:#134781; color:#fff; border:solid 1px #134781; transition:all .4s; }

.action_list{ padding:0; margin:0; }
.action_list li{ list-style-type:none; display:inline-block; border-right:solid 1px #444; padding-right:6px; padding-left:6px; line-height:1; }
.action_list li:first-child{ padding-left:0; }
.action_list li:last-child{ padding-right:0; border-right:0; }

/*- Modal code -*/
.modal,.modal-open{ overflow:hidden; }
.close{float:right;font-size:1.3rem;font-weight:700;line-height:1;color:#000;text-shadow:0 1px 0 #fff;}
button.close{padding:0;cursor:pointer;background:0 0;border:0;-webkit-appearance:none;}
.modal{position:fixed;top:0;right:0;bottom:0;left:0;z-index:1050;display:none;outline:0;}
.modal.fade .modal-dialog{transform:scale(0);opacity:0;-webkit-transition:.3s ease-in-out;-o-transition:.3s ease-in-out;transition:.3s ease-in-out;}
.modal.show .modal-dialog{opacity:1;transform:scale(1);}
.modal-open .modal{overflow-x:hidden;overflow-y:auto;}
.modal-dialog{position:relative;width:auto;margin:100px auto;}
.modal-content{position:relative;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;background-color:#fff;-webkit-background-clip:padding-box;background-clip:padding-box;border:1px solid rgba(0,0,0,.2);border-radius:.3rem;outline:0; min-height:200px;}
.modal-backdrop { position: fixed; top: 0; right: 0; bottom: 0; left: 0; z-index: 1040; background-color: #000; }
.modal-backdrop.fade { opacity: 0; }
.modal-backdrop.show { opacity: 0.5; }
.modal-header { display: -webkit-box; display: -webkit-flex; display: -ms-flexbox; display: flex; -webkit-box-align: center; -webkit-align-items: center;
      -ms-flex-align: center; align-items: center; -webkit-box-pack: justify; -webkit-justify-content: space-between; -ms-flex-pack: justify; justify-content: space-between; padding: 15px; border-bottom: 1px solid #eceeef; }
.modal-title { margin-bottom: 0; line-height: 1.5; }
.modal-body { position: relative; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; -ms-flex: 1 1 auto; flex: 1 1 auto; padding: 15px; }
.modal-footer { display: -webkit-box; display: -webkit-flex; display: -ms-flexbox; display: flex; -webkit-box-align: center; -webkit-align-items: center;
      -ms-flex-align: center; align-items: center; -webkit-box-pack: end; -webkit-justify-content: flex-end; -ms-flex-pack: end; justify-content: flex-end;
  padding: 15px; border-top: 1px solid #eceeef; }
.modal-footer > :not(:first-child) { margin-left: .25rem; }
.modal-footer > :not(:last-child) { margin-right: .25rem; }
.modal-scrollbar-measure { position: absolute; top: -9999px; width: 50px; height: 50px; overflow: scroll; }
.gh-modalcontents{ padding:20px; }
.o-cross-symbol {cursor: pointer; position: absolute; top: -1.625rem; right: -25px; background: #fff; height:45px; width:45px; display: inline-block;
    line-height:45px; border-radius: 100%; box-shadow: 0px 0px 11px #ccc; }
.modal__head{ border-bottom:solid 1px #d6d6d6; margin-bottom:20px; }	

@media (min-width: 992px){
		.modal-lg { max-width: 600px!important; }
}

.multi-select-container {
  display: inline-block;
  position: relative;
}

.multi-select-menu {
  position: absolute;
  left: 0;
  top: 0.8em;
  float: left;
  min-width: 100%;
  background: #fff;
  margin: 1em 0;
  padding: 0.4em 0.4em;
  border: 1px solid #aaa;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  display: none;
}

.multi-select-menu input {
  margin-right: 0.3em;
  vertical-align: 0.1em;
}

.multi-select-button {
  display: inline-block;
  font-size: 0.875em;
  padding: 0.2em 0.6em;
  max-width: 20em;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: -0.5em;
  background-color: #fff;
  border: 1px solid #aaa;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  cursor: default;
}



.multi-select-button:after {
  content: "";
  display: inline-block;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0.4em 0.4em 0 0.4em;
  border-color: #999 transparent transparent transparent;
  margin-left: 0.4em;
  vertical-align: 0.1em;
}

.multi-select-container--open .multi-select-menu { display: block; }

.multi-select-container--open .multi-select-button:after {
  border-width: 0 0.4em 0.4em 0.4em;
  border-color: transparent transparent #999 transparent;
}

.booster {
    padding : 3px 10px 2px 2px;
    font-style : oblique;
    color : blue;
    display : inline-block
}


/*- /Modal code -*/
/*-- /05-Mar --*/