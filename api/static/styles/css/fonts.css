@font-face {
    font-family: 'Lato-Regular';
    src: url('../fonts/lato/lato-regular/lato-regular.woff2') format('woff2'),
         url('../fonts/lato/lato-regular/lato-regular.woff') format('woff'),
         url('../fonts/lato/lato-regular/lato-regular.ttf') format('truetype'),
		 url('../fonts/lato/lato-regular/lato-regular.eot?#iefix') format('embedded-opentype');
    font-weight: normal;
    font-style: normal;
	text-rendering: optimizeLegibility;
}


@font-face {
    font-family: 'Lato-Light';
    src: url('../fonts/lato/lato-light/lato-light.woff2') format('woff2'),
         url('../fonts/lato/lato-light/lato-light.woff') format('woff'),
         url('../fonts/lato/lato-light/lato-light.ttf') format('truetype'),
		 url('../fonts/lato/lato-light/lato-light.eot?#iefix') format('embedded-opentype');
    font-weight: normal;
    font-style: normal;
	text-rendering: optimizeLegibility;
}


@font-face {
    font-family: 'Lato-Bold';
    src: url('../fonts/lato/lato-bold/lato-bold.woff2') format('woff2'),
         url('../fonts/lato/lato-bold/lato-bold.woff') format('woff'),
         url('../fonts/lato/lato-bold/lato-bold.ttf') format('truetype'),
		 url('../fonts/lato/lato-bold/lato-bold.eot?#iefix') format('embedded-opentype');
    font-weight: normal;
    font-style: normal;
	text-rendering: optimizeLegibility;
}

@font-face {
    font-family: 'Lato-Medium';
    src: url('../fonts/lato/lato-medium/lato-medium.woff2') format('woff2'),
         url('../fonts/lato/lato-medium/lato-medium.woff') format('woff'),
         url('../fonts/lato/lato-medium/lato-medium.ttf') format('truetype'),
		 url('../fonts/lato/lato-medium/lato-medium.eot?#iefix') format('embedded-opentype');
    font-weight: normal;
    font-style: normal;
	text-rendering: optimizeLegibility;
}


@font-face {
    font-family: 'Lato-Semibold';
    src: url('../fonts/lato/lato-semibold/lato-semibold.woff2') format('woff2'),
         url('../fonts/lato/lato-semibold/lato-semibold.woff') format('woff'),
         url('../fonts/lato/lato-semibold/lato-semibold.ttf') format('truetype'),
		 url('../fonts/lato/lato-semibold/lato-semibold.eot?#iefix') format('embedded-opentype');
    font-weight: normal;
    font-style: normal;
	text-rendering: optimizeLegibility;
}

@font-face {
    font-family: 'Lato-Italic';
    src: url('../fonts/lato/lato-italic/lato-italic.woff2') format('woff2'),
		 url('../fonts/lato/lato-italic/lato-italic.woff') format('woff'),
         url('../fonts/lato/lato-italic/lato-italic.ttf') format('truetype'),
		 url('../fonts/lato/lato-italic/lato-italic.eot?#iefix') format('embedded-opentype');
    font-weight: normal;
    font-style: normal;
	text-rendering: optimizeLegibility;
}


.lato-medium{ font-family:'Lato-Medium', Arial, Helvetica, sans-serif; }
.lato-regular{ font-family:'Lato-Regular', Arial, Helvetica, sans-serif; }
.lato-semibold{ font-family:'Lato-Semibold', Arial, Helvetica, sans-serif; }
.lato-bold{ font-family:'Lato-Bold', Arial, Helvetica, sans-serif;}
.lato-light{ font-family:'Lato-Light', Arial, Helvetica, sans-serif;}
.LatoBlack{ font-family:'Conv_Lato-Black', Arial, Helvetica, sans-serif; }
.lato-italic{font-family: 'Lato-Italic', Arial, Helvetica, sans-serif; }