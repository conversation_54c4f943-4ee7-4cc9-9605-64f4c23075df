# Author: <PERSON><PERSON><PERSON>
# Date: 02-07-2018
# coding: utf-8

from datetime import datetime
from db.MySQLConnection import MySQLConnection
from flask import current_app as app


class dao_mysql_mmt_read(object):
    __db = None

    def __init__(self):
        self.__db = MySQLConnection("_MMT_READ")

    def close(self):
        self.__db.close()

    def get_new_hotel_city_list(self, is_dom):
        variables = {'new_hotels_from': app.config['NEW_HOTELS_FROM'], 'new_hotels_to': app.config['NEW_HOTELS_TO']}

        query = "select hot_htlseq from hotel " \
                "where hot_actstat = 'A' and case when str_to_date(hot_htlseq, '%Y%m%d%H%i%s') is null then hot_crdt " \
                "else str_to_date(hot_htlseq, '%Y%m%d%H%i%s') end >= date_add(now(), interval {new_hotels_from}) " \
                "and case when str_to_date(hot_htlseq, '%Y%m%d%H%i%s') is null then hot_crdt " \
                "else str_to_date(hot_htlseq, '%Y%m%d%H%i%s') end <= date_add(now(), interval {new_hotels_to})".format(**variables)

        app.config['LOG'].info("NEW HOTEL CITY LIST QUERY :::: %s", query)
        return self.__db.run_query(query)

    def get_cities_basis_hotel_count(self, city_list):
        variables = {'cities': "','".join(city_list), 'min_hotels': app.config['MINIMUM_HOTELS_IN_CITY']}

        query = "select hot_citycd, count(*) from hotel where hot_actstat = 'A' and hot_citycd in ('{cities}') " \
                "group by hot_citycd having count(*) <= {min_hotels}".format(**variables)

        app.config['LOG'].info("CITIES BASIS HOTEL COUNT QUERY :::: %s", query)
        return self.__db.run_query(query)

    def get_fraud_hotels(self, hotel_list):
        variables = {'hotels': "','".join(str(hotel) for hotel in hotel_list)}

        query = "select htlseq from htl_extended_info where hei_actstat = 'A' and attribute_type = 'HOTEL_ECOUPON_CATEGORY' " \
                "and (find_in_set('FRAUD', attribute_value) or find_in_set('NPS', attribute_value) or find_in_set('STUCK', attribute_value))" \
                "and htlseq in ('{hotels}')".format(**variables)

        app.config['LOG'].info("FRAUD HOTELS QUERY :::: %s", query)
        return self.__db.run_query(query)

    def get_hotel_count_for_cities(self, city_list):
        variables = {'cities': "','".join(city_list)}

        query = "select hot_citycd, count(*) from hotel where hot_actstat = 'A' and " \
                "hot_citycd in ('{cities}') group by hot_citycd".format(**variables)

        app.config['LOG'].info("HOTEL COUNT FOR CITIES QUERY :::: %s", query)
        return self.__db.run_query(query)

    def get_bookings_for_hotels(self, device):

        if device == 'iphone':
            device = 'ios'

        variables = {'from': app.config['BOOKINGS_FROM'], 'to': app.config['BOOKINGS_TO'], 'device': device}

        query = ""

        if device == 'desktop':
            query = "select bkg_htlseq, count(*) from booking where bkg_bkingdt >= date_add(now(), interval {from}) " \
                    "and bkg_bkingdt <= date_add(now(), interval {to}) and bkg_mmtcnfmid like 'NH2%' " \
                    "and bkg_email not like '%-<EMAIL>' and bkg_fname not like 'test%' " \
                    "and bkg_lastname not like 'test%' group by bkg_htlseq".format(**variables)

        elif device == 'android' or device == 'ios':
            query = "select bkg_htlseq, count(*) from booking join bookingClientTokens on " \
                    "booking.bkg_bkingseq = bookingClientTokens.bkg_bkingseq " \
                    "where bkg_bkingdt >= date_add(now(), interval {from}) and bkg_bkingdt <= date_add(now(), interval {to}) " \
                    "and bkg_mmtcnfmid like 'NH7%' and bkg_tokentype = 'bookingDevice' and " \
                    "bkg_tokenvalue = '{device}' and bkg_email not like '%-<EMAIL>' and " \
                    "bkg_fname not like 'test%' and bkg_lastname not like 'test%' group by bkg_htlseq".format(
                **variables)

        app.config['LOG'].info("BOOKINGS FOR HOTELS QUERY :::: %s", query)
        return self.__db.run_query(query)
