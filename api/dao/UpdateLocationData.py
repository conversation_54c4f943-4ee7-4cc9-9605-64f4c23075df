import common.helpers as hp

from dao.dao_mysql_mmt_write import dao_mysql_mmt_write as dao_mysql_mmt_write
from flask import current_app as app
import pandas as pd
import datetime
import math


class UpdateLocationData(object):
    __log = 'UpdateLocation :: '
    def __init__(self):
        self.log = hp.mdc_logger("UpdateLocationData")
        self.log.info(self.__log + "initializing")
        self.__dao_mysql = dao_mysql_mmt_write()
        self.log.info(self.__log + "initialized")

    def update_location_data(self):
        current_time = datetime.datetime.now()
        cur = self.__dao_mysql.get_max_last_udpdate_date()
        max_last_update_time = (cur.fetchone())[0]
        if max_last_update_time is not None :
            max_last_update_time = max_last_update_time.strftime('%Y-%m-%d %H:%M:%S')
        current_time = current_time.strftime('%Y-%m-%d %H:%M:%S')
        location_data = pd.read_csv(app.config['DATADIR']+app.config['HOTEL_LISTABLE_LOCATION_MAPPING'])
        location_data['is_new'] = location_data.apply(lambda x: int(x['is_new']), axis=1)
        update_tuples = []
        for row in location_data.itertuples(name=None, index=False):
            if len(row) == 6:
                update_tuples.append(
                    '(' + ','.join(['"' + str(v) + '"' for v in row]) + ',NULL,"A" , "' + current_time + '")')
            else:
                if math.isnan(row[-1]) or row[-1] == 0:
                    update_tuples.append(
                        '(' + ','.join(['"' + str(v) + '"' for v in row[:-1]]) + ',NULL,"A" , "' + current_time + '")')
                else:
                    update_tuples.append(
                        '(' + ','.join(['"' + str(v) + '"' for v in row]) + ',"A" , "' + current_time + '")')

        self.log.info("Updating/Inserting rows in hotel_location_mapping ")
        for i in range(math.ceil(len(update_tuples)/1000)):
            self.__dao_mysql.update_location(update_tuples[i*1000:i*1000 + 1000])

        cur = self.__dao_mysql.get_recent_inserted_location_data("'" + current_time + "'")
        current_updated_rows_cnt = (cur.fetchone())[0]
        if current_updated_rows_cnt > 1 and max_last_update_time is not None :
            self.__dao_mysql.set_all_location_inactive("'" + max_last_update_time  + "'")
        self.log.info(self.__log + "Location Update Successful")
        self.__dao_mysql.commit()

        return "Success"