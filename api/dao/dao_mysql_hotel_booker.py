

from db.MySQLConnection import MySQLConnection
from flask import current_app as app

class dao_mysql_hotel_booker(object):
    __db = None

    def __init__(self):
        self.__db = MySQLConnection("_HOTEL_BOOKER")

    def close(self):
        self.__db.close()

    def get_bookings_for_hotels(self, device):

        if device == 'iphone':
            device = 'ios'

        variables = {'from': app.config['BOOKINGS_FROM'], 'to': app.config['BOOKINGS_TO'], 'device': device}

        query = ""

        if device == 'desktop':
            query = "select hotel_code, count(*) from booking where inserted_on >= date_add(now(), interval {from}) " \
                    "and inserted_on <= date_add(now(), interval {to}) and booking_id like 'NH2%' " \
                    "and first_name not like 'test%' " \
                    "and last_name not like 'test%' group by hotel_code".format(**variables)

        elif device == 'android' or device == 'ios':
            query = "select hotel_code, count(*) from booking join booking_client on " \
                    "booking.booking_seq = booking_client.booking_seq " \
                    "where booking.inserted_on >= date_add(now(), interval {from}) and booking.inserted_on <= date_add(now(), interval {to}) " \
                    "and booking.booking_id like 'NH7%' and " \
                    "booking_device_type = '{device}' and " \
                    "first_name not like 'test%' and last_name not like 'test%' group by hotel_code".format(
                **variables)

        app.config['LOG'].info("BOOKINGS FOR HOTELS QUERY :::: %s", query)
        return self.__db.run_query(query)