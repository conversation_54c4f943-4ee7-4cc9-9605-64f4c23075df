from db.MySQLConnection import MySQLConnection

import common.helpers as hp


class dao_mysql_mmt3_read(object):
    __db = None

    def __init__(self):
        self.log = hp.mdc_logger("dao_mysql_mmt3_read")
        self.__db = MySQLConnection("_MMT3_READ")

    def close(self):
        self.__db.close()

    def get_location_hotel_rank(self, rank_table, rank_column, location_list):
        location_hotel_rank = list()
        batch_size = 100
        location_batch_list = [location_list[i:i + batch_size] for i in range(0, len(location_list), batch_size)]
        for location_batch in location_batch_list:
            variables = {'rank_table': rank_table, 'rank_column': rank_column,
                         'location_batch': "','".join(str(location) for location in location_batch)}
            query = "select citycd, htlseq, {rank_column} from {rank_table} " \
                    "where citycd in ('{location_batch}') and actstat = 'A'" \
                .format(**variables)
            self.log.info("GET LOCATION HOTEL RANK QUERY: %s", query)
            cur = self.__db.run_query(query)
            location_hotel_rank += [(data[0], data[1], data[2]) for data in cur.fetchall()]
        return location_hotel_rank
