# Author: <PERSON><PERSON><PERSON>
# Date: 31 Jan 2018

# coding: utf-8

import pandas as pd

from db.HiveConnection import HiveConnection
from flask import current_app as app

class DataAccessObjectHive(object):

    __hc = None

    def __init__(self):
        self.__hc = HiveConnection()

    def close(self):
        self.__hc.close()

    def get_listing_impressions(self, cities, from_date, to_date):

        query = '''SELECT ps110, u5, cht_citycode, flavor, pagename, hit_date, session_id, visitor_id
        FROM
            (SELECT
                ps110,
                u5,
                pd5 as citycd,
                CASE
                    WHEN Lower(u17) = 'ios' Then 'ios'
                    WHEN Lower(u17) = 'iphone os' Then 'ios'
                    WHEN Lower(u17) = 'android' Then 'android'
                    ELSE 'unknown'
                END as flavor,
                from_unixtime(CAST(m2/1000 as BIGINT), 'yyyy-MM-dd') as hit_date,
                lower(p1) as pagename, u11 as visitor_id, l1 as session_id
            FROM pdt_db.p_1026
        WHERE date >= '{0}' and date <='{1}'
        AND lower(ps7) IN ('domestic','international') AND lower(p1) IN ('mob:funnel:domestic hotels:listing','mob:funnel:intl hotels:listing')) as A
        INNER JOIN
        (
            SELECT cht_citycode, cht_citycd AS city_code FROM sitedb.city WHERE cht_citycd IN ({2})
        ) as B
        ON A.citycd = B.cht_citycode
        '''.format(from_date, to_date, ','.join(["'{}'".format(c) for c in cities]))
 
        app.config['LOG'].info("LISTING IMPRESSIONS QUERY :::: %s", query) 

        result_list = []
        impression_df = pd.DataFrame(self.__hc.auto_query(query))
        for index, row in impression_df.iterrows():
            result_row = dict()

            result_row['city_abbr'] = row[2]
            result_row['device'] = row[3]
            result_row['page_name'] = row[4]
            result_row['hit_date'] = row[5]
            result_row['visits'] = row[6] + "_" + row[7]
            result_row['visitors'] = str(row[7])

            try:
                if re.search("Android_7.2",row[1]) or re.search("ios_5.9.[6-9]",row[1]):
                    for info in json.loads(row[0]):
                        for hotel_info in ast.literal_eval(info):
                            for hotel_id_info in hotel_info["hotelInfoList"]:
                                result_row['hotel_id'] = str(hotel_id_info["hotelId"])
                                result_list.append(result_row)
                else:
                    m = str.replace(row[0],"\"]","")
                    m = str.replace(m,"^([a-z]|[A-Z]|[\\\\s]|[\\+])*\\\\{{|\\\\}}([a-z]|[A-Z]|[\\\\s])*$","")
                    for hotel_info in str.replace(m,"[\"","").split("|"):
                        result_row['hotel_id'] = str(hotel_info.split(":")[0])
                        result_list.append(result_row)
            except Exception as ex:
                counter = counter + 1
                device_list = device_list + " " + row[1] + " " + row[0]

        result_df = pd.DataFrame(result_list)

        result_agg_df = result_df.groupby(['city_abbr', 'device', 'hotel_id', 'page_name', 'hit_date']).agg({'visits':'nunique','visitors':'nunique'}).reset_index()

        return result_agg_df

    def get_bookings(self, cities, from_date, to_date):

        query = '''SELECT city_code, flavor, hotel_id, page_name, hit_date, count(distinct booking_id) as num_bookings, count(distinct visitor_id) as num_users
        FROM
            (select l2 as lob, lower(p1) as page_name,
                CASE
                    WHEN Lower(u17) = 'ios' Then 'ios'
                    WHEN Lower(u17) = 'iphone os' Then 'ios'
                    WHEN Lower(u17) = 'android' Then 'android'
                    ELSE 'unknown'
                END as flavor,
                from_unixtime(CAST(m2/1000 as BIGINT), 'yyyy-MM-dd') as hit_date,
                pd5 as city_code, pd1 as hotel_id, date, bd1 as booking_id, u11 as visitor_id
            from pdt_db.p_1025
            where date >= '{0}' and date <='{1}'
            and lower(ps7) IN ('domestic','international')
            and lower(p1) IN ('mob:funnel:domestic hotels:thankyou','mob:funnel:intl hotels:thankyou')
            and l2 = 'NH7'
            and pd5 IN ({2})) as A
        GROUP BY
        city_code, flavor, hotel_id, page_name, hit_date'''.format(from_date, to_date, ','.join(["'{}'".format(c) for c in cities]))

        app.config['LOG'].info("BOOKINGS QUERY :::: %s", query)

        booking_df = pd.DataFrame(self.__hc.auto_query(query), columns=['city_abbr','device','hotel_id','page_name','hit_date','num_bookings','num_users'])
        return booking_df
