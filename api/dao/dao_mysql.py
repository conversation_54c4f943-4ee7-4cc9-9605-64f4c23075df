# Author: <PERSON><PERSON><PERSON>
# Date: 23 Jan 2018

# coding: utf-8

from datetime import datetime

from db.MySQLConnection import MySQLConnection
from flask import current_app as app
from flask_login import current_user

class DataAccessObjectMySQL(object):

    __db = None

    def __init__(self, auto_commit=True):
        self.__db = MySQLConnection("_HTL_CONTENT", auto_commit)

    def close(self):
        self.__db.close()

    def commit(self):
        self.__db.commit()

    def rollback(self):
        self.__db.rollback()

    def get_active_boosters(self, algorithm, seqid, brand='MMT'):
        query = "SELECT distinct concat_ws('_', a.algorithm, a.seqid, 'GH'), \
                        concat_ws('_', \
                                if(a.booster_type = 'P', \
                                    concat_ws('_', 'P', a.boost_percentage), \
                                    if(a.automated_booster_type is null, \
                                        concat_ws('_', a.startblock,a.blocksize,a.nslots,a.maxhotels),\
                                        concat_ws('_', 'AS', a.startblock,a.blocksize,a.nslots,a.maxhotels) \
                                    )\
                                ), \
                                ifnull(check_in_dow, ''), \
                                ifnull(ap_bucket, ''), \
                                ifnull(min_adults, ''), \
                                ifnull(max_adults, ''), \
                                ifnull(children, ''), \
                                ifnull(min_los, ''), \
                                ifnull(max_los, ''), \
                                if(min_check_in_date, date_format(min_check_in_date, '%Y-%m-%d'),''), \
                                if(max_check_in_date, date_format(max_check_in_date, '%Y-%m-%d'),''), \
                                ifnull(priority,''), \
                                ifnull(is_personalized,''), \
                                ifnull(a.uid,''), \
                                ifnull(a.auto_booster_id,''), \
                                ifnull(a.user_segments,''), \
                                ifnull(a.pokus_exp,'') \
                        ), \
                        b.hotelid, b.score, b.display_sequence,  \
                        a.booster_type,  \
                        b.boost_percentage, \
                        a.automated_booster_type, \
                        b.city \
                        FROM boosted_sequence a, boosted_hotels b \
                        WHERE a.uid = b.uid \
                        AND a.isactive = 1 \
                        AND b.state = 'active'\
                        AND a.startdate <= now() \
                        AND date(a.enddate) >= current_date"

        if algorithm:
            query = query + " AND a.algorithm = '" + algorithm + "'"
        if seqid:
            query = query + " AND a.seqid = '" + seqid + "'"
        if brand:
            query = query + " AND a.brand = '" + brand + "'"
        app.config['LOG'].info("ACTIVE BOOSTERS QUERY :::: %s", query)
        return self.__db.run_query(query)

    def get_inactive_boosters(self, algorithm, seqid, brand='MMT'):
        query = "SELECT distinct concat(a.algorithm,'_',a.seqid,'_GH'), b.hotelid, a.automated_booster_type, b.city \
                 FROM boosted_sequence a, boosted_hotels b \
                 WHERE a.uid = b.uid"

        if algorithm:
            query = query + " AND a.algorithm = '" + algorithm + "'"
        if seqid:
            query = query + " AND a.seqid = '" + seqid + "'"

        if brand:
            query = query + " AND a.brand = '" + brand + "'"

        app.config['LOG'].info("INACTIVE BOOSTERS QUERY :::: %s", query)
        return self.__db.run_query(query)

    # This method only get the result for those sequences whose end date has passed and are to be removed from cache.
    def get_expired_boosters(self, algorithm, seqid):
        query = "SELECT distinct concat(a.algorithm,'_',a.seqid,'_GH'), b.hotelid, a.automated_booster_type, b.city \
                       FROM boosted_sequence a, boosted_hotels b \
                       WHERE a.uid = b.uid \
                       AND a.isactive = 1 \
                       AND b.state = 'active' \
                       AND a.startdate <= now() \
                       AND date(a.enddate) < current_date"

        if algorithm:
            query = query + " AND a.algorithm = '" + algorithm + "'"
        if seqid:
            query = query + " AND a.seqid = '" + seqid + "'"

        app.config['LOG'].info("Expired boosters query  :::: %s", query)
        return self.__db.run_query(query)

    def make_sequence_inactive(self, uid):
        query = "UPDATE boosted_sequence \
                    SET isactive = 0, \
                        lastupdated = '" + str(datetime.now()) + "', modified_by = '" + current_user.username + "'  \
                    WHERE uid = '" + uid + "'"

        app.config['LOG'].info("MAKE SEQUENCE INACTIVE IN DB QUERY :::: %s", query)
        return self.__db.run_query(query)

    def make_hotels_inactive(self, uid):
        query = "UPDATE boosted_hotels \
                    SET state = 'inactive', \
                        lastupdated = '" + str(datetime.now()) + "', modified_by = '" + current_user.username + "' \
                    WHERE uid = '" + uid + "'"

        app.config['LOG'].info("MAKE HOTELS INACTIVE IN DB QUERY :::: %s", query)
        return self.__db.run_query(query)


    def find_input_newhotel_sequence(self,source_seq_id,algorithm,domain):
        query = "SELECT * \
                    FROM boosted_sequence \
                    WHERE seqid = '" + source_seq_id + "' \
                    AND algorithm = '" + algorithm + "' \
                    AND isactive = 1\
                    AND domain = '" + domain + "'"

        app.config['LOG'].info("FIND INPUT NEW HOTEL SEQUENCE IN DB QUERY :::: %s", query)
        return self.__db.run_query(query)


    def find_input_active_description(self,description):

        query = "SELECT * \
                    FROM boosted_sequence \
                    WHERE description = '" + description + "' "

        app.config['LOG'].info("FIND INPUT NEW HOTEL SEQUENCE IN DB QUERY :::: %s", query)
        return self.__db.run_query(query)

    def find_input_descriptions(self, str_descriptions):
        query = f'SELECT description, uid, isactive, algorithm, seqid FROM boosted_sequence WHERE description in ("{str_descriptions}") '

        app.config['LOG'].info("FIND SEQUENCE IN DB QUERY BY DESCRIPTION :::: %s", query)
        return self.__db.run_query(query)

    def find_input_booster_sequence(self, source_seq_id, start_block, algorithm, check_in_dow, ap_bucket, min_adults,
                                    max_adults, children, min_los, max_los, domain, min_check_in_date,
                                    max_check_in_date, is_personalized, booster_type, default_percentage):

        # if booster type is slot than query column will be startblock else boost_percentage
        if booster_type == "percentage_based":
            query_column = "AND boost_percentage = " + str(default_percentage)
        else:
            query_column = "AND startblock = " + str(start_block)

        query = "SELECT * \
                    FROM boosted_sequence \
                    WHERE seqid = '" + str(source_seq_id) + "'" + query_column + "\
                    AND algorithm = '" + str(algorithm) + "' \
                    AND ifnull(check_in_dow, '') = '" + (check_in_dow or '') + "' \
                    AND ifnull(ap_bucket, '') = '" + (ap_bucket or '') + "' \
                    AND ifnull(min_adults, 0) = '" + str(min_adults or 0) + "' \
                    AND ifnull(max_adults, 0) = '" + str(max_adults or 0) + "' \
                    AND case children when True then 'True' when False then 'False' else '' end = '" + (
            str('' if children is None else children)) + "' \
                    AND ifnull(min_los, 0) = '" + str(min_los or 0) + "' \
                    AND ifnull(max_los, 0) = '" + str(max_los or 0) + "' \
                    AND isactive = 1 \
                    AND is_personalized = '" + str(is_personalized) + "'\
                    AND domain = '" + domain + "' \
                    AND if(min_check_in_date,date_format(min_check_in_date, '%Y-%m-%d'),'') = '" + str(
            min_check_in_date or '') + "' \
                    AND if(max_check_in_date,date_format(max_check_in_date, '%Y-%m-%d'),'') = '" + str(
            max_check_in_date or '') + "' "
        app.config['LOG'].info("FIND INPUT BOOSTER SEQUENCE IN DB QUERY :::: %s", query)
        return self.__db.run_query(query)

    def add_hotels(self, rows):
        query = "INSERT INTO boosted_hotels (uid,hotelid,score,state,startrank,endrank,startdate,enddate,lastupdated,modified_by," \
                "retire_rotate_signals,original_sequence,display_sequence, boost_percentage, city, retire_absolute_threshold, retire_conversion_threshold) \
                    VALUES " + rows

        app.config['LOG'].info("ADD NEW SEQUENCE HOTELS IN DB QUERY :::: %s", query)
        return self.__db.run_query(query)

    def fetch_sequence_from_seqid_algorithm(self, brand):
        query = "Select seqid,algorithm from boosted_sequence where brand='{0}' AND isactive=1 group by seqid,algorithm".format(brand)
        app.config['LOG'].info("QUERY TO FETCH SEQID AND ALGORITHM :::: %s", query)
        return self.__db.run_query(query)


    def add_sequence(self, uid, description, brand, source_seq_id, start_block, algorithm, block_size, nslots, max_hotels,
                     start_date, end_date, check_in_dow, ap_bucket, min_adults, max_adults, children, min_los, max_los,
                     appfest_booster, domain, is_personalized, min_check_in_date, max_check_in_date, modified_by,
                     rotation_time_minutes, retire_rotate_signals, booster_type, default_percentage, retirement_strategy=None,
                     automated_booster_type=None, automated_booster_experiment=None, auto_booster_id=None, shuffling_enable=None, retire_enable=None, user_segments=None, pokus_exp=None, master_uid=None):

        query = "INSERT INTO boosted_sequence (uid,description,algorithm,seqid,startblock,isactive,blocksize,nslots,maxhotels, \
                startdate, enddate, check_in_dow, ap_bucket, min_adults, max_adults, children, min_los, max_los, priority,domain,  \
                is_personalized, min_check_in_date, max_check_in_date,lastupdated, modified_by, rotation_time_minutes, " \
                "retire_rotate_signals, booster_type, boost_percentage, automated_booster_type, automated_booster_experiment, auto_booster_id, shuffling_enable, retire_enable, user_segments, brand, retirement_strategy,pokus_exp,master_uid) \
                values(%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s) ";
        app.config['LOG'].info("ADD NEW SEQUENCE IN DB QUERY :::: %s", query)
        return self.__db.run_query(query, (
        str(uid), description, algorithm, source_seq_id, start_block, 1, block_size, nslots, max_hotels,
        start_date, end_date, check_in_dow, ap_bucket, min_adults, max_adults, children, min_los, max_los,
        appfest_booster,
        domain, is_personalized, min_check_in_date, max_check_in_date, datetime.now(), modified_by,
        rotation_time_minutes, retire_rotate_signals, booster_type, default_percentage, automated_booster_type,
        automated_booster_experiment, auto_booster_id, shuffling_enable, retire_enable, user_segments, brand, retirement_strategy, pokus_exp, str(master_uid)))

    def add_uid_map(self, uid, mmUid):

        query = "INSERT INTO boosted_sequence_uid_map (uid, mm_uid) values (%s, %s);";

        app.config['LOG'].info("ADD NEW UID MAP IN DB QUERY :::: %s", query)
        return self.__db.run_query(query, (str(uid), str(mmUid)))

    def find_input_active_uid(self, source_seq_id, start_block, algorithm):

        query = "SELECT uid \
                    FROM boosted_sequence \
                    WHERE seqid = '" + source_seq_id + "' \
                    AND startblock = " + str(start_block) + " \
                    AND algorithm = '" + algorithm + "' \
                    AND isactive = 1"

        app.config['LOG'].info("FIND INPUT UID IN DB QUERY :::: %s", query)
        return self.__db.run_query(query)

    def find_other_active_uids(self, source_seq_id, start_block, algorithm):
        query = "SELECT uid \
                    FROM boosted_sequence \
                    WHERE algorithm != '" + algorithm + "' \
                    AND isactive = 1 \
                 UNION \
                 SELECT uid \
                    FROM boosted_sequence \
                    WHERE seqid = '" + source_seq_id + "' \
                    AND startblock != " + str(start_block) + " \
                    AND algorithm = '" + algorithm + "' \
                    AND isactive = 1"

        app.config['LOG'].info("FIND OTHER UIDs OF INTEREST IN DB QUERY :::: %s", query)
        return self.__db.run_query(query)

    def get_hotel_sequence_for_uid(self, uid):
        query = "SELECT hotelid, display_sequence \
                    FROM boosted_hotels \
                    WHERE uid = '" + uid + "'"

        app.config['LOG'].info("FIND ALL HOTELS FOR INPUT UIDs IN DB QUERY :::: %s", query)
        return self.__db.run_query(query)

    def get_hotel_location_sequence(self, uid):
        query = "SELECT hotelid, city, display_sequence \
                    FROM boosted_hotels \
                    WHERE uid = '" + uid + "'"
        app.config['LOG'].info("FIND HOTEL LOCATION FOR UID QUERY :::: %s", query)
        return self.__db.run_query(query)

    def update_hotels_with_score(self, update_query_list):

        query = "UPDATE boosted_hotels SET score = %s, state = %s, " \
                "startdate = %s, lastupdated = %s , modified_by = %s, " \
                "original_sequence = %s, display_sequence = %s, " \
                "boost_percentage = %s WHERE uid = %s AND hotelid = %s"

        app.config['LOG'].info("UPDATE HOTELS WITH SCORE QUERY :::: %s", query)
        return self.__db.run_bulk_query(query, update_query_list)

    def update_hotels(self, update_query_list):
        query = "UPDATE boosted_hotels SET state = %s, " \
                "startdate = %s, lastupdated = %s , modified_by = %s, " \
                "original_sequence = %s, display_sequence = %s, " \
                "boost_percentage = %s WHERE uid = %s AND hotelid = %s"

        app.config['LOG'].info("UPDATE HOTELS QUERY :::: %s", query)
        return self.__db.run_bulk_query(query, update_query_list)

    def update_hotels_display(self, update_query_list):
        query = "UPDATE boosted_hotels SET lastupdated = %s , modified_by = %s, " \
                "display_sequence = %s, boost_percentage = %s WHERE uid = %s AND hotelid = %s"

        app.config['LOG'].info("UPDATE HOTELS DISPLAY SEQUENCE QUERY :::: %s", query)
        return self.__db.run_bulk_query(query, update_query_list)

    def update_hotels_automated_booster(self, update_query_list):
        query = "UPDATE boosted_hotels SET state = %s, " \
                "startdate = %s, lastupdated = %s , modified_by = %s, " \
                "original_sequence = %s, display_sequence = %s, " \
                "boost_percentage = %s WHERE uid = %s AND hotelid = %s and city = %s"
        app.config['LOG'].info("UPDATE HOTEL AUTOMATED BOOSTER QUERY :::: %s", query)
        return self.__db.run_bulk_query(query, update_query_list)

    def remove_hotels(self, hotels, uid):
        query = "UPDATE boosted_hotels \
                    SET state = 'inactive', \
                        lastupdated = '" + str(datetime.now()) + "', \
                        enddate = '" + str(datetime.now()) + "' \
                    WHERE uid = '" + uid + "' \
                    AND hotelid IN " + hotels

        app.config['LOG'].info("REMOVE HOTELS IN EXISTING SEQUENCE IN DB QUERY :::: %s", query)
        return self.__db.run_query(query)

    def remove_hotels_automated_booster(self, remove_query_list):
        query = "UPDATE boosted_hotels SET state = %s, " \
                "lastupdated = %s, enddate = %s, modified_by = %s " \
                "WHERE uid = %s AND hotelid = %s and city = %s "
        app.config['LOG'].info("REMOVE HOTEL AUTOMATED BOOSTER QUERY :::: %s", query)
        return self.__db.run_bulk_query(query, remove_query_list)


    def remove_cities(self, cities, uid):
        query = "UPDATE boosted_hotels \
                    SET state = 'inactive', \
                        lastupdated = '" + str(datetime.now()) + "' ,\
                        enddate = '" + str(datetime.now()) + "' \
                    WHERE uid = '" + uid + "' \
                    AND city IN " + cities

        app.config['LOG'].info("REMOVE CITIES IN EXISTING SEQUENCE IN DB QUERY :::: %s", query)
        return self.__db.run_query(query)

    def fetch_active_sequences(self, algorithm, domain):

        query = "SELECT a.uid,GROUP_CONCAT(b.mm_uid) as mm_uid,a.description,a.brand,a.algorithm,a.seqid,a.startblock,a.isactive,a.blocksize,a.nslots,a.maxhotels, \
                    date_format(a.startdate, '%d %b %Y') as startdate,date_format(a.enddate, '%d %b %Y') as enddate, \
                    a.check_in_dow,a.ap_bucket,a.min_adults,a.max_adults,a.children,a.min_los,a.max_los,a.domain ,a.priority, \
                    a.min_check_in_date,a.max_check_in_date, a.lastupdated, a.retire_rotate_signals , a.is_personalized, a.booster_type, \
                    a.automated_booster_type, a.master_uid, a.boost_percentage, a.user_segments, a.pokus_exp \
                    FROM boosted_sequence a \
                    LEFT JOIN boosted_sequence_uid_map b on b.uid = a.uid \
                    WHERE isactive = 1 \
                    AND algorithm = '" + algorithm + "' \
                    AND domain = '" + domain + "' \
                    group by a.uid"

        app.config['LOG'].info("FETCH ACTIVE SEQUENCES FOR INPUT MODE IN DB QUERY :::: %s", query)
        return self.__db.run_query(query)

    def fetch_active_auto_sequences(self):

        query = "SELECT a.uid,GROUP_CONCAT(b.mm_uid) as mm_uid,a.description,a.brand,a.algorithm,a.seqid,a.startblock,a.isactive,a.blocksize,a.nslots,a.maxhotels, \
                    date_format(a.startdate, '%d %b %Y') as startdate,date_format(a.enddate, '%d %b %Y') as enddate, \
                    a.check_in_dow,a.ap_bucket,a.min_adults,a.max_adults,a.children,a.min_los,a.max_los,a.domain ,a.priority, \
                    a.min_check_in_date,a.max_check_in_date, a.lastupdated, a.retire_rotate_signals , a.is_personalized, a.booster_type, \
                    a.automated_booster_type, a.master_uid, a.boost_percentage, a.user_segments, a.pokus_exp \
                    FROM boosted_sequence a \
                    LEFT JOIN boosted_sequence_uid_map b on b.uid = a.uid \
                    WHERE isactive = 1 \
                    AND auto_booster_id is not null\
                    group by a.uid"

        app.config['LOG'].info("FETCH ACTIVE SEQUENCES FOR INPUT MODE IN DB QUERY :::: %s", query)
        return self.__db.run_query(query)

    def get_startdate(self, uid):
        query = "SELECT startdate \
                    FROM boosted_sequence \
                    WHERE uid = '" + uid + "'"

        app.config['LOG'].info("GET START DATE FOR INPUT SEQUENCE IN DB QUERY :::: %s", query)
        return self.__db.run_query(query)

    def get_hotels_basis_rank(self, hotel_list, column_name):
        variables = {'col_name': column_name, 'hotels': "','".join(str(hotel) for hotel in hotel_list),
                     'min_rank': app.config['MINIMUM_RANK_VALUE']}

        query = "select hot_htlseq, hot_citycd, {col_name} from hotel_ranking " \
                "where active = 1 and hot_htlseq in ('{hotels}') and {col_name} <= {min_rank}".format(
            **variables)

        app.config['LOG'].info("HOTELS BASIS RANK QUERY :::: %s", query)
        return self.__db.run_query(query)

    def get_exposed_hotels(self, hotel_list, sequence_new):
        variables = {'seqid': sequence_new, 'hotels': "','".join(str(hotel) for hotel in hotel_list)}

        query = "select hotelid from boosted_hotels join boosted_sequence on boosted_hotels.uid = boosted_sequence.uid " \
                "where algorithm = 'NEWHOTELS' and seqid = '{seqid}' and hotelid in ('{hotels}')".format(
            **variables)

        app.config['LOG'].info("GET EXPOSED HOTELS QUERY :::: %s", query)
        return self.__db.run_query(query)

    def get_hotel_location_rank(self, hotel_list, column_name):
        variables = {'col_name': column_name, 'hotels': "','".join(str(hotel) for hotel in hotel_list)}

        query = "select hot_htlseq, hot_citycd, {col_name} from hotel_ranking " \
                "where active = 1 and hot_htlseq in ('{hotels}')".format(**variables)

        app.config['LOG'].info("HOTEL LOCATION RANK QUERY :::: %s", query)
        return self.__db.run_query(query)

    def get_hotel_location_rn(self, hotel_list, device):
        variables = {'device': device, 'hotels': "','".join(str(hotel) for hotel in hotel_list),
                     'max_room_nights': app.config['MAXIMUM_ROOM_NIGHTS']}

        query = "select hot_htlseq, hot_citycd, room_nights from hotel_ranking_raw_attributes where active = 1 " \
                "and platform = '{device}' and room_nights >= {max_room_nights} and hot_htlseq in ('{hotels}')".format(
            **variables)

        app.config['LOG'].info("HOTEL LOCATION ROOM NIGHTS QUERY :::: %s", query)
        return self.__db.run_query(query)

    def get_boosted_sequence_uids(self, sequence_new):
        variables = {'seqid': sequence_new}
        query = "select uid from boosted_sequence where isactive = 1 and " \
                "algorithm = 'NEWHOTELS' and seqid = '{seqid}'".format(**variables)

        app.config['LOG'].info("BOOSTED SEQUENCE UIDS QUERY:::: %s", query)
        return self.__db.run_query(query)

    def get_boosted_hotels_for_uid(self, uid):
        variables = {'uid': uid}

        query = "select hotelid from boosted_hotels where uid = '{uid}'".format(**variables)

        app.config['LOG'].info("BOOSTED HOTELS FOR UID QUERY :::: %s", query)
        return self.__db.run_query(query)

    def get_filtered_hotels_for_uid(self, uid):
        variables = {'uid': uid}

        query = "select hotelid from filtered_hotels where uid = '{uid}'".format(**variables)

        app.config['LOG'].info("FILTERED HOTELS FOR UID QUERY :::: %s", query)
        return self.__db.run_query(query)

    def update_sequence(self, uid, modified_by):
        variables = {'uid': uid, 'modified_by': modified_by}

        query = "update boosted_sequence set lastupdated = now(), modified_by = '{modified_by}' where " \
                "uid = '{uid}'".format(**variables)

        app.config['LOG'].info("UPDATE BOOSTED SEQUENCE IN DB QUERY :::: %s", query)
        return self.__db.run_query(query)

    def update_boosted_hotels(self, rows):
        query = "UPDATE boosted_hotels SET score = %s, state = %s, lastupdated = %s , modified_by = %s " \
                "WHERE uid = %s and hotelid = %s"

        app.config['LOG'].info("UPDATE BOOSTED HOTELS IN DB QUERY :::: %s %s", query, str(rows))
        return self.__db.run_bulk_query(query, rows)

    def update_filtered_hotels(self, rows):
        query = "UPDATE filtered_hotels SET filtration_reason = %s, filtration_score = %s, updated_by = %s , updated_on = %s " \
                "WHERE uid = %s and hotelid = %s"

        app.config['LOG'].info("UPDATE FILTERED HOTELS IN DB QUERY :::: %s %s", query, str(rows))
        return self.__db.run_bulk_query(query, rows)

    def insert_filtered_hotels(self, rows):
        query = "INSERT IGNORE INTO filtered_hotels (uid,hotelid,filtration_reason,filtration_score,created_by,created_on,updated_by,updated_on) \
                    VALUES " + rows

        app.config['LOG'].info("INSERT FILTERED HOTELS IN DB QUERY :::: %s", query)
        return self.__db.run_query(query)

    def get_hotels_content_score(self):
        query = "SELECT hotel_content_score.hotel_sequence, hotel_content_score.hotel_content_score " \
                "FROM hotel_content_score INNER JOIN (SELECT hotel_sequence,MAX(hotel_score_date) max_hotel_score_date " \
                "FROM hotel_content_score GROUP BY hotel_sequence) temp " \
                "ON hotel_content_score.hotel_sequence = temp.hotel_sequence AND hotel_content_score.hotel_score_date = temp.max_hotel_score_date"

        app.config['LOG'].info("HOTELS CONTENT SCORE QUERY :::: %s", query)
        return self.__db.run_query(query)

    def get_expired_sequences(self):
        query = "SELECT uid \
                    FROM boosted_sequence \
                    WHERE isactive = 1 \
                    AND date(enddate) < current_date "

        app.config['LOG'].info("FETCH EXPIRED SEQUENCES IN DB QUERY :::: %s", query)
        return self.__db.run_query(query)

    def fetch_hotel_from_uid(self, uid):
        query = "SELECT hotelid,state,date_format(startdate, '%d %b %Y %H:%i:%s') as startdate,date_format(enddate, '%d %b %Y %H:%i:%s') as enddate,date_format(lastupdated, '%d %b %Y %H:%i:%s') as lastupdated,boost_percentage,retire_absolute_threshold,retire_conversion_threshold \
                    FROM boosted_hotels \
                    WHERE uid = '" + uid + "'\
                    AND (state like 'active' or state like 'retire') \
                    order by display_sequence ASC "
        return self.__db.run_query(query)

    def fetch_sequence_from_uid(self, uid):
        query = "SELECT description,brand,algorithm,seqid,startblock,isactive,blocksize,nslots,date_format(startdate, '%d %b %Y') as startdate,date_format(enddate, '%d %b %Y') as enddate, boost_percentage, booster_type \
                            , automated_booster_type, automated_booster_experiment,retire_enable,retirement_strategy \
                            FROM boosted_sequence \
                            WHERE uid = '" + uid + "'"
        return self.__db.run_query(query)

    def get_active_sequences(self):
        query = "SELECT bs.uid, algorithm, hotelid, bh.startdate, bh.retire_rotate_signals, display_sequence, " \
                "seqid, score FROM boosted_sequence bs JOIN boosted_hotels bh ON bh.uid = bs.uid WHERE state = 'active' and isactive = 1"

        app.config['LOG'].info("ACTIVE SEQUENCES IN DB QUERY :::: %s", query)
        return self.__db.run_query(query)

    def retire_hotels(self, update_query_list):
        query = "UPDATE boosted_hotels SET state = %s, " \
                "lastupdated = %s , modified_by = %s " \
                "WHERE uid = %s AND hotelid = %s"
        app.config['LOG'].info("RETIRE HOTELS QUERY :::: %s", query)
        return self.__db.run_bulk_query(query, update_query_list)

    def get_boosted_sequence_info(self, uid):
        query = "SELECT startdate, booster_type, boost_percentage \
                    FROM boosted_sequence \
                    WHERE uid = '" + uid + "'"

        app.config['LOG'].info("GET START DATE FOR INPUT SEQUENCE IN DB QUERY :::: %s", query)
        return self.__db.run_query(query)

    def get_aa_hotel_set(self, device, location_list):
        aa_hotel_set = set()
        batch_size = 500
        location_batch_list = [location_list[i:i + batch_size] for i in range(0, len(location_list), batch_size)]
        for location_batch in location_batch_list:
            variables = {'device': device, 'location_batch': "','".join(str(location) for location in location_batch)}
            query = "select hot_htlseq from hotel_ranking_raw_attributes " \
                    "where hot_citycd in ('{location_batch}') and platform = '{device}' and domain_sbu = 'all' " \
                    "and active = 1 and alt_acco = 1" \
                .format(**variables)
            app.config['LOG'].info("GET AA HOTEL SET QUERY: %s", query)
            cur = self.__db.run_query(query)
            aa_hotel_batch = set([aa_hotel_data[0] for aa_hotel_data in cur.fetchall()])
            aa_hotel_set.update(aa_hotel_batch)
        return aa_hotel_set

    def get_automated_booster(self, automated_booster_type):
        query = "SELECT a.uid, a.algorithm, a.seqid, b.city, a.automated_booster_experiment, " \
                "a.startdate, a.enddate, a.booster_type, a.boost_percentage, a.retire_rotate_signals " \
                "FROM boosted_sequence a, boosted_hotels b " \
                "WHERE a.uid = b.uid " \
                "AND a.isactive = 1 " \
                "AND a.startdate <= now() " \
                "AND date(a.enddate) >= current_date " \
                "AND automated_booster_type = '{}'"\
            .format(automated_booster_type)
        app.config['LOG'].info("GET AUTOMATED BOOSTER QUERY: %s", query)
        return self.__db.run_query(query)
