
from db.MySQLConnection import MySQLConnection
from flask import current_app as app


class dao_mysql_mmt_write(object):
    __db = None

    def __init__(self):
        self.__db = MySQLConnection("_MMT_WRITE",False)

    def close(self):
        self.__db.close()

    def set_all_location_inactive(self,last_update_time):
        query = "update hotel_location_mapping set hot_actstat = 'I' where last_update <= {0}".format(last_update_time)

        app.config['LOG'].info("Updating All hotels as Inactive:::: %s", query)
        return self.__db.run_query(query)

    def get_max_last_udpdate_date(self):
        query = "select max(last_update) from hotel_location_mapping where hot_actstat = 'A'"
        return self.__db.run_query(query)

    def update_location(self, update_tuples):

        query = """insert into hotel_location_mapping (hotel_id , location_id , location_name , location_type , is_new , hotel_country_id , voyager_id, hot_actstat, last_update) values{0}
                    on duplicate key update location_name = VALUES(location_name) , location_type = VALUES(location_type) , 
                     is_new = VALUES(is_new) , hotel_country_id = VALUES(hotel_country_id) , voyager_id = VALUES(voyager_id), last_update = VALUES(last_update) , hot_actstat = 'A'""".format(",".join(update_tuples))

        query = query.encode('ascii','ignore')
        query = query.decode('utf-8')
        # app.config['LOG'].info("Update / Inserting Rows In Hotel_location_mapping ")
        return self.__db.run_query(query)

    def get_recent_inserted_location_data(self,cur_time):
        query = "select count(*) from hotel_location_mapping where hot_actstat = 'A' and last_update = {0} ".format(cur_time)
        return self.__db.run_query(query)
    def commit(self):
        return self.__db.commit()