import json
import os
import yaml


config_file = os.path.dirname(os.path.realpath(__file__)) + "/config/gunicorn_config.yaml"
with open(config_file, 'r') as cf:
    cfg = yaml.safe_load(cf)

errorlog = cfg['errorlog']
bind = cfg['bind']
workers = cfg['workers']
worker_class = cfg['worker_class']
timeout = cfg['timeout']
graceful_timeout = cfg['graceful_timeout']
accesslog = cfg['accesslog']
access_log_format = cfg['access_log_format']
pid = cfg['pid']


def post_worker_init(worker):
    with open('workers_status.txt', 'r+') as f:
        worker_status = json.load(f)
        if worker_status.get('count') is None:
            worker.log.error("worker count variable not present")
            raise TypeError
        else:
            current_workers_count = int(worker_status.get('count')) + 1
            f.seek(0)
            worker_status = {'count': current_workers_count}
            json.dump(worker_status, f)
            worker.log.info("worker count incremented by 1 : %s ", current_workers_count)


def when_ready(server):
    worker_status = {'count': 0}
    with open('workers_status.txt', 'w') as f:
        json.dump(worker_status, f)
    server.log.info("Server is ready. Spawning workers")


def worker_exit(server, worker):
    with open('workers_status.txt', 'r+') as f:
        worker_status = json.load(f)
        if worker_status.get('count') is None:
            worker.log.error("worker count variable not present")
            raise TypeError
        else:
            current_workers_count = int(worker_status['count']) - 1
            f.seek(0)
            worker_status = {'count': current_workers_count}
            json.dump(worker_status, f)
            worker.log.info("worker count decremented by 1 : %s ", current_workers_count)
