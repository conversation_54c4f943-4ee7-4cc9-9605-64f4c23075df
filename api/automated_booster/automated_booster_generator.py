from flask import current_app as app

import resources.device_util as device_util
import common.helpers as hp

'''
    For Automated Booster, booster configuration (including automated booster type and automated booster experiment) 
    and location list (instead of hotel list) is uploaded via UI.
    Currently, automated booster type AA is supported. For AA, for each location uploaded, AA hotels in order of 
    base rank (i.e. automated booster experiment) are considered as hotel list.
'''


class AutomatedBoosterGenerator(object):

    def __init__(self, dao_mysql, dao_mysql_mmt3_read):
        self.log = hp.mdc_logger("AutomatedBoosterGenerator")
        self.__dao_mysql = dao_mysql
        self.__dao_mysql_mmt3 = dao_mysql_mmt3_read
        self.__automated_booster_aa_count = int(app.config['pms_prop']['automated_booster_aa_count'])

    def generate_booster_list(self, automated_booster_type, experiment, csv_data_list):
        self.log.info("Generating automated booster for {}", automated_booster_type)
        booster_list = list()

        # Case 1: Alternate Accommodations
        if automated_booster_type == "AA":
            location_list = [csv_data[0] for csv_data in csv_data_list]
            self.log.info("Locations: {}", location_list)
            # Get hotel rank data
            location_hotel_rank_list = self.__get_location_hotel_rank_list(experiment, location_list)
            if location_hotel_rank_list:
                # Get AA hotels
                aa_hotel_set = self.__get_aa_hotel_set(experiment, location_list)
                if aa_hotel_set:
                    # Filter hotel rank data for AA hotels
                    location_to_hotel_rank_aa = self.__filter_location_to_hotel_rank_aa(location_hotel_rank_list,
                                                                                        aa_hotel_set)
                    # Sort hotel rank data and add AA hotels to booster list,
                    # where count of AA hotels per location <= automated_booster_aa_count
                    booster_list_aa = self.__get_booster_list_aa(location_to_hotel_rank_aa)
                    booster_list += booster_list_aa
        self.log.info("Generated automated booster for {}", automated_booster_type)
        return booster_list

    def __get_location_hotel_rank_list(self, experiment, location_list):
        rank_table, rank_column = device_util.get_rank_table_and_column(experiment)
        location_hotel_rank_list = self.__dao_mysql_mmt3.get_location_hotel_rank(rank_table, rank_column, location_list)
        return location_hotel_rank_list

    def __get_aa_hotel_set(self, experiment, location_list):
        device = device_util.get_device_name(experiment)
        aa_hotel_set = self.__dao_mysql.get_aa_hotel_set(device, location_list)
        self.log.info("AA Hotels: {}", aa_hotel_set)
        return aa_hotel_set

    def __filter_location_to_hotel_rank_aa(self, location_hotel_rank_list, aa_hotel_set):
        location_to_hotel_rank_aa = dict()
        for location, hotel, rank in location_hotel_rank_list:
            if hotel in aa_hotel_set:
                hotel_rank_list = location_to_hotel_rank_aa[
                    location] if location in location_to_hotel_rank_aa else list()
                hotel_rank_list.append((hotel, rank))
                location_to_hotel_rank_aa[location] = hotel_rank_list
        return location_to_hotel_rank_aa

    def __get_booster_list_aa(self, location_to_hotel_rank_aa):
        booster_list_aa = list()
        for location, hotel_rank_list in location_to_hotel_rank_aa.items():
            booster_count = 0
            hotel_rank_list = sorted(hotel_rank_list, key=lambda x: x[1])
            for hotel, rank in hotel_rank_list:
                if booster_count < self.__automated_booster_aa_count:
                    # As booster list expects hotel, boost_percentage, location.
                    # Therefore, setting boost_percentage as None here,
                    # will be updated by boosted_sequence's i.e. default boost_percentage in Uploader
                    booster_list_aa.append((str(hotel), None, location))
                    booster_count += 1
                else:
                    # Limiting AA Hotels Count (automated_booster_aa_count - configured via PMS) per Location
                    self.log.info("AA Hotels Count > {} in Location: {}", self.__automated_booster_aa_count, location)
                    break
        return booster_list_aa
