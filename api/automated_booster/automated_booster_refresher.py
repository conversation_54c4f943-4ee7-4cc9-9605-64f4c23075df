from automated_booster.automated_booster_generator import Automated<PERSON>oosterGenerator
from automated_booster.automated_booster_upserter import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from dao.dao_mysql import DataAccessObjectMySQL as dao_mysql
from dao.dao_mysql_mmt3_read import dao_mysql_mmt3_read as dao_mysql_mmt3_read

import common.helpers as hp

'''
    For Automated Booster, as the hotel list is generated automatically using booster configuration 
    and location list uploaded via UI, the automated booster is refreshed weekly so that if any AA hotel
    is added/removed from location or AA hotel base rank (i.e. automated booster experiment) is
    updated, that is reflected.
    
    Note: If location does not have AA hotel at time of upload or refresh, 
    then that location is not considered for refresh in future.
    To consider location for refresh in future, the booster needs to be deleted and added again via UI.
'''


class AutomatedBoosterRefresher(object):

    def __init__(self):
        self.log = hp.mdc_logger("AutomatedBoosterRefreshService")
        self.__dao_mysql = dao_mysql(False)
        self.__dao_mysql_mmt3_read = dao_mysql_mmt3_read()
        self.__automated_booster_generator = AutomatedBoosterGenerator(self.__dao_mysql, self.__dao_mysql_mmt3_read)
        self.__automated_booster_upserter = AutomatedBoosterUpserter(self.__dao_mysql)

    def refresh_automated_booster(self, automated_booster_type):
        self.log.info("Refreshing automated booster for {}", automated_booster_type)
        experiment_location_to_uids = dict()
        experiment_locations_dict = dict()
        uid_data_dict = dict()
        algorithm_sequence_set = set()
        automated_booster_cursor = self.__dao_mysql.get_automated_booster(automated_booster_type)
        # Initialize data required for refreshing automated booster
        for uid, algorithm, sequence, location, experiment, startdate, enddate, booster_type, boost_percentage, \
            retire_rotate_signals in automated_booster_cursor.fetchall():
            hp.update_dict_value(experiment_location_to_uids, (experiment, location), uid)
            hp.update_dict_value(experiment_locations_dict, experiment, location)
            uid_data_dict[uid] = (startdate, enddate, booster_type, boost_percentage, retire_rotate_signals)
            algorithm_sequence_set.add((algorithm, sequence))
        self.log.info("UIDs: {}", uid_data_dict.keys())
        # Get booster list for each uid
        uid_boosters_dict = self.__get_uid_to_boosters(automated_booster_type, experiment_location_to_uids,
                                                       experiment_locations_dict)
        # Upload booster list for each uid
        self.__automated_booster_upserter.upsert_booster_in_database(uid_boosters_dict, uid_data_dict)
        # Refresh cache for each algorithm & sequence
        self.__automated_booster_upserter.upsert_booster_in_cache(algorithm_sequence_set)
        self.log.info("Refreshed automated booster for {}", automated_booster_type)

    def __get_uid_to_boosters(self, automated_booster_type, experiment_location_to_uids, experiment_locations_dict):
        uid_boosters_dict = dict()
        for experiment, locations in experiment_locations_dict.items():
            booster_list = self.__automated_booster_generator.generate_booster_list(automated_booster_type, experiment,
                                                                                    [[location] for location in
                                                                                     locations])
            self.log.info("Booster Count for Experiment: {}, Locations: {} is {}", experiment, locations,
                          len(booster_list))
            for location in locations:
                uids = experiment_location_to_uids[(experiment, location)]
                for uid in uids:
                    booster_list_for_uid = [booster for booster in booster_list
                                            if booster[2].upper() == location.upper()]

                    hp.update_dict_value(uid_boosters_dict, uid, booster_list_for_uid, 'list')
                    self.log.info("Booster Count for Experiment: {}, Location: {}, UID: {} is {}", experiment, location,
                                  uid, len(booster_list_for_uid))
        return uid_boosters_dict
