from datetime import datetime
from flask import current_app as app
from resources.ExploreSetUpdater import ExploreSetUpdater

import common.helpers as hp
import copy

'''
    For Automated Booster, the database is refreshed for each UID and the cache is refreshed 
    for each algorithm and sequence. The cache is refreshed for each algorithm and sequence as the same
    algorithm and sequence might contain Non-Automated Booster as well.
'''


class AutomatedBoosterUpserter(object):

    def __init__(self, dao_mysql):
        self.log = hp.mdc_logger("AutomatedBoosterUpserter")
        self.__dao_mysql = dao_mysql

    def upsert_booster_in_database(self, uid_boosters_dict, uid_data_dict):
        for uid, booster_list in uid_boosters_dict.items():
            existing_hotel_location_list, max_display_sequence = self.__get_existing_booster_data(uid)
            insert_rows, update_rows, remove_rows = self.__get_updated_booster_data(uid, booster_list, uid_data_dict,
                                                                                    existing_hotel_location_list,
                                                                                    max_display_sequence)
            self.__update_booster_in_database(insert_rows, update_rows, remove_rows)
            self.log.info("Uploaded Booster for UID: {}", uid)

    def __get_existing_booster_data(self, uid):
        existing_hotel_location_list = list()
        max_display_sequence = 0
        cur = self.__dao_mysql.get_hotel_location_sequence(uid)
        booster_data = cur.fetchall()
        if booster_data:
            existing_hotel_location_list = [(data[0], data[1]) for data in booster_data]
            booster_data = sorted(booster_data, key=lambda data: data[2])
            max_display_sequence = int(booster_data[-1][2])
        return existing_hotel_location_list, max_display_sequence

    def __get_updated_booster_data(self, uid, booster_list, uid_data_dict, existing_hotel_location_list,
                                   max_display_sequence):
        insert_rows = ""
        update_rows, remove_rows = [], []
        lastupdated = datetime.now()
        username = app.config['DEFAULT_USER']
        remove_hotel_location_list = copy.deepcopy(existing_hotel_location_list)
        if booster_list:
            startdate, enddate, booster_type, sequence_boost_percentage, retire_rotate_signals = uid_data_dict[uid]
            startdate = max(startdate, lastupdated)
            sequence = max_display_sequence + 1
            for hotel_id, boost_percentage, location_id in booster_list:
                hotel_location = (hotel_id, location_id)
                if hotel_location in existing_hotel_location_list:
                    update_rows.append(
                        ('active', startdate, lastupdated, username, sequence, sequence, sequence_boost_percentage,
                         uid, hotel_id, location_id))
                    remove_hotel_location_list.remove(hotel_location)
                else:
                    insert_rows = insert_rows + "('" + str(uid) + "','" + hotel_id + "','" + str(0.0) + \
                                  "','active',-1,-1,'" + str(startdate) + "','" + \
                                  str(enddate) + "','" + str(lastupdated) + "','" + \
                                  username + "','" + retire_rotate_signals + "'," + str(sequence) + "," + str(
                        sequence) + \
                                  "," + str(sequence_boost_percentage) + ",'" + location_id + "'),"
                sequence = sequence + 1
        for hotel, location in remove_hotel_location_list:
            remove_rows.append(('inactive', lastupdated, lastupdated, username, uid, hotel, location))
        insert_rows = insert_rows[:-1]
        return insert_rows, update_rows, remove_rows

    def __update_booster_in_database(self, insert_rows, update_rows, remove_rows):
        try:
            if insert_rows:
                self.__dao_mysql.add_hotels(insert_rows)
            if update_rows:
                self.__dao_mysql.update_hotels_automated_booster(update_rows)
            if remove_rows:
                self.__dao_mysql.remove_hotels_automated_booster(remove_rows)
            self.__dao_mysql.commit()
        except Exception as e:
            self.__dao_mysql.rollback()
            raise Exception(e, "Exception while updating Automated Booster in database")

    def upsert_booster_in_cache(self, algorithm_sequence_set):
        for algorithm, sequence in algorithm_sequence_set:
            explore_set_updater = ExploreSetUpdater()
            explore_set_updater.refresh_cache(algorithm, sequence)
            self.log.info("Refreshed Cache for Algorithm: {}, Sequence: {}", algorithm, sequence)
