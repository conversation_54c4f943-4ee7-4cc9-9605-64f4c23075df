
import redis
import common.helpers as hp
import common.gzip_util as gzip_util


class RedisCacheManager:
    log = hp.mdc_logger("redis-cache-manager")

    def __init__(self, config):
        self.config = config
        self.redis = self.get_redis_conn()

    def get_redis_conn(self):
        return redis.Redis(host=self.config['REDIS_HOST'],port=self.config['REDIS_PORT'])

    def get_data(self, key):
        try:
            val = self.redis.get(key)
            if val:
                return gzip_util.decompress_from_bytes(val)
        except Exception as e:
            RedisCacheManager.log.error("exception occurred {}".format(e))
            self.redis.close()
            self.redis = self.get_redis_conn()
        return None




