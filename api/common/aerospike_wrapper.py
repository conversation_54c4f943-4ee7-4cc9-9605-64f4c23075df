import aerospike
from common import gzip_util, helpers
from db.aws import secret_manager_config
CURR_ENV = "PROD"
class AerospikeWrapper:
    _asp_client = None
    aerospike_secrets = None
    log = helpers.mdc_logger("aerospike-cache-manager")

    def __init__(self, config):
        # if CURR_ENV in ["PROD"] and AerospikeWrapper.aerospike_secrets is None:
        #     try:
        #         AerospikeWrapper.aerospike_secrets = secret_manager_config.get_secret(CURR_ENV, "_AEROSPIKE")
        #     except Exception as e:
        #         AerospikeWrapper.log.error("failed to fetch secrets for aerospike {0}".format(e))

        self.config = {
          'hosts': config['AEROSPIKE_HOST']
        }
    def get_asp_client(self):
        if AerospikeWrapper._asp_client is None:
            try:
                if CURR_ENV in ["DEV"]:
                    # if AerospikeWrapper.aerospike_secrets:
                    username = "admin"
                    password = "admin"
                    AerospikeWrapper._asp_client = aerospike.client(self.config).connect(username, password)
                else:
                    AerospikeWrapper._asp_client = aerospike.client(self.config).connect()
            except Exception as e:
                AerospikeWrapper.log.info("failed to connect to the cluster with", self.config['hosts'])

    def set_data(self, asp_key, data, asp_set, namespace, ttl=None):
        compressed_value = gzip_util.compress_to_bytes(data)
        self.get_asp_client()
        if AerospikeWrapper._asp_client is None:
            return
        try:
            aerospike_key = (namespace, asp_set, asp_key)
            AerospikeWrapper._asp_client.put(aerospike_key, {
                "data": compressed_value
            }, meta={'ttl': ttl if ttl is not None else 60})
        except Exception as e:
            AerospikeWrapper.log.error("Error in setting data :: {0}".format(str(e)))

    def remove_data(self, asp_key, asp_set, namespace):
        self.get_asp_client()
        if AerospikeWrapper._asp_client is None:
            return
        try:
            aerospike_key = (namespace, asp_set, asp_key)
            AerospikeWrapper._asp_client.remove(aerospike_key)
        except Exception as e:
            error_code = getattr(e, 'code', None)
            # AEROSPIKE_ERR_RECORD_NOT_FOUND
            if error_code != 2:
                AerospikeWrapper.log.error("Error in removing data :: {0}".format(str(e)))

    def get_data(self, asp_key, asp_set, namespace):
        self.get_asp_client()
        if AerospikeWrapper._asp_client is None:
            return None
        try:
            aerospike_key = (namespace, asp_set, asp_key)
            (key, metadata, record) = AerospikeWrapper._asp_client.get(aerospike_key)
            if record:
                compressed_value = record['data']
                data = gzip_util.decompress_from_bytes(compressed_value)
                return data
            else:
                return None
        except Exception as e:
            AerospikeWrapper.log.error("Error in getting data :: {0}".format(str(e)))
            return None