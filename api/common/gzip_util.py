import json
import gzip


def compress_to_bytes(value):
    compressed_value = None
    try:
        bytes_value = json.dumps(value).encode()
        compressed_value = gzip.compress(bytes_value)
    except Exception as e:
        print("Exception while compressing value to bytes {}".format(e))
    return compressed_value


def decompress_from_bytes(compressed_value):
    value = None
    try:
        bytes_value = gzip.decompress(compressed_value)
        value = json.loads(bytes_value.decode())
    except Exception as e:
        print("Exception while decompressing value from bytes {}".format(e))
    return value
