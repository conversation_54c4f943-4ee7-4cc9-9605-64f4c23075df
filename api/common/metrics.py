import functools
import os
import pyformance.registry as reg
from pyformance.reporters.reporter import Reporter
from pyformance.reporters.csv_reporter import CsvReporter

from common.kafka_logger import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Utf8Encoder
from common.helpers import localhostip, mdc_logger


def time_calls(name):
    def wrapper2(fn):
        @functools.wraps(fn)
        def wrapper(*args, **kwargs):
            reg.meter("{}_calls".format(name)).mark()
            _timer = reg.timer("{}_time".format(name))
            with _timer.time(fn=name):
                try:
                    return fn(*args, **kwargs)
                except:
                    reg.meter("{}_errors".format(name)).mark()
                    raise

        return wrapper

    return wrapper2

def time_calls2(name):
    def wrapper2(fn):
        @functools.wraps(fn)
        def wrapper(*args, **kwargs):
            reg.meter("{}.calls".format(name)).mark()
            _timer = reg.timer("{}.time".format(name))
            with _timer.time(fn=name):
                try:
                    return fn(*args, **kwargs)
                except:
                    reg.meter("{}.errors".format(name)).mark()
                    raise

        return wrapper

    return wrapper2


def init_metrics_lib_opentsdb_reporter(project_name, kafka_hosts):
    reporter = KafkaOpenTSDBReporter(
        "DS-Hotels",
        project_name,
        kafka_hosts,
        reporting_interval=300)  #reporting_interval is in seconds
    reporter.start()


def init_csv_metrics(path):
    reporter = CsvReporter(path=path, reporting_interval=5)
    reporter.start()


class KafkaOpenTSDBReporter(Reporter):
    def __init__(self, name, project_name, kafka_hosts, reporting_interval, registry=None):
        super(KafkaOpenTSDBReporter, self).__init__(registry=registry,
                                                    reporting_interval=reporting_interval)
        self.name = name

        self.tags = {'IP_ADDRESS': localhostip(), 'PROJECT_NAME': project_name, 'PID': os.getpid()}

        self.client = KafkaLogger({"topic_id": "opentsdb_queue", "hosts": kafka_hosts},
                                  Utf8Encoder())
        self.log = mdc_logger("KafkaOpenTSDBReporter")

    def report_now(self, registry=None, timestamp=None):
        timestamp = timestamp or int(round(self.clock.time()))
        metrics = self._collect_metrics(registry or self.registry, timestamp)

        for m in metrics:
            try:
                self.client.send(m)
            except Exception as e:
                self.log.error("failed to write to kafka %s", e.message)

    def _collect_metrics(self, registry, timestamp):
        # todo: add METRIC_NAME tag also.
        metrics = registry.dump_metrics()
        tags_tmp = dict(self.tags)
        for metric_name, subtype_value_group in list(metrics.items()):
            tags_tmp['METHOD'] = metric_name
            for sub_type, value in list(subtype_value_group.items()):
                tags_tmp['VALUE'] = sub_type
                ts = ["{}={}".format(k, v) for k, v in list(tags_tmp.items())]
                yield "put {} {} {} {}".format(self.name, timestamp, value, " ".join(ts))

