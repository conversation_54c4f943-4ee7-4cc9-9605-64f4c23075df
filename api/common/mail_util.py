from email.message import EmailMessage
from flask import current_app as app

import smtplib


class mail_util(object):
    __log = 'Mail Util :: '

    def __init__(self):
        app.config['LOG'].info(self.__log + "initializing")
        self.__smtp = smtplib.SMTP('nsmail.mmt.mmt', 25)
        app.config['LOG'].info(self.__log + "initialized")

    def send_mail(self, subject, content, mail_to):
        app.config['LOG'].info(self.__log + "sending mail")
        try:
            msg = EmailMessage()
            msg['From'] = app.config['MAIL_FROM']

            default_mail_to = app.config['MAIL_TO'] if app.config['MAIL_TO'] else []
            default_mail_to = [default_mail_to] if type(default_mail_to).__name__ == 'str' else default_mail_to
            mail_to = mail_to if mail_to else []
            mail_to = [mail_to] if type(mail_to).__name__ == 'str' else mail_to
            default_mail_to.extend(mail_to)

            msg['To'] = default_mail_to
            msg['Subject'] = subject
            msg.set_content(content)
            self.__smtp.send_message(msg)
            app.config['LOG'].info(self.__log + "sent mail")
        except Exception as e:
            app.config['LOG'].error(self.__log + "exception while sending mail " + str(e))

    def __del__(self):
        app.config['LOG'].info(self.__log + "destructing")
        self.__smtp.quit()
        app.config['LOG'].info(self.__log + "destructed")
