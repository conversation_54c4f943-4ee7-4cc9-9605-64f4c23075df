from builtins import object
import boto3
import botocore
import os,sys,inspect
currentdir = os.path.dirname(os.path.abspath(inspect.getfile(inspect.currentframe())))
parentdir = os.path.dirname(currentdir)
sys.path.insert(0,parentdir)
import common.helpers as hp


class S3Client(object):
    def __init__(self, bkt):
        s3 = boto3.resource('s3')
        self.bucket = s3.Bucket(bkt)
        self.log = hp.mdc_logger("s3 client")

    def download_file_from_s3(self, key, out_file):
        try:
            self.bucket.download_file(key, out_file)
            self.log.info("file downloaded. Key : {}, out_file : {}".format(key, out_file))

        except botocore.exceptions.ClientError as e:
            self.log.error("The object does not exist." + e)
            if e.response['Error']['Code'] == "404":
                self.log.info("The object does not exist.")
            else:
                raise

    def download_folder_from_s3(self, file_filter, bucket, bkt_key, out_folder):
        if (os.path.exists(out_folder)) is False:
            os.makedirs(out_folder)
        a = boto3.client('s3').list_objects(Bucket=bucket, Prefix=bkt_key)['Contents']
        keys = [row['Key'] for row in a]
        try:
            for k in keys:
                if k[-1:] == '/' or file_filter(k) is False:
                    continue
                self.bucket.download_file(k, os.path.join(out_folder, k.split('/')[-1]))
            self.log.info("folder downloaded. out_file : {}".format(out_folder))
        except botocore.exceptions.ClientError as e:
            self.log.error("The object does not exist." + e)
            if e.response['Error']['Code'] == "404":
                self.log.info("The object does not exist.")
            else:
                raise
