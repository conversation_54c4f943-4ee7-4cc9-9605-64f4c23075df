from datetime import datetime, timedelta

class QueryFeaturizer(object):

    @staticmethod
    def get_is_weekend_visit(ci_dow, co_dow):
        is_weekend_visit = False
        if ci_dow == 'Thu' and co_dow != 'Fri': # and co_dow != 'Sat' :
            is_weekend_visit = True
        elif ci_dow == 'Fri' and co_dow != 'Sat':
            is_weekend_visit = True
        elif ci_dow == 'Sat':
            is_weekend_visit = True
        elif ci_dow == 'Sun' and co_dow == 'Mon':
            is_weekend_visit = True
        return is_weekend_visit

    @staticmethod
    def get_advance_period_cat(ap):
        if ap <= 2:
            ap_cat = str(ap)
        elif 3 <= ap <= 5:
            ap_cat = '3-5'
        elif 6 <= ap <= 10:
            ap_cat = '6-10'
        else:
            ap_cat = '>=11'
        return ap_cat


class DateUtils(object):
    dow_dict = {
        0 : "<PERSON>",
        1 : "<PERSON><PERSON>",
        2 : "Wed",
        3 : "Thu",
        4 : "Fri",
        5 : "Sat",
        6 : "Sun"
    }

    @staticmethod
    def Date2MinusDate1Days(date1=None, date2=None, formatString='%Y-%m-%d'):
        '''
        Compute date2-date1 in days
        :return: number of days
        '''
        if not date1: date1 = datetime.now().date()
        if not date2: date2 = datetime.now().date()
        if type(date1) == str:
            date1 = datetime.strptime(date1, formatString).date()
        if type(date2) == str:
            date2 = datetime.strptime(date2, formatString).date()
        # print (date1, date2)
        return (date2 - date1).days

    @staticmethod
    def get_dow(dt, dtformat='%Y-%m-%d'):
        '''
        Get day of week
        :param dt: date
        :param dtformat:
        :return: day of week in readable format ('Mon', 'Tue', etc.)
        '''
        return DateUtils.dow_dict[datetime.strptime(dt, dtformat).weekday()]

    @staticmethod
    def test():
        assert 'Sun' == DateUtils.get_dow('2017-12-31')
        # print('Tests successful')
