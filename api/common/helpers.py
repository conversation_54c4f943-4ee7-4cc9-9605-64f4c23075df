import functools
import uuid
import logging
import inspect
import os
import json,requests
import socket
import yaml
import time
from common.upsell_constants import UpsellConstants

from flask import request

hotel_upsell_api_filepath = '/opt/logs/Hotel-Personalization/upsell/hotel_upsell_api.log'
hotel_upsell_api_log_format = '%(asctime)s,%(msecs)d %(levelname)s [%(process)d] %(name)s.%(funcName)s[%(lineno)d]: %(message)s'


def readfile(file_like):
    with open(file_like, mode='r') as f:
        return f.read()


def localhostip():
    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    try:
        # doesn't even have to be reachable
        s.connect(('**************', 1))
        return s.getsockname()[0]
    except:
        return '127.0.0.1'
    finally:
        s.close()


class MDCAdapter(logging.LoggerAdapter):
    req_id = ""

    def process(self, msg, kwargs):
        return "[{}] {}".format(MDCAdapter.req_id, msg), kwargs

#
def mdc_uuid(fn):
    @functools.wraps(fn)
    def wrapper(*args, **kwargs):
        reqid = ""
        try:
            params = json.loads(request.data)
            reqid = params["correlationKey"]
        except Exception as e:
            reqid = uuid.uuid4()
        old_val = _mdc_update(reqid)
        try:
            return fn(*args, **kwargs)
        finally:
            _mdc_update(old_val)

    return wrapper

def _mdc_update(val):
    old_val = MDCAdapter.req_id
    MDCAdapter.req_id = val
    return old_val

# Used to create a LoggerAdapter instance
def mdc_logger(name):
    file_logger = logging.getLogger(name)
    file_logger.handlers = []
    gunicorn_logger = logging.getLogger('gunicorn.error')
    # append all gunicorn handlers except file handlers.
    for gunicorn_handler in gunicorn_logger.handlers[:]:
        if not isinstance(gunicorn_handler, logging.FileHandler):
            file_logger.handlers.append(gunicorn_handler)
    # we append our file handler to the file logger
    file_handler = logging.FileHandler(hotel_upsell_api_filepath)
    formatter = logging.Formatter(hotel_upsell_api_log_format)
    file_handler.setFormatter(formatter)
    file_logger.addHandler(file_handler)
    file_logger.setLevel(gunicorn_logger.level)
    return MDCAdapter(file_logger, {})


def log_assert(bool_, message, rethrow=False):
    """
    Use this if you want the failure of assert statements to be logged
    http://code.activestate.com/recipes/577074-logging-asserts/
    """
    try:
        assert bool_, message
    except AssertionError:
        last_stackframe = inspect.stack()[-2]
        source_file, line_no, func = last_stackframe[1:4]
        source = "Traceback (most recent call last):" + \
                 '  File "{}", line {}, in {}\n    '.format(source_file, line_no, func)

        source += last_stackframe[-2][0].strip()
        source = source.replace("\n", "^")
        errmsg = "{}: {}".format(message, source)
        logging.error(errmsg)
        if rethrow:
            raise AssertionError(errmsg)

def read_json(req, log=None):
    log = log or mdc_logger("helpers")
    raw_json = ""
    try:
        raw_json = req.stream.read()
        log.debug("Json read from request stream : {}.".format(raw_json))
        return json.loads(raw_json, encoding='utf-8')
    except ValueError as ex:
        log.exception("BAD_JSON: Error parsing input raw json{}. Exception occurred: {}".format(raw_json, ex.message))
        raise ex


def read_pms(config_path='/../config/comparator_config.yaml', log=None):
    log = log or mdc_logger("helpers")
    try:
        config_file = os.path.dirname(os.path.realpath(__file__)) + config_path
        with open(config_file, 'r') as cf:
            cfg = yaml.safe_load(cf)
        pms_url = cfg[cfg['Env']]['pmsUrl']
        pms_file_path = cfg[cfg['Env']]['pmsFilePath']
        resp = requests.get(pms_url)
        file1 = open(pms_file_path, "w")
        file1.writelines(resp.text)
        file1.close()
        return load_properties(pms_file_path)
    except Exception as ex:
        log.exception("Error getting pms properties. PMS url {}. Exception occurred: {}".format(pms_url, ex))
        raise ex


def load_properties(filepath, sep='=', comment_char='#'):
    """
    Read the file passed as parameter as a properties file.
    """
    props = {}
    with open(filepath, "rt") as f:
        for line in f:
            l = line.strip()
            if l and not l.startswith(comment_char):
                key_value = l.split(sep)
                key = key_value[0].strip()
                value = sep.join(key_value[1:]).strip().strip('"')
                props[key] = value
    if len(props) > 0 and props.get(UpsellConstants.PMS_USER_SEGMENT_KEY):
        props[UpsellConstants.PMS_USER_SEGMENT_KEY] = json.loads(props[UpsellConstants.PMS_USER_SEGMENT_KEY])
    return props


async def get_json_data_from_get_call_async(log, session, url, headers, params, timeout, api_name):
    start_time = time.time()
    async with session.get(url, params=params, headers=headers, timeout=timeout) as response:
        log.info('{} Time: {}, Status: {}'.format(api_name, (time.time() - start_time), response.status))
        start_time = time.time()
        resp_text = await response.text('UTF-8')
        log.info('{} Await Time: {}'.format(api_name, (time.time() - start_time)))
        start_time = time.time()
        result = json.loads(resp_text)
        log.info('{} Load Time: {}'.format(api_name, (time.time() - start_time)))
        return result


async def get_json_data_from_post_call_async(log, session, url, payload, headers, timeout, api_name):
    req = json.dumps(payload)
    start_time = time.time()
    async with session.post(url, data=req, headers=headers, timeout=timeout) as resp:
        log.info('{} Time: {}, Status: {}'.format(api_name, (time.time() - start_time), resp.status))
        start_time = time.time()
        resp_text = await resp.text()
        log.info('{} Await Time: {}'.format(api_name, (time.time() - start_time)))
        start_time = time.time()
        result = json.loads(resp_text)
        log.info('{} Load Time: {}'.format(api_name, (time.time() - start_time)))
        return result


def get_json_data_from_post_call(log, cfg, url, headers, body, log_message,timeout=None):
    req = json.dumps(body)
    log.info("{} Request is {}".format(log_message,req))
    url = cfg[cfg['Env']][url]
    resp = requests.post(url, data=req, headers=headers,timeout=timeout)
    resp_time = resp.elapsed
    log.info('{} Response Time: {}'.format(log_message, resp_time))
    start_time = time.time()
    resp = json.loads(resp.text)
    log.info('{} Load Time: {}'.format(log_message, (time.time() - start_time)))
    return resp


def get_json_data_from_get_call(log, cfg, url, headers, params, log_message):
    url = cfg[cfg['Env']][url]
    return get_data_for_url(url,log,headers,log_message, params)


def get_data_for_url( url,log,headers=None,log_message='', params=None):
    log.info("{} Url is {}".format(log_message, url))
    t = time.time()
    resp = requests.get(url, params=params, headers=headers)
    resp = json.loads(resp.text)
    log.info("Time taken by {} is {} ms ".format(log_message, time.time() - t))
    return resp


# Supported for adding/updating value as set or list to dict
def update_dict_value(data, key, value, value_type='set'):
    if value_type == 'list':
        values = data[key] if key in data else list()
        values.extend(value)
    else:
        values = data[key] if key in data else set()
        values.add(value)
    data[key] = values
