import common.gzip_util as gzip_util

from db.aws import secret_manager_config

import common.helpers as hp
from pyformance import registry as reg
from common.metrics import time_calls2


class CacheManager:

    log = hp.mdc_logger("cache-manager")

    def __init__(self, cfg):
        self.cfg = cfg
        self.SECRETS = secret_manager_config.get_secret("PROD", "_COUCHBASE")
        self._cb = Bucket('couchbase://' + cfg['HOSTS'] + cfg['BUCKET'], password=self.SECRETS['couchPassword'], quiet=True, timeout=cfg['TIMEOUT'])
        self._cache_hits = reg.counter("user.reco.cache.hits")
        self._cache_misses = reg.counter("user.reco.cache.misses")

    @time_calls2("user.reco.cache.get")
    def get_single(self, key):
        value = None
        try:
            doc = self._cb.get(key)
            value = doc.value
            # CacheManager.log.debug("The doc is {} and value is {}".format(doc, doc.value))
            if value is None:
                self._cache_misses.inc()
            else:
                '''
                    TODO: Below if check is required to handle uncompressed keys. 
                    To be removed after running gh_cache_refresh_dag and /similarDataS3ToCouch with compressed values
                '''
                if type(value).__name__ == 'bytes':
                    value = gzip_util.decompress_from_bytes(value)
                else:
                    CacheManager.log.info("Fetching old value for key {}".format(key))
                self._cache_hits.inc()
        except Exception as e:
            CacheManager.log.error("exception occurred {}".format(e))
            self.close_connection()
            self._cb = Bucket('couchbase://' + self.cfg['HOSTS'] + self.cfg['BUCKET'], password=self.SECRETS['couchPassword'], quiet=True,
                              timeout=self.cfg['TIMEOUT'])
            self._cache_misses.inc()
        return value

    @time_calls2("user.reco.cache.put")
    def put(self, key, value, ttl=604800):
        try:
            compressed_value = gzip_util.compress_to_bytes(value)
            self._cb.upsert(key, compressed_value, ttl=ttl, format=couchbase.FMT_BYTES)
        except Exception as e:
            CacheManager.log.error("error inserting value in couchbase: {}".format(e))

    @time_calls2("user.reco.cache.rmv")
    def remove(self, key):
        try:
            self._cb.remove(key)
        except Exception as e:
            CacheManager.log.error("error deleting value in couchbase: {}".format(e))
            raise e

    def close_connection(self):
        self._cb._close()