import io
import struct

import avro.schema
import avro.io
from kafka import SimpleProducer, SimpleClient

from common.helpers import readfile


class AvroEncoder:
    def __init__(self, template_id, schema_file):
        magic_byte = '\x00'
        self._header = magic_byte + struct.pack('>I', template_id) #"\x00\x00'\x95" #
        schema = readfile(schema_file)
        schema_avro = avro.schema.parse(schema)
        self._writer = avro.io.DatumWriter(schema_avro)

    def encode(self, msg):
        bytes_writer = io.BytesIO()
        # Adding MAGIC BYTES + TEMPLATE ID as header to message
        bytes_writer.write(self._header)
        encoder = avro.io.BinaryEncoder(bytes_writer)
        self._writer.write(msg, encoder)
        raw_bytes = bytes_writer.getvalue()
        return raw_bytes


class Utf8Encoder:
    def encode(self, msg):
        return msg.encode(encoding='UTF-8')


class KafkaLogger:
    def __init__(self, cfg, encoder):
        self._encoder = encoder
        self._topic_id = cfg['topic_id']
        self._producer = SimpleProducer(SimpleClient(cfg['hosts']), async=True)

    def send(self, payload):
        raw_bytes = self._encoder.encode(payload)
        self._producer.send_messages(self._topic_id, raw_bytes)
