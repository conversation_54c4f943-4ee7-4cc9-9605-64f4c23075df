import sys
import struct
import os
import io

import avro.schema
import avro.io

from common.kafka_logger import Kafka<PERSON>og<PERSON>, AvroEncoder
from common.helpers import readfile

class BoosterStatusLogger:
    def __init__(self):
        base_dir = os.path.dirname(os.path.abspath(__file__))
        schema_path = os.path.join(base_dir, '../resources/booster_log_schema')

        #self._kafka_logger = KafkaLogger({"topic_id": "hotels_ranking_booster_status", "hosts": 'kafka-hotels-c2.mmt.mmt:9092'}, AvroEncoder(98543, schema_path))

    def log(self, payload, log_reqd):
        if log_reqd:
            context = {"template_id" : "98543",
                       "server_timestamp" : payload.get("server_timestamp"),
                       "topic_name" : "hotels_ranking_booster_status"}
            payload["context"] = context
            self._kafka_logger.send(payload)

class AvroEncoder:
    def __init__(self, template_id, schema_file):
        magic_byte = '\x00'
        self._header = magic_byte.encode() + struct.pack('>I', template_id) #"\x00\x00'\x95" #
        schema = readfile(schema_file)
        schema_avro = avro.schema.Parse(schema)
        self._writer = avro.io.DatumWriter(schema_avro)

    def encode(self, msg):
        bytes_writer = io.BytesIO()
        # Adding MAGIC BYTES + TEMPLATE ID as header to message
        bytes_writer.write(self._header)
        encoder = avro.io.BinaryEncoder(bytes_writer)
        self._writer.write(msg, encoder)
        raw_bytes = bytes_writer.getvalue()
        return raw_bytes