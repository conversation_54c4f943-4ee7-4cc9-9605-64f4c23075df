# Author: <PERSON><PERSON><PERSON>
# Date: 6 Oct 2017

# coding: utf-8

from common.Utils import DateUtils
import os, json

from constants.api_constants import LineOfBusiness


class Settings(object):
    def __init__(self, log):
        log.info("Validating Request")

    def check_settings_gh(self, algorithm, source_seq_id, target_seq_id, city, hotels, log):
        if not (algorithm):
            log.error("Algorithm missing!")
            return 1
        if not (source_seq_id):
            log.error("Source sequence missing!")
            return 1
        if not (target_seq_id):
            log.error("Target sequence missing!")
            return 1
        if not (city):
            log.error("City missing!")
            return 1
        if not (hotels):
            log.error("Hotels missing!")
            return 1

        log.info("All well")
        return 0


    def check_settings_add_bu(self, algorithm, source_seq_id, start_date, end_date, block_size, nslots, log,
                              booster_type, appfest_booster,lob=LineOfBusiness.DEFAULT, start_block=0):
        if not algorithm:
            return 1, "Algorithm missing!"
        if not source_seq_id:
            return 1, "Source sequence missing!"
        if not start_date:
            return 1, "Start date for booster sequence missing!"
        if not end_date:
            return 1, "End date for booster sequence missing!"
        if booster_type == "slot_based":
            if block_size <= 1:
                return 3, "Block size of less than two is not allowed!"
            if block_size <= nslots:
                return 2, "BlockSize should be greater than nslots!"
        if DateUtils.Date2MinusDate1Days(start_date, end_date) < 0:
            return 1, "End date for booster is less than startdate !!"

        if(lob == LineOfBusiness.DEFAULT or appfest_booster == 'on'):
            return 0, "All well"

        # booster_restrictions are applicable only if appfestBooster is false.
        booster_restrictions = "/../config/booster_restrictions.json"
        config_file = os.path.dirname(os.path.realpath(__file__)) + booster_restrictions

        restrictions = []
        is_restriction_bucket_check = True
        if is_restriction_bucket_check and booster_type == "slot_based":
            with open(config_file, 'r') as f:
                restrictions = json.load(f)

        selected_lob_wise_restrictions = []
        for lob_wise_restrictions in restrictions:
            if lob_wise_restrictions['lob'] == lob:
                selected_lob_wise_restrictions = lob_wise_restrictions['buckets']

        if selected_lob_wise_restrictions:
            log.info(f'selected_lob_wise_restrictions: {selected_lob_wise_restrictions}')

            for restriction in selected_lob_wise_restrictions:
                start_index = restriction['start_index']
                end_index = restriction['end_index']
                max_allowed_hotels = restriction['max_allowed_hotels']

                if start_index <= start_block < end_index:
                    if block_size / nslots < (end_index - start_index) / max_allowed_hotels:
                        message = f'For slots {start_index} to {end_index}, only {max_allowed_hotels} in ' \
                                  f'{end_index - start_index} hotels can be boosted for {lob} .'
                        log.error(message)
                        return 1, message
        return 0, "All well"
