
# flask imports

# flask-login anonymous user class
from flask_login import AnonymousUserMixin

import ldap
from flask import request, current_app as app

from flask_login import UserMixin

class Authentication():
    def user_autheticate(self,username,password, domain):
        try:
            ldap_client = ldap.initialize(app.config['LDAP_IP_' + domain])
            ldap_client.set_option(ldap.OPT_REFERRALS, 0)
            ldap_client.simple_bind_s(app.config['USERNAME_PREFIX_' + domain] + username, password)
            base_dn = app.config['BASE_DN_' + domain]
            search_filter = "(|(objectclass=groupofnames)(sAMAccountName=" + username + "))"
            results = ldap_client.search_s(base_dn, ldap.SCOPE_SUBTREE, search_filter, ['memberOf'])
            for result in results:
                result_dn = result[0]
                result_attrs = result[1]
                if "memberOf" in result_attrs:
                    for member in result_attrs["memberOf"]:
                        str_member = member.decode()
                        if str_member.lower() == app.config['GROUP_NAME_' + domain]:
                            return {"success" : True}
            return {"success" : False,"message":"You are not allowed to view this page"}
        except ldap.LDAPError:
            return {"success" : False,"message":"Invalid username or password"}

class User(UserMixin):

    def __init__(self, username, password):
        self.username = username
        self.password = password

    def is_authenticated(self):
        return True

    def is_active(self):
        return True

    def is_anonymous(self):
        return False

    def get_id(self):
        return self.username

class Anonymous(AnonymousUserMixin):
	def __init__(self):
		self.username = 'System'