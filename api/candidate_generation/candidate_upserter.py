# Author: <PERSON><PERSON><PERSON>
# Date: 02-07-2018
# coding: utf-8

from flask import current_app as app
from datetime import datetime
from dateutil.relativedelta import relativedelta
from resources.Uploader import Uploader
import uuid


class candidate_upserter(object):
    __dao_mysql = None
    __log = 'Candidate Upserter :: '

    def __init__(self, dao_mysql):
        app.config['LOG'].info(self.__log + "initializing")
        self.__dao_mysql = dao_mysql
        app.config['LOG'].info(self.__log + "initialized")

    # Boosted Sequences

    def get_boosted_sequence_uids(self, device, sequence_new):
        uids = []
        app.config['LOG'].info(self.__log + "getting boosted sequence uids for device " + device)
        try:
            cur = self.__dao_mysql.get_boosted_sequence_uids(sequence_new)
            uids = [uid[0] for uid in cur.fetchall()]
            app.config['LOG'].info(self.__log + "got boosted sequence uids for device " + device + " - " + str(uids))
        except Exception as e:
            raise Exception(e, "exception while getting boosted sequence uids")
        return uids

    def add_or_update_boosted_sequence(self, is_dom, device, uids, sequence_new):
        uid = None

        push_filtered_hotels_to_db = app.config['PUSH_FILTERED_HOTELS_TO_DB_DOM'] if is_dom else app.config[
            'PUSH_FILTERED_HOTELS_TO_DB_INTL']

        push_gh_candidates_to_db = app.config['PUSH_GH_CANDIDATES_TO_DB_DOM'] if is_dom else app.config[
            'PUSH_GH_CANDIDATES_TO_DB_INTL']

        if push_filtered_hotels_to_db or push_gh_candidates_to_db:
            app.config['LOG'].info(self.__log + "inserting/updating boosted sequence uid for device " + device)
            try:
                if len(uids) == 1:
                    uid = uids[0]
                    self.__dao_mysql.update_sequence(uid, app.config['DEFAULT_USER'])
                else:
                    uid = str(uuid.uuid1())
                    domain = 'DOM' if is_dom else 'INTL'
                    retire_rotate_signals = "{\"booking_absolute\":[" + str(app.config['BKG_ABS_CUTOFF_VAL']) + "," + \
                                            str(app.config['BKG_ABS_CUTOFF_PERC']) + "]}"
                    self.__dao_mysql.add_sequence(uid, (domain + '_' + device.upper()), sequence_new,
                                                  app.config['DEFAULT_START_BLOCK'], 'NEWHOTELS',
                                                  app.config['DEFAULT_BLOCKSIZE'], app.config['DEFAULT_N_SLOTS'],
                                                  app.config['DEFAULT_MAX_HOTELS'], str(datetime.now()),
                                                  str(datetime.now() + relativedelta(years=1)), None, None, None,
                                                  None, None, None, None, app.config['DEFAULT_PRIORITY'], domain, 0,None,
                                                  None,
                                                  app.config['DEFAULT_USER'], None, retire_rotate_signals, 'S', 0)
                app.config['LOG'].info(self.__log + "inserted/updated boosted sequence uid " + str(uid))
            except Exception as e:
                raise Exception(e, "exception while inserting/updating boosted sequence uids")
        else:
            app.config['LOG'].error(
                self.__log + "skipping getting and pushing boosted sequence to db as push_filtered_hotels_to_db = False and push_gh_candidates_to_db = False")
        return uid

    # Boosted Hotels

    def get_boosted_hotels_for_uid(self, uid):
        boosted_hotels = []
        app.config['LOG'].info(self.__log + "getting boosted hotels for uid " + str(uid))
        try:
            cur = self.__dao_mysql.get_boosted_hotels_for_uid(uid)
            boosted_hotels = [hotel[0] for hotel in cur.fetchall()]
            app.config['LOG'].info(self.__log + "got boosted hotels " + str(len(boosted_hotels)))
        except Exception as e:
            raise Exception(e, "exception while getting boosted hotels")
        return boosted_hotels

    def push_boosted_hotels_to_db_basis_device(self, is_dom, device, uid, filtered_hotel_to_city_list, hotel_scores):
        push_gh_candidates_to_db = app.config['PUSH_GH_CANDIDATES_TO_DB_DOM'] if is_dom else app.config[
            'PUSH_GH_CANDIDATES_TO_DB_INTL']
        if push_gh_candidates_to_db:
            if uid:
                app.config['LOG'].info(self.__log + "pushing boosted hotels to db for device " + device)
                self.upsert_boosted_hotels(device, uid, filtered_hotel_to_city_list, hotel_scores)
                app.config['LOG'].info(self.__log + "pushed boosted hotels to db for device " + device)
            else:
                app.config['LOG'].error(
                    self.__log + "skipping pushing boosted hotels to db as no uid for device " + device)
        else:
            app.config['LOG'].error(
                self.__log + "skipping pushing boosted hotels to db for device " + device + " as push_gh_candidates_to_db = False")

    def upsert_boosted_hotels(self, device, uid, filtered_hotel_to_city_list, hotel_scores):

        hotels = set()
        cities = set()
        city_hotels = dict()

        hotels = set(hotel_city[0] for hotel_city in filtered_hotel_to_city_list)

        app.config['LOG'].info(self.__log + "appending boosted hotels to db for device " + device)
        try:
            if hotels:
                up = Uploader()
                up.uid = [uid]
                append_hotel_scores = {str(hotel): score if score else 0 for hotel, score in hotel_scores.items()}
                sorted_hotels = sorted(append_hotel_scores, key=lambda x: append_hotel_scores[x], reverse=True)
                up.booster_list = [[str(hotel), 0] for hotel in sorted_hotels]
                up.end_date = str(datetime.now() + relativedelta(years=1))
                up.retire_rotate_signals = "{\"booking_absolute\":[" + str(app.config['BKG_ABS_CUTOFF_VAL']) + "," \
                                             + str(app.config['BKG_ABS_CUTOFF_PERC']) + "]}"
                up.append_greenhorn_hotels(app.config['DEFAULT_USER'], append_hotel_scores,
                                           app.config['SOURCE_CANDIDATE_GENERATION'])
        except Exception as e:
            raise Exception(e, "exception while appending boosted hotels to db for device " + device)
        app.config['LOG'].info(self.__log + "appended boosted hotels to db for device " + device)

    # Filtered Hotels

    def get_filtered_hotels_for_uid(self, uid):
        filtered_hotels = []
        app.config['LOG'].info(self.__log + "getting filtered hotels for uid " + str(uid))
        try:
            cur = self.__dao_mysql.get_filtered_hotels_for_uid(uid)
            filtered_hotels = [hotel[0] for hotel in cur.fetchall()]
            app.config['LOG'].info(self.__log + "got filtered hotels " + str(len(filtered_hotels)))
        except Exception as e:
            raise Exception(e, "exception while getting filtered hotels")
        return filtered_hotels

    def push_filtered_hotels_to_db(self, is_dom, filter, devices, device_to_uid, filtered_data):
        for device in devices.split(','):
            self.push_filtered_hotels_to_db_basis_device(is_dom, filter, device, device_to_uid.get(device),
                                                         filtered_data)

    def push_filtered_hotels_to_db_basis_device(self, is_dom, filter, device, uid, filtered_data):

        push_filtered_hotels_to_db = app.config['PUSH_FILTERED_HOTELS_TO_DB_DOM'] if is_dom else app.config[
            'PUSH_FILTERED_HOTELS_TO_DB_INTL']

        if push_filtered_hotels_to_db:
            if uid:
                app.config['LOG'].info(
                    self.__log + "pushing filtered hotels to db for filter and device " + filter + " and " + device)
                try:
                    self.upsert_filtered_hotels(device, uid, filtered_data)
                    app.config['LOG'].info(
                        self.__log + "pushed filtered hotels to db for filter and device " + filter + " and " + device)
                except Exception as e:
                    raise Exception(e,
                                    "exception while pushing filtered hotels to db for filter and device " + filter + " and " + device)
            else:
                app.config['LOG'].error(
                    self.__log + "skipping pushing filtered hotels to db as no uid for device " + device)
        else:
            app.config['LOG'].error(
                self.__log + "skipping pushing filtered hotels to db for filter and device " + filter + " and " + device + " as push_filtered_hotels_to_db = False")

    def upsert_filtered_hotels(self, device, uid, filtered_data):

        filtered_out_hotel= filtered_data[1]
        filtration_reason = filtered_data[2]
        filtration_score = filtered_data[3]
        filtered_out_hotel_to_reason = filtered_data[4]
        filtered_out_hotel_to_score = filtered_data[5]

        variables = {'uid': uid, 'created_by': app.config['DEFAULT_USER'],
                     'updated_by': app.config['DEFAULT_USER']}
        filtered_hotels_in_db = self.get_filtered_hotels_for_uid(uid)
        add_rows = ''
        update_rows = []

        if filtered_out_hotel:
            app.config['LOG'].info(self.__log + "creating insert/update query for filtered hotels for device " + device)
            for hotel in filtered_out_hotel:
                hotel_filtration_reason = filtration_reason
                hotel_filtration_score = filtration_score
                try:

                    if not hotel_filtration_reason and filtered_out_hotel_to_reason:
                        hotel_filtration_reason = filtered_out_hotel_to_reason.get(hotel)
                    if not hotel_filtration_score and filtered_out_hotel_to_score:
                        hotel_filtration_score = filtered_out_hotel_to_score.get(hotel)

                    if hotel in filtered_hotels_in_db:
                        update_rows.append(
                            (hotel_filtration_reason, hotel_filtration_score, app.config['DEFAULT_USER'],
                             str(datetime.now()), uid,
                             str(hotel)))
                    else:
                        variables['hotelid'] = hotel
                        variables['filtration_reason'] = hotel_filtration_reason
                        variables['filtration_score'] = hotel_filtration_score
                        add_rows = add_rows + "('{uid}','{hotelid}','{filtration_reason}','{filtration_score}','{created_by}',now(),'{updated_by}',now()),".format(
                            **variables)
                except Exception as e:
                    raise Exception(e, "exception while creating insert/update query for hotel " + str(hotel))

            add_rows = add_rows[:-1]
            app.config['LOG'].info(self.__log + "created insert/update query for filtered hotels for device " + device)
        else:
            app.config['LOG'].info(
                self.__log + "not creating insert/update query for filtered hotels as no filtered out hotels for device " + device)

        app.config['LOG'].info(self.__log + "inserting/updating filtered hotels to db for device " + device)
        try:
            if add_rows:
                self.__dao_mysql.insert_filtered_hotels(add_rows)
            if update_rows:
                self.__dao_mysql.update_filtered_hotels(update_rows)
        except Exception as e:
            raise Exception(e, "exception while pushing inserting/updating hotels to db for device " + device)
        app.config['LOG'].info(self.__log + "inserted/updated filtered hotels to db for device " + device)
