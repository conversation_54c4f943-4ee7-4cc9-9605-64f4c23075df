# Author: <PERSON><PERSON><PERSON>
# Date: 02-07-2018
# coding: utf-8

from flask import current_app as app
from candidate_generation.candidate_upserter import candidate_upserter

import pandas as pd
import os


class candidate_filter(object):
    __dao_mysql = None
    __dao_mysql_mmt_read = None
    __log = 'Candidate Filter :: '

    def __init__(self, dao_mysql, dao_mysql_mmt_read):
        app.config['LOG'].info(self.__log + "initializing")
        self.__dao_mysql = dao_mysql
        self.__dao_mysql_mmt_read = dao_mysql_mmt_read
        self.__cu = candidate_upserter(self.__dao_mysql)
        fname = app.config['S3_FILE_HOTEL_LOCUS']
        data_dir = app.config['DATADIR']
        self.hotel_location_df = pd.read_csv(os.path.join(data_dir, fname), dtype={'hotel_id': str, 'location_id': str})
        app.config['LOG'].info(self.__log + "initialized")
        self.pms_properties = app.config['pms_prop']

    '''
        Result is initialized, and contains:
        1. filtered_hotel_location_list represents hotel and location after filtering
        2. filtered_out_hotels represents hotels that are filtered out
        3. filtered_out_hotel_to_score represents respective scores of hotels that are filtered out
    '''
    def __initialize_result(self, hotel_location_list):
        return list(hotel_location_list), set(), dict()

    '''
        Result is filtered/updated as per the filtering score and condition
    '''
    def __filter_result(self, result, hotel, location, score, is_minimum_condition):
        filtered_hotel_location_list = result[0]
        filtered_out_hotels = result[1]
        filtered_out_hotel_to_score = result[2]
        filtered_out_hotels.add(hotel)
        if hotel not in filtered_out_hotel_to_score or \
                (score < filtered_out_hotel_to_score[hotel] if is_minimum_condition
                    else score > filtered_out_hotel_to_score[hotel]):
            filtered_out_hotel_to_score[hotel] = score
        filtered_hotel_location_list.remove((hotel, location))
        return filtered_hotel_location_list, filtered_out_hotels, filtered_out_hotel_to_score

    '''
        Result is cleaned, 
        i.e. all the hotels that are present in filtered_hotel_location_list are removed from 
        filtered_out_hotels/filtered_out_hotel_to_score.
        This is required, as a hotel can be present in multiple locations and can be a part of both 
        filtered_hotel_location_list as well as filtered_out_hotels/filtered_out_hotel_to_score.
        But, currently the new hotels and boosted hotels do not support multi location 
        i.e. a rank of a hotel for boosting is same irrespective of it's location
    '''
    def __clean_result(self, result, filtration_reason):
        filtered_hotel_location_list = result[0]
        filtered_out_hotels = result[1]
        filtered_out_hotel_to_score = result[2]
        for hotel, location in filtered_hotel_location_list:
            if hotel in filtered_out_hotels:
                filtered_out_hotels.remove(hotel)
            if hotel in filtered_out_hotel_to_score:
                filtered_out_hotel_to_score.pop(hotel)
        return result[:2] + (filtration_reason, None, None) + result[2:]

    def __get_hotel_set(self, hotel_location_list, is_string_type=None):
        return set([str(hotel) if is_string_type else hotel for hotel, location in hotel_location_list])

    def filter_cities(self, hotel_location_list, hotel_to_attributes):
        location_to_hotel_count = self.hotel_location_df.groupby(['location_id']).hotel_id.agg({'count'}).reset_index()
        filtered_loc_to_htl_count = location_to_hotel_count[location_to_hotel_count['count']<app.config['MINIMUM_HOTELS_IN_CITY']]
        filtered_location_to_hotel_count = filtered_loc_to_htl_count.astype({'count': str})
        filtered_loc_to_htl_count_dict = dict(zip(filtered_location_to_hotel_count['location_id'], filtered_location_to_hotel_count['count']))
        filtered_out_locations =   set(filtered_loc_to_htl_count['location_id'])
        app.config['LOG'].info("Cities :: got cities to be filtered - " + str(len(filtered_out_locations)))
        result = self.__initialize_result(hotel_location_list)
        for hotel, location in hotel_location_list:
            if location in filtered_out_locations:
                hotel_count = filtered_loc_to_htl_count_dict.get(location)
                result = self.__filter_result(result, hotel, location, hotel_count, True)
        return self.__clean_result(result, "HOTEL_COUNT_IN_CITY < " + str(app.config['MINIMUM_HOTELS_IN_CITY']))

    def filter_hotels_basis_content(self, hotel_location_list, hotel_to_attributes):
        return self.__filter_hotels_basis_score(hotel_location_list, hotel_to_attributes, 'cs', 'CONTENT_SCORE',
                                                float(self.pms_properties['nh_min_content_score']))

    def filter_hotels_basis_inventory(self, hotel_location_list, hotel_to_attributes):
        return self.__filter_hotels_basis_score(hotel_location_list, hotel_to_attributes, 'is', 'INVENTORY_SCORE',
                                                float(self.pms_properties['nh_min_inventory_score']))

    def filter_hotels_basis_review(self, hotel_location_list, hotel_to_attributes):
        return self.__filter_hotels_basis_score(hotel_location_list, hotel_to_attributes, 'rs', 'REVIEW_SCORE',
                                                float(self.pms_properties['nh_min_review_score']))

    def filter_fraud_hotels(self, hotel_location_list, hotel_to_attributes):
        hotel_list = self.__get_hotel_set(hotel_location_list, True)
        cur = self.__dao_mysql_mmt_read.get_fraud_hotels(hotel_list)
        filtered_out_hotels = set(str(hotel_list[0]) for hotel_list in cur.fetchall())
        app.config['LOG'].info("Fraud :: got hotels to be filtered - " + str(len(filtered_out_hotels)))
        filtered_hotel_city_list = [hotel_city for hotel_city in hotel_location_list if
                                    str(hotel_city[0]) not in filtered_out_hotels]
        return (filtered_hotel_city_list, filtered_out_hotels, "FRAUD_HOTEL", "NA", None, None)

    def filter_hotel_basis_exposure(self, hotel_location_list, city_hotelcount_dict, sequence, sequence_new, device):
        hotel_list = self.__get_hotel_set(hotel_location_list, True)
        cur = self.__dao_mysql.get_exposed_hotels(hotel_list, sequence_new)
        filtered_out_hotels = set(str(hotel_list[0]) for hotel_list in cur.fetchall())
        app.config['LOG'].info("Exposure :: got hotels to be filtered - " + str(len(filtered_out_hotels)))
        filtered_hotel_city_list = [hotel_city for hotel_city in hotel_location_list if
                                    str(hotel_city[0]) not in filtered_out_hotels]
        return (filtered_hotel_city_list, filtered_out_hotels, "HOTEL_EXPOSED_TO_GH", "NA", None, None)

    def filter_hotel_basis_rank(self, hotel_location_list, city_hotelcount_dict, sequence, sequence_new, device):
        hotel_list = self.__get_hotel_set(hotel_location_list)
        cur = self.__dao_mysql.get_hotels_basis_rank(hotel_list, "HSEQ" + sequence)
        hotel_location_to_rank = {(str(hotel), location): rank for hotel, location, rank in cur.fetchall()}
        result = self.__initialize_result(hotel_location_list)
        for hotel, location in hotel_location_list:
            hotel_location = (hotel, location)
            if hotel_location in hotel_location_to_rank:
                rank = hotel_location_to_rank.get(hotel_location)
                hotel_count = city_hotelcount_dict.get(location)
                minimum_rank = int(app.config['MINIMUM_RANK_VALUE'])
                minimum_rank_relative = int(app.config['MINIMUM_RANK_PERCENTAGE'] * (hotel_count if hotel_count else 0))
                if rank < minimum_rank and rank < minimum_rank_relative:
                    result = self.__filter_result(result, hotel, location, rank, True)
        return self.__clean_result(result, "RANK < " + str(app.config['MINIMUM_RANK_VALUE']) + "/"
                                   + str(app.config['MINIMUM_RANK_PERCENTAGE']) + "%")

    def filter_hotels_basis_room_nights(self, hotel_location_list, city_hotelcount_dict, sequence, sequence_new, device):
        hotel_list = self.__get_hotel_set(hotel_location_list)
        cur = self.__dao_mysql.get_hotel_location_rn(hotel_list, device)
        hotel_location_to_rn = {(str(hotel), location): room_nights for hotel, location, room_nights in cur.fetchall()}
        result = self.__initialize_result(hotel_location_list)
        for hotel, location in hotel_location_list:
            hotel_location = (hotel, location)
            if hotel_location in hotel_location_to_rn:
                room_nights = hotel_location_to_rn.get(hotel_location)
                result = self.__filter_result(result, hotel, location, room_nights, False)
        app.config['LOG'].info("Room Nights :: got hotels to be filtered - " + str(len(result[1])))
        return self.__clean_result(result, "ROOM_NIGHTS >= " + str(app.config['MAXIMUM_ROOM_NIGHTS']))

    def __filter_hotels_basis_score(self, hotel_location_list, hotel_to_attributes, attr_key, attr_name, min_attr_score):
        result = self.__initialize_result(hotel_location_list)
        for hotel, location in hotel_location_list:
            attributes = hotel_to_attributes.get(hotel)
            attribute_score = attributes.get(attr_key)
            if attribute_score != 0.0 and attribute_score < min_attr_score:
                result = self.__filter_result(result, hotel, location, attribute_score, True)
        app.config['LOG'].info(attr_name + " :: got hotels to be filtered - " + str(len(result[1])))
        return self.__clean_result(result, attr_name + " < " + str(min_attr_score))
