# Author: <PERSON><PERSON><PERSON>
# Date: 02-07-2018
# coding: utf-8

from flask import current_app as app


class candidate_scorer(object):
    __dao_mysql = None
    __dao_mysql_mmt_read = None
    __log = 'Candidate Scorer :: '

    def __init__(self, dao_mysql, dao_mysql_mmt_read):
        app.config['LOG'].info(self.__log + "initializing")
        self.__dao_mysql = dao_mysql
        self.__dao_mysql_mmt_read = dao_mysql_mmt_read
        app.config['LOG'].info(self.__log + "initialized")

    def score_basis_area(self, is_dom, device, hotel_to_location_list, hotel_to_area, hotel_to_city, area_to_hotels, city_to_hotels,
                         hotel_to_booking, sequence, hotel_to_attributes):

        normalized_hotel_score = {}
        hotel_score = {}

        # Get bookings for each area
        area_to_booking = {}
        for key, value in area_to_hotels.items():
            booking = 0
            for hotel in value:
                booking = booking + (hotel_to_booking.get(hotel) if hotel_to_booking.get(hotel) else 0)
            area_to_booking.update({key: booking})

        # Get bookings for each city
        city_to_booking = {}
        for key, value in city_to_hotels.items():
            booking = 0
            for hotel in value:
                booking = booking + (hotel_to_booking.get(hotel) if hotel_to_booking.get(hotel) else 0)
            city_to_booking.update({key: booking})

        # Find hotels score
        for hotel, location in hotel_to_location_list:
            area = hotel_to_area.get(hotel)
            area_booking = area_to_booking.get(area)

            city_list = hotel_to_city.get(hotel)
            city_booking = 0
            for city in city_list:
              city_booking = max(city_booking, city_to_booking.get(city) if city_to_booking.get(city) else 0)

            score = 0 if not city_booking or not area_booking else (area_booking / city_booking)
            if score > 1:
                app.config['LOG'].error(
                    self.__log + "area score > 1 (setting as 0) for hotel - " + str(hotel))
            hotel_score.update({hotel: 0 if score > 1 else score})

        app.config['LOG'].info(self.__log + "score basis area for " + device + " " + str(hotel_score))

        # Normalize area score
        for key, value in hotel_score.items():
            if value or value == 0:
                value = (1 - value) if is_dom else value
                normalized_hotel_score.update({key: round(value, 3)})

        return normalized_hotel_score

    def score_basis_quality(self, is_dom, device, hotel_to_location_list, hotel_to_area, hotel_to_city, area_to_hotels, city_to_hotels,
                            hotel_to_booking, sequence, hotel_to_attributes):

        normalized_hotel_score = {}
        hotel_score = dict((hotel, attributes.get('qs')) for hotel, attributes in hotel_to_attributes.items())
        app.config['LOG'].info(self.__log + "score basis quality for " + device + " " + str(hotel_score))

        # Normalize quality score
        if len(hotel_score) > 0:
            maximum_score = max(hotel_score.values())
            minimum_score = min(hotel_score.values())

            maximum_score = maximum_score if maximum_score else 0
            minimum_score = minimum_score if minimum_score else 0

            denominator = maximum_score - minimum_score

            if not denominator:
                normalized_hotel_score = dict.fromkeys(hotel_score, 0)
            else:
                for key, value in hotel_score.items():
                    if value or value == 0:
                        normalized_hotel_score.update({key: round((value - minimum_score) / denominator, 3)})

        return normalized_hotel_score

    def score_basis_rank(self, is_dom, device, hotel_to_location_list, hotel_to_area, hotel_to_city, area_to_hotels, city_to_hotels,
                         hotel_to_booking, sequence, hotel_to_attributes):

        normalized_hotel_score = {}
        hotel_score = {}

        hotels = set([hotel for hotel, location in hotel_to_location_list])
        cur = self.__dao_mysql.get_hotel_location_rank(hotels, "HSEQ" + sequence)
        hotel_location_rank_list = [(str(hotel), location, rank) for hotel, location, rank in cur.fetchall()]

        for hotel, location, rank in hotel_location_rank_list:
            if (hotel, location) in hotel_to_location_list and (hotel not in hotel_score or rank < hotel_score[hotel]):
                hotel_score[hotel] = rank
        app.config['LOG'].info(self.__log + "score basis rank for " + device + " " + str(hotel_score))

        # Normalize rank score
        if len(hotel_score) > 0:

            maximum_rank = max(hotel_score.values())
            minimum_rank = min(hotel_score.values())

            maximum_rank = maximum_rank if maximum_rank else 0
            minimum_rank = minimum_rank if minimum_rank else 0

            denominator = maximum_rank - minimum_rank

            if not denominator:
                normalized_hotel_score = dict.fromkeys(hotel_score, 0)
            else:
                for key, value in hotel_score.items():
                    if value:
                        normalized_hotel_score.update({key: round((maximum_rank - value) / denominator, 3)})

        return normalized_hotel_score
