# Author: <PERSON><PERSON><PERSON>
# Date: 02-07-2018
# coding: utf-8

from dao.dao_mysql import DataAccessObjectMySQL as dao_mysql
from dao.dao_mysql_mmt_read import dao_mysql_mmt_read as dao_mysql_mmt_read
from dao.dao_mysql_hotel_booker import dao_mysql_hotel_booker as dao_mysql_hotel_booker
from flask import current_app as app
from candidate_generation.candidate_filter import candidate_filter
from candidate_generation.candidate_scorer import candidate_scorer
from candidate_generation.candidate_upserter import candidate_upserter
from common.mail_util import mail_util
from handlers.common_handler import CommonHandler

import pandas as pd
import os


class candidate_generator(object):
    __dao_mysql = None
    __dao_mysql_mmt_read = None
    __dao_mysql_hotel_booker = None
    __log = 'Candidate Generator :: '
    __device_sequence = {"android": ["119", "2"], "iphone": ["069", "1"], "desktop": ["019", "0"]}

    def __init__(self):
        app.config['LOG'].info(self.__log + "initializing")
        self.__dao_mysql_mmt_read = dao_mysql_mmt_read()
        self.__dao_mysql = dao_mysql()
        self.__dao_mysql_hotel_booker = dao_mysql_hotel_booker()
        self.__cu = candidate_upserter(self.__dao_mysql)
        self.__device_hotel_to_booking = {}
        self.__hotel_to_attributes = {}
        fname = app.config['S3_FILE_HOTEL_LOCUS']
        data_dir = app.config['DATADIR']
        self.hotel_location_df = pd.read_csv(os.path.join(data_dir, fname),dtype={'hotel_id':str,'location_id':str})
        self.__filters = app.config['NEW_HOTELS_FILTERS']
        self.__scorers = app.config['NEW_HOTELS_SCORERS']
        app.config['LOG'].info(self.__log + "initialized")

    def generate_candidates(self, is_dom, devices):

        mail_content = ''

        try:

            app.config['LOG'].info(self.__log +
                                   "generating candidates")

            # Step 1: Getting new hotels
            hotel_to_location_list = self.get_new_hotel_city_list(is_dom)


            # Step 2 : Getting boosted sequence for device and pushing boosted sequence to db
            device_to_uid = dict()
            for device in devices.split(','):
                uids = self.__cu.get_boosted_sequence_uids(device, self.__device_sequence.get(device)[1])
                if len(uids) > 1:
                    app.config['LOG'].error(
                        self.__log + "skipping pushing boosted sequence to db as multiple uids for " + device)
                else:
                    uid = self.__cu.add_or_update_boosted_sequence(is_dom, device, uids,
                                                                   self.__device_sequence.get(device)[1])
                    device_to_uid[device] = uid

            # Step 3 : Fetch hotel attributes required for content, inventory and review filter; and quality scorer
            if self.is_hotel_to_attributes_required():
                if self.__hotel_to_attributes:
                    app.config['LOG'].info("Already got hotel to attributes")
                else:
                    self.__hotel_to_attributes = self.get_hotel_to_attributes(hotel_to_location_list)


            # Step 3(a) : Filtering new hotels and pushing filtered hotels to db
            hotel_to_location_list = self.filter_new_hotels(is_dom, hotel_to_location_list, devices, device_to_uid)

            # Step 3(b): Filtering new hotels basis device and pushing filtered hotels to db
            filtered_device_hotel_to_city_list = {}
            filtered_hotel_to_city_list = set()
            city_hotelcount_dict = self.get_hotel_count_for_location(hotel_to_location_list)
            for device in devices.split(','):
                hotel_to_city_list_device = self.filter_new_hotels_basis_device(is_dom, device, hotel_to_location_list,
                                                                                city_hotelcount_dict,
                                                                                self.__device_sequence.get(device),
                                                                                device_to_uid.get(device))
                filtered_hotel_to_city_list.update(hotel_to_city_list_device)
                filtered_device_hotel_to_city_list.update({device: hotel_to_city_list_device})
                app.config['LOG'].info(
                    self.__log + "filtered hotels for " + device + " " + str(len(hotel_to_city_list_device)))

            # Fetch all the data required for area score calculation
            hotel_to_area = {}
            hotel_to_location = {}
            area_to_hotels = {}
            location_to_hotels = {}

            if self.__scorers.__contains__("score_basis_area"):
                hotels = set([fhtc[0] for fhtc in filtered_hotel_to_city_list])
                locations = set([fhtc[1] for fhtc in filtered_hotel_to_city_list])
                hotel_to_area_list = self.get_hotel_to_area_list(hotels)
                areas = set([hotel_area[1] for hotel_area in hotel_to_area_list])

                [hotel_to_area.update({ha[0]: ha[1]}) for ha in hotel_to_area_list]

                for hotel, location in hotel_to_location_list:
                    if hotel not in hotel_to_location:
                        hotel_to_location[hotel] = []
                    hotel_to_location[hotel].append(location)


                area_to_hotels = self.get_area_to_hotels(areas)
                location_to_hotels = self.get_location_to_hotels(locations)

                # Getting hotel to booking basis device
                for device in devices.split(','):
                    if self.__device_hotel_to_booking:
                        if self.__device_hotel_to_booking.get(device):
                            app.config['LOG'].info("already got hotel to booking for " + device)
                        else:
                            hotel_to_booking = self.__get_hotel_to_booking(device)
                            self.__device_hotel_to_booking.update({device: hotel_to_booking})
                    else:
                        hotel_to_booking = self.__get_hotel_to_booking(device)
                        self.__device_hotel_to_booking.update({device: hotel_to_booking})

            # Step 4: Scoring new hotels basis device and pushing boosted hotels to db
            for device in devices.split(','):
                if filtered_device_hotel_to_city_list.get(device):
                    hotel_scores = self.score_new_hotels_basis_device(is_dom, device,
                                                                      filtered_device_hotel_to_city_list.get(device),
                                                                      hotel_to_area, hotel_to_location, area_to_hotels,
                                                                      location_to_hotels,
                                                                      self.__device_hotel_to_booking.get(device),
                                                                      self.__device_sequence.get(device))
                    self.__cu.push_boosted_hotels_to_db_basis_device(is_dom, device, device_to_uid.get(device),
                                                                     filtered_device_hotel_to_city_list.get(device),
                                                                     hotel_scores)
                else:
                    app.config['LOG'].info(
                        "no hotels to be scored, skipping scoring new hotels and pushing boosted hotels for device " + str(
                            device))

            mail_content = "Successful Greenhorn Candidate Generation. \nSee tables filtered_hotels, boosted_hotels and boosted_sequence for more information."
            app.config['LOG'].info(self.__log + "generated candidates")

        except Exception as e:
            mail_content = "Exception while Greenhorn Candidate Generation ::\n\n" + str(
                e) + '\n\nSee /opt/logs/Hotel-Personalization/upsell/ for more information.'
            app.config['LOG'].error(self.__log + "exception while generating candidates" + str(e))
        finally:
            mu = mail_util()
            mail_subject = app.config['MAIL_SUBJECT_GH_CANDIDATE_GENERATION']
            if is_dom:
                mail_subject = mail_subject + " Domestic"
            else:
                mail_subject = mail_subject + " International"

            mu.send_mail(mail_subject, mail_content, app.config['MAIL_TO_GH_CANDIDATE_GENERATION'])

    def get_new_hotel_city_list(self, is_dom):
        hotel_city_list = []
        app.config['LOG'].info(self.__log + "getting new hotels")
        try:
            cur = self.__dao_mysql_mmt_read.get_new_hotel_city_list(is_dom)
            hotel_list =pd.DataFrame(x[0] for x in cur.fetchall())
            app.config['LOG'].info(self.__log + "got new hotels - ", str(len(hotel_list)))
            hotel_list.columns = ['hotel_id']
            hotel_list = hotel_list.astype({'hotel_id': str})
            hotel_location_filtered_df = self.hotel_location_df[
                (self.hotel_location_df.location_type != 'Poi') & (self.hotel_location_df.location_type != 'Area')]
            hotel_city_list_df = pd.merge(hotel_list,
                                          hotel_location_filtered_df[['hotel_id', 'location_id', 'hotel_country_id']],
                                               on=['hotel_id'])

            if is_dom:
                hotel_city_list_df = hotel_city_list_df[(hotel_city_list_df['hotel_country_id'] == 'IN')]
            else:
                hotel_city_list_df = hotel_city_list_df[(hotel_city_list_df['hotel_country_id'] != 'IN')]
            hotel_city_list_df = hotel_city_list_df[['hotel_id','location_id']]
            hotel_city_list = list(zip(hotel_city_list_df['hotel_id'], hotel_city_list_df['location_id']))

            top_cities_intl = app.config['TOP_CITIES_INTL']
            if not is_dom and top_cities_intl:
                hotel_city_list = [hotel_city for hotel_city in hotel_city_list if
                                   hotel_city[1] in top_cities_intl]

        except Exception as e:
            raise Exception(e, "exception while getting new hotels")
        return hotel_city_list

    def is_hotel_to_attributes_required(self):
        return self.__filters.__contains__("filter_hotels_basis_content") \
               or self.__filters.__contains__("filter_hotels_basis_inventory") \
               or self.__filters.__contains__("filter_hotels_basis_review") \
               or self.__scorers.__contains__("score_basis_quality")

    def get_hotel_to_attributes(self, hotel_location_list):
        app.config['LOG'].info(self.__log + "Getting hotel to attributes")
        hotel_to_attributes = dict()
        if hotel_location_list:
            try:
                location_set = {location for hotel, location in hotel_location_list}
                hotel_attributes_handler = CommonHandler("HOTEL_ATTRIBUTES")
                cached_hotel_to_attributes, location_to_median_qs = hotel_attributes_handler.get_response(list(location_set))
                for hotel, location in hotel_location_list:
                    cached_attributes = cached_hotel_to_attributes.get(hotel, {'cs': 0.0, 'is': 0.0, 'rs': 0.0, 'qs': 0.0})
                    median_qs = location_to_median_qs.get(location, 0.0)
                    attributes = dict(cached_attributes)
                    if attributes['qs'] == 0.0:
                        attributes['qs'] = median_qs
                        app.config['LOG'].info(self.__log + "Setting QS of hotel {} as median QS of location {}"
                                               .format(hotel, location))
                    hotel_to_attributes[hotel] = attributes
                app.config['LOG'].info(self.__log + "Got hotel to attributes - " + str(len(hotel_to_attributes)))
            except Exception as e:
                raise Exception(e, "Exception while getting hotel to attributes")
        return hotel_to_attributes

    def filter_new_hotels(self, is_dom, hotel_city_list, devices, device_to_uid):
        app.config['LOG'].info(self.__log + "filtering new hotels")
        cf = candidate_filter(self.__dao_mysql, self.__dao_mysql_mmt_read)
        for filter in self.__filters:
            app.config['LOG'].info(self.__log + "started filter " + filter)
            if hotel_city_list:
                try:
                    function = getattr(cf, filter)
                    filtered_data = function(hotel_city_list, self.__hotel_to_attributes)
                    hotel_city_list = filtered_data[0]
                    self.__cu.push_filtered_hotels_to_db(is_dom, filter, devices, device_to_uid, filtered_data)
                    app.config['LOG'].info(self.__log + "completed filter " + filter)
                except Exception as e:
                    raise Exception(e, "exception while using filter " + filter)
            else:
                app.config['LOG'].info(self.__log + "no hotels to be filtered, skipping filter " + filter)

        app.config['LOG'].info(self.__log + "filtered new hotels")
        return hotel_city_list

    def filter_new_hotels_basis_device(self, is_dom, device, hotel_city_list, city_hotelcount_dict, sequences, uid):
        app.config['LOG'].info(self.__log + "filtering new hotels for sequence " + str(sequences))
        cf = candidate_filter(self.__dao_mysql, self.__dao_mysql_mmt_read)
        filters = app.config['NEW_HOTELS_FILTERS_DEVICE']
        for filter in filters:
            app.config['LOG'].info(self.__log + "started filter " + filter + " for device " + device)
            if hotel_city_list:
                try:
                    function = getattr(cf, filter)
                    filtered_data = function(hotel_city_list, city_hotelcount_dict, sequences[0], sequences[1], device)
                    hotel_city_list = filtered_data[0]
                    self.__cu.push_filtered_hotels_to_db_basis_device(is_dom, filter, device, uid, filtered_data)
                    app.config['LOG'].info(self.__log + "completed filter " + filter + " for device " + device)
                except Exception as e:
                    raise Exception(e, "exception while using filter " + filter + " for device " + device)
            else:
                app.config['LOG'].info(
                    self.__log + "no hotels to be filtered, skipping filter " + filter + " for device " + device)
        app.config['LOG'].info(self.__log + "filtered new hotels for sequence " + str(sequences))
        return hotel_city_list

    def score_new_hotels_basis_device(self, is_dom, device, hotel_to_city_list, hotel_to_area, hotel_to_city,
                                      area_to_hotels, city_to_hotels, hotel_to_booking, sequences):
        app.config['LOG'].info(self.__log + "scoring new hotels for device " + device)
        hotel_score = {}

        cs = candidate_scorer(self.__dao_mysql, self.__dao_mysql_mmt_read)

        hotel_score_scorerwise = {}
        for scorer in self.__scorers:
            app.config['LOG'].info(self.__log + "started scorer - " + scorer)
            if hotel_to_city_list:
                try:
                    function = getattr(cs, scorer)
                    hotel_score_scorer = function(is_dom, device, hotel_to_city_list, hotel_to_area, hotel_to_city, area_to_hotels,
                                                  city_to_hotels, hotel_to_booking, sequences[0],
                                                  self.__hotel_to_attributes)
                    hotel_score_scorerwise.update({scorer: hotel_score_scorer})
                    app.config['LOG'].info(
                        self.__log + "completed scorer " + scorer + ", normalized score for device " + device + " " + str(
                            hotel_score_scorer))
                except Exception as e:
                    raise Exception(e, "exception while using scorer " + scorer + " for device " + device)
            else:
                app.config['LOG'].info(
                    self.__log + "no hotels to be scored, skipping scorer " + scorer + " for device " + device)

        # Calculate overall score for hotel
        if hotel_score_scorerwise:
            app.config['LOG'].info(self.__log + "calculating overall score for hotels for device " + device)
            for hotel, city in hotel_to_city_list:
                try:
                    score = 0
                    for scorer in self.__scorers:
                        if hotel_score_scorerwise.get(scorer):
                            multiplier = 0
                            if scorer == 'score_basis_area':
                                multiplier = app.config['SCORE_MULTIPLIER_AREA']
                            elif scorer == 'score_basis_quality':
                                multiplier = app.config['SCORE_MULTIPLIER_QUALITY']
                            elif scorer == 'score_basis_rank':
                                multiplier = app.config['SCORE_MULTIPLIER_RANK']
                            if hotel_score_scorerwise.get(scorer).get(hotel):
                                score = score + multiplier * hotel_score_scorerwise.get(scorer).get(hotel)
                    hotel_score.update({hotel: round(score, 3)})
                except Exception as e:
                    raise Exception(e, "exception while calculating overall score for hotel " + str(
                        hotel) + " for device " + device)
            app.config['LOG'].info(
                self.__log + "calculated overall score for device " + device + " " + str(hotel_score))

        app.config['LOG'].info(self.__log + "scored new hotels for " + device)
        return hotel_score

    def get_hotel_count_for_location(self, hotel_location_list):
        app.config['LOG'].info(self.__log + "getting hotel count for cities")
        location_hotel_count_dict = {}
        if hotel_location_list:
            try:
                location_hotel_count =self.hotel_location_df.groupby(['location_id']).hotel_id.agg({'count'}).reset_index()
                location_hotel_count = dict(zip(location_hotel_count['location_id'], location_hotel_count['count']))

                for row in hotel_location_list:
                    location_hotel_count_dict.update({row[1]: (location_hotel_count.get(row[1]))})
                app.config['LOG'].info(self.__log + "got hotel count for cities")
            except Exception as e:
                raise Exception(e, "exception while getting hotel count for cities")
        return location_hotel_count_dict

    def get_hotel_to_area_list(self, hotels):
        hotel_to_area_list = []
        app.config['LOG'].info(self.__log + "getting hotel to area list")
        try:
            hotel_area_df = self.hotel_location_df[(self.hotel_location_df.hotel_id.isin(hotels)) & (self.hotel_location_df['location_type'] == 'Area')]
            hotel_to_area_list = list(zip(hotel_area_df['hotel_id'], hotel_area_df['location_id']))

            app.config['LOG'].info(self.__log + "got hotel to area list - " + str(len(hotel_to_area_list)))
        except Exception as e:
            raise Exception(e, "exception while getting hotel to area list")
        return hotel_to_area_list

    def get_area_to_hotels(self, areas):
        area_to_hotels = {}
        app.config['LOG'].info(self.__log + "getting area to hotels")
        try:
            hotel_area_df = self.hotel_location_df[(self.hotel_location_df.location_id.isin(areas)) & (self.hotel_location_df['location_type'] == 'Area')]
            area_hotel_df = hotel_area_df.groupby('location_id')['hotel_id'].apply(list).reset_index()
            area_to_hotels = dict(zip(area_hotel_df['location_id'],area_hotel_df['hotel_id']))

            app.config['LOG'].info(self.__log + "got area to hotels - " + str(len(area_to_hotels)))
        except Exception as e:
            raise Exception(e, "exception while getting area to hotels")
        return area_to_hotels

    def get_location_to_hotels(self, locations):
        location_to_hotels = {}
        app.config['LOG'].info(self.__log + "getting city to hotels")
        try:
            hotel_location_df = self.hotel_location_df[(self.hotel_location_df['location_type'] != 'Area')
                                                        & (self.hotel_location_df['location_type'] != 'Poi')
                                                        & (self.hotel_location_df.location_id.isin (locations))]
            location_hotel_df = hotel_location_df.groupby('location_id')['hotel_id'].apply(list).reset_index()
            location_to_hotels = dict(zip(location_hotel_df['location_id'],location_hotel_df['hotel_id']))

            app.config['LOG'].info(self.__log + "got location to hotels - " + str( len(location_to_hotels)))
        except Exception as e:
            raise Exception(e, "exception while getting location to hotels")
        return location_to_hotels

    def __get_hotels(self, hotel_to_area_or_city):
        area_or_city_to_hotels = {}
        for htaoc in hotel_to_area_or_city:
            hotel = htaoc[0]
            area_or_city = htaoc[1]
            hotels = area_or_city_to_hotels.get(area_or_city)
            if not hotels:
                hotels = []
            if hotel not in hotels:
                hotels.append(hotel)
                area_or_city_to_hotels.update({area_or_city: hotels})
        return area_or_city_to_hotels

    def __get_hotel_to_booking(self, device):
        hotel_to_booking = {}
        app.config['LOG'].info(self.__log + "getting hotel to booking for " + device)
        try:
            cur = self.__dao_mysql_hotel_booker.get_bookings_for_hotels(device)
            [hotel_to_booking.update({hotel_booking[0]: hotel_booking[1]}) for hotel_booking in cur.fetchall()]
            app.config['LOG'].info(
                self.__log + "got hotel to booking for " + device + " " + str(len(hotel_to_booking)))
        except Exception as e:
            raise Exception(e, "exception while getting hotel to booking for device " + device)
        return hotel_to_booking

    def __get_hotel_to_content_score(self):
        hotel_to_content_score = {}
        app.config['LOG'].info(self.__log + "getting hotel to content score")
        try:
            cur = self.__dao_mysql.get_hotels_content_score()
            [hotel_to_content_score.update({hotel_content_score[0]: hotel_content_score[1]}) for
             hotel_content_score in cur.fetchall()]
            app.config['LOG'].info(self.__log + "got hotel to content score" + str(len(hotel_to_content_score)))
        except Exception as e:
            raise Exception(e, "exception while getting hotel to content score")
        return hotel_to_content_score

    def __del__(self):
        app.config['LOG'].info(self.__log + "destructing")
        self.__dao_mysql.close()
        self.__dao_mysql_mmt_read.close()
        self.__dao_mysql_hotel_booker.close()
        app.config['LOG'].info(self.__log + "destructed")
