# Author: <PERSON><PERSON><PERSON>
# Date: 23 Jan 2018

# coding: utf-8

import pymysql
from flask import current_app as app

from .aws import secret_manager_config


class MySQLConnection(object):
    __database = ""

    __connection = None
    __cursor = None

    def __init__(self, __database,auto_commit = True):

        app.config['LOG'].info("Establishing MySQL connection" + __database)
        self.__database = __database

        DB_SECRETS = secret_manager_config.get_secret("PROD", __database)

        self.__connection = pymysql.connect(host="**************",
                                            port=3306,
                                            user='mmtweb1',
                                            password='p@55word',
                                            db='mmt',
                                            read_timeout=app.config['MYSQL_READ_TIMEOUT'])

        self.__connection.autocommit(auto_commit)
        self.__cursor = self.__connection.cursor()

    def run_query(self, query, params=None, retry=3):
        retry_count = 3 - retry + 1
        try:
            if params == None:
                self.__cursor.execute(query)
            else:
                self.__cursor.execute(query, params)
            app.config['LOG'].info("Number of rows impacted: %d" % self.__cursor.rowcount)
        except (
                pymysql.err.OperationalError, pymysql.err.InterfaceError, pymysql.err.DataError,
                pymysql.err.InternalError,
                pymysql.err.IntegrityError, pymysql.err.OperationalError, pymysql.err.NotSupportedError,
                pymysql.err.ProgrammingError) as e:
            app.config['LOG'].error(str(type(e).__name__) + str(e) + " while executing query " + str(query))
            app.config['LOG'].info("Re-connecting MySQL " + self.__database)
            self.__connection.ping()
            app.config['LOG'].info("Re-connected MySQL " + self.__database)
            if retry:
                app.config['LOG'].info(
                    "Re-trying (retry count = " + str(retry_count) + "), MySQL query " + str(query))
                retry -= 1
                return self.run_query(query, params, retry)
            else:
                raise Exception(e, "exception while running MySQL query " + str(query))
        return self.__cursor

    def run_bulk_query(self, query, params, retry=3):
        retry_count = 3 - retry + 1
        try:
            if params == None:
                self.__cursor.executemany(query)
            else:
                self.__cursor.executemany(query, params)
            app.config['LOG'].info("Number of rows impacted: %d" % self.__cursor.rowcount)
        except (
                pymysql.err.OperationalError, pymysql.err.InterfaceError, pymysql.err.DataError,
                pymysql.err.InternalError,
                pymysql.err.IntegrityError, pymysql.err.OperationalError, pymysql.err.NotSupportedError,
                pymysql.err.ProgrammingError) as e:
            app.config['LOG'].error(str(type(e).__name__) + str(e) + " while executing query " + str(query))
            app.config['LOG'].info("Re-connecting MySQL " + self.__database)
            self.__connection.ping()
            app.config['LOG'].info("Re-connected MySQL " + self.__database)
            if retry:
                app.config['LOG'].info(
                    "Re-trying (retry count = " + str(retry_count) + "), MySQL query " + str(query))
                retry -= 1
                return self.run_bulk_query(query, params, retry)
            else:
                raise Exception(e, "exception while running MySQL query " + str(query))
        return self.__cursor

    def close(self):
        app.config['LOG'].info("Closing MySQL connection" + self.__database)
        self.__cursor.close()
        self.__connection.close()

    def commit(self):
        self.__connection.commit();

    def rollback(self):
        self.__connection.rollback();
