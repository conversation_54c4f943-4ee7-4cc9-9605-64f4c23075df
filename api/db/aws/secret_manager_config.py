import logging
import base64
import boto3
import json
from botocore.exceptions import ClientError


logger = logging.getLogger(__name__)

def get_secret(env, database):

    SECRET_STRING='SecretString'
    REGION_NAME = "ap-south-1"

    prod_secret_mapping = {
        "_HTL_CONTENT": "/prod/hotels/mysqldb/supplier/HTL_CONTENT/htlcontent/credentials",
        "_HOTEL_BOOKER": "/prod/hotels/mysqldb/booker/hotel_booker/htlcontent/credentials",
        "_MMT_READ": "/prod/hotels/mysqldb/supplier/mmt/htlcontent/credentials",
        "_MMT_WRITE": "/prod/hotels/mysqldb/supplier/mmt/htlcontent/credentials",
        "_COUCHBASE": "/prod/hotels/hotels-singularity/secrets",
        "_MMT3_READ": "/prod/hotels/mysqldb/supplier/mmt/htlcontent/credentials",
        "_AEROSPIKE": "/dev/hotels/hotels-ranking/secrets"
    }

    stage_secret_mapping = {
        "_HTL_CONTENT": "/alpha/hotels/mysqldb/supplier/HTL_CONTENT/htlcontentstg/credentials",
        "_HOTEL_BOOKER": "/alpha/hotels/mysqldb/booker/hotel_booker/htlcontentstg/credentials",
        "_MMT_READ": "/alpha/hotels/mysqldb/supplier/mmt/htlcontentstg/credentials",
        "_MMT_WRITE": "/alpha/hotels/mysqldb/supplier/mmt/htlcontentstg/credentials",
        "_COUCHBASE": "/alpha/hotels/hotels-singularity/secrets",
        "_MMT3_READ": "/alpha/hotels/mysqldb/supplier/mmt/htlcontentstg/credentials"
    }

    dev_secret_mapping = {
        "_HTL_CONTENT": "/dev/hotels/mysqldb/payments/mmt/mmtweb1/credentials",
        "_HOTEL_BOOKER": "/dev/hotels/mysqldb/payments/mmt/mmtweb1/credentials",
        "_MMT_READ": "/dev/hotels/mysqldb/payments/mmt/mmtweb1/credentials",
        "_MMT_WRITE": "/dev/hotels/mysqldb/payments/mmt/mmtweb1/credentials",
        "_COUCHBASE": "/dev/hotels/hotels-singularity/secrets",
        "_MMT3_READ": "/dev/hotels/mysqldb/payments/mmt/mmtweb1/credentials"
    }

    env = "DEV"
    env = env.upper()

    if(env=="DEV"):
        env_secret_mapping = dev_secret_mapping
    elif(env=="STAGE"):
        env_secret_mapping = stage_secret_mapping
    else:
        env_secret_mapping = prod_secret_mapping

    secret_name = env_secret_mapping[database]
    

    # Create a Secrets Manager client
    session = boto3.session.Session()
    client = session.client(
        service_name='secretsmanager',
        region_name=REGION_NAME
    )
    
    try:
        get_secret_value_response = client.get_secret_value(
            SecretId=secret_name
        )
        # Decrypts secret using the associated KMS CMK. Depending on whether the secret is a string or binary
        if SECRET_STRING in get_secret_value_response:
            secret = get_secret_value_response[SECRET_STRING]
            db_secrets = json.loads(secret)
        else:
            decoded_binary_secret = base64.b64decode(get_secret_value_response['SecretBinary'])
            db_secrets = json.loads(decoded_binary_secret)
    except Exception as ex:
        db_secrets = dict()
        logger.critical(f"Exception in secretsmanager {ex} {ex.response}")
        raise ex
    return db_secrets
