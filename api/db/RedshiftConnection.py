# Author: <PERSON><PERSON><PERSON>
# Date: 10 May 2018

# coding: utf-8

import csv
import logging
import psycopg2

from flask import current_app as app

class RedshiftConnection(object):

    __connection = None
    __cursor = None

    def __init__(self):
        app.config['LOG'].info("Establishing Redshift connection")

        self.__connection=psycopg2.connect(dbname = app.config['RS_MMT_DB'],
                                  host = app.config['RS_HOST'],
                                  port= app.config['RS_PORT'],
                                  user = app.config['RS_USER'],
                                  password= app.config['RS_CONFIG_P'])
        self.__cursor = self.__connection.cursor()

    def execute_ns_query_list(self, query_list):
        for query in query_list:
            self.__cursor.execute(query)
        self.__connection.commit()
        app.config['LOG'].info("%d queries executed successfully"%len(query_list))

    def execute_ns_query(self, query):
        self.__cursor.execute(query)
        self.__connection.commit()
        app.config['LOG'].info("Query executed successfully")

    def sel_query(self, query, inc_cols = False):
        self.__cursor.execute(query)
        res = self.__cursor.fetchall()
        app.config['LOG'].info("Returning %d records"%len(res))
        if inc_cols:
            columns=[desc[0] for desc in self.cur.description]
            return [res, columns]
        else:
            return res

    def close(self):
        self.__cursor.close()
        self.__connection.close()
        app.config['LOG'].info("Closing Redshift connection")

