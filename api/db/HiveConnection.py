# Author: <PERSON><PERSON><PERSON>
# Date: 31 Jan 2018

# coding: utf-8

import csv
from impala.dbapi import connect
import os
#import pyhs2
from flask import current_app as app

class HiveConnection:

    __connection = None

    def __init__(self):
        app.config['LOG'].info("Establishing HIVE connection")

        os.system("kinit -kt " + app.config['HIVE_KEYTAB'])
        self.__connection = connect(host=app.config['HIVE_HOST'],
                        port=app.config['HIVE_PORT'],
                        auth_mechanism="GSSAPI",
                        kerberos_service_name='hive')

    def select_query(self, query, output_file = None):
        return self.query(query, output_file=output_file, isSelectQuery=True)
    
    def nonselect_query(self, query):
        return self.query(query, isSelectQuery=False)
        
    def auto_query(self, query, output_file=None):
        isSelectQuery = True if query.lstrip()[:6].lower() == 'select' else False
        return self.query(query, output_file=output_file, isSelectQuery=isSelectQuery)
        
    def query(self, query, output_file = None, isSelectQuery=False):
        ret_val = None
        with self.__connection.cursor() as cur:
            cur.execute(query)
            if isSelectQuery:
                if output_file is None:
                    res = cur.fetchall()
                    app.config['LOG'].info("Query executed successfully. No. of rows returned : %d"%len(res))
                    ret_val = res
                else:
                    csv_file = csv.writer(open(output_file,"wb"))

                    num_recs = 0
                    col_names = [i[0] for i in cur.description]
                    csv_file.writerow(col_names)
                    for row in cur:
                        csv_file.writerow(row)
                        num_recs += 1
                    app.config['LOG'].info("Query executed successfully. %d records written to file '%s'"%(num_recs, output_file))

                    if num_recs == 0:
                        raise ValueError('zero records found')

                    ret_val = num_recs   
        return ret_val

    def close(self):
        app.config['LOG'].info("Closing HIVE connection")
        self.__connection.close()

