# Author: <PERSON><PERSON><PERSON>
# Date: 19 Jan 2018

# coding: utf-8

# python imports
import os
import sys
import json
import math
import time


# flask imports
import threading

from flask import Flask, request, jsonify, render_template, make_response, redirect, url_for, session, send_file

from datetime import timed<PERSON><PERSON>
from flask_jsglue import JSGlue

from validator.authenticate import User, Anonymous, Authentication
from common.booster_logging import BoosterStatusLogger

# ensure that application directory is on path
app_dir = os.path.dirname(__file__)
sys.path.append(app_dir)

# my local imports

from flask_login import LoginManager, login_required, login_user, logout_user, current_user
from resources.ReadRequest import ReadRequest
from resources.GreenHorn import GreenHorn
from resources.ExploreSetValidator import ExploreSetValidator
from resources.Uploader import Uploader
from resources.FetchSequence import FetchSequence
from resources.ManualRankManager import ManualRankManager
from resources.tribe_model import TribeModel

from handlers.comparator.comparator_req_handler import Comparator<PERSON>andler
from handlers.comparator.hotstore_handler import Comparator<PERSON>otStoreHandler
from handlers.comparator.dr_handler import Comparator<PERSON><PERSON><PERSON><PERSON>
from handlers.comparator.mm_handler import ComparatorMMHandler

from common import helpers as hp
from common.aerospike_wrapper import AerospikeWrapper
from common.redis_cache_manager import RedisCacheManager
from common.mail_util import mail_util
from dao.UpdateLocationData import UpdateLocationData
import yaml
from common.aws.s3 import S3Client


from candidate_generation.candidate_generator import candidate_generator

from automated_booster.automated_booster_refresher import AutomatedBoosterRefresher

# TODO change the API definition to the specs of application factory
#  as specified in http://flask.pocoo.org/docs/1.0/tutorial/factory/


def create_app(config=None, updates=None):
    app = Flask(__name__)

    gunicorn_config = os.path.dirname(os.path.realpath(__file__)) + "/config/gunicorn_config.yaml"
    with open(gunicorn_config, 'r') as cf:
        gunicorn_cfg = yaml.safe_load(cf)

    # root folder
    APP_ROOT_FOLDER = os.path.abspath(os.path.dirname(__file__))

    # configurations
    config_map = {
        "default": "config.config_obj.DefaultConfig",
        "testing": "config.config_obj.TestingConfig"
    }

    # read parameters from configuration file
    if config is None:
        config = "testing"
    app.config.from_object(config_map[config])

    # set log path
    app.config['LOG'] = hp.mdc_logger("greenhorn")
    app.config['SECRET_KEY'] = 'random key for form'
    # set upload location
    app.config['UPLOAD_FOLDER'] = APP_ROOT_FOLDER + '/data/'
    if updates is not None:
        # add extra settings
        app.config.update(**updates)

    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = '/login'
    login_manager.anonymous_user = Anonymous
    # user_store = CacheManager(app.config)
    user_store_asp = AerospikeWrapper(app.config)
    redis_conn = RedisCacheManager(app.config)
    app.config['redis_conn'] = redis_conn
    # app.config['cb_obj'] = user_store
    app.config['asp_obj'] = user_store_asp
    jsglue = JSGlue(app)

    app.config['pms_prop'] = hp.read_pms()

    #trigger download file htl_loc_mapping_without_poi
    app.config['pms_prop']
    sys.tracebacklimit = 0

    @app.before_request
    def before_request():
        session.permanent = True
        app.permanent_session_lifetime = timedelta(milliseconds=app.config['COOKIES_EXPIRY_TIME'])
        session.modified = True

    @login_manager.user_loader
    def load_user(username):
        namespace = app.config['AEROSPIKE_NAMESPACE']
        set = app.config['AEROSPIKE_COMMON_DATA_SET']
        flag = True
        # flag = user_store.get_single(username)
        if flag == None:
            return None
        user = User(username, flag)
        return user

    @app.route('/login', methods=['GET'])
    def login():
        return render_template('ldapAuthentication.html', error="")

    @app.route('/booster-restriction-buckets', methods=['GET'])
    def get_booster_restrictions():
        booster_restrictions = "/config/booster_restrictions.json"
        config_file = os.path.dirname(os.path.realpath(__file__)) + booster_restrictions

        with open(config_file, 'r') as f:
            response = json.load(f)

        return jsonify(response)


    @app.route('/manualRank', methods=['GET'])
    def manualRank():
        return render_template('manualRanking.html')

    @app.route("/pushRank", methods=['POST'])
    def pushRank():
        data_dir = app.config['DATADIR']
        fname = app.config['HOTEL_LISTABLE_LOCATION_MAPPING']
        rank_mgr = ManualRankManager()
        cityList = request.form.get('cityList').split(',')
        brand = request.form.get('brand')
        # app.config['LOG'].info("Cities " + str(len(cityList)))
        result = rank_mgr.push_rank(os.path.join(data_dir, fname), request.files['fileName'], cityList, brand)
        msg = "Success"
        if result == False:
            msg = "Failure"
        return jsonify({"success": result, "message": msg})

    @app.route("/authenticate", methods=['POST'])
    def authentication():
        error = ""
        username = request.form.get('username')
        password = request.form.get('password')
        domain = request.form.get('domain')
        auth = Authentication()
        resp = auth.user_autheticate(username, password, domain)
        if resp["success"]:
            user = User(username, True)
            # user_store.put(username, True)
            namespace = app.config['AEROSPIKE_NAMESPACE']
            set = app.config['AEROSPIKE_COMMON_DATA_SET']
            ttl = app.config['AEROSPIKE_TTL']
            user_store_asp.set_data(username, True, set, namespace, ttl)
            login_user(user, remember=False)
            return redirect(url_for('static_page'))
        else:
            return render_template('ldapAuthentication.html', error=resp["message"])

    @app.route('/logout', methods=['GET'])
    @login_required
    def logout():
        # user_store.remove(current_user.username)
        namespace = app.config['AEROSPIKE_NAMESPACE']
        set = app.config['AEROSPIKE_COMMON_DATA_SET']
        user_store_asp.remove_data(current_user.username, set, namespace)
        logout_user()
        return redirect(url_for('login'))

    # GH UI
    @app.route('/GreenHornUploader')
    @login_required
    def static_page():
        return render_template('greenhorn.html')

    # GH active sequence
    @app.route('/FetchSequence', methods=['GET'])
    @login_required
    def triggerFetchSequence():
        mode = request.args.get("mode")
        callback = request.args.get("callback")
        fs = FetchSequence()
        return_data = fs.fetch_sequence(mode)
        return callback + '(' + return_data + ')'


    # Download hotel_location_mapping file from s3
    @app.route('/DownloadFromS3', methods=['GET'])
    def downloadFromS3():
        message = "SUCCESS"
        try:
            trigger_download_from_s3(app)
        except Exception:
            message = "FAILURE"
        return jsonify({"message": message})

    booster_logger = BoosterStatusLogger()
    # GH add sequence
    @app.route('/addSequence', methods=['POST'])
    # @login_required
    def triggerUploader():
        cr = ReadRequest()
        request_status, message = cr.read_request_add_bu()
        source = cr.params.get('source')
        is_auto = True if source == "AUTOBOOSTERS" else False
        booster_id = cr.params.get('auto_booster_id') if is_auto else "MANUAL"
        server_timestamp = int(time.time() * 1000)
        app.config['LOG'].info("Creating booster :{}".format(booster_id))
        if request_status:
            app.config['LOG'].error("Booster {}:: Error: {}".format(booster_id, message))
            try:
                booster_logger.log({"booster_id" : booster_id,"status":"FAILED",
                                    "error_code": "GH_VALIDATION_FAILED", "reason" : message,
                                    "server_timestamp": server_timestamp}, is_auto)
            except Exception as e1:
                app.config['LOG'].error("Booster logging: Exception: {}".format(str(e1)))
            return jsonify({"success": False, "message": message})
        bu = Uploader()
        try:
            succees, message = bu.initializeDataForAdd()
            if succees:
                succees, message = bu.addNewSequence()
            else:
                try:
                    booster_logger.log({"booster_id": booster_id, "status": "FAILED",
                                        "error_code": "GH_INIT_DATA_FAILED", "reason": message,
                                        "server_timestamp": server_timestamp}, is_auto)
                except Exception as e1:
                    app.config['LOG'].error("Booster logging: Exception: {}".format(str(e1)))
                app.config['LOG'].error("Error: {}".format( message))
                return jsonify({"success": succees, "message": message})
        except Exception as e:
            #TODO: Kafka logging on exception also
            app.config['LOG'].error("Exception: {}".format(e))
            succees, message = False, "Unknown Failure Occurred"

        if succees:
            app.config['LOG'].info("Successfully created booster ")
            try:
                booster_logger.log({"booster_id": booster_id, "status": "SUCCESS",
                                    "reason": "Booster successfully created", "server_timestamp": server_timestamp}, is_auto)
            except Exception as e1:
                app.config['LOG'].error("Booster logging: Exception: {}".format(str(e1)))
        else:
            app.config['LOG'].error("Error: {}".format(message))
            try:
                booster_logger.log({"booster_id": booster_id, "status": "FAILED",
                                "error_code": "GH_ADD_SEQUENCE_FAILED", "reason": message,
                                    "server_timestamp": server_timestamp}, is_auto)
            except Exception as e1:
                app.config['LOG'].error("Booster logging: Exception: {}".format(str(e1)))
        return jsonify({"success": succees, "message": message})

    @app.route('/deleteSequence', methods=['POST'])
    @login_required
    def triggerDelete():
        cr = ReadRequest()
        request_status = cr.read_request_delete_bu()
        if request_status:
            return "FAILURE"
        bu = Uploader()
        try:
            bu.initializeDataForDelete()
            succees, message = bu.deleteSequence()
        except Exception as e:
            app.config['LOG'].error("Error in delete sequence: " + str(e))
            succees, message = False, "Unknown Failure Occurred"
        return jsonify({"success": succees, "message": message})

    #write a api to check delete booster status
    @app.route('/deleteSequenceStatus', methods=['POST'])
    #@login_required
    def triggerDeleteStatus():

        app.config['LOG'].info("Delete Booster Status")
        cr = ReadRequest()
        request_status = cr.read_request_delete_status()
        if request_status:
            return "FAILURE"
        bu = Uploader()
        try:
            bu.initializeDataForDeleteStatus()
            succees, message = bu.deleteSequenceStatus()
        except Exception as e:
            app.config['LOG'].error("Error in delete sequence status: " + str(e))
            succees, message = False, "Unknown Failure Occurred"
        return jsonify({"success": succees, "message": message})


    @app.route('/deleteBulkSequence', methods=['POST'])
    @login_required
    def triggerBulkDelete():
        bu = Uploader()
        try:
            succees, message = bu.initializeAndDeactivateBoostersForBulkDelete()
        except Exception as e:
            app.config['LOG'].error("Error in bulk delete sequence: " + str(e))
            succees, message = False, "Unknown Failure Occurred"
        return jsonify({"success": succees, "message": message})

    @app.route('/appendSequence', methods=['POST'])
    @login_required
    def triggerAppend():
        cr = ReadRequest()
        request_status = cr.read_request_modify_bu()
        if request_status:
            return "FAILURE"
        bu = Uploader()
        try:
            succees, message = bu.initializeDataForAppendRemove()
            if succees:
                succees, message = bu.appendSequence()
            else:
                return jsonify({"success": succees, "message": message})
        except Exception as e:
            app.config['LOG'].error("Error in append sequence: " + e)
            succees, message = False, "Unknown Failure Occurred"
        return jsonify({"success": succees, "message": message})

    @app.route('/triggerDownLoad', methods=['POST'])
    def triggerDownLoad():
        cr = ReadRequest()
        request_status = cr.read_request_modify_bu()
        if request_status:
            return "FAILURE"
        bu = Uploader()
        try:
            bu.initializeDataForDownload()
            succees, boosted_hotels, boosted_sequence, boosted_hotels_header, boosted_sequence_header = bu.getData()
        except Exception as e:
            app.config['LOG'].error("Error in download: " + e)
            succees, boosted_hotels, boosted_sequence, boosted_hotels_header, boosted_sequence_header = False, [], [], [], []
        return jsonify({"success": succees, "boosted_hotels": boosted_hotels, "boosted_sequence": boosted_sequence,
                        "boosted_hotels_header": boosted_hotels_header,
                        "boosted_sequence_header": boosted_sequence_header})

    @app.route('/downloadSampleFile')
    def download_file():
        file_path = './data/sampleFile.csv'
        return send_file(file_path, as_attachment=False, add_etags=False, mimetype='text/plain')

    @app.route('/removeSequence', methods=['POST'])
    @login_required
    def triggerRemove():
        cr = ReadRequest()
        request_status = cr.read_request_modify_bu()
        if request_status:
            return "FAILURE"
        bu = Uploader()
        try:
            succees, message = bu.initializeDataForAppendRemove()
            if succees:
                succees, message = bu.removeSequence()
            else:
                return jsonify({"success": succees, "message": message})
        except Exception as e:
            app.config['LOG'].error("Error in remove sequence: " + e)
            succees, message = False, "Unknown Failure Occurred"
        return jsonify({"success": succees, "message": message})

    # Upsell
    @app.route('/getUpSellHotel', methods=['POST'])
    def index():
        params = {}
        params["upsellRequest"] = []
        params['totalHotelCounts'] = 0
        params['hotelList'] = []
        return json.dumps(params)

    # Comparator v2 without distance and recency ordering
    @app.route('/v2/getComparatorHotels', methods=['POST'])
    def comparatorV2():
        c = ComparatorHandler()
        return c.postV2()

    @app.route('/v3/getComparatorHotels', methods=['POST'])
    def comparatorV3():
        c = ComparatorHandler()
        return c.postV3()

    # Health check
    @app.route('/getUpSellHotel/healthCheck', methods=['GET'])
    def healthCheck():
        with open('workers_status.txt') as f:
            workers_status = json.load(f)
            if workers_status.get('count') is None:
                app.config['LOG'].error("Count variable not present")
                raise TypeError

            else:
                if int(workers_status.get('count')) >= math.ceil(gunicorn_cfg['workers'] / 2):
                    app.config['LOG'].info("Node is Healthy")
                    message = {
                        'message': 'UP',
                    }
                    resp = jsonify(message)
                    resp.status_code = 200
                else:
                    app.config['LOG'].info("Initializing Workers")
                    message = {
                        'message': 'Initializing Workers',
                    }
                    resp = jsonify(message)
                    resp.status_code = 202
        return resp

    # Caller: DAG (DS-models)
    @app.route('/candidateGeneration/<countries>/<devices>', methods=['GET'])
    def candidateGeneration(countries, devices):
        try:
            cg = candidate_generator()
            for country in countries.split(','):
                if country == 'dom':
                    cg.generate_candidates(True, devices)
                elif country == 'intl':
                    cg.generate_candidates(False, devices)
        except Exception as e:
            app.config['LOG'].error("exception while generating candidates" + e)
            mu = mail_util()
            mail_subject = app.config['MAIL_SUBJECT_GH_CANDIDATE_GENERATION']
            mail_content = "Exception while Greenhorn Candidate Generation ::\n\n" + str(
                e) + '\n\nSee /opt/logs/Hotel-Personalization/upsell/ for more information.'
            mu.send_mail(mail_subject, mail_content, app.config['MAIL_TO_GH_CANDIDATE_GENERATION'])
        return "COMPLETED"

    @app.route('/cacheRefreshAerospike')
    def triggerCacheRefreshAerospike():
        try:
            bu = Uploader()
            bu.triggerCacheRefreshAerospikeApi()
            success = True
            message = "Successfully refreshed Aerospike cache"
        except Exception as e:
            app.config['LOG'].error("Error in cache refresh: " + str(e))
            success, message = False,"Exception while refreshing aerospike cache :: \n\n " + str(
                e) + '\n\nSee /opt/logs/Hotel-Personalization/upsell/ for more information.'
        finally:
            mu = mail_util()
            mu.send_mail(app.config['MAIL_SUBJECT_AEROSPIKE_CACHE_REFRESH'], message, None)
        return jsonify({"success": success, "message": message})

    # Update hotel location table
    @app.route('/UpdateHotelLocationTable', methods=['GET'])
    def UpdateHotelLocationMappingTable():
        mail_content = ''
        try:
            update_location = UpdateLocationData()
            message = update_location.update_location_data()
            mail_content = 'Hotel_Location_Table Successfully Updated'
        except Exception as e:
            message = "FAILURE"
            app.config['LOG'].error("Exception while updating Hotel_location_mapping table" + e)
            mail_content = "Exception while Updating hotel_lcation_mapping table ::\n\n" + str(
                e) + '\n\nSee /opt/logs/Hotel-Personalization/upsell/ for more information.'
        finally:
            mu = mail_util()
            mu.send_mail(app.config['MAIL_SUBJECT_HOTEL_LOCATION_MAPPING'], mail_content, None)
        return jsonify({"message": message})


    # Error codes
    @app.errorhandler(400)
    def error_found(error):
        return make_response(
            jsonify({'responses': [], 'success': "false", 'error': [{'message': "Error", 'code': 400}]}),
            400)

    @app.errorhandler(404)
    def not_found(error):
        return make_response(
            jsonify({'responses': [], 'success': "false", 'error': [{'message': "Not Found", 'code': 404}]}), 404)

    @app.errorhandler(405)
    def not_allowed(error):
        return make_response(
            jsonify({'responses': [], 'success': "false", 'error': [{'message': "Method not allowed", 'code': 405}]}),
            405)

    @app.errorhandler(500)
    def processing_error(error):
        return make_response(
            jsonify({'responses': [], 'success': "false", 'error': [{'message': "Processing Error", 'code': 500}]}),
            500)

    return app


def trigger_download_from_s3(app):
    try:
        s3 = S3Client(app.config['S3_BKT'])
        fname = app.config['S3_FILE_HOTEL_LOCUS']
        data_dir = app.config['DATADIR']
        fname_without_poi = app.config['S3_FILE_HOTEL_LOCUS_WITHOUT_POI']
        hotel_data_csv = app.config['HOTEL_DATA_CSV']
        s3.download_file_from_s3(os.path.join(app.config['S3_BKT_KEY'], fname), os.path.join(data_dir, fname))
        s3.download_file_from_s3(os.path.join(app.config['S3_BKT_KEY'], fname_without_poi),
                                 os.path.join(data_dir, fname_without_poi))
        s3.download_file_from_s3(os.path.join(app.config['S3_BKT_KEY'], hotel_data_csv),
                                 os.path.join(data_dir, hotel_data_csv))
    except Exception as e:
        app.config['LOG'].error("Error occurred {}", e)
        raise e

    # Premium Hotel UI
    @app.route('/PremiumHotelUploader')
    def static_page_premium():
        return render_template('premiumHotel.html')


    # Caller: DAG (DS-models)
    @app.route('/AutomatedBooster/Refresh/<automated_booster_type>', methods=['POST'])
    def refreshAutomatedBooster(automated_booster_type):
        success = True
        try:
            automated_booster_refresher = AutomatedBoosterRefresher()
            automated_booster_refresher.refresh_automated_booster(automated_booster_type)
        except Exception as e:
            success = False
            app.config['LOG'].error("Exception while refreshing automated booster type " + automated_booster_type + ": " + str(e))
            mu = mail_util()
            mail_subject = app.config['MAIL_SUBJECT_AUTOMATED_BOOSTER_REFRESH']
            mail_content = "Exception while Automated Booster Refresh for " + automated_booster_type + ":\n\n" + str(
                e) + '\n\nSee /opt/logs/Hotel-Personalization/upsell/ for more information.'
            mu.send_mail(mail_subject, mail_content, app.config['MAIL_TO_AUTOMATED_BOOSTER_REFRESH'])
        return str(success)

    @app.route('/cacheRefresh', methods=['POST'])
    def triggerCache():
        cr = ReadRequest()
        request_status = cr.read_request_cache_refresh()
        message = ""
        if request_status:
            return "FAILURE"
        bu = Uploader()
        try:
            bu.initializeDataForCache()
            bu.triggerCacheRefreshApi()
            succees = True
        except Exception as e:

            app.config['LOG'].error("Error in cache refresh: " + str(e))
            succees, message = False, "Unknown Failure Occurred"
        return jsonify({"success": succees, "message": message})

manager = create_app()

if __name__ == '__main__':
    manager.run()
